<template>
  <div class="knowledge-overview">
    <div class="page-header">
      <h2>知识概览</h2>
      <p>知识库整体概况和核心指标</p>
      <div class="header-actions">
        <el-button type="primary" @click="createKnowledge">
          <el-icon><Plus /></el-icon>
          创建知识
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in statsData" :key="stat.key">
          <el-card class="stat-card" :class="stat.type">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="32">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
                  <el-icon><component :is="stat.trend > 0 ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ Math.abs(stat.trend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧内容 -->
        <el-col :xs="24" :lg="16">
          <!-- 热门知识 -->
          <el-card class="content-card">
            <template #header>
              <div class="card-header">
                <span>热门知识</span>
                <el-button type="text" @click="viewAllPopular">查看全部</el-button>
              </div>
            </template>
            <div class="knowledge-list">
              <div 
                v-for="(item, index) in popularKnowledge" 
                :key="item.id" 
                class="knowledge-item"
                @click="viewKnowledge(item)"
              >
                <div class="item-rank">{{ index + 1 }}</div>
                <div class="item-content">
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-meta">
                    <el-tag size="small" :type="getCategoryType(item.category)">
                      {{ item.category }}
                    </el-tag>
                    <span class="item-views">
                      <el-icon><View /></el-icon>
                      {{ item.views }}
                    </span>
                    <span class="item-rating">
                      <el-icon><Star /></el-icon>
                      {{ item.rating }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 最新更新 -->
          <el-card class="content-card">
            <template #header>
              <div class="card-header">
                <span>最新更新</span>
                <el-button type="text" @click="viewAllLatest">查看全部</el-button>
              </div>
            </template>
            <div class="knowledge-list">
              <div 
                v-for="item in latestKnowledge" 
                :key="item.id" 
                class="knowledge-item"
                @click="viewKnowledge(item)"
              >
                <div class="item-avatar">
                  <el-avatar :size="40">{{ item.author.charAt(0) }}</el-avatar>
                </div>
                <div class="item-content">
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-meta">
                    <span class="item-author">{{ item.author }}</span>
                    <span class="item-time">{{ formatTime(item.updateTime) }}</span>
                    <el-tag size="small" :type="getStatusType(item.status)">
                      {{ getStatusText(item.status) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :xs="24" :lg="8">
          <!-- 知识分类统计 -->
          <el-card class="content-card">
            <template #header>
              <span>知识分类</span>
            </template>
            <div class="category-stats">
              <div 
                v-for="category in categoryStats" 
                :key="category.id"
                class="category-item"
                @click="viewCategoryKnowledge(category)"
              >
                <div class="category-info">
                  <div class="category-name">{{ category.name }}</div>
                  <div class="category-count">{{ category.count }} 篇</div>
                </div>
                <div class="category-progress">
                  <el-progress 
                    :percentage="category.percentage" 
                    :show-text="false"
                    :stroke-width="6"
                    :color="category.color"
                  />
                </div>
              </div>
            </div>
          </el-card>

          <!-- 知识质量分析 -->
          <el-card class="content-card">
            <template #header>
              <span>知识质量</span>
            </template>
            <div class="quality-analysis">
              <div class="quality-score">
                <div class="score-circle">
                  <el-progress 
                    type="circle" 
                    :percentage="qualityScore" 
                    :width="120"
                    :stroke-width="8"
                    :color="getQualityColor(qualityScore)"
                  >
                    <template #default="{ percentage }">
                      <span class="score-text">{{ percentage }}</span>
                    </template>
                  </el-progress>
                </div>
                <div class="score-label">综合质量评分</div>
              </div>
              <div class="quality-metrics">
                <div class="metric-item">
                  <span class="metric-label">完整性</span>
                  <el-progress :percentage="85" :show-text="false" />
                </div>
                <div class="metric-item">
                  <span class="metric-label">准确性</span>
                  <el-progress :percentage="92" :show-text="false" />
                </div>
                <div class="metric-item">
                  <span class="metric-label">时效性</span>
                  <el-progress :percentage="78" :show-text="false" />
                </div>
                <div class="metric-item">
                  <span class="metric-label">有用性</span>
                  <el-progress :percentage="88" :show-text="false" />
                </div>
              </div>
            </div>
          </el-card>

          <!-- 快速操作 -->
          <el-card class="content-card">
            <template #header>
              <span>快速操作</span>
            </template>
            <div class="quick-actions">
              <el-button 
                v-for="action in quickActions" 
                :key="action.key"
                :type="action.type"
                :icon="action.icon"
                class="action-button"
                @click="handleQuickAction(action)"
              >
                {{ action.label }}
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Plus, Refresh, View, Star, ArrowUp, ArrowDown,
  Document, Collection, QuestionFilled, Bell
} from '@element-plus/icons-vue'
import { 
  getKnowledgeStats, 
  getPopularKnowledge, 
  getLatestKnowledge,
  getKnowledgeCategories 
} from '@/api/knowledgeApi'
import { useRouter } from 'vue-router'

const router = useRouter()

// 统计数据
const statsData = ref([
  {
    key: 'total',
    label: '知识总数',
    value: '1,234',
    icon: 'Document',
    type: 'primary',
    trend: 12
  },
  {
    key: 'published',
    label: '已发布',
    value: '1,156',
    icon: 'Collection',
    type: 'success',
    trend: 8
  },
  {
    key: 'pending',
    label: '待审核',
    value: '45',
    icon: 'QuestionFilled',
    type: 'warning',
    trend: -5
  },
  {
    key: 'views',
    label: '总浏览量',
    value: '45,678',
    icon: 'View',
    type: 'info',
    trend: 15
  }
])

// 热门知识
const popularKnowledge = ref([
  {
    id: 1,
    title: '服务器性能监控配置完整指南',
    category: '技术文档',
    views: 2456,
    rating: 4.8
  },
  {
    id: 2,
    title: '网络故障快速排除流程',
    category: '流程规范',
    views: 1892,
    rating: 4.6
  },
  {
    id: 3,
    title: '邮箱配置常见问题解答',
    category: 'FAQ',
    views: 1654,
    rating: 4.5
  },
  {
    id: 4,
    title: 'VPN连接问题处理方案',
    category: '解决方案',
    views: 1432,
    rating: 4.7
  },
  {
    id: 5,
    title: '数据库备份恢复操作手册',
    category: '操作指南',
    views: 1298,
    rating: 4.4
  }
])

// 最新更新
const latestKnowledge = ref([
  {
    id: 6,
    title: '新版本系统升级注意事项',
    author: '张工',
    updateTime: '2025-01-30 14:30',
    status: 'published'
  },
  {
    id: 7,
    title: '安全策略更新说明',
    author: '李工',
    updateTime: '2025-01-30 11:20',
    status: 'draft'
  },
  {
    id: 8,
    title: '移动办公VPN配置指南',
    author: '王工',
    updateTime: '2025-01-29 16:45',
    status: 'review'
  },
  {
    id: 9,
    title: '云服务器监控告警配置',
    author: '赵工',
    updateTime: '2025-01-29 09:15',
    status: 'published'
  }
])

// 分类统计
const categoryStats = ref([
  { id: 1, name: '技术文档', count: 456, percentage: 37, color: '#409EFF' },
  { id: 2, name: '流程规范', count: 234, percentage: 19, color: '#67C23A' },
  { id: 3, name: 'FAQ', count: 189, percentage: 15, color: '#E6A23C' },
  { id: 4, name: '解决方案', count: 167, percentage: 14, color: '#F56C6C' },
  { id: 5, name: '操作指南', count: 123, percentage: 10, color: '#909399' },
  { id: 6, name: '其他', count: 65, percentage: 5, color: '#C0C4CC' }
])

// 质量评分
const qualityScore = ref(86)

// 快速操作
const quickActions = ref([
  { key: 'create', label: '创建知识', type: 'primary', icon: 'Plus' },
  { key: 'import', label: '批量导入', type: '', icon: 'Upload' },
  { key: 'export', label: '导出数据', type: '', icon: 'Download' },
  { key: 'template', label: '模板管理', type: '', icon: 'Document' },
  { key: 'category', label: '分类管理', type: '', icon: 'Collection' },
  { key: 'audit', label: '审核管理', type: '', icon: 'Select' }
])

// 生命周期
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  try {
    // 这里可以调用实际的API
    // const stats = await getKnowledgeStats()
    // const popular = await getPopularKnowledge({ limit: 5 })
    // const latest = await getLatestKnowledge({ limit: 4 })
    // const categories = await getKnowledgeCategories()
    
    ElMessage.success('数据加载完成')
  } catch (error) {
    ElMessage.error('数据加载失败')
  }
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 创建知识
const createKnowledge = () => {
  router.push('/knowledge/create')
}

// 查看知识详情
const viewKnowledge = (item) => {
  router.push(`/knowledge/articles/${item.id}`)
}

// 查看全部热门
const viewAllPopular = () => {
  router.push('/knowledge/base?sort=popular')
}

// 查看全部最新
const viewAllLatest = () => {
  router.push('/knowledge/base?sort=latest')
}

// 查看分类知识
const viewCategoryKnowledge = (category) => {
  router.push(`/knowledge/base?category=${category.id}`)
}

// 快速操作处理
const handleQuickAction = (action) => {
  switch (action.key) {
    case 'create':
      createKnowledge()
      break
    case 'import':
      ElMessage.info('批量导入功能开发中...')
      break
    case 'export':
      ElMessage.info('导出数据功能开发中...')
      break
    case 'template':
      router.push('/knowledge/templates')
      break
    case 'category':
      router.push('/knowledge/categories')
      break
    case 'audit':
      router.push('/knowledge/audit')
      break
  }
}

// 工具函数
const getCategoryType = (category) => {
  const types = {
    '技术文档': 'primary',
    '流程规范': 'success',
    'FAQ': 'warning',
    '解决方案': 'danger',
    '操作指南': 'info'
  }
  return types[category] || ''
}

const getStatusType = (status) => {
  const types = {
    'published': 'success',
    'draft': 'info',
    'review': 'warning',
    'archived': 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    'published': '已发布',
    'draft': '草稿',
    'review': '审核中',
    'archived': '已归档'
  }
  return texts[status] || status
}

const getQualityColor = (score) => {
  if (score >= 90) return '#67C23A'
  if (score >= 80) return '#409EFF'
  if (score >= 70) return '#E6A23C'
  return '#F56C6C'
}

const formatTime = (time) => {
  // 简单的时间格式化，实际项目中可以使用更完善的时间库
  return time
}
</script>

<style scoped>
.knowledge-overview {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
  border-left: 4px solid #409EFF;
}

.stat-card.success {
  border-left: 4px solid #67C23A;
}

.stat-card.warning {
  border-left: 4px solid #E6A23C;
}

.stat-card.info {
  border-left: 4px solid #909399;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;
}

.stat-icon {
  margin-right: 16px;
  color: #409EFF;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.stat-trend.positive {
  color: #67C23A;
}

.stat-trend.negative {
  color: #F56C6C;
}

.main-content {
  margin-top: 20px;
}

.content-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.knowledge-list {
  max-height: 400px;
  overflow-y: auto;
}

.knowledge-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.knowledge-item:hover {
  background-color: #f8f9fa;
}

.knowledge-item:last-child {
  border-bottom: none;
}

.item-rank {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.item-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.item-views,
.item-rating {
  display: flex;
  align-items: center;
  gap: 2px;
}

.item-author,
.item-time {
  color: #909399;
}

.category-stats {
  max-height: 300px;
  overflow-y: auto;
}

.category-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.category-item:hover {
  background-color: #f8f9fa;
}

.category-item:last-child {
  border-bottom: none;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.category-count {
  font-size: 12px;
  color: #909399;
}

.quality-analysis {
  text-align: center;
}

.quality-score {
  margin-bottom: 20px;
}

.score-circle {
  margin-bottom: 12px;
}

.score-text {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.score-label {
  font-size: 14px;
  color: #606266;
}

.quality-metrics {
  text-align: left;
}

.metric-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  font-size: 12px;
  color: #606266;
  width: 60px;
  flex-shrink: 0;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-button {
  width: 100%;
  height: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-overview {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stat-value {
    font-size: 24px;
  }

  .item-meta {
    flex-wrap: wrap;
    gap: 8px;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}
</style>
