<template>
  <div class="incident-test-page">
    <div class="page-header">
      <h1>🔧 事件管理模块测试页面</h1>
      <p>测试所有事件管理相关页面的导航和加载</p>
    </div>

    <div class="test-content">
      <div class="test-section">
        <h3>📋 事件管理页面测试</h3>
        <div class="test-buttons">
          <button class="btn btn-primary" @click="navigateTo('/incident')">
            事件管理主页
          </button>
          <button class="btn btn-info" @click="navigateTo('/incident/list')">
            事件列表
          </button>
          <button class="btn btn-warning" @click="navigateTo('/incident/escalation')">
            升级管理
          </button>
        </div>
      </div>

      <div class="test-section">
        <h3>📊 测试结果</h3>
        <div class="test-results">
          <div v-for="result in testResults" :key="result.path" class="result-item">
            <span class="path">{{ result.path }}</span>
            <span class="status" :class="result.status">
              {{ result.status === 'success' ? '✅ 成功' : result.status === 'error' ? '❌ 失败' : '⏳ 测试中' }}
            </span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>🔄 批量测试</h3>
        <button class="btn btn-success" @click="runAllTests" :disabled="testing">
          {{ testing ? '测试中...' : '运行所有测试' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const testing = ref(false)

const testResults = ref([
  { path: '/incident', status: 'pending' },
  { path: '/incident/list', status: 'pending' },
  { path: '/incident/escalation', status: 'pending' }
])

const navigateTo = (path) => {
  try {
    router.push(path)
    updateTestResult(path, 'success')
  } catch (error) {
    console.error('Navigation error:', error)
    updateTestResult(path, 'error')
  }
}

const updateTestResult = (path, status) => {
  const result = testResults.value.find(r => r.path === path)
  if (result) {
    result.status = status
  }
}

const runAllTests = async () => {
  testing.value = true
  
  for (const result of testResults.value) {
    result.status = 'testing'
    
    try {
      // 模拟导航测试
      await new Promise(resolve => setTimeout(resolve, 500))
      result.status = 'success'
    } catch (error) {
      result.status = 'error'
    }
  }
  
  testing.value = false
  alert('所有测试完成！请检查结果。')
}
</script>

<style scoped>
.incident-test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  color: #1976D2;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 16px;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  color: #333;
  margin-bottom: 15px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #409EFF;
  color: white;
}

.btn-info {
  background: #909399;
  color: white;
}

.btn-warning {
  background: #E6A23C;
  color: white;
}

.btn-success {
  background: #67C23A;
  color: white;
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  opacity: 0.9;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.path {
  font-family: monospace;
  color: #333;
}

.status.success {
  color: #67C23A;
}

.status.error {
  color: #F56C6C;
}

.status.pending {
  color: #909399;
}

.status.testing {
  color: #E6A23C;
}
</style>
