<template>
  <div class="multi-channel-access">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>多渠道接入管理</span>
          <el-button type="primary" size="small" @click="addChannel">
            <el-icon><Plus /></el-icon>
            添加渠道
          </el-button>
        </div>
      </template>
      
      <!-- 渠道概览 -->
      <div class="channel-overview">
        <el-row :gutter="20">
          <el-col :xs="12" :sm="6" v-for="channel in channelStats" :key="channel.key">
            <div class="channel-card" :class="channel.status" @click="viewChannelDetail(channel)">
              <div class="channel-icon">
                <el-icon :size="32" :color="channel.color">
                  <component :is="channel.icon" />
                </el-icon>
              </div>
              <div class="channel-content">
                <div class="channel-name">{{ channel.name }}</div>
                <div class="channel-count">{{ channel.count }}</div>
                <div class="channel-status">{{ channel.statusText }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 渠道配置 -->
      <div class="channel-config">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="Web门户" name="web">
            <div class="config-section">
              <h3>Web门户配置</h3>
              <el-form :model="webConfig" label-width="120px">
                <el-form-item label="门户地址">
                  <el-input v-model="webConfig.url" placeholder="https://helpdesk.company.com" />
                </el-form-item>
                <el-form-item label="自助服务">
                  <el-switch v-model="webConfig.selfService" />
                  <span class="form-help">允许用户自助创建和查看工单</span>
                </el-form-item>
                <el-form-item label="知识库集成">
                  <el-switch v-model="webConfig.knowledgeBase" />
                  <span class="form-help">在创建工单前推荐相关知识库文章</span>
                </el-form-item>
                <el-form-item label="文件上传">
                  <el-switch v-model="webConfig.fileUpload" />
                  <span class="form-help">允许用户上传附件</span>
                </el-form-item>
              </el-form>
              
              <div class="channel-metrics">
                <h4>今日统计</h4>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-statistic title="访问量" :value="webConfig.metrics.visits" />
                  </el-col>
                  <el-col :span="8">
                    <el-statistic title="创建工单" :value="webConfig.metrics.tickets" />
                  </el-col>
                  <el-col :span="8">
                    <el-statistic title="转化率" :value="webConfig.metrics.conversion" suffix="%" />
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="邮件转工单" name="email">
            <div class="config-section">
              <h3>邮件转工单配置</h3>
              <el-form :model="emailConfig" label-width="120px">
                <el-form-item label="监控邮箱">
                  <el-input v-model="emailConfig.monitorEmail" placeholder="<EMAIL>" />
                </el-form-item>
                <el-form-item label="邮件服务器">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-input v-model="emailConfig.server" placeholder="mail.company.com" />
                    </el-col>
                    <el-col :span="6">
                      <el-input v-model="emailConfig.port" placeholder="993" />
                    </el-col>
                    <el-col :span="6">
                      <el-select v-model="emailConfig.protocol">
                        <el-option label="IMAP" value="imap" />
                        <el-option label="POP3" value="pop3" />
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>
                <el-form-item label="自动分类">
                  <el-switch v-model="emailConfig.autoClassify" />
                  <span class="form-help">根据邮件内容自动分类工单</span>
                </el-form-item>
                <el-form-item label="自动回复">
                  <el-switch v-model="emailConfig.autoReply" />
                  <span class="form-help">自动回复确认邮件</span>
                </el-form-item>
                <el-form-item label="垃圾邮件过滤">
                  <el-switch v-model="emailConfig.spamFilter" />
                </el-form-item>
              </el-form>
              
              <div class="email-rules">
                <h4>邮件处理规则</h4>
                <el-table :data="emailConfig.rules" style="width: 100%">
                  <el-table-column prop="name" label="规则名称" />
                  <el-table-column prop="condition" label="触发条件" />
                  <el-table-column prop="action" label="处理动作" />
                  <el-table-column prop="enabled" label="状态" width="80">
                    <template #default="scope">
                      <el-switch v-model="scope.row.enabled" size="small" />
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="移动端" name="mobile">
            <div class="config-section">
              <h3>移动端配置</h3>
              <el-form :model="mobileConfig" label-width="120px">
                <el-form-item label="APP下载地址">
                  <el-input v-model="mobileConfig.appUrl" placeholder="https://app.company.com/download" />
                </el-form-item>
                <el-form-item label="推送通知">
                  <el-switch v-model="mobileConfig.pushNotification" />
                  <span class="form-help">工单状态变更时推送通知</span>
                </el-form-item>
                <el-form-item label="位置服务">
                  <el-switch v-model="mobileConfig.locationService" />
                  <span class="form-help">自动获取用户位置信息</span>
                </el-form-item>
                <el-form-item label="拍照上传">
                  <el-switch v-model="mobileConfig.cameraUpload" />
                </el-form-item>
                <el-form-item label="语音输入">
                  <el-switch v-model="mobileConfig.voiceInput" />
                </el-form-item>
              </el-form>
              
              <div class="mobile-stats">
                <h4>移动端统计</h4>
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-statistic title="活跃用户" :value="mobileConfig.stats.activeUsers" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="今日工单" :value="mobileConfig.stats.todayTickets" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="平均响应" :value="mobileConfig.stats.avgResponse" suffix="分钟" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="满意度" :value="mobileConfig.stats.satisfaction" suffix="%" />
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="企业微信" name="wechat">
            <div class="config-section">
              <h3>企业微信配置</h3>
              <el-form :model="wechatConfig" label-width="120px">
                <el-form-item label="企业ID">
                  <el-input v-model="wechatConfig.corpId" placeholder="企业微信ID" />
                </el-form-item>
                <el-form-item label="应用Secret">
                  <el-input v-model="wechatConfig.secret" type="password" placeholder="应用密钥" show-password />
                </el-form-item>
                <el-form-item label="机器人回复">
                  <el-switch v-model="wechatConfig.botReply" />
                  <span class="form-help">自动回复常见问题</span>
                </el-form-item>
                <el-form-item label="群组通知">
                  <el-switch v-model="wechatConfig.groupNotify" />
                  <span class="form-help">工单状态变更时通知相关群组</span>
                </el-form-item>
                <el-form-item label="审批流程">
                  <el-switch v-model="wechatConfig.approvalFlow" />
                  <span class="form-help">集成企业微信审批流程</span>
                </el-form-item>
              </el-form>
              
              <div class="wechat-commands">
                <h4>支持的命令</h4>
                <el-table :data="wechatConfig.commands" style="width: 100%">
                  <el-table-column prop="command" label="命令" width="120" />
                  <el-table-column prop="description" label="说明" />
                  <el-table-column prop="example" label="示例" />
                  <el-table-column prop="enabled" label="启用" width="80">
                    <template #default="scope">
                      <el-switch v-model="scope.row.enabled" size="small" />
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="API接口" name="api">
            <div class="config-section">
              <h3>API接口配置</h3>
              <el-form :model="apiConfig" label-width="120px">
                <el-form-item label="API地址">
                  <el-input v-model="apiConfig.baseUrl" placeholder="https://api.company.com/helpdesk" />
                </el-form-item>
                <el-form-item label="认证方式">
                  <el-radio-group v-model="apiConfig.authType">
                    <el-radio label="token">Token认证</el-radio>
                    <el-radio label="oauth">OAuth2.0</el-radio>
                    <el-radio label="basic">Basic认证</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="API密钥">
                  <el-input v-model="apiConfig.apiKey" type="password" placeholder="API密钥" show-password />
                </el-form-item>
                <el-form-item label="速率限制">
                  <el-input-number v-model="apiConfig.rateLimit" :min="1" :max="1000" />
                  <span class="form-help">每分钟最大请求数</span>
                </el-form-item>
              </el-form>
              
              <div class="api-endpoints">
                <h4>API端点</h4>
                <el-table :data="apiConfig.endpoints" style="width: 100%">
                  <el-table-column prop="method" label="方法" width="80" />
                  <el-table-column prop="path" label="路径" />
                  <el-table-column prop="description" label="说明" />
                  <el-table-column prop="enabled" label="启用" width="80">
                    <template #default="scope">
                      <el-switch v-model="scope.row.enabled" size="small" />
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
    
    <!-- 渠道详情对话框 -->
    <el-dialog v-model="showChannelDetail" :title="`${currentChannel?.name} - 详细信息`" width="70%">
      <div v-if="currentChannel" class="channel-detail">
        <el-tabs v-model="activeDetailTab">
          <el-tab-pane label="实时数据" name="realtime">
            <div class="realtime-data">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-statistic title="今日接入量" :value="currentChannel.todayCount" />
                </el-col>
                <el-col :span="8">
                  <el-statistic title="成功率" :value="currentChannel.successRate" suffix="%" />
                </el-col>
                <el-col :span="8">
                  <el-statistic title="平均响应时间" :value="currentChannel.avgResponse" suffix="ms" />
                </el-col>
              </el-row>
              
              <div class="realtime-chart" ref="channelChart" style="height: 300px; margin-top: 20px;"></div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="历史统计" name="history">
            <div class="history-stats">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="margin-bottom: 20px;"
              />
              
              <el-table :data="channelHistory" style="width: 100%">
                <el-table-column prop="date" label="日期" />
                <el-table-column prop="count" label="接入量" />
                <el-table-column prop="tickets" label="生成工单" />
                <el-table-column prop="successRate" label="成功率" />
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <el-button @click="showChannelDetail = false">关闭</el-button>
        <el-button type="primary" @click="exportChannelData">导出数据</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const activeTab = ref('web')
const showChannelDetail = ref(false)
const activeDetailTab = ref('realtime')
const currentChannel = ref(null)
const dateRange = ref([])

// 渠道统计
const channelStats = ref([
  {
    key: 'web',
    name: 'Web门户',
    icon: 'Monitor',
    color: '#1976D2',
    count: 156,
    status: 'active',
    statusText: '正常运行',
    todayCount: 45,
    successRate: 98.5,
    avgResponse: 120
  },
  {
    key: 'email',
    name: '邮件转工单',
    icon: 'Message',
    color: '#4CAF50',
    count: 89,
    status: 'active',
    statusText: '正常运行',
    todayCount: 23,
    successRate: 95.2,
    avgResponse: 300
  },
  {
    key: 'mobile',
    name: '移动端',
    icon: 'Cellphone',
    color: '#FF9800',
    count: 67,
    status: 'warning',
    statusText: '部分异常',
    todayCount: 18,
    successRate: 92.1,
    avgResponse: 180
  },
  {
    key: 'wechat',
    name: '企业微信',
    icon: 'ChatDotRound',
    color: '#9C27B0',
    count: 34,
    status: 'active',
    statusText: '正常运行',
    todayCount: 12,
    successRate: 96.8,
    avgResponse: 90
  }
])

// 各渠道配置
const webConfig = reactive({
  url: 'https://helpdesk.company.com',
  selfService: true,
  knowledgeBase: true,
  fileUpload: true,
  metrics: {
    visits: 1250,
    tickets: 45,
    conversion: 3.6
  }
})

const emailConfig = reactive({
  monitorEmail: '<EMAIL>',
  server: 'mail.company.com',
  port: '993',
  protocol: 'imap',
  autoClassify: true,
  autoReply: true,
  spamFilter: true,
  rules: [
    {
      name: '紧急邮件',
      condition: '主题包含"紧急"',
      action: '设置为高优先级',
      enabled: true
    },
    {
      name: '自动分类',
      condition: '发件人为内部员工',
      action: '分配给对应部门',
      enabled: true
    }
  ]
})

const mobileConfig = reactive({
  appUrl: 'https://app.company.com/download',
  pushNotification: true,
  locationService: true,
  cameraUpload: true,
  voiceInput: false,
  stats: {
    activeUsers: 234,
    todayTickets: 18,
    avgResponse: 12,
    satisfaction: 94
  }
})

const wechatConfig = reactive({
  corpId: 'wx1234567890abcdef',
  secret: '***************',
  botReply: true,
  groupNotify: true,
  approvalFlow: false,
  commands: [
    {
      command: '/ticket',
      description: '创建工单',
      example: '/ticket 电脑无法开机',
      enabled: true
    },
    {
      command: '/status',
      description: '查询工单状态',
      example: '/status INC-2025-001',
      enabled: true
    },
    {
      command: '/help',
      description: '获取帮助',
      example: '/help',
      enabled: true
    }
  ]
})

const apiConfig = reactive({
  baseUrl: 'https://api.company.com/helpdesk',
  authType: 'token',
  apiKey: '***************',
  rateLimit: 100,
  endpoints: [
    {
      method: 'POST',
      path: '/tickets',
      description: '创建工单',
      enabled: true
    },
    {
      method: 'GET',
      path: '/tickets/{id}',
      description: '获取工单详情',
      enabled: true
    },
    {
      method: 'PUT',
      path: '/tickets/{id}',
      description: '更新工单',
      enabled: true
    }
  ]
})

// 渠道历史数据
const channelHistory = ref([
  { date: '2025-01-30', count: 45, tickets: 42, successRate: '93.3%' },
  { date: '2025-01-29', count: 52, tickets: 48, successRate: '92.3%' },
  { date: '2025-01-28', count: 38, tickets: 35, successRate: '92.1%' }
])

// 图表引用
const channelChart = ref()

// 添加渠道
const addChannel = () => {
  ElMessage.info('添加新渠道功能')
}

// 查看渠道详情
const viewChannelDetail = (channel) => {
  currentChannel.value = channel
  showChannelDetail.value = true
  activeDetailTab.value = 'realtime'

  // 初始化图表
  nextTick(() => {
    initChannelChart()
  })
}

// 导出渠道数据
const exportChannelData = () => {
  ElMessage.success('渠道数据导出中...')
}

// 初始化渠道图表
const initChannelChart = () => {
  if (!channelChart.value) return

  const chartInstance = echarts.init(channelChart.value)
  chartInstance.setOption({
    title: {
      text: '渠道接入趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '接入量',
        type: 'line',
        data: [5, 8, 12, 18, 25, 15, 8],
        itemStyle: { color: currentChannel.value?.color || '#1976D2' },
        areaStyle: {
          color: `rgba(25, 118, 210, 0.1)`
        }
      }
    ]
  })
}

onMounted(() => {
  // 模拟实时数据更新
  setInterval(() => {
    channelStats.value.forEach(channel => {
      // 随机更新今日接入量
      const change = Math.floor(Math.random() * 3) - 1
      channel.todayCount = Math.max(0, channel.todayCount + change)
      channel.count += change > 0 ? change : 0
    })
  }, 30000) // 每30秒更新一次
})
</script>

<style scoped>
.multi-channel-access {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.channel-overview {
  margin-bottom: 30px;
}

.channel-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.channel-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.channel-card.active {
  border-left: 4px solid #4CAF50;
}

.channel-card.warning {
  border-left: 4px solid #FF9800;
}

.channel-card.error {
  border-left: 4px solid #F44336;
}

.channel-icon {
  margin-right: 16px;
}

.channel-content {
  flex: 1;
}

.channel-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.channel-count {
  font-size: 24px;
  font-weight: bold;
  color: #1976D2;
  margin-bottom: 4px;
}

.channel-status {
  font-size: 12px;
  color: #666;
}

.config-section {
  padding: 20px 0;
}

.config-section h3 {
  color: #333;
  margin-bottom: 20px;
}

.channel-metrics,
.mobile-stats {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.channel-metrics h4,
.mobile-stats h4 {
  color: #333;
  margin-bottom: 16px;
}

.email-rules,
.wechat-commands,
.api-endpoints {
  margin-top: 30px;
}

.email-rules h4,
.wechat-commands h4,
.api-endpoints h4 {
  color: #333;
  margin-bottom: 16px;
}

.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

.channel-detail {
  padding: 20px 0;
}

.realtime-data {
  padding: 20px 0;
}

.history-stats {
  padding: 20px 0;
}

@media (max-width: 768px) {
  .channel-card {
    flex-direction: column;
    text-align: center;
  }
  
  .channel-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .config-section {
    padding: 12px 0;
  }
}
</style>
