<template>
  <div class="release-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <div class="breadcrumb">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/release' }">发布管理</el-breadcrumb-item>
              <el-breadcrumb-item>{{ release?.name || '发布详情' }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <h2>{{ release?.name }}</h2>
          <p>{{ release?.description }}</p>
        </div>
        <div class="action-section">
          <el-button 
            v-if="canPromote"
            type="primary" 
            :icon="Top"
            @click="promoteStage"
          >
            推进阶段
          </el-button>
          <el-button 
            v-if="canRollback"
            type="danger" 
            :icon="RefreshLeft"
            @click="rollbackRelease"
          >
            回滚
          </el-button>
          <el-button 
            v-if="release?.status === 'prod'"
            type="success" 
            :icon="Monitor"
            @click="performHealthCheck"
          >
            健康检查
          </el-button>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 发布状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon current">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-label">当前状态</div>
              <div class="status-value">
                <el-tag :type="getStatusTagType(release?.status)">
                  {{ getStatusLabel(release?.status) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon stage">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-label">当前阶段</div>
              <div class="status-value">{{ getStageLabel(release?.currentStage) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon progress">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-label">完成进度</div>
              <div class="status-value">{{ getProgress() }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon risk">
              <el-icon><WarningFilled /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-label">风险等级</div>
              <div class="status-value">
                <el-tag :type="getRiskTagType(release?.riskLevel)">
                  {{ getRiskLabel(release?.riskLevel) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 标签页内容 -->
    <el-card class="main-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 发布计划 -->
        <el-tab-pane label="发布计划" name="planning">
          <ReleasePlanning v-if="release" :release="release" />
        </el-tab-pane>

        <!-- 健康检查 -->
        <el-tab-pane label="健康检查" name="health">
          <div class="health-checks">
            <div class="health-header">
              <h3>系统健康检查</h3>
              <el-button 
                type="primary" 
                :icon="Refresh"
                @click="performHealthCheck"
                :loading="healthCheckLoading"
              >
                执行检查
              </el-button>
            </div>
            
            <div class="health-results">
              <div 
                v-for="check in release?.healthChecks || []" 
                :key="check.name"
                class="health-item"
                :class="{ 'passed': check.status === 'passed', 'failed': check.status === 'failed' }"
              >
                <div class="health-icon">
                  <el-icon v-if="check.status === 'passed'">
                    <SuccessFilled />
                  </el-icon>
                  <el-icon v-else>
                    <CircleCloseFilled />
                  </el-icon>
                </div>
                <div class="health-info">
                  <div class="health-name">{{ check.name }}</div>
                  <div class="health-time">最后检查：{{ check.lastCheck }}</div>
                </div>
                <div class="health-status">
                  <el-tag :type="check.status === 'passed' ? 'success' : 'danger'">
                    {{ check.status === 'passed' ? '通过' : '失败' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 回滚计划 -->
        <el-tab-pane label="回滚计划" name="rollback">
          <RollbackManagement
            v-if="release"
            :releaseId="release.id"
            :releaseStatus="release.status"
          />
        </el-tab-pane>

        <!-- 变更关联 -->
        <el-tab-pane label="变更关联" name="changes">
          <ChangeAssociation v-if="release" :releaseId="release.id" />
        </el-tab-pane>

        <!-- 操作日志 -->
        <el-tab-pane label="操作日志" name="logs">
          <div class="operation-logs">
            <h3>操作历史</h3>
            <el-timeline>
              <el-timeline-item
                v-for="log in operationLogs"
                :key="log.id"
                :timestamp="log.timestamp"
                :type="log.type"
              >
                <div class="log-content">
                  <div class="log-action">{{ log.action }}</div>
                  <div class="log-user">操作人：{{ log.user }}</div>
                  <div class="log-desc" v-if="log.description">{{ log.description }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Top, RefreshLeft, Monitor, Refresh, DataBoard, Operation, 
  TrendCharts, WarningFilled, SuccessFilled, CircleCloseFilled, Link
} from '@element-plus/icons-vue'
import ReleasePlanning from '@/components/ReleasePlanning.vue'
import ChangeAssociation from '@/components/ChangeAssociation.vue'
import RollbackManagement from '@/components/RollbackManagement.vue'
import { 
  getReleaseDetail, 
  promoteReleaseStage, 
  rollbackRelease as rollbackReleaseApi,
  performHealthCheck as performHealthCheckApi
} from '@/api/releaseApi.js'

// 路由
const route = useRoute()

// 响应式数据
const loading = ref(false)
const healthCheckLoading = ref(false)
const activeTab = ref('planning')
const release = ref(null)

// 模拟操作日志
const operationLogs = ref([
  {
    id: 1,
    timestamp: '2025-01-15 10:30:00',
    action: '创建发布',
    user: '张三',
    type: 'primary',
    description: '创建了新的发布计划'
  },
  {
    id: 2,
    timestamp: '2025-01-15 14:20:00',
    action: '推进到测试阶段',
    user: '李四',
    type: 'success',
    description: '开发阶段完成，推进到测试阶段'
  }
])

// 计算属性
const canPromote = computed(() => {
  return release.value && ['planning', 'dev', 'test', 'uat'].includes(release.value.status)
})

const canRollback = computed(() => {
  return release.value && ['prod', 'verified'].includes(release.value.status)
})

// 生命周期
onMounted(() => {
  loadReleaseDetail()
})

// 方法
const loadReleaseDetail = async () => {
  const releaseId = route.params.id
  if (!releaseId) return
  
  loading.value = true
  try {
    const response = await getReleaseDetail(releaseId)
    release.value = response.data
  } catch (error) {
    ElMessage.error('加载发布详情失败')
    console.error('Load release detail error:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadReleaseDetail()
}

const getProgress = () => {
  if (!release.value?.stages) return 0
  const completedStages = release.value.stages.filter(stage => stage.status === 'completed').length
  return Math.round((completedStages / release.value.stages.length) * 100)
}

// 工具方法
const getStatusLabel = (status) => {
  const statusMap = {
    planning: '计划中',
    dev: '开发',
    test: '测试',
    uat: '预生产',
    prod: '生产',
    verified: '已验证',
    failed: '失败',
    rollback: '已回滚',
    closed: '已关闭'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const statusMap = {
    planning: 'info',
    dev: 'primary',
    test: 'warning',
    uat: 'warning',
    prod: 'success',
    verified: 'success',
    failed: 'danger',
    rollback: 'danger',
    closed: 'info'
  }
  return statusMap[status] || 'info'
}

const getStageLabel = (stage) => {
  const stageMap = {
    dev: '开发',
    test: '测试',
    uat: '预生产',
    prod: '生产'
  }
  return stageMap[stage] || stage
}

const getRiskLabel = (risk) => {
  const riskMap = {
    low: '低风险',
    medium: '中风险',
    high: '高风险',
    critical: '极高风险'
  }
  return riskMap[risk] || risk
}

const getRiskTagType = (risk) => {
  const riskMap = {
    low: 'success',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return riskMap[risk] || 'info'
}

// 操作方法
const promoteStage = async () => {
  if (!release.value) return

  const stageMap = {
    'planning': 'dev',
    'dev': 'test',
    'test': 'uat',
    'uat': 'prod'
  }

  const nextStage = stageMap[release.value.status]
  if (!nextStage) {
    ElMessage.warning('当前阶段无法推进')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将发布 "${release.value.name}" 推进到下一阶段吗？`,
      '推进确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const { promoteReleaseStage } = await import('@/api/releaseApi.js')
    const response = await promoteReleaseStage(release.value.id, nextStage)
    ElMessage.success(response.msg)
    loadReleaseDetail()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('推进阶段失败')
    console.error('Promote stage error:', error)
  }
}

const rollbackRelease = async () => {
  if (!release.value) return

  try {
    const { value: reason } = await ElMessageBox.prompt(
      `确定要回滚发布 "${release.value.name}" 吗？请输入回滚原因：`,
      '回滚确认',
      {
        confirmButtonText: '确定回滚',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入回滚原因'
      }
    )

    const { rollbackRelease: rollbackReleaseApi } = await import('@/api/releaseApi.js')
    const response = await rollbackReleaseApi(release.value.id, reason)
    ElMessage.success(response.msg)
    loadReleaseDetail()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('回滚失败')
    console.error('Rollback error:', error)
  }
}

const performHealthCheck = async () => {
  healthCheckLoading.value = true
  try {
    const response = await performHealthCheckApi(release.value.id)
    ElMessage.success('健康检查完成')
    loadReleaseDetail()
  } catch (error) {
    ElMessage.error('健康检查失败')
  } finally {
    healthCheckLoading.value = false
  }
}
</script>

<style scoped>
.release-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
  margin-bottom: 8px;
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 状态卡片 */
.status-cards {
  margin-bottom: 20px;
}

.status-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.status-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.status-icon.current {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
}

.status-icon.stage {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.status-icon.progress {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.status-icon.risk {
  background: linear-gradient(135deg, #F44336, #EF5350);
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.status-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 主要内容 */
.main-content {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 健康检查 */
.health-checks {
  padding: 20px;
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.health-header h3 {
  margin: 0;
  color: #333;
}

.health-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.health-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.health-item.passed {
  border-color: #4CAF50;
  background-color: #f8fff8;
}

.health-item.failed {
  border-color: #F44336;
  background-color: #fff8f8;
}

.health-icon {
  margin-right: 12px;
  font-size: 20px;
}

.health-icon .el-icon {
  color: #4CAF50;
}

.health-item.failed .health-icon .el-icon {
  color: #F44336;
}

.health-info {
  flex: 1;
}

.health-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.health-time {
  font-size: 12px;
  color: #666;
}

.health-status {
  margin-left: 12px;
}

/* 回滚计划 */
.rollback-plan {
  padding: 20px;
}

.rollback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.rollback-header h3 {
  margin: 0;
  color: #333;
}

.rollback-info {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item label {
  font-weight: 600;
  color: #666;
  min-width: 120px;
}

.rollback-checklist h4 {
  color: #333;
  margin-bottom: 12px;
}

.checklist {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checklist-item {
  padding: 8px 0;
}

/* 关联变更 */
.related-changes {
  padding: 20px;
}

.changes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.changes-header h3 {
  margin: 0;
  color: #333;
}

.change-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.change-info {
  flex: 1;
}

.change-id {
  font-weight: 600;
  color: #1976D2;
  margin-bottom: 4px;
}

.change-desc {
  font-size: 14px;
  color: #666;
}

/* 操作日志 */
.operation-logs {
  padding: 20px;
}

.operation-logs h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.log-content {
  padding: 8px 0;
}

.log-action {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.log-user {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.log-desc {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .release-detail {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
    flex-wrap: wrap;
  }

  .status-cards .el-col {
    margin-bottom: 10px;
  }

  .health-header,
  .rollback-header,
  .changes-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .health-item,
  .change-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-item label {
    min-width: auto;
    margin-bottom: 4px;
  }
}
</style>
