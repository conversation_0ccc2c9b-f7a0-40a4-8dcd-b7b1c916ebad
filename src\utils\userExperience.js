/**
 * 用户体验优化工具函数
 * 提供统一的加载状态、错误处理、用户反馈等功能
 */

import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'

/**
 * 统一的加载状态管理
 */
export class LoadingManager {
  constructor() {
    this.loadingInstances = new Map()
  }

  /**
   * 显示全局加载
   * @param {string} text - 加载文本
   * @param {string} target - 目标元素选择器
   */
  show(text = '加载中...', target = 'body') {
    const loading = ElLoading.service({
      lock: true,
      text,
      target,
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    const key = target || 'global'
    this.loadingInstances.set(key, loading)
    return loading
  }

  /**
   * 隐藏加载状态
   * @param {string} target - 目标元素选择器
   */
  hide(target = 'body') {
    const key = target || 'global'
    const loading = this.loadingInstances.get(key)
    if (loading) {
      loading.close()
      this.loadingInstances.delete(key)
    }
  }

  /**
   * 隐藏所有加载状态
   */
  hideAll() {
    this.loadingInstances.forEach(loading => loading.close())
    this.loadingInstances.clear()
  }
}

/**
 * 统一的错误处理
 */
export class ErrorHandler {
  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  static handleApiError(error, context = '') {
    console.error(`API错误 ${context}:`, error)
    
    let message = '操作失败，请稍后重试'
    
    if (error.response) {
      // 服务器响应错误
      const status = error.response.status
      switch (status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          break
        case 403:
          message = '权限不足'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      message = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      message = error.message || '未知错误'
    }
    
    ElMessage.error(message)
    return message
  }

  /**
   * 处理业务逻辑错误
   * @param {string} message - 错误消息
   * @param {string} type - 错误类型
   */
  static handleBusinessError(message, type = 'warning') {
    console.warn('业务错误:', message)
    
    if (type === 'error') {
      ElMessage.error(message)
    } else {
      ElMessage.warning(message)
    }
  }

  /**
   * 显示确认对话框
   * @param {string} message - 确认消息
   * @param {string} title - 对话框标题
   * @param {string} type - 对话框类型
   */
  static async confirm(message, title = '确认操作', type = 'warning') {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type
      })
      return true
    } catch {
      return false
    }
  }
}

/**
 * 用户反馈管理
 */
export class FeedbackManager {
  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长
   */
  static success(message, duration = 3000) {
    ElMessage({
      message,
      type: 'success',
      duration,
      showClose: true
    })
  }

  /**
   * 显示信息消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长
   */
  static info(message, duration = 3000) {
    ElMessage({
      message,
      type: 'info',
      duration,
      showClose: true
    })
  }

  /**
   * 显示警告消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长
   */
  static warning(message, duration = 4000) {
    ElMessage({
      message,
      type: 'warning',
      duration,
      showClose: true
    })
  }

  /**
   * 显示错误消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长
   */
  static error(message, duration = 5000) {
    ElMessage({
      message,
      type: 'error',
      duration,
      showClose: true
    })
  }

  /**
   * 显示操作进度提示
   * @param {string} message - 消息内容
   * @param {Function} operation - 操作函数
   */
  static async withProgress(message, operation) {
    const loading = new LoadingManager()
    loading.show(message)
    
    try {
      const result = await operation()
      this.success('操作完成')
      return result
    } catch (error) {
      ErrorHandler.handleApiError(error, message)
      throw error
    } finally {
      loading.hideAll()
    }
  }
}

/**
 * 响应式设计工具
 */
export class ResponsiveHelper {
  /**
   * 获取当前屏幕尺寸类型
   */
  static getScreenSize() {
    const width = window.innerWidth
    
    if (width < 480) return 'xs'
    if (width < 768) return 'sm'
    if (width < 1024) return 'md'
    if (width < 1200) return 'lg'
    return 'xl'
  }

  /**
   * 判断是否为移动设备
   */
  static isMobile() {
    return this.getScreenSize() === 'xs' || this.getScreenSize() === 'sm'
  }

  /**
   * 判断是否为平板设备
   */
  static isTablet() {
    return this.getScreenSize() === 'md'
  }

  /**
   * 判断是否为桌面设备
   */
  static isDesktop() {
    return this.getScreenSize() === 'lg' || this.getScreenSize() === 'xl'
  }

  /**
   * 监听屏幕尺寸变化
   * @param {Function} callback - 回调函数
   */
  static onResize(callback) {
    let timeout
    const handleResize = () => {
      clearTimeout(timeout)
      timeout = setTimeout(() => {
        callback(this.getScreenSize())
      }, 100)
    }
    
    window.addEventListener('resize', handleResize)
    
    // 返回清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
      clearTimeout(timeout)
    }
  }
}

/**
 * 性能优化工具
 */
export class PerformanceHelper {
  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间
   */
  static debounce(func, delay = 300) {
    let timeout
    return function (...args) {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(this, args), delay)
    }
  }

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 延迟时间
   */
  static throttle(func, delay = 300) {
    let lastTime = 0
    return function (...args) {
      const now = Date.now()
      if (now - lastTime >= delay) {
        lastTime = now
        func.apply(this, args)
      }
    }
  }

  /**
   * 延迟执行
   * @param {number} ms - 延迟毫秒数
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 批量处理数据
   * @param {Array} data - 数据数组
   * @param {Function} processor - 处理函数
   * @param {number} batchSize - 批次大小
   */
  static async processBatch(data, processor, batchSize = 100) {
    const results = []
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize)
      const batchResults = await Promise.all(batch.map(processor))
      results.push(...batchResults)
      
      // 给浏览器一些时间处理其他任务
      if (i + batchSize < data.length) {
        await this.delay(10)
      }
    }
    
    return results
  }
}

// 创建全局实例
export const loadingManager = new LoadingManager()
export const errorHandler = ErrorHandler
export const feedbackManager = FeedbackManager
export const responsiveHelper = ResponsiveHelper
export const performanceHelper = PerformanceHelper

// 默认导出
export default {
  LoadingManager,
  ErrorHandler,
  FeedbackManager,
  ResponsiveHelper,
  PerformanceHelper,
  loadingManager,
  errorHandler,
  feedbackManager,
  responsiveHelper,
  performanceHelper
}
