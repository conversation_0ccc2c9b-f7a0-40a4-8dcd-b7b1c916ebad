<template>
  <div class="ci-details">
    <div class="page-header">
      <div class="header-content">
        <div class="ci-title">
          <el-icon :size="32" :color="getCITypeColor(ciData.type)">
            <component :is="getCITypeIcon(ciData.type)" />
          </el-icon>
          <div class="title-info">
            <h2>{{ ciData.name }}</h2>
            <div class="ci-meta">
              <el-tag :type="getStatusType(ciData.status)" size="small">
                {{ ciData.status }}
              </el-tag>
              <span class="ci-type">{{ getCITypeText(ciData.type) }}</span>
              <span class="ci-id">ID: {{ ciData.id }}</span>
            </div>
          </div>
        </div>
        <div class="health-indicator">
          <el-progress
            type="circle"
            :percentage="ciData.healthScore"
            :width="60"
            :color="getHealthColor(ciData.healthScore)"
          />
          <div class="health-label">健康分</div>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="refreshCI">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="editCI">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button @click="exportCI">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-dropdown @command="handleMoreAction" trigger="click">
          <el-button>
            更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="clone">
                <el-icon><CopyDocument /></el-icon>
                克隆CI
              </el-dropdown-item>
              <el-dropdown-item command="archive">
                <el-icon><Box /></el-icon>
                归档CI
              </el-dropdown-item>
              <el-dropdown-item command="delete" divided>
                <el-icon><Delete /></el-icon>
                删除CI
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="ci-content">
      <el-row :gutter="20">
        <!-- 左侧主要信息 -->
        <el-col :span="16">
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="基本信息" name="basic">
              <div class="basic-info">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="配置项名称">
                    {{ ciData.name }}
                  </el-descriptions-item>
                  <el-descriptions-item label="配置项类型">
                    <el-tag :type="getCITypeTagType(ciData.type)" size="small">
                      {{ getCITypeText(ciData.type) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="IP地址">
                    {{ ciData.ip || 'N/A' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="MAC地址">
                    {{ ciData.mac || 'N/A' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="操作系统">
                    {{ ciData.os || 'N/A' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="版本">
                    {{ ciData.version || 'N/A' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="负责人">
                    {{ ciData.owner || 'N/A' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="部门">
                    {{ ciData.department || 'N/A' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="位置">
                    {{ ciData.location || 'N/A' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="供应商">
                    {{ ciData.vendor || 'N/A' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="创建时间">
                    {{ ciData.createTime }}
                  </el-descriptions-item>
                  <el-descriptions-item label="更新时间">
                    {{ ciData.updateTime }}
                  </el-descriptions-item>
                  <el-descriptions-item label="描述" :span="2">
                    {{ ciData.description || '暂无描述' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-tab-pane>

            <el-tab-pane label="关系图谱" name="relations">
              <div class="relations-view">
                <div class="relations-toolbar">
                  <el-button-group size="small">
                    <el-button @click="showUpstream">上游依赖</el-button>
                    <el-button @click="showDownstream">下游影响</el-button>
                    <el-button @click="showAll">全部关系</el-button>
                  </el-button-group>
                  <el-button type="primary" size="small" @click="addRelation">
                    <el-icon><Plus /></el-icon>
                    添加关系
                  </el-button>
                </div>
                <div class="relations-graph" ref="relationsContainer">
                  <!-- 关系图谱将在这里渲染 -->
                  <div class="graph-placeholder" v-if="!relations.length">
                    <el-icon :size="64" color="#C0C4CC">
                      <Share />
                    </el-icon>
                    <p>暂无关系数据</p>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="变更历史" name="history">
              <div class="change-history">
                <div class="history-toolbar">
                  <el-input
                    v-model="historySearch"
                    placeholder="搜索变更记录..."
                    style="width: 200px"
                    prefix-icon="Search"
                  />
                  <el-date-picker
                    v-model="historyDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    style="margin-left: 12px"
                  />
                </div>
                <el-timeline>
                  <el-timeline-item
                    v-for="change in filteredHistory"
                    :key="change.id"
                    :timestamp="change.timestamp"
                    :type="getChangeType(change.type)"
                  >
                    <div class="change-item">
                      <div class="change-header">
                        <span class="change-title">{{ change.title }}</span>
                        <el-tag :type="getChangeType(change.type)" size="small">
                          {{ change.type }}
                        </el-tag>
                      </div>
                      <div class="change-content">
                        <p>{{ change.description }}</p>
                        <div class="change-meta">
                          <span>操作人: {{ change.operator }}</span>
                          <span>变更单号: {{ change.changeId || 'N/A' }}</span>
                        </div>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-tab-pane>

            <el-tab-pane label="版本管理" name="versions">
              <div class="version-management">
                <div class="version-toolbar">
                  <el-button type="primary" @click="createSnapshot">
                    <el-icon><Document /></el-icon>
                    创建快照
                  </el-button>
                  <el-button @click="compareVersions" :disabled="selectedVersions.length !== 2">
                    <el-icon><Rank /></el-icon>
                    版本对比
                  </el-button>
                </div>
                <el-table
                  :data="versions"
                  style="width: 100%"
                  @selection-change="handleVersionSelection"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="version" label="版本号" width="120" />
                  <el-table-column prop="description" label="描述" />
                  <el-table-column prop="createTime" label="创建时间" width="160" />
                  <el-table-column prop="creator" label="创建人" width="100" />
                  <el-table-column label="操作" width="150">
                    <template #default="scope">
                      <el-button type="primary" size="small" text @click="viewVersion(scope.row)">
                        查看
                      </el-button>
                      <el-button type="warning" size="small" text @click="restoreVersion(scope.row)">
                        恢复
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <el-tab-pane label="关联事件" name="events">
              <div class="related-events">
                <el-table :data="relatedEvents" style="width: 100%">
                  <el-table-column prop="id" label="事件ID" width="120" />
                  <el-table-column prop="title" label="事件标题" />
                  <el-table-column prop="type" label="类型" width="100">
                    <template #default="scope">
                      <el-tag :type="getEventType(scope.row.type)" size="small">
                        {{ scope.row.type }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                      <el-tag :type="getEventStatus(scope.row.status)" size="small">
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" width="160" />
                  <el-table-column label="操作" width="100">
                    <template #default="scope">
                      <el-button type="primary" size="small" text @click="viewEvent(scope.row)">
                        查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>

        <!-- 右侧信息面板 -->
        <el-col :span="8">
          <div class="info-panels">
            <!-- 监控指标 -->
            <el-card class="info-panel">
              <template #header>
                <span>监控指标</span>
              </template>
              <div class="metrics-grid">
                <div class="metric-item" v-for="metric in monitoringMetrics" :key="metric.key">
                  <div class="metric-label">{{ metric.label }}</div>
                  <div class="metric-value" :style="{ color: metric.color }">
                    {{ metric.value }}
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 快速操作 -->
            <el-card class="info-panel">
              <template #header>
                <span>快速操作</span>
              </template>
              <div class="quick-actions">
                <el-button size="small" @click="pingTest" style="width: 100%; margin-bottom: 8px">
                  <el-icon><Connection /></el-icon>
                  连通性测试
                </el-button>
                <el-button size="small" @click="performanceTest" style="width: 100%; margin-bottom: 8px">
                  <el-icon><TrendCharts /></el-icon>
                  性能测试
                </el-button>
                <el-button size="small" @click="securityScan" style="width: 100%; margin-bottom: 8px">
                  <el-icon><Lock /></el-icon>
                  安全扫描
                </el-button>
                <el-button size="small" @click="backupData" style="width: 100%">
                  <el-icon><FolderAdd /></el-icon>
                  数据备份
                </el-button>
              </div>
            </el-card>

            <!-- 相关统计 -->
            <el-card class="info-panel">
              <template #header>
                <span>相关统计</span>
              </template>
              <div class="stats-list">
                <div class="stat-item">
                  <span class="stat-label">上游依赖:</span>
                  <span class="stat-value">{{ ciStats.upstreamCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">下游影响:</span>
                  <span class="stat-value">{{ ciStats.downstreamCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">关联事件:</span>
                  <span class="stat-value">{{ ciStats.eventCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">变更次数:</span>
                  <span class="stat-value">{{ ciStats.changeCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">版本数量:</span>
                  <span class="stat-value">{{ ciStats.versionCount }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'

const route = useRoute()

// 响应式数据
const activeTab = ref('basic')
const historySearch = ref('')
const historyDateRange = ref([])
const selectedVersions = ref([])
const relationsContainer = ref(null)

// CI基本数据
const ciData = reactive({
  id: 'CI-001',
  name: 'WEB-SRV-01',
  type: 'server',
  status: '运行中',
  ip: '************',
  mac: '00:1B:44:11:3A:B7',
  os: 'CentOS 7.9',
  version: 'v2.1.3',
  owner: '张工',
  department: 'IT运维部',
  location: '机房A-01',
  vendor: 'Dell',
  healthScore: 92,
  createTime: '2024-01-15 10:30:00',
  updateTime: '2025-01-30 14:25:00',
  description: 'Web应用服务器，承载公司官网和内部系统'
})

// 关系数据
const relations = ref([
  {
    id: 'rel-1',
    source: 'CI-001',
    target: 'CI-002',
    type: 'depends',
    label: '依赖于',
    description: 'Web服务器依赖于数据库服务器'
  },
  {
    id: 'rel-2',
    source: 'CI-003',
    target: 'CI-001',
    type: 'connects',
    label: '连接到',
    description: '负载均衡器连接到Web服务器'
  }
])

// 变更历史
const changeHistory = ref([
  {
    id: 'ch-1',
    title: '更新操作系统补丁',
    type: '维护',
    description: '安装最新的安全补丁，修复已知漏洞',
    timestamp: '2025-01-30 14:25:00',
    operator: '张工',
    changeId: 'CHG-2025-001'
  },
  {
    id: 'ch-2',
    title: '增加内存配置',
    type: '变更',
    description: '从8GB内存升级到16GB内存',
    timestamp: '2025-01-25 09:15:00',
    operator: '李工',
    changeId: 'CHG-2025-002'
  },
  {
    id: 'ch-3',
    title: '部署新版本应用',
    type: '发布',
    description: '部署应用版本v2.1.3，包含新功能和bug修复',
    timestamp: '2025-01-20 16:00:00',
    operator: '王工',
    changeId: 'REL-2025-001'
  }
])

// 版本数据
const versions = ref([
  {
    id: 'v-1',
    version: 'v2.1.3',
    description: '当前版本 - 包含最新功能和安全补丁',
    createTime: '2025-01-20 16:00:00',
    creator: '王工',
    isCurrent: true
  },
  {
    id: 'v-2',
    version: 'v2.1.2',
    description: '稳定版本 - 修复了若干bug',
    createTime: '2025-01-10 14:30:00',
    creator: '张工',
    isCurrent: false
  },
  {
    id: 'v-3',
    version: 'v2.1.1',
    description: '初始版本 - 基础功能实现',
    createTime: '2025-01-01 10:00:00',
    creator: '李工',
    isCurrent: false
  }
])

// 关联事件
const relatedEvents = ref([
  {
    id: 'INC-001',
    title: 'Web服务器响应缓慢',
    type: '事件',
    status: '已解决',
    createTime: '2025-01-28 10:30:00'
  },
  {
    id: 'REQ-001',
    title: '增加服务器内存配置',
    type: '请求',
    status: '已完成',
    createTime: '2025-01-25 08:00:00'
  }
])

// 监控指标
const monitoringMetrics = ref([
  { key: 'cpu', label: 'CPU使用率', value: '45%', color: '#4CAF50' },
  { key: 'memory', label: '内存使用率', value: '68%', color: '#FF9800' },
  { key: 'disk', label: '磁盘使用率', value: '32%', color: '#4CAF50' },
  { key: 'network', label: '网络流量', value: '125MB/s', color: '#2196F3' },
  { key: 'uptime', label: '运行时间', value: '15天', color: '#4CAF50' },
  { key: 'response', label: '响应时间', value: '120ms', color: '#4CAF50' }
])

// 统计数据
const ciStats = reactive({
  upstreamCount: 3,
  downstreamCount: 8,
  eventCount: 12,
  changeCount: 25,
  versionCount: 3
})

// 计算属性
const filteredHistory = computed(() => {
  let history = changeHistory.value

  if (historySearch.value) {
    const keyword = historySearch.value.toLowerCase()
    history = history.filter(item =>
      item.title.toLowerCase().includes(keyword) ||
      item.description.toLowerCase().includes(keyword)
    )
  }

  if (historyDateRange.value && historyDateRange.value.length === 2) {
    const [startDate, endDate] = historyDateRange.value
    history = history.filter(item => {
      const itemDate = new Date(item.timestamp)
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  return history
})

// 方法函数
const refreshCI = () => {
  ElMessage.success('CI数据已刷新')
  // 这里可以实现数据刷新逻辑
}

const editCI = () => {
  ElMessage.info('打开CI编辑对话框')
  // 这里可以实现CI编辑逻辑
}

const exportCI = () => {
  ElMessage.success('CI数据导出中...')
  // 这里可以实现数据导出逻辑
}

const handleMoreAction = (command) => {
  switch (command) {
    case 'clone':
      ElMessage.info('克隆CI功能')
      break
    case 'archive':
      ElMessageBox.confirm('确定要归档此CI吗？', '确认归档', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('CI已归档')
      }).catch(() => {
        ElMessage.info('已取消归档')
      })
      break
    case 'delete':
      ElMessageBox.confirm('确定要删除此CI吗？此操作不可恢复！', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('CI已删除')
      }).catch(() => {
        ElMessage.info('已取消删除')
      })
      break
  }
}

// 关系图谱方法
const showUpstream = () => {
  ElMessage.info('显示上游依赖关系')
  // 这里可以实现上游关系显示逻辑
}

const showDownstream = () => {
  ElMessage.info('显示下游影响关系')
  // 这里可以实现下游关系显示逻辑
}

const showAll = () => {
  ElMessage.info('显示全部关系')
  // 这里可以实现全部关系显示逻辑
}

const addRelation = () => {
  ElMessage.info('添加新关系')
  // 这里可以实现添加关系逻辑
}

// 版本管理方法
const createSnapshot = () => {
  ElMessage.success('正在创建版本快照...')
  // 模拟创建快照
  setTimeout(() => {
    const newVersion = {
      id: `v-${Date.now()}`,
      version: `v2.1.${versions.value.length + 1}`,
      description: '手动创建的快照版本',
      createTime: new Date().toLocaleString(),
      creator: '当前用户',
      isCurrent: false
    }
    versions.value.unshift(newVersion)
    ElMessage.success('版本快照创建成功')
  }, 2000)
}

const compareVersions = () => {
  if (selectedVersions.value.length !== 2) {
    ElMessage.warning('请选择两个版本进行对比')
    return
  }
  ElMessage.info('打开版本对比页面')
  // 这里可以实现版本对比逻辑
}

const handleVersionSelection = (selection) => {
  selectedVersions.value = selection
}

const viewVersion = (version) => {
  ElMessage.info(`查看版本: ${version.version}`)
  // 这里可以实现版本查看逻辑
}

const restoreVersion = (version) => {
  ElMessageBox.confirm(
    `确定要恢复到版本 ${version.version} 吗？当前数据将被覆盖！`,
    '确认恢复',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success(`已恢复到版本 ${version.version}`)
    // 这里可以实现版本恢复逻辑
  }).catch(() => {
    ElMessage.info('已取消恢复')
  })
}

// 事件相关方法
const viewEvent = (event) => {
  ElMessage.info(`查看事件: ${event.id}`)
  // 这里可以实现事件查看逻辑
}

// 快速操作方法
const pingTest = () => {
  ElMessage.info('正在进行连通性测试...')
  setTimeout(() => {
    ElMessage.success('连通性测试通过')
  }, 2000)
}

const performanceTest = () => {
  ElMessage.info('正在进行性能测试...')
  setTimeout(() => {
    ElMessage.success('性能测试完成')
  }, 3000)
}

const securityScan = () => {
  ElMessage.info('正在进行安全扫描...')
  setTimeout(() => {
    ElMessage.success('安全扫描完成，未发现安全问题')
  }, 4000)
}

const backupData = () => {
  ElMessage.info('正在备份数据...')
  setTimeout(() => {
    ElMessage.success('数据备份完成')
  }, 2500)
}

// 辅助函数
const getCITypeColor = (type) => {
  const colorMap = {
    'server': '#4CAF50',
    'database': '#2196F3',
    'network': '#FF9800',
    'application': '#9C27B0',
    'storage': '#607D8B'
  }
  return colorMap[type] || '#666'
}

const getCITypeIcon = (type) => {
  const iconMap = {
    'server': 'Monitor',
    'database': 'Coin',
    'network': 'Share',
    'application': 'Service',
    'storage': 'Box'
  }
  return iconMap[type] || 'Box'
}

const getCITypeText = (type) => {
  const textMap = {
    'server': '服务器',
    'database': '数据库',
    'network': '网络设备',
    'application': '应用系统',
    'storage': '存储设备'
  }
  return textMap[type] || type
}

const getCITypeTagType = (type) => {
  const tagMap = {
    'server': 'success',
    'database': 'primary',
    'network': 'warning',
    'application': 'danger',
    'storage': 'info'
  }
  return tagMap[type] || 'info'
}

const getStatusType = (status) => {
  const typeMap = {
    '运行中': 'success',
    '正常': 'success',
    '维护中': 'warning',
    '故障': 'danger',
    '离线': 'info'
  }
  return typeMap[status] || 'info'
}

const getHealthColor = (score) => {
  if (score >= 90) return '#4CAF50'
  if (score >= 70) return '#FF9800'
  return '#F44336'
}

const getChangeType = (type) => {
  const typeMap = {
    '维护': 'primary',
    '变更': 'warning',
    '发布': 'success',
    '故障': 'danger'
  }
  return typeMap[type] || 'info'
}

const getEventType = (type) => {
  const typeMap = {
    '事件': 'danger',
    '请求': 'primary',
    '问题': 'warning',
    '变更': 'success'
  }
  return typeMap[type] || 'info'
}

const getEventStatus = (status) => {
  const typeMap = {
    '已解决': 'success',
    '处理中': 'warning',
    '已完成': 'success',
    '待处理': 'info',
    '已关闭': 'info'
  }
  return typeMap[status] || 'info'
}

// 组件挂载时初始化
onMounted(() => {
  // 可以根据路由参数加载对应的CI数据
  const ciId = route.params.id
  if (ciId) {
    // 加载CI数据
    console.log('Loading CI data for:', ciId)
  }
})
</script>

<style scoped>
.ci-details {
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.ci-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-info h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.ci-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #666;
}

.ci-type,
.ci-id {
  padding: 2px 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
}

.health-indicator {
  text-align: center;
}

.health-label {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 主要内容区域 */
.ci-content {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* 基本信息样式 */
.basic-info {
  padding: 20px;
}

/* 关系图谱样式 */
.relations-view {
  padding: 20px;
}

.relations-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.relations-graph {
  height: 400px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  position: relative;
}

.graph-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.graph-placeholder p {
  margin: 16px 0 0 0;
  font-size: 16px;
}

/* 变更历史样式 */
.change-history {
  padding: 20px;
}

.history-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.change-item {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
}

.change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.change-title {
  font-weight: 500;
  color: #333;
}

.change-content p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.change-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

/* 版本管理样式 */
.version-management {
  padding: 20px;
}

.version-toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

/* 关联事件样式 */
.related-events {
  padding: 20px;
}

/* 右侧信息面板样式 */
.info-panels {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-panel {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 监控指标样式 */
.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: bold;
}

/* 快速操作样式 */
.quick-actions {
  padding: 12px 0;
}

/* 统计信息样式 */
.stats-list {
  padding: 12px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .ci-details {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    padding: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .ci-title {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .ci-meta {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }

  .relations-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .history-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .version-toolbar {
    flex-direction: column;
    gap: 8px;
  }

  .change-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .change-meta {
    flex-direction: column;
    gap: 4px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .metric-item {
    padding: 8px;
  }

  /* 表格在移动端的优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* 描述列表在移动端的优化 */
  .el-descriptions {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .ci-title {
    text-align: center;
  }

  .title-info h2 {
    font-size: 20px;
  }

  .relations-graph {
    height: 300px;
  }

  .quick-actions .el-button {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* 动画效果 */
.info-panel {
  transition: all 0.3s ease;
}

.info-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metric-item {
  transition: all 0.3s ease;
}

.metric-item:hover {
  background: #e3f2fd;
}

/* 标签页样式优化 */
.el-tabs--border-card {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.el-tabs--border-card > .el-tabs__content {
  padding: 0;
}

/* 时间线样式优化 */
.el-timeline-item__timestamp {
  font-size: 12px;
  color: #999;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-button--text {
  padding: 4px 8px;
}

/* 进度条样式 */
.el-progress--circle {
  margin: 0;
}

/* 描述列表样式 */
.el-descriptions {
  margin: 0;
}

.el-descriptions__body .el-descriptions__table {
  border-radius: 8px;
  overflow: hidden;
}
</style>
