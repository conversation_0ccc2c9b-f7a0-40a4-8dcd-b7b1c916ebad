<template>
  <div class="asset-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>资产管理</h2>
          <p>管理IT资产从采购到报废的全生命周期，确保资产合规使用</p>
        </div>
        <div class="action-section">
          <el-tooltip content="新增资产" placement="bottom">
            <el-button type="primary" :icon="Plus" @click="showCreateAssetDialog = true">
              新增资产
            </el-button>
          </el-tooltip>
          <el-button :icon="Document" @click="$router.push('/asset/lifecycle')">
            生命周期
          </el-button>
          <el-button :icon="Key" @click="$router.push('/asset/license')">
            许可证管理
          </el-button>
          <el-tooltip content="刷新数据" placement="bottom">
            <el-button :icon="Refresh" @click="refreshData" :loading="loading">
              刷新
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Box /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.totalAssets }}</div>
                <div class="stat-label">总资产数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.activeAssets }}</div>
                <div class="stat-label">使用中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon maintenance">
                <el-icon><Tools /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.inMaintenanceAssets }}</div>
                <div class="stat-label">维修中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon value">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ formatCurrency(statistics.totalValue) }}</div>
                <div class="stat-label">总价值</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作和图表展示 -->
    <el-row :gutter="20" class="content-row">
      <!-- 快速操作 -->
      <el-col :span="8">
        <el-card class="quick-actions-card">
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
              <el-icon><Operation /></el-icon>
            </div>
          </template>
          <div class="quick-actions">
            <div class="action-item" @click="showCreateAssetDialog = true">
              <el-icon class="action-icon"><Plus /></el-icon>
              <div class="action-content">
                <div class="action-title">新增资产</div>
                <div class="action-desc">添加新的IT资产到系统</div>
              </div>
              <el-icon class="action-arrow"><ArrowRight /></el-icon>
            </div>
            <div class="action-item" @click="$router.push('/asset/lifecycle')">
              <el-icon class="action-icon"><Timer /></el-icon>
              <div class="action-content">
                <div class="action-title">生命周期管理</div>
                <div class="action-desc">管理资产状态流转</div>
              </div>
              <el-icon class="action-arrow"><ArrowRight /></el-icon>
            </div>
            <div class="action-item" @click="$router.push('/asset/license')">
              <el-icon class="action-icon"><Key /></el-icon>
              <div class="action-content">
                <div class="action-title">许可证管理</div>
                <div class="action-desc">管理软件许可证合规</div>
              </div>
              <el-icon class="action-arrow"><ArrowRight /></el-icon>
            </div>
            <div class="action-item" @click="showAssetReportDialog = true">
              <el-icon class="action-icon"><DataBoard /></el-icon>
              <div class="action-content">
                <div class="action-title">资产报表</div>
                <div class="action-desc">生成资产统计报表</div>
              </div>
              <el-icon class="action-arrow"><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 资产分类分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>资产分类分布</span>
              <el-icon><PieChart /></el-icon>
            </div>
          </template>
          <div class="chart-container" ref="categoryChartRef"></div>
        </el-card>
      </el-col>

      <!-- 资产状态分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>资产状态分布</span>
              <el-icon><DataBoard /></el-icon>
            </div>
          </template>
          <div class="chart-container" ref="statusChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 资产趋势和最近资产 -->
    <el-row :gutter="20" class="content-row">
      <!-- 资产价值趋势 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>资产价值趋势</span>
              <el-icon><TrendCharts /></el-icon>
            </div>
          </template>
          <div class="chart-container trend-chart" ref="trendChartRef"></div>
        </el-card>
      </el-col>

      <!-- 最近资产 -->
      <el-col :span="12">
        <el-card class="recent-assets-card">
          <template #header>
            <div class="card-header">
              <span>最近资产</span>
              <el-button type="text" @click="$router.push('/asset/list')">
                查看全部
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="recent-assets">
            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else>
              <div
                v-for="asset in recentAssets"
                :key="asset.id"
                class="asset-item"
                @click="viewAssetDetail(asset.id)"
              >
                <div class="asset-icon">
                  <el-icon>
                    <component :is="getCategoryIcon(asset.category)" />
                  </el-icon>
                </div>
                <div class="asset-info">
                  <div class="asset-name">{{ asset.name }}</div>
                  <div class="asset-meta">
                    <span class="asset-code">{{ asset.assetCode }}</span>
                    <span class="asset-department">{{ asset.department || '未分配' }}</span>
                  </div>
                </div>
                <div class="asset-status">
                  <el-tag
                    :type="getStatusType(asset.status)"
                    size="small"
                  >
                    {{ asset.status }}
                  </el-tag>
                </div>
              </div>
              <div v-if="recentAssets.length === 0" class="empty-state">
                <el-empty description="暂无资产数据" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增资产对话框 -->
    <el-dialog
      v-model="showCreateAssetDialog"
      title="新增资产"
      width="600px"
      :before-close="handleCloseCreateDialog"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资产名称" prop="name">
              <el-input v-model="createForm.name" placeholder="请输入资产名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产类别" prop="category">
              <el-select v-model="createForm.category" placeholder="请选择资产类别">
                <el-option
                  v-for="category in assetCategories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="createForm.brand" placeholder="请输入品牌" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-input v-model="createForm.model" placeholder="请输入型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="序列号" prop="serialNumber">
              <el-input v-model="createForm.serialNumber" placeholder="请输入序列号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购价格" prop="purchasePrice">
              <el-input-number
                v-model="createForm.purchasePrice"
                :min="0"
                :precision="2"
                placeholder="请输入采购价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采购日期" prop="purchaseDate">
              <el-date-picker
                v-model="createForm.purchaseDate"
                type="date"
                placeholder="请选择采购日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier">
              <el-input v-model="createForm.supplier" placeholder="请输入供应商" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="位置" prop="location">
          <el-input v-model="createForm.location" placeholder="请输入资产位置" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateAssetDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreateAsset" :loading="createLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 资产报表对话框 -->
    <el-dialog
      v-model="showAssetReportDialog"
      title="资产报表"
      width="500px"
    >
      <div class="report-options">
        <el-form label-width="100px">
          <el-form-item label="报表类型">
            <el-select v-model="reportType" placeholder="请选择报表类型">
              <el-option label="资产台账总览" value="inventory" />
              <el-option label="资产折旧报告" value="depreciation" />
              <el-option label="资产分布报告" value="distribution" />
              <el-option label="高价值资产报告" value="highValue" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="reportDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAssetReportDialog = false">取消</el-button>
          <el-button type="primary" @click="generateReport" :loading="reportLoading">
            生成报表
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Document, Key, Refresh, Box, SuccessFilled, Tools, Money,
  Operation, ArrowRight, Timer, DataBoard, PieChart, TrendCharts
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getAssetStatistics,
  getAssetList,
  createAsset,
  getAssetCategories
} from '@/api/assetApi.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const reportLoading = ref(false)
const showCreateAssetDialog = ref(false)
const showAssetReportDialog = ref(false)

// 统计数据
const statistics = ref({
  totalAssets: 0,
  activeAssets: 0,
  inMaintenanceAssets: 0,
  totalValue: 0
})

// 最近资产
const recentAssets = ref([])

// 资产类别
const assetCategories = ref([])

// 图表引用
const categoryChartRef = ref(null)
const statusChartRef = ref(null)
const trendChartRef = ref(null)

// 图表实例
let categoryChart = null
let statusChart = null
let trendChart = null

// 创建表单
const createFormRef = ref(null)
const createForm = reactive({
  name: '',
  category: '',
  brand: '',
  model: '',
  serialNumber: '',
  purchasePrice: null,
  purchaseDate: '',
  supplier: '',
  location: ''
})

// 表单验证规则
const createFormRules = {
  name: [
    { required: true, message: '请输入资产名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择资产类别', trigger: 'change' }
  ],
  brand: [
    { required: true, message: '请输入品牌', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '请输入型号', trigger: 'blur' }
  ],
  serialNumber: [
    { required: true, message: '请输入序列号', trigger: 'blur' }
  ],
  purchasePrice: [
    { required: true, message: '请输入采购价格', trigger: 'blur' }
  ],
  purchaseDate: [
    { required: true, message: '请选择采购日期', trigger: 'change' }
  ],
  supplier: [
    { required: true, message: '请输入供应商', trigger: 'blur' }
  ]
}

// 报表相关
const reportType = ref('inventory')
const reportDateRange = ref([])

// 初始化数据
const initData = async () => {
  loading.value = true
  try {
    // 获取统计数据
    const statsRes = await getAssetStatistics()
    statistics.value = statsRes.data

    // 获取最近资产
    const assetsRes = await getAssetList({ page: 1, pageSize: 10 })
    recentAssets.value = assetsRes.data.list || []

    // 获取资产类别
    const categoriesRes = await getAssetCategories()
    assetCategories.value = categoriesRes.data

    // 初始化图表
    await nextTick()
    initCharts()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  initCategoryChart()
  initStatusChart()
  initTrendChart()
}

// 初始化分类分布图表
const initCategoryChart = () => {
  if (!categoryChartRef.value) return

  categoryChart = echarts.init(categoryChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '资产分类',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: statistics.value.assetsByCategory || []
      }
    ]
  }

  categoryChart.setOption(option)
}

// 初始化状态分布图表
const initStatusChart = () => {
  if (!statusChartRef.value) return

  statusChart = echarts.init(statusChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '资产状态',
        type: 'pie',
        radius: '70%',
        center: ['60%', '50%'],
        data: statistics.value.assetsByStatus || [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  statusChart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  trendChart = echarts.init(trendChartRef.value)

  const trendData = statistics.value.monthlyTrend || []

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['资产数量', '资产价值']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: trendData.map(item => item.month)
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '价值(万元)',
        position: 'right',
        axisLabel: {
          formatter: function (value) {
            return (value / 10000).toFixed(0)
          }
        }
      }
    ],
    series: [
      {
        name: '资产数量',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: trendData.map(item => item.assets)
      },
      {
        name: '资产价值',
        type: 'line',
        yAxisIndex: 1,
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: trendData.map(item => item.value)
      }
    ]
  }

  trendChart.setOption(option)
}

// 刷新数据
const refreshData = async () => {
  await initData()
  ElMessage.success('数据刷新成功')
}

// 格式化货币
const formatCurrency = (value) => {
  if (!value) return '¥0'
  return `¥${(value / 10000).toFixed(1)}万`
}

// 获取分类图标
const getCategoryIcon = (category) => {
  const iconMap = {
    server: 'Monitor',
    laptop: 'Monitor', // 使用Monitor代替Laptop
    desktop: 'Monitor', // 使用Monitor代替Desktop
    network: 'Connection',
    storage: 'FolderOpened',
    printer: 'Printer',
    mobile: 'Cellphone',
    other: 'Box'
  }
  return iconMap[category] || 'Box'
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '使用中': 'success',
    '库存中': 'info',
    '维修中': 'warning',
    '借用中': 'danger',
    '停用': 'info',
    '已报废': 'danger'
  }
  return typeMap[status] || 'info'
}

// 查看资产详情
const viewAssetDetail = (assetId) => {
  router.push(`/asset/detail/${assetId}`)
}

// 处理创建资产
const handleCreateAsset = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    createLoading.value = true

    const formData = {
      ...createForm,
      purchaseDate: createForm.purchaseDate ?
        createForm.purchaseDate.toISOString().split('T')[0] : '',
      status: '待入库'
    }

    await createAsset(formData)
    ElMessage.success('资产创建成功')
    showCreateAssetDialog.value = false
    resetCreateForm()
    refreshData()
  } catch (error) {
    console.error('创建资产失败:', error)
    ElMessage.error('创建资产失败')
  } finally {
    createLoading.value = false
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.keys(createForm).forEach(key => {
    createForm[key] = ''
  })
  createForm.purchasePrice = null
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

// 处理关闭创建对话框
const handleCloseCreateDialog = (done) => {
  ElMessageBox.confirm('确认关闭？未保存的数据将丢失。')
    .then(() => {
      resetCreateForm()
      done()
    })
    .catch(() => {})
}

// 生成报表
const generateReport = async () => {
  if (!reportType.value) {
    ElMessage.warning('请选择报表类型')
    return
  }

  reportLoading.value = true
  try {
    // 模拟报表生成
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('报表生成成功，正在下载...')
    showAssetReportDialog.value = false
  } catch (error) {
    console.error('生成报表失败:', error)
    ElMessage.error('生成报表失败')
  } finally {
    reportLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  initData()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 销毁图表实例
  if (categoryChart) {
    categoryChart.dispose()
    categoryChart = null
  }
  if (statusChart) {
    statusChart.dispose()
    statusChart = null
  }
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }

  // 移除事件监听
  window.removeEventListener('resize', handleResize)
})

// 处理窗口大小变化
const handleResize = () => {
  if (categoryChart) categoryChart.resize()
  if (statusChart) statusChart.resize()
  if (trendChart) trendChart.resize()
}
</script>

<style scoped>
.asset-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.maintenance {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.value {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 内容行 */
.content-row {
  margin-bottom: 20px;
}

/* 快速操作卡片 */
.quick-actions-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fafafa;
}

.action-item:hover {
  border-color: #1976D2;
  background-color: #f8fbff;
  transform: translateX(4px);
}

.action-icon {
  font-size: 20px;
  color: #1976D2;
  margin-right: 12px;
}

.action-content {
  flex: 1;
}

.action-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #666;
}

.action-arrow {
  font-size: 16px;
  color: #c0c4cc;
  transition: all 0.2s;
}

.action-item:hover .action-arrow {
  color: #1976D2;
  transform: translateX(2px);
}

/* 图表卡片 */
.chart-card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-container {
  height: 200px;
  width: 100%;
}

.trend-chart {
  height: 300px;
}

/* 最近资产卡片 */
.recent-assets-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recent-assets {
  max-height: 400px;
  overflow-y: auto;
}

.loading-container {
  padding: 20px;
}

.asset-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.asset-item:hover {
  background-color: #f8fbff;
  padding-left: 8px;
}

.asset-item:last-child {
  border-bottom: none;
}

.asset-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #1976D2;
  font-size: 18px;
}

.asset-info {
  flex: 1;
}

.asset-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.asset-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.asset-status {
  margin-left: 12px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 对话框 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.report-options {
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-row .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .asset-management {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
  }

  .stats-cards .el-col {
    margin-bottom: 16px;
  }

  .stat-number {
    font-size: 24px;
  }

  .chart-container {
    height: 180px;
  }

  .trend-chart {
    height: 250px;
  }
}
</style>
