// ITSM可视化大屏模板数据
export const itsmTemplates = [
  {
    id: 'itsm-service-desk',
    name: 'ITSM服务台监控大屏',
    category: 'itsm',
    description: '实时监控服务台工单处理情况、客户满意度、响应时间等关键指标',
    thumbnail: '/templates/service-desk.png',
    tags: ['服务台', '工单', '监控'],
    meta: {
      title: 'ITSM服务台监控大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #0a1a2f 0%, #1a2a4f 50%, #2a3a6f 100%)'
    },
    components: [
      {
        id: 'title-001',
        type: 'title',
        position: { x: 50, y: 30, w: 800, h: 80 },
        props: {
          text: 'ITSM服务台实时监控',
          fontSize: '36px',
          fontWeight: 'bold',
          color: '#00ff88',
          textAlign: 'center'
        }
      },
      {
        id: 'ticket-total',
        type: 'number-card',
        position: { x: 100, y: 150, w: 280, h: 180 },
        props: {
          title: '今日工单总数',
          value: 1247,
          unit: '个',
          trend: 12.5,
          trendText: '较昨日',
          color: '#409eff',
          icon: 'Document'
        },
        dataConfig: {
          source: 'api',
          url: '/api/tickets/total',
          interval: 30000,
          mapping: { value: 'count', trend: 'growth_rate' }
        }
      },
      {
        id: 'ticket-pending',
        type: 'number-card',
        position: { x: 420, y: 150, w: 280, h: 180 },
        props: {
          title: '待处理工单',
          value: 89,
          unit: '个',
          trend: -5.2,
          trendText: '较昨日',
          color: '#e6a23c',
          icon: 'Clock'
        },
        dataConfig: {
          source: 'api',
          url: '/api/tickets/pending',
          interval: 10000,
          mapping: { value: 'count', trend: 'growth_rate' }
        }
      },
      {
        id: 'ticket-resolved',
        type: 'number-card',
        position: { x: 740, y: 150, w: 280, h: 180 },
        props: {
          title: '已解决工单',
          value: 1158,
          unit: '个',
          trend: 8.7,
          trendText: '较昨日',
          color: '#67c23a',
          icon: 'CircleCheck'
        },
        dataConfig: {
          source: 'api',
          url: '/api/tickets/resolved',
          interval: 30000,
          mapping: { value: 'count', trend: 'growth_rate' }
        }
      },
      {
        id: 'satisfaction-rate',
        type: 'progress-ring',
        position: { x: 1080, y: 150, w: 280, h: 180 },
        props: {
          title: '客户满意度',
          value: 94.5,
          unit: '%',
          color: '#00ff88',
          strokeWidth: 12
        },
        dataConfig: {
          source: 'api',
          url: '/api/satisfaction/rate',
          interval: 60000,
          mapping: { value: 'rate' }
        }
      },
      {
        id: 'ticket-trend',
        type: 'line-chart',
        position: { x: 100, y: 380, w: 600, h: 300 },
        props: {
          title: '工单趋势分析（近7天）',
          colors: ['#409eff', '#67c23a', '#e6a23c'],
          smooth: true,
          showArea: true
        },
        dataConfig: {
          source: 'api',
          url: '/api/tickets/trend',
          interval: 300000,
          mapping: {
            xAxis: 'date',
            series: [
              { name: '新建工单', field: 'created' },
              { name: '已解决', field: 'resolved' },
              { name: '待处理', field: 'pending' }
            ]
          }
        }
      },
      {
        id: 'priority-distribution',
        type: 'pie-chart',
        position: { x: 750, y: 380, w: 400, h: 300 },
        props: {
          title: '工单优先级分布',
          colors: ['#f56c6c', '#e6a23c', '#409eff', '#67c23a'],
          showLegend: true,
          labelPosition: 'outside'
        },
        dataConfig: {
          source: 'api',
          url: '/api/tickets/priority',
          interval: 60000,
          mapping: {
            name: 'priority',
            value: 'count'
          }
        }
      },
      {
        id: 'response-time',
        type: 'gauge-chart',
        position: { x: 1200, y: 380, w: 300, h: 300 },
        props: {
          title: '平均响应时间',
          value: 2.3,
          unit: '小时',
          min: 0,
          max: 8,
          color: '#6b4eff',
          threshold: [
            { value: 2, color: '#67c23a' },
            { value: 4, color: '#e6a23c' },
            { value: 8, color: '#f56c6c' }
          ]
        },
        dataConfig: {
          source: 'api',
          url: '/api/tickets/response-time',
          interval: 60000,
          mapping: { value: 'avg_hours' }
        }
      },
      {
        id: 'agent-workload',
        type: 'bar-chart',
        position: { x: 100, y: 720, w: 500, h: 280 },
        props: {
          title: '工程师工作负载',
          colors: ['#409eff'],
          showValues: true,
          horizontal: false
        },
        dataConfig: {
          source: 'api',
          url: '/api/agents/workload',
          interval: 120000,
          mapping: {
            xAxis: 'agent_name',
            yAxis: 'ticket_count'
          }
        }
      },
      {
        id: 'category-distribution',
        type: 'radar-chart',
        position: { x: 650, y: 720, w: 400, h: 280 },
        props: {
          title: '工单类别分布',
          colors: ['#6b4eff', '#00ff88'],
          showArea: true,
          areaOpacity: 0.3
        },
        dataConfig: {
          source: 'api',
          url: '/api/tickets/category',
          interval: 300000,
          mapping: {
            indicator: 'category',
            value: 'count'
          }
        }
      },
      {
        id: 'real-time-alerts',
        type: 'scrolling-list',
        position: { x: 1100, y: 720, w: 400, h: 280 },
        props: {
          title: '实时告警信息',
          itemHeight: 40,
          scrollSpeed: 2000,
          maxItems: 6
        },
        dataConfig: {
          source: 'websocket',
          url: 'ws://localhost:8080/alerts',
          mapping: {
            text: 'message',
            time: 'timestamp',
            level: 'severity'
          }
        }
      }
    ]
  },
  
  {
    id: 'itsm-incident-management',
    name: 'ITSM事件管理大屏',
    category: 'itsm',
    description: '监控IT事件处理流程、影响分析、解决效率等关键业务指标',
    thumbnail: '/templates/incident-management.png',
    tags: ['事件管理', '故障', '影响分析'],
    meta: {
      title: 'ITSM事件管理监控大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #1a0a2f 0%, #2a1a4f 50%, #3a2a6f 100%)'
    },
    components: [
      {
        id: 'title-002',
        type: 'title',
        position: { x: 50, y: 30, w: 800, h: 80 },
        props: {
          text: 'ITSM事件管理监控中心',
          fontSize: '36px',
          fontWeight: 'bold',
          color: '#ff6b6b',
          textAlign: 'center'
        }
      },
      {
        id: 'incident-overview',
        type: 'number-grid',
        position: { x: 100, y: 150, w: 1200, h: 200 },
        props: {
          title: '事件概览',
          items: [
            { label: '活跃事件', value: 23, unit: '个', color: '#f56c6c', trend: 5.2 },
            { label: '严重事件', value: 3, unit: '个', color: '#e6a23c', trend: -1 },
            { label: '已解决', value: 156, unit: '个', color: '#67c23a', trend: 12.8 },
            { label: '平均MTTR', value: 4.2, unit: '小时', color: '#409eff', trend: -8.5 }
          ]
        },
        dataConfig: {
          source: 'api',
          url: '/api/incidents/overview',
          interval: 30000
        }
      },
      {
        id: 'incident-severity',
        type: 'funnel-chart',
        position: { x: 100, y: 400, w: 500, h: 350 },
        props: {
          title: '事件严重性漏斗',
          colors: ['#f56c6c', '#e6a23c', '#409eff', '#67c23a'],
          showLabels: true
        },
        dataConfig: {
          source: 'api',
          url: '/api/incidents/severity',
          interval: 60000,
          mapping: {
            name: 'severity',
            value: 'count'
          }
        }
      },
      {
        id: 'mttr-trend',
        type: 'area-chart',
        position: { x: 650, y: 400, w: 600, h: 350 },
        props: {
          title: 'MTTR趋势分析',
          colors: ['#6b4eff'],
          smooth: true,
          showArea: true,
          areaOpacity: 0.3
        },
        dataConfig: {
          source: 'api',
          url: '/api/incidents/mttr-trend',
          interval: 300000,
          mapping: {
            xAxis: 'date',
            yAxis: 'mttr_hours'
          }
        }
      },
      {
        id: 'impact-analysis',
        type: 'heatmap',
        position: { x: 100, y: 800, w: 600, h: 200 },
        props: {
          title: '业务影响热力图',
          colorRange: ['#67c23a', '#e6a23c', '#f56c6c']
        },
        dataConfig: {
          source: 'api',
          url: '/api/incidents/impact',
          interval: 120000,
          mapping: {
            xAxis: 'service',
            yAxis: 'time_period',
            value: 'impact_score'
          }
        }
      },
      {
        id: 'resolution-team',
        type: 'horizontal-bar',
        position: { x: 750, y: 800, w: 500, h: 200 },
        props: {
          title: '解决团队效率排名',
          colors: ['#00ff88'],
          showValues: true
        },
        dataConfig: {
          source: 'api',
          url: '/api/incidents/team-performance',
          interval: 300000,
          mapping: {
            xAxis: 'team_name',
            yAxis: 'resolution_rate'
          }
        }
      }
    ]
  },

  {
    id: 'itsm-asset-monitoring',
    name: 'ITSM资产监控大屏',
    category: 'itsm',
    description: '实时监控IT资产状态、利用率、生命周期管理等关键指标',
    thumbnail: '/templates/asset-monitoring.png',
    tags: ['资产管理', '监控', '生命周期'],
    meta: {
      title: 'ITSM资产监控大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #0f1419 0%, #1f2429 50%, #2f3439 100%)'
    },
    components: [
      {
        id: 'asset-overview',
        type: 'number-grid',
        position: { x: 100, y: 100, w: 1200, h: 180 },
        props: {
          title: '资产概览',
          items: [
            { label: '总资产数', value: 15847, unit: '台', color: '#409eff' },
            { label: '在线设备', value: 14523, unit: '台', color: '#67c23a' },
            { label: '离线设备', value: 1324, unit: '台', color: '#f56c6c' },
            { label: '资产利用率', value: 87.3, unit: '%', color: '#6b4eff' }
          ]
        }
      },
      {
        id: 'asset-category',
        type: 'doughnut-chart',
        position: { x: 100, y: 320, w: 400, h: 300 },
        props: {
          title: '资产类别分布',
          colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#6b4eff'],
          innerRadius: 60
        }
      },
      {
        id: 'asset-health',
        type: 'gauge-cluster',
        position: { x: 550, y: 320, w: 600, h: 300 },
        props: {
          title: '资产健康度监控',
          gauges: [
            { name: '服务器', value: 92, color: '#67c23a' },
            { name: '网络设备', value: 88, color: '#409eff' },
            { name: '存储设备', value: 95, color: '#00ff88' },
            { name: '安全设备', value: 85, color: '#e6a23c' }
          ]
        }
      },
      {
        id: 'lifecycle-status',
        type: 'stacked-bar',
        position: { x: 1200, y: 320, w: 300, h: 300 },
        props: {
          title: '生命周期状态',
          colors: ['#67c23a', '#409eff', '#e6a23c', '#f56c6c'],
          horizontal: true
        }
      },
      {
        id: 'asset-map',
        type: 'topology-map',
        position: { x: 100, y: 660, w: 700, h: 320 },
        props: {
          title: '资产拓扑图',
          showLabels: true,
          nodeSize: 'auto',
          linkColor: '#409eff'
        }
      },
      {
        id: 'maintenance-schedule',
        type: 'timeline',
        position: { x: 850, y: 660, w: 650, h: 320 },
        props: {
          title: '维护计划时间线',
          itemHeight: 40,
          showTime: true
        }
      }
    ]
  },

  {
    id: 'itsm-change-management',
    name: 'ITSM变更管理大屏',
    category: 'itsm',
    description: '监控变更请求处理流程、成功率、风险评估等管理指标',
    thumbnail: '/templates/change-management.png',
    tags: ['变更管理', '风险评估', '流程监控'],
    meta: {
      title: 'ITSM变更管理大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'
    },
    components: [
      {
        id: 'change-stats',
        type: 'metric-cards',
        position: { x: 100, y: 100, w: 1200, h: 200 },
        props: {
          cards: [
            { title: '本月变更', value: 234, unit: '个', trend: 8.5, color: '#409eff' },
            { title: '成功率', value: 94.2, unit: '%', trend: 2.1, color: '#67c23a' },
            { title: '紧急变更', value: 12, unit: '个', trend: -15.3, color: '#e6a23c' },
            { title: '回滚变更', value: 8, unit: '个', trend: -25.0, color: '#f56c6c' }
          ]
        }
      },
      {
        id: 'change-flow',
        type: 'sankey-diagram',
        position: { x: 100, y: 340, w: 600, h: 350 },
        props: {
          title: '变更流程分析',
          nodeColor: '#409eff',
          linkOpacity: 0.6
        }
      },
      {
        id: 'risk-matrix',
        type: 'scatter-plot',
        position: { x: 750, y: 340, w: 550, h: 350 },
        props: {
          title: '变更风险矩阵',
          xAxisTitle: '影响程度',
          yAxisTitle: '发生概率',
          colors: ['#67c23a', '#e6a23c', '#f56c6c']
        }
      },
      {
        id: 'approval-time',
        type: 'box-plot',
        position: { x: 100, y: 730, w: 500, h: 250 },
        props: {
          title: '审批时间分布',
          color: '#6b4eff'
        }
      },
      {
        id: 'change-calendar',
        type: 'calendar-heatmap',
        position: { x: 650, y: 730, w: 650, h: 250 },
        props: {
          title: '变更日历热力图',
          colorRange: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
        }
      }
    ]
  },

  {
    id: 'itsm-performance-monitoring',
    name: 'ITSM性能监控大屏',
    category: 'itsm',
    description: '监控IT服务性能指标、SLA达成情况、系统可用性等关键数据',
    thumbnail: '/templates/performance-monitoring.png',
    tags: ['性能监控', 'SLA', '可用性'],
    meta: {
      title: 'ITSM性能监控大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #0d1421 0%, #1a2332 50%, #273244 100%)'
    },
    components: [
      {
        id: 'sla-dashboard',
        type: 'sla-grid',
        position: { x: 100, y: 100, w: 1200, h: 200 },
        props: {
          title: 'SLA达成情况',
          items: [
            { service: '邮件服务', target: 99.9, actual: 99.95, status: 'good' },
            { service: 'ERP系统', target: 99.5, actual: 98.8, status: 'warning' },
            { service: '办公网络', target: 99.8, actual: 99.92, status: 'good' },
            { service: '数据库', target: 99.9, actual: 99.7, status: 'critical' }
          ]
        }
      },
      {
        id: 'response-time-trend',
        type: 'multi-line-chart',
        position: { x: 100, y: 340, w: 600, h: 300 },
        props: {
          title: '响应时间趋势',
          colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c'],
          smooth: true,
          showPoints: false
        }
      },
      {
        id: 'availability-gauge',
        type: 'multi-gauge',
        position: { x: 750, y: 340, w: 550, h: 300 },
        props: {
          title: '系统可用性',
          gauges: [
            { name: '核心系统', value: 99.8, target: 99.9 },
            { name: '业务系统', value: 98.5, target: 99.0 },
            { name: '支撑系统', value: 99.2, target: 98.5 }
          ]
        }
      },
      {
        id: 'throughput-chart',
        type: 'area-stack-chart',
        position: { x: 100, y: 680, w: 500, h: 280 },
        props: {
          title: '系统吞吐量',
          colors: ['#409eff', '#67c23a', '#e6a23c'],
          showArea: true,
          areaOpacity: 0.6
        }
      },
      {
        id: 'error-rate-heatmap',
        type: 'service-heatmap',
        position: { x: 650, y: 680, w: 400, h: 280 },
        props: {
          title: '服务错误率热力图',
          colorRange: ['#67c23a', '#e6a23c', '#f56c6c']
        }
      },
      {
        id: 'alert-timeline',
        type: 'alert-timeline',
        position: { x: 1100, y: 680, w: 400, h: 280 },
        props: {
          title: '告警时间线',
          maxItems: 10,
          autoScroll: true
        }
      }
    ]
  },

  {
    id: 'itsm-security-monitoring',
    name: 'ITSM安全监控大屏',
    category: 'itsm',
    description: '监控安全事件、威胁检测、合规性检查等安全管理指标',
    thumbnail: '/templates/security-monitoring.png',
    tags: ['安全监控', '威胁检测', '合规性'],
    meta: {
      title: 'ITSM安全监控大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #1a0a0a 0%, #2a1a1a 50%, #3a2a2a 100%)'
    },
    components: [
      {
        id: 'security-overview',
        type: 'security-metrics',
        position: { x: 100, y: 100, w: 1200, h: 180 },
        props: {
          metrics: [
            { name: '安全事件', value: 45, unit: '个', level: 'high', trend: -12.5 },
            { name: '威胁检测', value: 1247, unit: '次', level: 'medium', trend: 8.3 },
            { name: '阻断攻击', value: 892, unit: '次', level: 'good', trend: 15.7 },
            { name: '合规得分', value: 92, unit: '分', level: 'good', trend: 2.1 }
          ]
        }
      },
      {
        id: 'threat-map',
        type: 'world-threat-map',
        position: { x: 100, y: 320, w: 700, h: 400 },
        props: {
          title: '全球威胁态势图',
          showAttackLines: true,
          attackColor: '#f56c6c',
          defenseColor: '#67c23a'
        }
      },
      {
        id: 'attack-types',
        type: 'radar-chart',
        position: { x: 850, y: 320, w: 450, h: 400 },
        props: {
          title: '攻击类型分析',
          colors: ['#f56c6c', '#e6a23c'],
          showArea: true,
          areaOpacity: 0.3
        }
      },
      {
        id: 'security-events',
        type: 'event-stream',
        position: { x: 100, y: 760, w: 600, h: 220 },
        props: {
          title: '实时安全事件',
          maxItems: 8,
          scrollSpeed: 3000,
          showSeverity: true
        }
      },
      {
        id: 'compliance-status',
        type: 'compliance-grid',
        position: { x: 750, y: 760, w: 550, h: 220 },
        props: {
          title: '合规性检查状态',
          items: [
            { name: 'ISO27001', status: 'passed', score: 95 },
            { name: 'SOX', status: 'warning', score: 87 },
            { name: 'GDPR', status: 'passed', score: 92 },
            { name: 'PCI-DSS', status: 'failed', score: 78 }
          ]
        }
      }
    ]
  },

  {
    id: 'itsm-knowledge-management',
    name: 'ITSM知识管理大屏',
    category: 'itsm',
    description: '监控知识库使用情况、文档质量、知识共享效率等指标',
    thumbnail: '/templates/knowledge-management.png',
    tags: ['知识管理', '文档', '共享'],
    meta: {
      title: 'ITSM知识管理大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #0a1a0a 0%, #1a2a1a 50%, #2a3a2a 100%)'
    },
    components: [
      {
        id: 'knowledge-stats',
        type: 'info-cards',
        position: { x: 100, y: 100, w: 1200, h: 180 },
        props: {
          cards: [
            { title: '知识文档', value: 8547, unit: '篇', icon: 'Document' },
            { title: '月访问量', value: 45623, unit: '次', icon: 'View' },
            { title: '解决率', value: 78.5, unit: '%', icon: 'CircleCheck' },
            { title: '满意度', value: 4.6, unit: '分', icon: 'Star' }
          ]
        }
      },
      {
        id: 'category-tree',
        type: 'tree-map',
        position: { x: 100, y: 320, w: 500, h: 350 },
        props: {
          title: '知识分类树图',
          colorRange: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c']
        }
      },
      {
        id: 'usage-trend',
        type: 'area-chart',
        position: { x: 650, y: 320, w: 650, h: 350 },
        props: {
          title: '知识库使用趋势',
          colors: ['#6b4eff'],
          smooth: true,
          showArea: true
        }
      },
      {
        id: 'popular-articles',
        type: 'ranking-list',
        position: { x: 100, y: 710, w: 500, h: 270 },
        props: {
          title: '热门文档排行',
          showRank: true,
          showTrend: true,
          maxItems: 8
        }
      },
      {
        id: 'quality-score',
        type: 'quality-radar',
        position: { x: 650, y: 710, w: 400, h: 270 },
        props: {
          title: '文档质量评分',
          dimensions: ['完整性', '准确性', '时效性', '实用性', '易读性']
        }
      },
      {
        id: 'contribution-map',
        type: 'contribution-heatmap',
        position: { x: 1100, y: 710, w: 400, h: 270 },
        props: {
          title: '贡献者活跃度',
          colorRange: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b']
        }
      }
    ]
  },

  {
    id: 'itsm-capacity-planning',
    name: 'ITSM容量规划大屏',
    category: 'itsm',
    description: '监控系统容量使用情况、预测分析、资源规划等管理数据',
    thumbnail: '/templates/capacity-planning.png',
    tags: ['容量规划', '预测分析', '资源管理'],
    meta: {
      title: 'ITSM容量规划大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #1a1a0a 0%, #2a2a1a 50%, #3a3a2a 100%)'
    },
    components: [
      {
        id: 'capacity-overview',
        type: 'capacity-meters',
        position: { x: 100, y: 100, w: 1200, h: 200 },
        props: {
          meters: [
            { name: 'CPU使用率', current: 68, threshold: 80, unit: '%' },
            { name: '内存使用率', current: 72, threshold: 85, unit: '%' },
            { name: '存储使用率', current: 45, threshold: 75, unit: '%' },
            { name: '网络带宽', current: 35, threshold: 70, unit: '%' }
          ]
        }
      },
      {
        id: 'forecast-chart',
        type: 'forecast-line',
        position: { x: 100, y: 340, w: 700, h: 350 },
        props: {
          title: '容量预测分析',
          forecastPeriod: 90,
          confidenceInterval: true,
          colors: ['#409eff', '#67c23a', '#e6a23c']
        }
      },
      {
        id: 'resource-allocation',
        type: 'sunburst-chart',
        position: { x: 850, y: 340, w: 450, h: 350 },
        props: {
          title: '资源分配结构',
          colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#6b4eff']
        }
      },
      {
        id: 'growth-trend',
        type: 'growth-chart',
        position: { x: 100, y: 730, w: 600, h: 250 },
        props: {
          title: '资源增长趋势',
          showGrowthRate: true,
          colors: ['#00ff88']
        }
      },
      {
        id: 'threshold-alerts',
        type: 'threshold-monitor',
        position: { x: 750, y: 730, w: 550, h: 250 },
        props: {
          title: '阈值告警监控',
          showHistory: true,
          alertLevels: ['info', 'warning', 'critical']
        }
      }
    ]
  },

  {
    id: 'itsm-cost-management',
    name: 'ITSM成本管理大屏',
    category: 'itsm',
    description: '监控IT成本分析、预算执行、成本优化等财务管理指标',
    thumbnail: '/templates/cost-management.png',
    tags: ['成本管理', '预算', '财务分析'],
    meta: {
      title: 'ITSM成本管理大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #0a0a1a 0%, #1a1a2a 50%, #2a2a3a 100%)'
    },
    components: [
      {
        id: 'cost-summary',
        type: 'financial-cards',
        position: { x: 100, y: 100, w: 1200, h: 180 },
        props: {
          cards: [
            { title: '本月支出', value: 2847563, unit: '元', trend: 5.2, format: 'currency' },
            { title: '预算执行率', value: 78.5, unit: '%', trend: 2.1, format: 'percentage' },
            { title: '成本节约', value: 456789, unit: '元', trend: 15.8, format: 'currency' },
            { title: 'ROI', value: 3.2, unit: '', trend: 8.7, format: 'ratio' }
          ]
        }
      },
      {
        id: 'cost-breakdown',
        type: 'waterfall-chart',
        position: { x: 100, y: 320, w: 600, h: 350 },
        props: {
          title: '成本构成分析',
          colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c']
        }
      },
      {
        id: 'budget-vs-actual',
        type: 'comparison-bar',
        position: { x: 750, y: 320, w: 550, h: 350 },
        props: {
          title: '预算vs实际支出',
          colors: ['#409eff', '#e6a23c'],
          showVariance: true
        }
      },
      {
        id: 'cost-trend',
        type: 'cost-trend-chart',
        position: { x: 100, y: 710, w: 700, h: 270 },
        props: {
          title: '成本趋势分析',
          showForecast: true,
          forecastColor: '#6b4eff'
        }
      },
      {
        id: 'optimization-opportunities',
        type: 'optimization-matrix',
        position: { x: 850, y: 710, w: 450, h: 270 },
        props: {
          title: '成本优化机会',
          xAxis: '节约潜力',
          yAxis: '实施难度',
          bubbleSize: 'impact'
        }
      }
    ]
  },

  {
    id: 'itsm-compliance-audit',
    name: 'ITSM合规审计大屏',
    category: 'itsm',
    description: '监控合规性检查、审计结果、风险评估等治理管理指标',
    thumbnail: '/templates/compliance-audit.png',
    tags: ['合规审计', '风险评估', '治理'],
    meta: {
      title: 'ITSM合规审计大屏',
      author: 'ITSM Team',
      resolution: { width: 1920, height: 1080 },
      theme: 'dark-cyber'
    },
    background: {
      type: 'gradient',
      value: 'linear-gradient(135deg, #1a0a1a 0%, #2a1a2a 50%, #3a2a3a 100%)'
    },
    components: [
      {
        id: 'compliance-overview',
        type: 'compliance-dashboard',
        position: { x: 100, y: 100, w: 1200, h: 200 },
        props: {
          frameworks: [
            { name: 'ITIL', score: 92, status: 'compliant', trend: 2.5 },
            { name: 'ISO20000', score: 88, status: 'compliant', trend: 1.8 },
            { name: 'COBIT', score: 85, status: 'warning', trend: -1.2 },
            { name: 'SOX', score: 78, status: 'non-compliant', trend: -3.5 }
          ]
        }
      },
      {
        id: 'audit-timeline',
        type: 'audit-gantt',
        position: { x: 100, y: 340, w: 700, h: 350 },
        props: {
          title: '审计计划时间线',
          showProgress: true,
          showMilestones: true
        }
      },
      {
        id: 'risk-assessment',
        type: 'risk-bubble-chart',
        position: { x: 850, y: 340, w: 450, h: 350 },
        props: {
          title: '风险评估矩阵',
          xAxis: '影响程度',
          yAxis: '发生概率',
          bubbleSize: '风险值'
        }
      },
      {
        id: 'findings-summary',
        type: 'findings-chart',
        position: { x: 100, y: 730, w: 500, h: 250 },
        props: {
          title: '审计发现汇总',
          categories: ['高风险', '中风险', '低风险', '建议'],
          colors: ['#f56c6c', '#e6a23c', '#409eff', '#67c23a']
        }
      },
      {
        id: 'remediation-progress',
        type: 'remediation-tracker',
        position: { x: 650, y: 730, w: 650, h: 250 },
        props: {
          title: '整改进度跟踪',
          showDeadlines: true,
          showResponsible: true
        }
      }
    ]
  }
]

// 获取所有模板
export const getAllTemplates = () => {
  return itsmTemplates
}

// 根据ID获取模板
export const getTemplateById = (id) => {
  return itsmTemplates.find(template => template.id === id)
}

// 根据分类获取模板
export const getTemplatesByCategory = (category) => {
  return itsmTemplates.filter(template => template.category === category)
}

// 搜索模板
export const searchTemplates = (keyword) => {
  const lowerKeyword = keyword.toLowerCase()
  return itsmTemplates.filter(template => 
    template.name.toLowerCase().includes(lowerKeyword) ||
    template.description.toLowerCase().includes(lowerKeyword) ||
    template.tags.some(tag => tag.toLowerCase().includes(lowerKeyword))
  )
}
