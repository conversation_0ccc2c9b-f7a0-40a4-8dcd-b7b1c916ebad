// 数据源管理器 - 支持多种数据源类型
import axios from 'axios'

export class DataSourceManager {
  constructor() {
    this.dataSources = new Map()
    this.websockets = new Map()
    this.intervals = new Map()
  }

  // 注册数据源
  registerDataSource(componentId, config) {
    this.dataSources.set(componentId, config)
    
    // 根据数据源类型初始化
    switch (config.source) {
      case 'api':
        this.setupApiDataSource(componentId, config)
        break
      case 'websocket':
        this.setupWebSocketDataSource(componentId, config)
        break
      case 'mock':
        this.setupMockDataSource(componentId, config)
        break
      case 'static':
        this.setupStaticDataSource(componentId, config)
        break
    }
  }

  // 设置API数据源
  setupApiDataSource(componentId, config) {
    const fetchData = async () => {
      try {
        const response = await axios({
          method: config.method || 'GET',
          url: config.url,
          headers: config.headers || {},
          params: config.params || {},
          data: config.data || {},
          timeout: config.timeout || 10000
        })
        
        const processedData = this.processData(response.data, config.mapping)
        this.notifyDataUpdate(componentId, processedData)
      } catch (error) {
        console.error(`API数据源获取失败 (${componentId}):`, error)
        this.notifyDataError(componentId, error)
      }
    }

    // 立即获取一次数据
    fetchData()

    // 设置定时刷新
    if (config.interval && config.interval > 0) {
      const intervalId = setInterval(fetchData, config.interval)
      this.intervals.set(componentId, intervalId)
    }
  }

  // 设置WebSocket数据源
  setupWebSocketDataSource(componentId, config) {
    try {
      const ws = new WebSocket(config.url)
      
      ws.onopen = () => {
        console.log(`WebSocket连接已建立 (${componentId})`)
        
        // 发送初始化消息
        if (config.initMessage) {
          ws.send(JSON.stringify(config.initMessage))
        }
      }

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          const processedData = this.processData(data, config.mapping)
          this.notifyDataUpdate(componentId, processedData)
        } catch (error) {
          console.error(`WebSocket数据处理失败 (${componentId}):`, error)
        }
      }

      ws.onerror = (error) => {
        console.error(`WebSocket错误 (${componentId}):`, error)
        this.notifyDataError(componentId, error)
      }

      ws.onclose = () => {
        console.log(`WebSocket连接已关闭 (${componentId})`)
        
        // 自动重连
        if (config.autoReconnect !== false) {
          setTimeout(() => {
            this.setupWebSocketDataSource(componentId, config)
          }, config.reconnectInterval || 5000)
        }
      }

      this.websockets.set(componentId, ws)
    } catch (error) {
      console.error(`WebSocket连接失败 (${componentId}):`, error)
      this.notifyDataError(componentId, error)
    }
  }

  // 设置Mock数据源
  setupMockDataSource(componentId, config) {
    const generateMockData = () => {
      let mockData
      
      switch (config.mockType) {
        case 'timeSeries':
          mockData = this.generateTimeSeriesData(config.mockOptions)
          break
        case 'category':
          mockData = this.generateCategoryData(config.mockOptions)
          break
        case 'number':
          mockData = this.generateNumberData(config.mockOptions)
          break
        case 'table':
          mockData = this.generateTableData(config.mockOptions)
          break
        default:
          mockData = this.generateRandomData(config.mockOptions)
      }
      
      const processedData = this.processData(mockData, config.mapping)
      this.notifyDataUpdate(componentId, processedData)
    }

    // 立即生成一次数据
    generateMockData()

    // 设置定时更新
    if (config.interval && config.interval > 0) {
      const intervalId = setInterval(generateMockData, config.interval)
      this.intervals.set(componentId, intervalId)
    }
  }

  // 设置静态数据源
  setupStaticDataSource(componentId, config) {
    const processedData = this.processData(config.data, config.mapping)
    this.notifyDataUpdate(componentId, processedData)
  }

  // 数据处理和映射
  processData(rawData, mapping) {
    if (!mapping) return rawData

    try {
      // 支持表达式映射
      if (mapping.expression) {
        return this.evaluateExpression(rawData, mapping.expression)
      }

      // 字段映射
      const processedData = {}
      
      Object.keys(mapping).forEach(key => {
        const fieldPath = mapping[key]
        processedData[key] = this.getNestedValue(rawData, fieldPath)
      })

      return processedData
    } catch (error) {
      console.error('数据处理失败:', error)
      return rawData
    }
  }

  // 获取嵌套对象的值
  getNestedValue(obj, path) {
    if (typeof path !== 'string') return path
    
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  }

  // 执行表达式
  evaluateExpression(data, expression) {
    try {
      // 简单的表达式求值，实际项目中可以使用更安全的方式
      const func = new Function('data', `return ${expression}`)
      return func(data)
    } catch (error) {
      console.error('表达式执行失败:', error)
      return data
    }
  }

  // 生成时间序列数据
  generateTimeSeriesData(options = {}) {
    const { 
      count = 24, 
      startTime = Date.now() - 24 * 60 * 60 * 1000,
      interval = 60 * 60 * 1000,
      baseValue = 100,
      variance = 20
    } = options

    const data = []
    for (let i = 0; i < count; i++) {
      data.push({
        time: new Date(startTime + i * interval).toISOString(),
        value: baseValue + (Math.random() - 0.5) * variance * 2
      })
    }
    return data
  }

  // 生成分类数据
  generateCategoryData(options = {}) {
    const { 
      categories = ['A', 'B', 'C', 'D', 'E'],
      minValue = 10,
      maxValue = 100
    } = options

    return categories.map(category => ({
      name: category,
      value: Math.floor(Math.random() * (maxValue - minValue + 1)) + minValue
    }))
  }

  // 生成数字数据
  generateNumberData(options = {}) {
    const { 
      min = 0,
      max = 100,
      decimals = 0
    } = options

    const value = Math.random() * (max - min) + min
    return decimals > 0 ? Number(value.toFixed(decimals)) : Math.floor(value)
  }

  // 生成表格数据
  generateTableData(options = {}) {
    const { 
      rows = 10,
      columns = ['name', 'value', 'status'],
      namePrefix = 'Item'
    } = options

    const data = []
    for (let i = 0; i < rows; i++) {
      const row = {}
      columns.forEach(col => {
        switch (col) {
          case 'name':
            row[col] = `${namePrefix} ${i + 1}`
            break
          case 'value':
            row[col] = Math.floor(Math.random() * 1000)
            break
          case 'status':
            row[col] = ['正常', '警告', '异常'][Math.floor(Math.random() * 3)]
            break
          default:
            row[col] = `Data ${i + 1}`
        }
      })
      data.push(row)
    }
    return data
  }

  // 生成随机数据
  generateRandomData(options = {}) {
    const { type = 'object' } = options
    
    switch (type) {
      case 'array':
        return Array.from({ length: 10 }, (_, i) => ({
          id: i + 1,
          value: Math.random() * 100
        }))
      case 'number':
        return Math.random() * 100
      case 'string':
        return `Random String ${Math.random().toString(36).substr(2, 9)}`
      default:
        return {
          value: Math.random() * 100,
          timestamp: Date.now(),
          status: 'active'
        }
    }
  }

  // 通知数据更新
  notifyDataUpdate(componentId, data) {
    const event = new CustomEvent('dataUpdate', {
      detail: { componentId, data }
    })
    window.dispatchEvent(event)
  }

  // 通知数据错误
  notifyDataError(componentId, error) {
    const event = new CustomEvent('dataError', {
      detail: { componentId, error }
    })
    window.dispatchEvent(event)
  }

  // 移除数据源
  removeDataSource(componentId) {
    // 清除定时器
    if (this.intervals.has(componentId)) {
      clearInterval(this.intervals.get(componentId))
      this.intervals.delete(componentId)
    }

    // 关闭WebSocket连接
    if (this.websockets.has(componentId)) {
      const ws = this.websockets.get(componentId)
      ws.close()
      this.websockets.delete(componentId)
    }

    // 移除数据源配置
    this.dataSources.delete(componentId)
  }

  // 更新数据源配置
  updateDataSource(componentId, newConfig) {
    this.removeDataSource(componentId)
    this.registerDataSource(componentId, newConfig)
  }

  // 获取数据源状态
  getDataSourceStatus(componentId) {
    const config = this.dataSources.get(componentId)
    if (!config) return 'not_configured'

    switch (config.source) {
      case 'websocket':
        const ws = this.websockets.get(componentId)
        return ws ? (ws.readyState === WebSocket.OPEN ? 'connected' : 'connecting') : 'disconnected'
      case 'api':
        return this.intervals.has(componentId) ? 'active' : 'inactive'
      default:
        return 'active'
    }
  }

  // 清理所有数据源
  cleanup() {
    // 清除所有定时器
    this.intervals.forEach(intervalId => clearInterval(intervalId))
    this.intervals.clear()

    // 关闭所有WebSocket连接
    this.websockets.forEach(ws => ws.close())
    this.websockets.clear()

    // 清除数据源配置
    this.dataSources.clear()
  }
}

// 创建全局数据源管理器实例
export const dataSourceManager = new DataSourceManager()

// 在页面卸载时清理资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    dataSourceManager.cleanup()
  })
}
