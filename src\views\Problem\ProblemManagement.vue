<template>
  <div class="problem-management">
    <div class="page-header">
      <div class="header-info">
        <h2>问题管理</h2>
        <p>识别根本原因，防止事件重复发生</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateProblem = true">
          <el-icon><Plus /></el-icon>
          创建问题
        </el-button>
        <el-button @click="showIncidentAnalysis = true">
          <el-icon><TrendCharts /></el-icon>
          事件分析
        </el-button>
        <el-button @click="$router.push('/problem/rca')">
          <el-icon><Search /></el-icon>
          根因分析
        </el-button>
        <el-button @click="$router.push('/problem/kedb')">
          <el-icon><Document /></el-icon>
          已知错误库
        </el-button>
      </div>
    </div>

    <!-- AI智能提示 -->
    <div class="ai-insights" v-if="aiInsights.length > 0">
      <el-alert
        title="AI智能提示"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="insights-content">
            <div v-for="insight in aiInsights" :key="insight.id" class="insight-item">
              <div class="insight-text">{{ insight.message }}</div>
              <el-button type="text" size="small" @click="handleInsight(insight)">
                {{ insight.action }}
              </el-button>
            </div>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 问题统计 -->
    <div class="problem-stats">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in problemStats" :key="stat.key">
          <el-card class="stat-card" :class="stat.status" @click="filterByStatus(stat.key)">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24" :color="stat.color">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trend">
                  <el-icon><component :is="stat.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ stat.change }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 问题趋势图 -->
    <div class="problem-trends">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="16">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>问题趋势分析</span>
                <el-radio-group v-model="trendPeriod" size="small">
                  <el-radio-button label="7d">7天</el-radio-button>
                  <el-radio-button label="30d">30天</el-radio-button>
                  <el-radio-button label="90d">90天</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="trend-chart" ref="trendChart"></div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="8">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>问题分类分布</span>
                <el-button type="text" size="small" @click="viewCategoryDetails">详情</el-button>
              </div>
            </template>
            <div class="category-chart" ref="categoryChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 高频事件分析 -->
    <div class="incident-analysis">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>高频事件分析</span>
            <el-button type="primary" size="small" @click="runIncidentAnalysis">
              <el-icon><MagicStick /></el-icon>
              AI分析
            </el-button>
          </div>
        </template>
        <div class="analysis-content">
          <el-table :data="incidentAnalysis" style="width: 100%">
            <el-table-column prop="pattern" label="事件模式" />
            <el-table-column prop="frequency" label="频次" width="80" />
            <el-table-column prop="impact" label="影响范围" width="120" />
            <el-table-column prop="suggestion" label="建议" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" size="small" text @click="createProblemFromPattern(scope.row)">
                  创建问题
                </el-button>
                <el-button type="primary" size="small" text @click="viewPatternDetails(scope.row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 问题列表 -->
    <div class="problem-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <span>问题列表 ({{ filteredProblems.length }})</span>
              <el-tag v-if="currentFilter !== 'all'" type="primary" size="small" closable @close="clearFilter">
                {{ getFilterText(currentFilter) }}
              </el-tag>
            </div>
            <div class="header-right">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索问题..."
                prefix-icon="Search"
                size="small"
                style="width: 200px; margin-right: 12px;"
                @input="searchProblems"
              />
              <el-button type="text" size="small" @click="$router.push('/problem/list')">查看全部</el-button>
            </div>
          </div>
        </template>
        <el-table :data="paginatedProblems" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="问题ID" width="120" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.category }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)" size="small">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="处理人" width="120" />
          <el-table-column prop="relatedIncidents" label="关联事件" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewProblem(scope.row)">
                查看
              </el-button>
              <el-button type="primary" size="small" text @click="editProblem(scope.row)">
                编辑
              </el-button>
              <el-button type="warning" size="small" text @click="startRCA(scope.row)">
                根因分析
              </el-button>
              <el-dropdown @command="handleProblemCommand">
                <el-button type="primary" size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'close', problem: scope.row}">关闭问题</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'duplicate', problem: scope.row}">复制问题</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'export', problem: scope.row}">导出报告</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredProblems.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 创建问题对话框 -->
    <el-dialog v-model="showCreateProblem" title="创建问题" width="70%" :before-close="handleCreateDialogClose">
      <el-form :model="problemForm" :rules="problemRules" ref="problemFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="问题标题" prop="title">
              <el-input v-model="problemForm.title" placeholder="请输入问题标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="问题分类" prop="category">
              <el-select v-model="problemForm.category" placeholder="请选择分类" style="width: 100%">
                <el-option label="硬件故障" value="hardware" />
                <el-option label="软件缺陷" value="software" />
                <el-option label="网络问题" value="network" />
                <el-option label="性能问题" value="performance" />
                <el-option label="安全问题" value="security" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="problemForm.priority" placeholder="请选择优先级">
                <el-option label="低" value="低" />
                <el-option label="中" value="中" />
                <el-option label="高" value="高" />
                <el-option label="紧急" value="紧急" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理人" prop="assignee">
              <el-select v-model="problemForm.assignee" placeholder="请选择处理人">
                <el-option label="张工" value="张工" />
                <el-option label="李工" value="李工" />
                <el-option label="王工" value="王工" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="问题描述" prop="description">
          <el-input
            v-model="problemForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述问题现象、影响范围等"
          />
        </el-form-item>

        <el-form-item label="关联事件">
          <el-select
            v-model="problemForm.relatedIncidents"
            multiple
            placeholder="选择相关事件"
            style="width: 100%"
          >
            <el-option
              v-for="incident in availableIncidents"
              :key="incident.id"
              :label="`${incident.id} - ${incident.title}`"
              :value="incident.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="影响的CI">
          <el-select
            v-model="problemForm.affectedCIs"
            multiple
            placeholder="选择受影响的配置项"
            style="width: 100%"
          >
            <el-option
              v-for="ci in availableCIs"
              :key="ci.id"
              :label="ci.name"
              :value="ci.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="预期解决时间">
          <el-date-picker
            v-model="problemForm.expectedResolution"
            type="datetime"
            placeholder="选择预期解决时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateProblem = false">取消</el-button>
        <el-button @click="saveDraft">保存草稿</el-button>
        <el-button type="primary" @click="createProblem" :loading="creating">创建问题</el-button>
      </template>
    </el-dialog>

    <!-- 事件分析对话框 -->
    <el-dialog v-model="showIncidentAnalysis" title="事件关联分析" width="80%">
      <div class="incident-analysis-content">
        <div class="analysis-controls">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-date-picker
                v-model="analysisDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="8">
              <el-select v-model="analysisCategory" placeholder="选择分析类别" style="width: 100%">
                <el-option label="全部类别" value="all" />
                <el-option label="硬件故障" value="hardware" />
                <el-option label="软件缺陷" value="software" />
                <el-option label="网络问题" value="network" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-button type="primary" @click="performAnalysis" :loading="analyzing">
                <el-icon><MagicStick /></el-icon>
                开始分析
              </el-button>
            </el-col>
          </el-row>
        </div>

        <div v-if="analysisResults.length > 0" class="analysis-results">
          <h3>分析结果</h3>
          <el-table :data="analysisResults" style="width: 100%">
            <el-table-column prop="cluster" label="事件聚类" width="120" />
            <el-table-column prop="pattern" label="模式描述" />
            <el-table-column prop="incidents" label="事件数量" width="100" />
            <el-table-column prop="frequency" label="发生频率" width="120" />
            <el-table-column prop="impact" label="业务影响" width="120" />
            <el-table-column prop="confidence" label="置信度" width="100">
              <template #default="scope">
                <el-progress :percentage="scope.row.confidence" :show-text="false" />
                <span style="margin-left: 8px;">{{ scope.row.confidence }}%</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" size="small" @click="createProblemFromCluster(scope.row)">
                  创建问题
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-else-if="!analyzing" class="no-analysis">
          <el-empty description="请点击开始分析按钮进行事件关联分析" />
        </div>
      </div>

      <template #footer>
        <el-button @click="showIncidentAnalysis = false">关闭</el-button>
        <el-button type="primary" @click="exportAnalysisReport">导出分析报告</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const analyzing = ref(false)
const showCreateProblem = ref(false)
const showIncidentAnalysis = ref(false)
const searchKeyword = ref('')
const currentFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const trendPeriod = ref('7d')

// 问题统计数据
const problemStats = ref([
  {
    key: 'total',
    label: '总问题数',
    value: '45',
    icon: 'QuestionFilled',
    color: '#1976D2',
    status: 'info',
    trend: 'up',
    change: '+3'
  },
  {
    key: 'open',
    label: '待解决',
    value: '12',
    icon: 'Clock',
    color: '#FF9800',
    status: 'warning',
    trend: 'down',
    change: '-2'
  },
  {
    key: 'analyzing',
    label: '分析中',
    value: '8',
    icon: 'Search',
    color: '#2196F3',
    status: 'info',
    trend: 'up',
    change: '+1'
  },
  {
    key: 'resolved',
    label: '已解决',
    value: '25',
    icon: 'CircleCheck',
    color: '#4CAF50',
    status: 'success',
    trend: 'up',
    change: '+4'
  }
])

// AI智能提示
const aiInsights = ref([
  {
    id: 1,
    message: '检测到"打印机故障"类事件在过去7天内发生15次，建议创建问题记录',
    action: '创建问题',
    type: 'suggestion',
    data: { pattern: 'printer_failure', incidents: 15 }
  },
  {
    id: 2,
    message: '服务器CPU使用率异常事件聚类分析完成，发现3个潜在问题',
    action: '查看分析',
    type: 'analysis',
    data: { clusters: 3, category: 'performance' }
  }
])

// 问题列表数据
const problems = ref([
  {
    id: 'PRB-2025-001',
    title: '服务器频繁重启问题',
    category: '硬件故障',
    status: '分析中',
    priority: '高',
    assignee: '张工',
    relatedIncidents: 5,
    createTime: '2025-01-30 14:30',
    description: '数据中心服务器出现频繁重启现象，影响业务连续性'
  },
  {
    id: 'PRB-2025-002',
    title: '网络连接不稳定',
    category: '网络问题',
    status: '待解决',
    priority: '中',
    assignee: '李工',
    relatedIncidents: 8,
    createTime: '2025-01-29 16:20',
    description: '办公区域网络连接间歇性中断，影响员工工作效率'
  },
  {
    id: 'PRB-2025-003',
    title: '邮件系统性能缓慢',
    category: '性能问题',
    status: '已知错误',
    priority: '中',
    assignee: '王工',
    relatedIncidents: 12,
    createTime: '2025-01-28 09:15',
    description: '邮件系统响应时间过长，用户体验差'
  },
  {
    id: 'PRB-2025-004',
    title: '数据库连接池耗尽',
    category: '软件缺陷',
    status: '解决方案实施',
    priority: '高',
    assignee: '张工',
    relatedIncidents: 3,
    createTime: '2025-01-27 14:45',
    description: '应用系统数据库连接池配置不当导致连接耗尽'
  }
])

// 高频事件分析数据
const incidentAnalysis = ref([
  {
    pattern: '打印机驱动故障',
    frequency: 15,
    impact: '办公区域',
    suggestion: '统一更新打印机驱动程序',
    confidence: 92
  },
  {
    pattern: 'VPN连接失败',
    frequency: 8,
    impact: '远程办公',
    suggestion: '检查VPN服务器配置',
    confidence: 85
  },
  {
    pattern: '文件服务器访问缓慢',
    frequency: 6,
    impact: '全公司',
    suggestion: '优化文件服务器性能',
    confidence: 78
  }
])

// 创建问题表单
const problemForm = reactive({
  title: '',
  category: '',
  priority: '',
  assignee: '',
  description: '',
  relatedIncidents: [],
  affectedCIs: [],
  expectedResolution: null
})

// 表单验证规则
const problemRules = {
  title: [{ required: true, message: '请输入问题标题', trigger: 'blur' }],
  category: [{ required: true, message: '请选择问题分类', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  assignee: [{ required: true, message: '请选择处理人', trigger: 'change' }],
  description: [{ required: true, message: '请输入问题描述', trigger: 'blur' }]
}

// 可用的事件和CI数据
const availableIncidents = ref([
  { id: 'INC-2025-001', title: '服务器宕机' },
  { id: 'INC-2025-002', title: '网络中断' },
  { id: 'INC-2025-003', title: '应用无响应' }
])

const availableCIs = ref([
  { id: 'CI-001', name: '数据库服务器-DB01' },
  { id: 'CI-002', name: '应用服务器-APP01' },
  { id: 'CI-003', name: '网络交换机-SW01' }
])

// 事件分析相关数据
const analysisDateRange = ref([])
const analysisCategory = ref('all')
const analysisResults = ref([])

// 图表引用
const trendChart = ref()
const categoryChart = ref()

// 计算属性
const filteredProblems = computed(() => {
  let filtered = problems.value

  // 按状态过滤
  if (currentFilter.value !== 'all') {
    const statusMap = {
      'total': null,
      'open': '待解决',
      'analyzing': '分析中',
      'resolved': '已解决'
    }
    const targetStatus = statusMap[currentFilter.value]
    if (targetStatus) {
      filtered = filtered.filter(p => p.status === targetStatus)
    }
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(p =>
      p.id.toLowerCase().includes(keyword) ||
      p.title.toLowerCase().includes(keyword) ||
      p.description.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

const paginatedProblems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredProblems.value.slice(start, end)
})

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '待解决': 'info',
    '分析中': 'warning',
    '已知错误': 'warning',
    '解决方案实施': 'primary',
    '已解决': 'success',
    '已关闭': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const typeMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取过滤器文本
const getFilterText = (filter) => {
  const textMap = {
    'total': '全部',
    'open': '待解决',
    'analyzing': '分析中',
    'resolved': '已解决'
  }
  return textMap[filter] || filter
}

// 处理AI智能提示
const handleInsight = (insight) => {
  if (insight.type === 'suggestion') {
    // 基于建议创建问题
    problemForm.title = `${insight.data.pattern}问题分析`
    problemForm.description = `基于AI分析，发现${insight.data.pattern}在过去7天内发生${insight.data.incidents}次，建议进行根本原因分析`
    showCreateProblem.value = true
  } else if (insight.type === 'analysis') {
    // 查看分析结果
    showIncidentAnalysis.value = true
  }

  // 移除已处理的提示
  const index = aiInsights.value.findIndex(i => i.id === insight.id)
  if (index > -1) {
    aiInsights.value.splice(index, 1)
  }
}

// 按状态过滤
const filterByStatus = (status) => {
  currentFilter.value = status
  currentPage.value = 1
}

// 清除过滤器
const clearFilter = () => {
  currentFilter.value = 'all'
  currentPage.value = 1
}

// 搜索问题
const searchProblems = () => {
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 查看分类详情
const viewCategoryDetails = () => {
  router.push('/problem/category-analysis')
}

// 运行事件分析
const runIncidentAnalysis = () => {
  showIncidentAnalysis.value = true
}

// 从模式创建问题
const createProblemFromPattern = (pattern) => {
  problemForm.title = `${pattern.pattern}根本原因分析`
  problemForm.description = `基于事件模式分析，${pattern.pattern}发生频率为${pattern.frequency}次，建议进行深入的根本原因分析`
  problemForm.priority = pattern.frequency > 10 ? '高' : '中'
  showCreateProblem.value = true
}

// 查看模式详情
const viewPatternDetails = (pattern) => {
  ElMessage.info(`查看模式详情: ${pattern.pattern}`)
}

// 问题操作函数
const viewProblem = (problem) => {
  router.push(`/problem/detail/${problem.id}`)
}

const editProblem = (problem) => {
  router.push(`/problem/edit/${problem.id}`)
}

const startRCA = (problem) => {
  router.push(`/problem/root-cause-analysis?problemId=${problem.id}`)
}

const handleProblemCommand = (command) => {
  const { action, problem } = command

  switch (action) {
    case 'close':
      closeProblem(problem)
      break
    case 'duplicate':
      duplicateProblem(problem)
      break
    case 'export':
      exportProblemReport(problem)
      break
  }
}

const closeProblem = (problem) => {
  ElMessageBox.confirm(
    `确定要关闭问题 "${problem.title}" 吗？`,
    '确认关闭',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 更新问题状态
    const index = problems.value.findIndex(p => p.id === problem.id)
    if (index > -1) {
      problems.value[index].status = '已关闭'
    }
    ElMessage.success('问题已关闭')
  })
}

const duplicateProblem = (problem) => {
  // 复制问题信息到表单
  problemForm.title = `${problem.title} (副本)`
  problemForm.category = problem.category
  problemForm.priority = problem.priority
  problemForm.description = problem.description
  showCreateProblem.value = true
}

const exportProblemReport = (problem) => {
  ElMessage.success(`正在导出问题 ${problem.id} 的报告...`)
}

// 创建问题相关函数
const problemFormRef = ref()

const handleCreateDialogClose = (done) => {
  if (problemForm.title || problemForm.description) {
    ElMessageBox.confirm('表单内容将会丢失，确定要关闭吗？')
      .then(() => {
        resetProblemForm()
        done()
      })
      .catch(() => {})
  } else {
    done()
  }
}

const resetProblemForm = () => {
  Object.keys(problemForm).forEach(key => {
    if (Array.isArray(problemForm[key])) {
      problemForm[key] = []
    } else {
      problemForm[key] = ''
    }
  })
  problemForm.expectedResolution = null
}

const saveDraft = () => {
  ElMessage.success('草稿已保存')
}

const createProblem = () => {
  problemFormRef.value.validate((valid) => {
    if (valid) {
      creating.value = true

      // 模拟创建问题
      setTimeout(() => {
        const newProblem = {
          id: `PRB-2025-${String(problems.value.length + 1).padStart(3, '0')}`,
          title: problemForm.title,
          category: problemForm.category,
          status: '待解决',
          priority: problemForm.priority,
          assignee: problemForm.assignee,
          relatedIncidents: problemForm.relatedIncidents.length,
          createTime: new Date().toLocaleString(),
          description: problemForm.description
        }

        problems.value.unshift(newProblem)

        // 更新统计数据
        const totalStat = problemStats.value.find(s => s.key === 'total')
        const openStat = problemStats.value.find(s => s.key === 'open')
        if (totalStat) totalStat.value = String(parseInt(totalStat.value) + 1)
        if (openStat) openStat.value = String(parseInt(openStat.value) + 1)

        creating.value = false
        showCreateProblem.value = false
        resetProblemForm()

        ElMessage.success('问题创建成功')
      }, 2000)
    }
  })
}

// 事件分析相关函数
const performAnalysis = () => {
  analyzing.value = true

  // 模拟AI分析过程
  setTimeout(() => {
    analysisResults.value = [
      {
        cluster: 'C001',
        pattern: '打印机驱动程序冲突',
        incidents: 15,
        frequency: '每天2-3次',
        impact: '办公效率下降20%',
        confidence: 92
      },
      {
        cluster: 'C002',
        pattern: 'VPN连接超时',
        incidents: 8,
        frequency: '每天1-2次',
        impact: '远程办公受阻',
        confidence: 85
      },
      {
        cluster: 'C003',
        pattern: '邮件服务器响应缓慢',
        incidents: 12,
        frequency: '高峰期频发',
        impact: '邮件延迟发送',
        confidence: 78
      }
    ]

    analyzing.value = false
    ElMessage.success('事件分析完成')
  }, 3000)
}

const createProblemFromCluster = (cluster) => {
  problemForm.title = `${cluster.pattern}问题分析`
  problemForm.description = `基于事件聚类分析，发现${cluster.pattern}模式，涉及${cluster.incidents}个事件，置信度${cluster.confidence}%`
  problemForm.priority = cluster.confidence > 90 ? '高' : '中'
  showIncidentAnalysis.value = false
  showCreateProblem.value = true
}

const exportAnalysisReport = () => {
  ElMessage.success('正在导出分析报告...')
}

// 初始化图表
const initCharts = () => {
  // 问题趋势图
  if (trendChart.value) {
    const trendChartInstance = echarts.init(trendChart.value)
    trendChartInstance.setOption({
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新建问题', '已解决问题', '累计问题']
      },
      xAxis: {
        type: 'category',
        data: ['1/24', '1/25', '1/26', '1/27', '1/28', '1/29', '1/30']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新建问题',
          type: 'line',
          data: [2, 3, 1, 4, 2, 3, 2],
          itemStyle: { color: '#FF9800' }
        },
        {
          name: '已解决问题',
          type: 'line',
          data: [1, 2, 2, 3, 1, 2, 3],
          itemStyle: { color: '#4CAF50' }
        },
        {
          name: '累计问题',
          type: 'bar',
          data: [45, 46, 45, 46, 47, 48, 47],
          itemStyle: { color: '#1976D2' }
        }
      ]
    })
  }

  // 问题分类分布图
  if (categoryChart.value) {
    const categoryChartInstance = echarts.init(categoryChart.value)
    categoryChartInstance.setOption({
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '问题分类',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 15, name: '硬件故障' },
            { value: 12, name: '软件缺陷' },
            { value: 8, name: '网络问题' },
            { value: 6, name: '性能问题' },
            { value: 4, name: '安全问题' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })

  // 模拟实时数据更新
  setInterval(() => {
    // 随机更新统计数据
    problemStats.value.forEach(stat => {
      if (stat.key !== 'total') {
        const change = Math.floor(Math.random() * 3) - 1
        const currentValue = parseInt(stat.value) || 0
        const newValue = Math.max(0, currentValue + change)

        stat.value = newValue.toString()
        stat.change = change > 0 ? `+${change}` : change.toString()
        stat.trend = change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
      }
    })

    // 更新总数
    const totalProblems = problemStats.value
      .filter(s => s.key !== 'total')
      .reduce((sum, s) => sum + parseInt(s.value), 0)

    const totalStat = problemStats.value.find(s => s.key === 'total')
    if (totalStat) {
      totalStat.value = totalProblems.toString()
    }
  }, 30000) // 每30秒更新一次
})
</script>

<style scoped>
.problem-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-info h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-info p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* AI智能提示样式 */
.ai-insights {
  margin-bottom: 20px;
}

.insights-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.insight-text {
  flex: 1;
  margin-right: 16px;
}

.problem-stats,
.problem-trends,
.incident-analysis,
.problem-list {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.info {
  border-left: 4px solid #1976D2;
}

.stat-card.warning {
  border-left: 4px solid #FF9800;
}

.stat-card.success {
  border-left: 4px solid #4CAF50;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-trend.up {
  color: #4CAF50;
}

.stat-trend.down {
  color: #F44336;
}

.stat-trend.stable {
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 图表样式 */
.trend-chart,
.category-chart {
  height: 300px;
  margin-top: 20px;
}

/* 分析内容样式 */
.analysis-content {
  margin-top: 20px;
}

.incident-analysis-content {
  padding: 20px 0;
}

.analysis-controls {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-results {
  margin-top: 20px;
}

.analysis-results h3 {
  color: #333;
  margin-bottom: 16px;
}

.no-analysis {
  text-align: center;
  padding: 40px 0;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .stat-card {
    height: 120px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .analysis-controls .el-row {
    flex-direction: column;
  }

  .analysis-controls .el-col {
    margin-bottom: 12px;
  }
}
</style>
