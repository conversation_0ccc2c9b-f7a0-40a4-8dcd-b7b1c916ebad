<template>
  <div class="change-calendar">
    <div class="page-header">
      <div class="header-info">
        <h2>变更日历</h2>
        <p>可视化变更计划和时间安排，避免冲突</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createChange">
          <el-icon><Plus /></el-icon>
          创建变更
        </el-button>
        <el-button @click="detectConflicts">
          <el-icon><Warning /></el-icon>
          冲突检测
        </el-button>
        <el-button @click="exportCalendar">
          <el-icon><Download /></el-icon>
          导出日历
        </el-button>
      </div>
    </div>

    <!-- 日历控制栏 -->
    <div class="calendar-controls">
      <el-card>
        <div class="controls-content">
          <div class="view-controls">
            <el-radio-group v-model="currentView" @change="changeView">
              <el-radio-button label="month">月视图</el-radio-button>
              <el-radio-button label="week">周视图</el-radio-button>
              <el-radio-button label="day">日视图</el-radio-button>
            </el-radio-group>
          </div>

          <div class="date-navigation">
            <el-button @click="previousPeriod" :icon="ArrowLeft" circle />
            <el-date-picker
              v-model="currentDate"
              type="date"
              placeholder="选择日期"
              @change="dateChanged"
              style="margin: 0 12px;"
            />
            <el-button @click="nextPeriod" :icon="ArrowRight" circle />
            <el-button @click="goToday" type="primary" size="small" style="margin-left: 12px;">今天</el-button>
          </div>

          <div class="filter-controls">
            <el-select v-model="typeFilter" placeholder="变更类型" size="small" style="width: 120px; margin-right: 8px;">
              <el-option label="全部类型" value="all" />
              <el-option label="标准变更" value="标准变更" />
              <el-option label="紧急变更" value="紧急变更" />
              <el-option label="正常变更" value="正常变更" />
            </el-select>
            <el-select v-model="riskFilter" placeholder="风险等级" size="small" style="width: 120px;">
              <el-option label="全部风险" value="all" />
              <el-option label="低风险" value="低" />
              <el-option label="中风险" value="中" />
              <el-option label="高风险" value="高" />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 变更统计概览 -->
    <div class="calendar-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="本月变更" :value="monthlyStats.total" />
            <div class="stat-extra">
              <el-tag type="success" size="small">+{{ monthlyStats.increase }}</el-tag>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="冲突检测" :value="conflictStats.conflicts" />
            <div class="stat-extra">
              <el-tag :type="conflictStats.conflicts > 0 ? 'danger' : 'success'" size="small">
                {{ conflictStats.conflicts > 0 ? '有冲突' : '无冲突' }}
              </el-tag>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="窗口利用率" :value="windowStats.utilization" suffix="%" />
            <div class="stat-extra">
              <el-progress :percentage="windowStats.utilization" :show-text="false" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="成功率" :value="successStats.rate" suffix="%" />
            <div class="stat-extra">
              <el-tag type="success" size="small">{{ successStats.total }}个变更</el-tag>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 日历主体 -->
    <div class="calendar-main">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>{{ getCalendarTitle() }}</span>
            <div class="legend">
              <el-tag color="#4CAF50" size="small">标准变更</el-tag>
              <el-tag color="#FF9800" size="small">紧急变更</el-tag>
              <el-tag color="#2196F3" size="small">正常变更</el-tag>
              <el-tag color="#F44336" size="small">冲突</el-tag>
            </div>
          </div>
        </template>

        <!-- 月视图 -->
        <div v-if="currentView === 'month'" class="month-view">
          <el-calendar v-model="currentDate" @pick="handleDatePick">
            <template #date-cell="{ data }">
              <div class="calendar-cell">
                <div class="cell-date">{{ data.day.split('-').slice(-1)[0] }}</div>
                <div class="cell-changes">
                  <div
                    v-for="change in getChangesForDate(data.day)"
                    :key="change.id"
                    class="change-item"
                    :class="getChangeClass(change)"
                    @click="viewChangeDetails(change)"
                  >
                    <div class="change-title">{{ change.title }}</div>
                    <div class="change-time">{{ formatTime(change.implementDate) }}</div>
                  </div>
                </div>
              </div>
            </template>
          </el-calendar>
        </div>

        <!-- 周视图 -->
        <div v-if="currentView === 'week'" class="week-view">
          <div class="week-header">
            <div v-for="day in weekDays" :key="day.date" class="week-day-header">
              <div class="day-name">{{ day.name }}</div>
              <div class="day-date">{{ day.date }}</div>
            </div>
          </div>
          <div class="week-content">
            <div class="time-column">
              <div v-for="hour in 24" :key="hour" class="time-slot">
                {{ String(hour - 1).padStart(2, '0') }}:00
              </div>
            </div>
            <div v-for="day in weekDays" :key="day.date" class="week-day-column">
              <div v-for="hour in 24" :key="hour" class="hour-slot">
                <div
                  v-for="change in getChangesForDateTime(day.date, hour - 1)"
                  :key="change.id"
                  class="week-change-item"
                  :class="getChangeClass(change)"
                  @click="viewChangeDetails(change)"
                >
                  {{ change.title }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 日视图 -->
        <div v-if="currentView === 'day'" class="day-view">
          <div class="day-header">
            <h3>{{ formatDate(currentDate) }}</h3>
            <div class="day-stats">
              <el-tag>{{ getDayChanges().length }} 个变更</el-tag>
            </div>
          </div>
          <div class="day-timeline">
            <div class="timeline-hours">
              <div v-for="hour in 24" :key="hour" class="hour-block">
                <div class="hour-label">{{ String(hour - 1).padStart(2, '0') }}:00</div>
                <div class="hour-content">
                  <div
                    v-for="change in getChangesForHour(hour - 1)"
                    :key="change.id"
                    class="day-change-item"
                    :class="getChangeClass(change)"
                    @click="viewChangeDetails(change)"
                  >
                    <div class="change-header">
                      <span class="change-title">{{ change.title }}</span>
                      <el-tag :type="getRiskType(change.risk)" size="small">{{ change.risk }}</el-tag>
                    </div>
                    <div class="change-meta">
                      <span>{{ change.type }}</span>
                      <span>{{ change.assignee }}</span>
                      <span>{{ formatTime(change.implementDate) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 变更详情对话框 -->
    <el-dialog v-model="showChangeDetails" title="变更详情" width="60%">
      <div v-if="selectedChange" class="change-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="变更ID">{{ selectedChange.id }}</el-descriptions-item>
          <el-descriptions-item label="标题">{{ selectedChange.title }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag>{{ selectedChange.type }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedChange.status)">{{ selectedChange.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="风险等级">
            <el-tag :type="getRiskType(selectedChange.risk)">{{ selectedChange.risk }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(selectedChange.priority)">{{ selectedChange.priority }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="负责人">{{ selectedChange.assignee }}</el-descriptions-item>
          <el-descriptions-item label="实施时间">{{ selectedChange.implementDate }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ selectedChange.description }}</el-descriptions-item>
        </el-descriptions>

        <div class="change-actions" style="margin-top: 20px;">
          <el-button type="primary" @click="editChange(selectedChange)">编辑变更</el-button>
          <el-button type="success" @click="approveChange(selectedChange)" v-if="selectedChange.status === '待审批'">
            审批
          </el-button>
          <el-button type="warning" @click="rescheduleChange(selectedChange)">重新安排</el-button>
        </div>
      </div>
      <template #footer>
        <el-button @click="showChangeDetails = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 冲突检测结果对话框 -->
    <el-dialog v-model="showConflictDialog" title="变更冲突检测" width="70%">
      <div class="conflict-detection">
        <div class="detection-summary">
          <el-alert
            :title="`检测到 ${conflicts.length} 个潜在冲突`"
            :type="conflicts.length > 0 ? 'warning' : 'success'"
            :description="conflicts.length > 0 ? '请及时处理以下冲突' : '当前没有发现变更冲突'"
            show-icon
            :closable="false"
          />
        </div>

        <div v-if="conflicts.length > 0" class="conflict-list">
          <el-table :data="conflicts" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="type" label="冲突类型" width="120">
              <template #default="scope">
                <el-tag :type="getConflictType(scope.row.type)">{{ scope.row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="冲突描述" />
            <el-table-column prop="changes" label="涉及变更" width="200">
              <template #default="scope">
                <div v-for="change in scope.row.changes" :key="change" class="conflict-change">
                  {{ change }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="severity" label="严重程度" width="100">
              <template #default="scope">
                <el-tag :type="getSeverityType(scope.row.severity)">{{ scope.row.severity }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" size="small" @click="resolveConflict(scope.row)">
                  解决
                </el-button>
                <el-button type="warning" size="small" @click="ignoreConflict(scope.row)">
                  忽略
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="auto-resolution" style="margin-top: 20px;">
          <el-button type="success" @click="autoResolveConflicts" :disabled="conflicts.length === 0">
            <el-icon><MagicStick /></el-icon>
            智能解决冲突
          </el-button>
          <el-button @click="refreshConflictDetection">
            <el-icon><Refresh /></el-icon>
            重新检测
          </el-button>
        </div>
      </div>
      <template #footer>
        <el-button @click="showConflictDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 重新安排变更对话框 -->
    <el-dialog v-model="showRescheduleDialog" title="重新安排变更" width="50%">
      <div v-if="rescheduleChange" class="reschedule-form">
        <el-form :model="rescheduleForm" label-width="120px">
          <el-form-item label="变更标题">
            <el-input v-model="rescheduleForm.title" disabled />
          </el-form-item>
          <el-form-item label="当前时间">
            <el-input v-model="rescheduleForm.currentTime" disabled />
          </el-form-item>
          <el-form-item label="新的时间" required>
            <el-date-picker
              v-model="rescheduleForm.newTime"
              type="datetime"
              placeholder="选择新的实施时间"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="调整原因" required>
            <el-input
              v-model="rescheduleForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入调整原因"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showRescheduleDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmReschedule">确认调整</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const currentView = ref('month')
const currentDate = ref(new Date())
const typeFilter = ref('all')
const riskFilter = ref('all')
const showChangeDetails = ref(false)
const showConflictDialog = ref(false)
const showRescheduleDialog = ref(false)
const selectedChange = ref(null)

// 统计数据
const monthlyStats = ref({
  total: 32,
  increase: 5
})

const conflictStats = ref({
  conflicts: 2
})

const windowStats = ref({
  utilization: 75
})

const successStats = ref({
  rate: 92,
  total: 28
})

// 变更数据
const changes = ref([
  {
    id: 'CHG-2025-001',
    title: '服务器系统升级',
    type: '标准变更',
    status: '已批准',
    risk: '中',
    priority: '高',
    assignee: '张工',
    implementDate: '2025-02-01 02:00:00',
    description: '升级生产服务器操作系统到最新版本'
  },
  {
    id: 'CHG-2025-002',
    title: '网络设备配置更新',
    type: '紧急变更',
    status: '待审批',
    risk: '高',
    priority: '紧急',
    assignee: '李工',
    implementDate: '2025-01-31 20:00:00',
    description: '更新核心网络设备配置以修复安全漏洞'
  },
  {
    id: 'CHG-2025-003',
    title: '数据库性能优化',
    type: '正常变更',
    status: '已实施',
    risk: '低',
    priority: '中',
    assignee: '王工',
    implementDate: '2025-01-30 14:00:00',
    description: '优化数据库查询性能，提升系统响应速度'
  },
  {
    id: 'CHG-2025-004',
    title: '应用程序版本更新',
    type: '标准变更',
    status: '草稿',
    risk: '中',
    priority: '中',
    assignee: '赵工',
    implementDate: '2025-02-02 10:00:00',
    description: '更新应用程序到最新版本，修复已知问题'
  }
])

// 冲突数据
const conflicts = ref([
  {
    type: '时间冲突',
    description: '两个变更安排在同一时间窗口',
    changes: ['CHG-2025-001', 'CHG-2025-005'],
    severity: '高'
  },
  {
    type: '资源冲突',
    description: '变更涉及相同的系统组件',
    changes: ['CHG-2025-002', 'CHG-2025-006'],
    severity: '中'
  }
])

// 重新安排表单
const rescheduleForm = reactive({
  title: '',
  currentTime: '',
  newTime: null,
  reason: ''
})

// 计算属性
const weekDays = computed(() => {
  const startOfWeek = getStartOfWeek(currentDate.value)
  const days = []
  for (let i = 0; i < 7; i++) {
    const date = new Date(startOfWeek)
    date.setDate(startOfWeek.getDate() + i)
    days.push({
      name: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()],
      date: formatDateString(date)
    })
  }
  return days
})

const filteredChanges = computed(() => {
  let filtered = changes.value

  if (typeFilter.value !== 'all') {
    filtered = filtered.filter(c => c.type === typeFilter.value)
  }

  if (riskFilter.value !== 'all') {
    filtered = filtered.filter(c => c.risk === riskFilter.value)
  }

  return filtered
})

// 工具函数
const getStartOfWeek = (date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day
  return new Date(d.setDate(diff))
}

const formatDateString = (date) => {
  return date.toISOString().split('T')[0]
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

const formatTime = (dateTime) => {
  return new Date(dateTime).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取指定日期的变更
const getChangesForDate = (dateString) => {
  return filteredChanges.value.filter(change => {
    const changeDate = new Date(change.implementDate).toISOString().split('T')[0]
    return changeDate === dateString
  })
}

// 获取指定日期和小时的变更
const getChangesForDateTime = (dateString, hour) => {
  return filteredChanges.value.filter(change => {
    const changeDate = new Date(change.implementDate)
    const changeDateString = changeDate.toISOString().split('T')[0]
    const changeHour = changeDate.getHours()
    return changeDateString === dateString && changeHour === hour
  })
}

// 获取当天的变更
const getDayChanges = () => {
  const dateString = formatDateString(currentDate.value)
  return getChangesForDate(dateString)
}

// 获取指定小时的变更
const getChangesForHour = (hour) => {
  return getDayChanges().filter(change => {
    const changeHour = new Date(change.implementDate).getHours()
    return changeHour === hour
  })
}

// 获取变更样式类
const getChangeClass = (change) => {
  const classes = ['change-item']

  switch (change.type) {
    case '标准变更':
      classes.push('standard-change')
      break
    case '紧急变更':
      classes.push('emergency-change')
      break
    case '正常变更':
      classes.push('normal-change')
      break
  }

  if (change.risk === '高') {
    classes.push('high-risk')
  }

  return classes.join(' ')
}

// 获取日历标题
const getCalendarTitle = () => {
  const date = new Date(currentDate.value)
  switch (currentView.value) {
    case 'month':
      return `${date.getFullYear()}年${date.getMonth() + 1}月`
    case 'week':
      const startOfWeek = getStartOfWeek(date)
      const endOfWeek = new Date(startOfWeek)
      endOfWeek.setDate(startOfWeek.getDate() + 6)
      return `${formatDateString(startOfWeek)} 至 ${formatDateString(endOfWeek)}`
    case 'day':
      return formatDate(date)
    default:
      return ''
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '草稿': 'info',
    '待审批': 'warning',
    '已批准': 'success',
    '已拒绝': 'danger',
    '已实施': 'success',
    '已回滚': 'warning',
    '已关闭': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取风险类型
const getRiskType = (risk) => {
  const typeMap = {
    '低': 'success',
    '中': 'warning',
    '高': 'danger'
  }
  return typeMap[risk] || 'info'
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const typeMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取冲突类型
const getConflictType = (type) => {
  const typeMap = {
    '时间冲突': 'danger',
    '资源冲突': 'warning',
    '依赖冲突': 'info'
  }
  return typeMap[type] || 'info'
}

// 获取严重程度类型
const getSeverityType = (severity) => {
  const typeMap = {
    '低': 'success',
    '中': 'warning',
    '高': 'danger'
  }
  return typeMap[severity] || 'info'
}

// 事件处理函数
const createChange = () => {
  router.push('/change')
}

const detectConflicts = () => {
  showConflictDialog.value = true
  ElMessage.success('正在检测变更冲突...')
}

const exportCalendar = () => {
  ElMessage.success('正在导出变更日历...')
}

const changeView = (view) => {
  currentView.value = view
  ElMessage.info(`切换到${view === 'month' ? '月' : view === 'week' ? '周' : '日'}视图`)
}

const previousPeriod = () => {
  const date = new Date(currentDate.value)
  switch (currentView.value) {
    case 'month':
      date.setMonth(date.getMonth() - 1)
      break
    case 'week':
      date.setDate(date.getDate() - 7)
      break
    case 'day':
      date.setDate(date.getDate() - 1)
      break
  }
  currentDate.value = date
}

const nextPeriod = () => {
  const date = new Date(currentDate.value)
  switch (currentView.value) {
    case 'month':
      date.setMonth(date.getMonth() + 1)
      break
    case 'week':
      date.setDate(date.getDate() + 7)
      break
    case 'day':
      date.setDate(date.getDate() + 1)
      break
  }
  currentDate.value = date
}

const goToday = () => {
  currentDate.value = new Date()
}

const dateChanged = (date) => {
  currentDate.value = date
}

const handleDatePick = (date) => {
  currentDate.value = new Date(date)
  if (currentView.value === 'month') {
    currentView.value = 'day'
  }
}

const viewChangeDetails = (change) => {
  selectedChange.value = change
  showChangeDetails.value = true
}

const editChange = (change) => {
  router.push(`/change/edit/${change.id}`)
}

const approveChange = (change) => {
  router.push(`/change/cab?changeId=${change.id}`)
}

const rescheduleChange = (change) => {
  rescheduleForm.title = change.title
  rescheduleForm.currentTime = change.implementDate
  rescheduleForm.newTime = null
  rescheduleForm.reason = ''
  showRescheduleDialog.value = true
}

const confirmReschedule = () => {
  if (!rescheduleForm.newTime || !rescheduleForm.reason) {
    ElMessage.warning('请填写完整信息')
    return
  }

  ElMessage.success('变更时间已调整')
  showRescheduleDialog.value = false

  // 更新变更时间
  const change = changes.value.find(c => c.title === rescheduleForm.title)
  if (change) {
    change.implementDate = rescheduleForm.newTime.toISOString().replace('T', ' ').slice(0, 19)
  }
}

const resolveConflict = (conflict) => {
  ElMessage.success(`正在解决冲突: ${conflict.type}`)
  // 移除已解决的冲突
  const index = conflicts.value.findIndex(c => c === conflict)
  if (index > -1) {
    conflicts.value.splice(index, 1)
  }
}

const ignoreConflict = (conflict) => {
  ElMessageBox.confirm('确定要忽略此冲突吗？', '确认忽略', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = conflicts.value.findIndex(c => c === conflict)
    if (index > -1) {
      conflicts.value.splice(index, 1)
    }
    ElMessage.success('冲突已忽略')
  }).catch(() => {
    ElMessage.info('已取消忽略')
  })
}

const autoResolveConflicts = () => {
  ElMessage.success('正在智能解决冲突...')
  setTimeout(() => {
    conflicts.value = []
    ElMessage.success('所有冲突已自动解决')
  }, 2000)
}

const refreshConflictDetection = () => {
  ElMessage.success('正在重新检测冲突...')
  setTimeout(() => {
    ElMessage.success('冲突检测完成')
  }, 1500)
}

onMounted(() => {
  // 初始化日历
  ElMessage.success('变更日历加载完成')
})
</script>

<style scoped>
.change-calendar {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-info h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-info p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.calendar-controls,
.calendar-stats,
.calendar-main {
  margin-bottom: 20px;
}

.controls-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.view-controls,
.date-navigation,
.filter-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-card {
  text-align: center;
  padding: 16px;
}

.stat-extra {
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.legend {
  display: flex;
  gap: 8px;
}

/* 月视图样式 */
.month-view {
  margin-top: 20px;
}

.calendar-cell {
  min-height: 80px;
  padding: 4px;
}

.cell-date {
  font-weight: bold;
  margin-bottom: 4px;
}

.cell-changes {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.change-item {
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-item:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.standard-change {
  background-color: #e8f5e8;
  border-left: 3px solid #4CAF50;
}

.emergency-change {
  background-color: #fff3e0;
  border-left: 3px solid #FF9800;
}

.normal-change {
  background-color: #e3f2fd;
  border-left: 3px solid #2196F3;
}

.high-risk {
  border-left-color: #F44336 !important;
  background-color: #ffebee;
}

.change-title {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.change-time {
  color: #666;
  font-size: 10px;
}

/* 周视图样式 */
.week-view {
  margin-top: 20px;
}

.week-header {
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  border-bottom: 1px solid #e0e0e0;
}

.week-day-header {
  padding: 12px 8px;
  text-align: center;
  border-right: 1px solid #e0e0e0;
}

.day-name {
  font-weight: bold;
  color: #333;
}

.day-date {
  color: #666;
  font-size: 12px;
}

.week-content {
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  max-height: 600px;
  overflow-y: auto;
}

.time-column {
  border-right: 1px solid #e0e0e0;
}

.time-slot {
  height: 40px;
  padding: 8px 4px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
  color: #666;
}

.week-day-column {
  border-right: 1px solid #e0e0e0;
}

.hour-slot {
  height: 40px;
  border-bottom: 1px solid #f0f0f0;
  padding: 2px;
  position: relative;
}

.week-change-item {
  background-color: #e3f2fd;
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 11px;
  cursor: pointer;
  margin: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 日视图样式 */
.day-view {
  margin-top: 20px;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e0e0e0;
}

.day-stats {
  display: flex;
  gap: 8px;
}

.day-timeline {
  margin-top: 20px;
}

.timeline-hours {
  display: flex;
  flex-direction: column;
}

.hour-block {
  display: flex;
  min-height: 60px;
  border-bottom: 1px solid #f0f0f0;
}

.hour-label {
  width: 80px;
  padding: 8px;
  font-size: 12px;
  color: #666;
  border-right: 1px solid #e0e0e0;
  background-color: #fafafa;
}

.hour-content {
  flex: 1;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.day-change-item {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  border-left: 4px solid #2196F3;
  cursor: pointer;
  transition: all 0.3s ease;
}

.day-change-item:hover {
  background-color: #e3f2fd;
  transform: translateX(4px);
}

.change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.change-title {
  font-weight: 500;
  color: #333;
}

.change-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

/* 对话框样式 */
.change-details {
  padding: 20px 0;
}

.change-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.conflict-detection {
  padding: 20px 0;
}

.detection-summary {
  margin-bottom: 20px;
}

.conflict-change {
  padding: 2px 0;
  font-size: 12px;
}

.reschedule-form {
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .controls-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .week-content,
  .week-header {
    grid-template-columns: 60px repeat(7, 1fr);
  }

  .time-column {
    width: 60px;
  }

  .hour-label {
    width: 60px;
    font-size: 10px;
  }

  .change-item {
    font-size: 10px;
  }

  .week-change-item {
    font-size: 9px;
  }
}
</style>
