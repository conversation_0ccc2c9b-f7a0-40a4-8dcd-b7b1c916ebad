<template>
  <div class="license-management">
    <h1>许可证管理</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <el-icon class="stat-icon"><Key /></el-icon>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.totalLicenses }}</div>
              <div class="stat-label">总许可证数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <el-icon class="stat-icon"><SuccessFilled /></el-icon>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.activeLicenses }}</div>
              <div class="stat-label">有效许可证</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <el-icon class="stat-icon"><Warning /></el-icon>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.expiringLicenses }}</div>
              <div class="stat-label">即将到期</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <el-icon class="stat-icon"><CircleCheck /></el-icon>
            <div class="stat-info">
              <div class="stat-number">{{ (statistics.complianceRate * 100).toFixed(1) }}%</div>
              <div class="stat-label">合规率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button type="primary" @click="showCreateDialog = true">新增许可证</el-button>
      <el-button @click="refreshData" :loading="loading">刷新数据</el-button>
    </div>

    <!-- 许可证列表 -->
    <el-card>
      <template #header>
        <span>许可证列表 ({{ licenseList.length }})</span>
      </template>
      
      <el-table :data="licenseList" v-loading="loading" stripe>
        <el-table-column prop="softwareName" label="软件名称" />
        <el-table-column prop="version" label="版本" />
        <el-table-column prop="type" label="类型">
          <template #default="{ row }">
            <el-tag>{{ row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expiryDate" label="到期日期" />
        <el-table-column prop="cost" label="费用">
          <template #default="{ row }">
            ¥{{ row.cost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDetail(row)">详情</el-button>
            <el-button type="text" size="small" @click="editItem(row)">编辑</el-button>
            <el-button type="text" size="small" @click="deleteItem(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增对话框 -->
    <el-dialog v-model="showCreateDialog" title="新增许可证" width="500px">
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="软件名称">
          <el-input v-model="createForm.softwareName" />
        </el-form-item>
        <el-form-item label="版本">
          <el-input v-model="createForm.version" />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="createForm.type" style="width: 100%">
            <el-option label="永久许可证" value="永久许可证" />
            <el-option label="订阅许可证" value="订阅许可证" />
            <el-option label="并发许可证" value="并发许可证" />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商">
          <el-input v-model="createForm.supplier" />
        </el-form-item>
        <el-form-item label="费用">
          <el-input v-model.number="createForm.cost" type="number" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreate" :loading="createLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Key, SuccessFilled, Warning, CircleCheck } from '@element-plus/icons-vue'
import { 
  getLicenseStatistics, 
  getLicenseList, 
  createLicense, 
  deleteLicense 
} from '@/api/assetApi.js'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showCreateDialog = ref(false)

const statistics = ref({
  totalLicenses: 0,
  activeLicenses: 0,
  expiringLicenses: 0,
  complianceRate: 0
})

const licenseList = ref([])

const createForm = reactive({
  softwareName: '',
  version: '',
  type: '',
  supplier: '',
  cost: 0
})

// 生命周期
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 获取统计数据
    const statsRes = await getLicenseStatistics()
    statistics.value = statsRes.data

    // 获取许可证列表
    const listRes = await getLicenseList()
    licenseList.value = listRes.data.list || []
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadData()
  ElMessage.success('数据刷新成功')
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '有效': 'success',
    '即将到期': 'warning',
    '已过期': 'danger'
  }
  return typeMap[status] || 'info'
}

// 查看详情
const viewDetail = (row) => {
  router.push(`/asset/license/detail/${row.id}`)
}

// 编辑
const editItem = (row) => {
  router.push(`/asset/license/edit/${row.id}`)
}

// 删除
const deleteItem = (row) => {
  ElMessageBox.confirm(`确定要删除许可证 "${row.softwareName}" 吗？`, '确认删除', {
    type: 'warning'
  }).then(async () => {
    try {
      await deleteLicense(row.id)
      ElMessage.success('删除成功')
      loadData()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 创建许可证
const handleCreate = async () => {
  createLoading.value = true
  try {
    await createLicense({
      ...createForm,
      usedQuantity: 0,
      totalQuantity: 1,
      status: '有效',
      complianceStatus: '合规',
      purchaseDate: new Date().toISOString().split('T')[0],
      expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    })
    ElMessage.success('创建成功')
    showCreateDialog.value = false
    resetForm()
    loadData()
  } catch (error) {
    ElMessage.error('创建失败')
  } finally {
    createLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(createForm).forEach(key => {
    createForm[key] = key === 'cost' ? 0 : ''
  })
}
</script>

<style scoped>
.license-management {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 24px;
  color: #409eff;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.actions {
  margin-bottom: 20px;
}

.actions .el-button {
  margin-right: 10px;
}
</style>
