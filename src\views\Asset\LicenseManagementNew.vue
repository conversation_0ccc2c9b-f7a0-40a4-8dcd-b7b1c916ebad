<template>
  <div class="license-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>许可证管理</h2>
          <p>管理软件许可证合规性，确保企业软件使用合法合规</p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
            新增许可证
          </el-button>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Key /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.totalLicenses }}</div>
                <div class="stat-label">总许可证数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.activeLicenses }}</div>
                <div class="stat-label">有效许可证</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.expiringLicenses }}</div>
                <div class="stat-label">即将到期</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon compliance">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ (statistics.complianceRate * 100).toFixed(1) }}%</div>
                <div class="stat-label">合规率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 许可证列表 -->
    <el-card class="license-list-card">
      <template #header>
        <div class="card-header">
          <span>许可证列表 ({{ pagination.total }})</span>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="licenseList"
        stripe
      >
        <el-table-column prop="softwareName" label="软件名称" min-width="150" />
        <el-table-column prop="version" label="版本" width="100" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getLicenseTypeColor(row.type)" size="small">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="使用情况" width="150">
          <template #default="{ row }">
            <div class="usage-progress">
              <el-progress
                :percentage="getUsagePercentage(row)"
                :color="getUsageColor(row)"
                :stroke-width="8"
              />
              <span class="usage-text">{{ row.usedQuantity }}/{{ row.totalQuantity }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expiryDate" label="到期日期" width="120" />
        <el-table-column prop="cost" label="费用" width="100">
          <template #default="{ row }">
            ¥{{ row.cost.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewLicenseDetail(row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click="editLicense(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="deleteLicenseItem(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增许可证对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新增许可证"
      width="600px"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        label-width="120px"
      >
        <el-form-item label="软件名称">
          <el-input v-model="createForm.softwareName" placeholder="请输入软件名称" />
        </el-form-item>
        <el-form-item label="版本">
          <el-input v-model="createForm.version" placeholder="请输入版本号" />
        </el-form-item>
        <el-form-item label="许可证类型">
          <el-select v-model="createForm.type" placeholder="请选择许可证类型" style="width: 100%">
            <el-option label="永久许可证" value="永久许可证" />
            <el-option label="订阅许可证" value="订阅许可证" />
            <el-option label="并发许可证" value="并发许可证" />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商">
          <el-input v-model="createForm.supplier" placeholder="请输入供应商名称" />
        </el-form-item>
        <el-form-item label="许可证数量">
          <el-input-number
            v-model="createForm.totalQuantity"
            :min="1"
            :max="9999"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="采购费用">
          <el-input
            v-model.number="createForm.cost"
            type="number"
            :min="0"
            step="0.01"
            placeholder="请输入采购费用"
          >
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" :loading="createLoading" @click="handleCreateLicense">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, Key, SuccessFilled, Warning, CircleCheck
} from '@element-plus/icons-vue'
import {
  getLicenseStatistics,
  getLicenseList,
  createLicense,
  deleteLicense
} from '@/api/assetApi.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)

// 对话框状态
const showCreateDialog = ref(false)

// 统计数据
const statistics = ref({
  totalLicenses: 0,
  activeLicenses: 0,
  expiringLicenses: 0,
  complianceRate: 0
})

// 许可证列表和分页
const licenseList = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 创建表单
const createFormRef = ref(null)
const createForm = reactive({
  softwareName: '',
  version: '',
  type: '',
  supplier: '',
  totalQuantity: 1,
  cost: 0
})

// 生命周期
onMounted(() => {
  initData()
})

// 初始化数据
const initData = async () => {
  loading.value = true
  try {
    // 获取统计数据
    const statsRes = await getLicenseStatistics()
    statistics.value = statsRes.data

    // 获取许可证列表
    await loadLicenseList()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载许可证列表
const loadLicenseList = async () => {
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    const response = await getLicenseList(params)
    licenseList.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    console.error('加载许可证列表失败:', error)
    ElMessage.error('加载许可证列表失败')
  }
}

// 刷新数据
const refreshData = async () => {
  await initData()
  ElMessage.success('数据刷新成功')
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadLicenseList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadLicenseList()
}

// 获取许可证类型颜色
const getLicenseTypeColor = (type) => {
  const colorMap = {
    '永久许可证': 'success',
    '订阅许可证': 'primary',
    '并发许可证': 'warning'
  }
  return colorMap[type] || 'info'
}

// 获取使用百分比
const getUsagePercentage = (license) => {
  if (license.totalQuantity === 0) return 0
  return Math.round((license.usedQuantity / license.totalQuantity) * 100)
}

// 获取使用颜色
const getUsageColor = (license) => {
  const percentage = getUsagePercentage(license)
  if (percentage >= 100) return '#f56c6c'
  if (percentage >= 80) return '#e6a23c'
  return '#67c23a'
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '有效': 'success',
    '即将到期': 'warning',
    '已过期': 'danger'
  }
  return typeMap[status] || 'info'
}

// 查看许可证详情
const viewLicenseDetail = (license) => {
  router.push(`/asset/license/detail/${license.id}`)
}

// 编辑许可证
const editLicense = (license) => {
  router.push(`/asset/license/edit/${license.id}`)
}

// 删除许可证
const deleteLicenseItem = (license) => {
  ElMessageBox.confirm(
    `确定要删除许可证 "${license.softwareName}" 吗？此操作不可逆。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteLicense(license.id)
      ElMessage.success('许可证删除成功')
      loadLicenseList()
    } catch (error) {
      console.error('删除许可证失败:', error)
      ElMessage.error('删除许可证失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 处理创建许可证
const handleCreateLicense = async () => {
  if (!createFormRef.value) return

  try {
    createLoading.value = true

    const formData = {
      ...createForm,
      usedQuantity: 0,
      availableQuantity: createForm.totalQuantity,
      status: '有效',
      complianceStatus: '合规',
      purchaseDate: new Date().toISOString().split('T')[0],
      expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }

    await createLicense(formData)
    ElMessage.success('许可证创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    loadLicenseList()
  } catch (error) {
    console.error('创建许可证失败:', error)
    ElMessage.error('创建许可证失败')
  } finally {
    createLoading.value = false
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.keys(createForm).forEach(key => {
    if (key === 'totalQuantity') {
      createForm[key] = 1
    } else if (key === 'cost') {
      createForm[key] = 0
    } else {
      createForm[key] = ''
    }
  })
}
</script>

<style scoped>
.license-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.title-section p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #e6a23c;
}

.stat-icon.compliance {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #67c23a;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 许可证列表卡片 */
.license-list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 使用进度 */
.usage-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.usage-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 对话框 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式 */
@media (max-width: 768px) {
  .license-management {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
  }
}
</style>
</script>
