<template>
  <div class="knowledge-editor">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator="/">
      <el-breadcrumb-item :to="{ path: '/knowledge' }">知识概览</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/knowledge/base' }">知识库</el-breadcrumb-item>
      <el-breadcrumb-item>{{ isEdit ? '编辑知识' : '创建知识' }}</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="editor-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="knowledge-form"
      >
        <el-row :gutter="20">
          <!-- 左侧编辑区域 -->
          <el-col :xs="24" :lg="18">
            <el-card class="editor-card">
              <template #header>
                <div class="card-header">
                  <span>{{ isEdit ? '编辑知识' : '创建知识' }}</span>
                  <div class="header-actions">
                    <el-button @click="previewKnowledge" v-if="formData.content">
                      <el-icon><View /></el-icon>
                      预览
                    </el-button>
                    <el-button @click="saveAsDraft">
                      <el-icon><Document /></el-icon>
                      保存草稿
                    </el-button>
                    <el-button type="primary" @click="submitKnowledge">
                      <el-icon><Check /></el-icon>
                      {{ isEdit ? '更新' : '提交审核' }}
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- 基本信息 -->
              <div class="basic-info-section">
                <el-form-item label="知识标题" prop="title">
                  <el-input
                    v-model="formData.title"
                    placeholder="请输入知识标题"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>

                <el-form-item label="知识摘要" prop="summary">
                  <el-input
                    v-model="formData.summary"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入知识摘要，简要描述知识内容"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="知识分类" prop="categoryId">
                      <el-select
                        v-model="formData.categoryId"
                        placeholder="请选择知识分类"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="category in categories"
                          :key="category.id"
                          :label="category.name"
                          :value="category.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="知识标签">
                      <el-select
                        v-model="formData.tags"
                        multiple
                        filterable
                        allow-create
                        default-first-option
                        placeholder="请选择或输入标签"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="tag in availableTags"
                          :key="tag.id"
                          :label="tag.name"
                          :value="tag.name"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 内容编辑器 -->
              <div class="content-editor-section">
                <el-form-item label="知识内容" prop="content">
                  <div class="editor-toolbar">
                    <el-button-group>
                      <el-button size="small" @click="insertTemplate">
                        <el-icon><Document /></el-icon>
                        使用模板
                      </el-button>
                      <el-button size="small" @click="insertTable">
                        <el-icon><Grid /></el-icon>
                        插入表格
                      </el-button>
                      <el-button size="small" @click="insertCode">
                        <el-icon><Cpu /></el-icon>
                        代码块
                      </el-button>
                      <el-button size="small" @click="insertImage">
                        <el-icon><Picture /></el-icon>
                        插入图片
                      </el-button>
                    </el-button-group>
                  </div>
                  
                  <!-- 富文本编辑器 -->
                  <div class="rich-editor">
                    <el-input
                      v-model="formData.content"
                      type="textarea"
                      :rows="20"
                      placeholder="请输入知识内容，支持Markdown格式"
                      class="content-textarea"
                    />
                  </div>
                </el-form-item>
              </div>

              <!-- 附件上传 -->
              <div class="attachments-section">
                <el-form-item label="附件">
                  <el-upload
                    ref="uploadRef"
                    :file-list="formData.attachments"
                    :on-change="handleFileChange"
                    :on-remove="handleFileRemove"
                    :before-upload="beforeUpload"
                    :auto-upload="false"
                    multiple
                    drag
                  >
                    <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                    <div class="el-upload__text">
                      将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                      <div class="el-upload__tip">
                        支持jpg/png/gif/pdf/doc/docx/xls/xlsx等格式，单个文件不超过10MB
                      </div>
                    </template>
                  </el-upload>
                </el-form-item>
              </div>
            </el-card>
          </el-col>

          <!-- 右侧设置区域 -->
          <el-col :xs="24" :lg="6">
            <!-- 发布设置 -->
            <el-card class="settings-card">
              <template #header>
                <span>发布设置</span>
              </template>
              
              <el-form-item label="状态">
                <el-select v-model="formData.status" style="width: 100%">
                  <el-option label="草稿" value="draft" />
                  <el-option label="提交审核" value="review" />
                  <el-option label="发布" value="published" v-if="canPublish" />
                </el-select>
              </el-form-item>

              <el-form-item label="可见性">
                <el-radio-group v-model="formData.visibility">
                  <el-radio label="public">公开</el-radio>
                  <el-radio label="internal">内部</el-radio>
                  <el-radio label="private">私有</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="允许评论">
                <el-switch v-model="formData.allowComments" />
              </el-form-item>

              <el-form-item label="置顶">
                <el-switch v-model="formData.isTop" v-if="canManage" />
              </el-form-item>
            </el-card>

            <!-- 模板选择 -->
            <el-card class="templates-card">
              <template #header>
                <span>知识模板</span>
              </template>
              
              <div class="templates-list">
                <div 
                  v-for="template in templates" 
                  :key="template.id"
                  class="template-item"
                  @click="applyTemplate(template)"
                >
                  <div class="template-name">{{ template.name }}</div>
                  <div class="template-description">{{ template.description }}</div>
                </div>
              </div>
            </el-card>

            <!-- 版本信息 -->
            <el-card class="version-card" v-if="isEdit">
              <template #header>
                <span>版本信息</span>
              </template>
              
              <div class="version-info">
                <div class="version-item">
                  <span class="version-label">当前版本</span>
                  <span class="version-value">v{{ currentVersion }}</span>
                </div>
                <div class="version-item">
                  <span class="version-label">最后修改</span>
                  <span class="version-value">{{ formatTime(lastModified) }}</span>
                </div>
                <div class="version-item">
                  <span class="version-label">修改说明</span>
                  <el-input
                    v-model="formData.versionNote"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入本次修改的说明"
                    maxlength="200"
                  />
                </div>
              </div>
            </el-card>

            <!-- 操作历史 -->
            <el-card class="history-card" v-if="isEdit">
              <template #header>
                <span>操作历史</span>
              </template>
              
              <div class="history-list">
                <div 
                  v-for="history in operationHistory" 
                  :key="history.id"
                  class="history-item"
                >
                  <div class="history-action">{{ history.action }}</div>
                  <div class="history-meta">
                    <span class="history-user">{{ history.user }}</span>
                    <span class="history-time">{{ formatTime(history.time) }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialog"
      title="知识预览"
      width="80%"
      :before-close="handlePreviewClose"
    >
      <div class="preview-content">
        <h1 class="preview-title">{{ formData.title }}</h1>
        <div class="preview-meta">
          <el-tag>{{ getCategoryName(formData.categoryId) }}</el-tag>
          <span class="preview-author">作者：{{ currentUser.name }}</span>
        </div>
        <div class="preview-summary">{{ formData.summary }}</div>
        <div class="preview-body" v-html="renderedContent"></div>
      </div>
    </el-dialog>

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="templateDialog"
      title="选择知识模板"
      width="600px"
    >
      <div class="template-selection">
        <el-row :gutter="20">
          <el-col :span="12" v-for="template in templates" :key="template.id">
            <el-card 
              class="template-card"
              :class="{ selected: selectedTemplate?.id === template.id }"
              @click="selectTemplate(template)"
            >
              <div class="template-info">
                <h3>{{ template.name }}</h3>
                <p>{{ template.description }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <el-button @click="templateDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmTemplate" :disabled="!selectedTemplate">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  View, Document, Check, Grid, Cpu, Picture, UploadFilled
} from '@element-plus/icons-vue'
import { 
  getKnowledgeDetail,
  createKnowledge,
  updateKnowledge,
  getKnowledgeCategories,
  getKnowledgeTags,
  getKnowledgeTemplates,
  uploadKnowledgeAttachment
} from '@/api/knowledgeApi'

const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const previewDialog = ref(false)
const templateDialog = ref(false)
const selectedTemplate = ref(null)

const isEdit = computed(() => route.name === 'KnowledgeEdit')
const canPublish = ref(true)
const canManage = ref(true)
const currentVersion = ref('2.1')
const lastModified = ref('2025-01-30 14:30')

// 当前用户信息
const currentUser = ref({
  id: 1,
  name: '张工'
})

// 表单数据
const formData = reactive({
  title: '',
  summary: '',
  content: '',
  categoryId: '',
  tags: [],
  status: 'draft',
  visibility: 'public',
  allowComments: true,
  isTop: false,
  attachments: [],
  versionNote: ''
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入知识标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入知识摘要', trigger: 'blur' },
    { min: 10, max: 500, message: '摘要长度在 10 到 500 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入知识内容', trigger: 'blur' },
    { min: 50, message: '内容至少需要 50 个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择知识分类', trigger: 'change' }
  ]
}

// 分类数据
const categories = ref([
  { id: 1, name: '技术文档' },
  { id: 2, name: '流程规范' },
  { id: 3, name: 'FAQ' },
  { id: 4, name: '解决方案' },
  { id: 5, name: '操作指南' }
])

// 可用标签
const availableTags = ref([
  { id: 1, name: '服务器' },
  { id: 2, name: '网络' },
  { id: 3, name: '数据库' },
  { id: 4, name: '安全' },
  { id: 5, name: '监控' }
])

// 知识模板
const templates = ref([
  {
    id: 1,
    name: '技术文档模板',
    description: '适用于技术操作指南和配置文档',
    content: `# 标题

## 概述
简要描述文档内容和目的。

## 前提条件
- 条件1
- 条件2

## 操作步骤
1. 步骤1
2. 步骤2

## 注意事项
重要提醒和注意事项。

## 参考资料
相关链接和参考文档。`
  },
  {
    id: 2,
    name: 'FAQ模板',
    description: '适用于常见问题解答',
    content: `# 常见问题解答

## 问题1：问题描述
**答案：** 详细的解答内容。

## 问题2：问题描述
**答案：** 详细的解答内容。

## 相关问题
- [相关问题1](#)
- [相关问题2](#)`
  },
  {
    id: 3,
    name: '故障排除模板',
    description: '适用于故障诊断和解决方案',
    content: `# 故障排除指南

## 故障现象
描述故障的具体表现。

## 可能原因
1. 原因1
2. 原因2

## 解决方案
### 方案1
具体的解决步骤。

### 方案2
备选解决方案。

## 预防措施
如何避免类似问题再次发生。`
  }
])

// 操作历史
const operationHistory = ref([
  {
    id: 1,
    action: '创建文档',
    user: '张工',
    time: '2025-01-25 09:15'
  },
  {
    id: 2,
    action: '更新内容',
    user: '张工',
    time: '2025-01-28 14:20'
  }
])

// 计算属性
const renderedContent = computed(() => {
  // 这里可以使用markdown解析器将内容转换为HTML
  return formData.content.replace(/\n/g, '<br>')
})

// 生命周期
onMounted(() => {
  loadInitialData()
  if (isEdit.value) {
    loadKnowledgeData()
  } else {
    // 检查是否从工单创建知识
    loadKnowledgeFromTicket()
  }
})

// 加载初始数据
const loadInitialData = async () => {
  try {
    // const [categoriesRes, tagsRes, templatesRes] = await Promise.all([
    //   getKnowledgeCategories(),
    //   getKnowledgeTags(),
    //   getKnowledgeTemplates()
    // ])
    // categories.value = categoriesRes.data
    // availableTags.value = tagsRes.data
    // templates.value = templatesRes.data
  } catch (error) {
    ElMessage.error('加载数据失败')
  }
}

// 加载知识数据（编辑模式）
const loadKnowledgeData = async () => {
  try {
    const id = route.params.id
    // const response = await getKnowledgeDetail(id)
    // const data = response.data

    // 模拟数据
    const data = {
      title: '服务器性能监控配置完整指南',
      summary: '详细介绍如何配置服务器性能监控系统，包括CPU、内存、磁盘等关键指标的监控设置。',
      content: '# 服务器性能监控配置指南\n\n## 概述\n本文档详细介绍...',
      categoryId: 1,
      tags: ['服务器', '监控'],
      status: 'published',
      visibility: 'public',
      allowComments: true,
      isTop: false
    }

    Object.assign(formData, data)
  } catch (error) {
    ElMessage.error('加载知识数据失败')
  }
}

// 从工单加载知识数据
const loadKnowledgeFromTicket = () => {
  try {
    const ticketData = sessionStorage.getItem('knowledgeFromTicket')
    if (ticketData) {
      const data = JSON.parse(ticketData)
      Object.assign(formData, data)

      // 清除sessionStorage中的数据
      sessionStorage.removeItem('knowledgeFromTicket')

      ElMessage.success('已从工单信息预填充知识内容')
    }
  } catch (error) {
    console.error('加载工单数据失败:', error)
  }
}

// 事件处理
const previewKnowledge = () => {
  previewDialog.value = true
}

const handlePreviewClose = () => {
  previewDialog.value = false
}

const saveAsDraft = async () => {
  formData.status = 'draft'
  await saveKnowledge()
}

const submitKnowledge = async () => {
  if (!await validateForm()) return

  if (isEdit.value) {
    formData.status = 'published'
  } else {
    formData.status = 'review'
  }

  await saveKnowledge()
}

const saveKnowledge = async () => {
  try {
    if (isEdit.value) {
      // await updateKnowledge(route.params.id, formData)
      ElMessage.success('知识更新成功')
    } else {
      // const response = await createKnowledge(formData)
      ElMessage.success('知识创建成功')
      // router.push(`/knowledge/articles/${response.data.id}`)
      router.push('/knowledge/base')
    }
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

const validateForm = async () => {
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    ElMessage.warning('请完善表单信息')
    return false
  }
}

// 编辑器工具栏功能
const insertTemplate = () => {
  templateDialog.value = true
}

const insertTable = () => {
  const tableMarkdown = `
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 内容1 | 内容2 | 内容3 |
| 内容4 | 内容5 | 内容6 |
`
  formData.content += tableMarkdown
}

const insertCode = () => {
  const codeMarkdown = `
\`\`\`javascript
// 在此输入代码
console.log('Hello World');
\`\`\`
`
  formData.content += codeMarkdown
}

const insertImage = () => {
  const imageMarkdown = '![图片描述](图片链接)'
  formData.content += imageMarkdown
}

// 模板相关
const selectTemplate = (template) => {
  selectedTemplate.value = template
}

const confirmTemplate = () => {
  if (selectedTemplate.value) {
    applyTemplate(selectedTemplate.value)
    templateDialog.value = false
    selectedTemplate.value = null
  }
}

const applyTemplate = (template) => {
  if (formData.content && formData.content.trim()) {
    ElMessageBox.confirm(
      '应用模板将覆盖当前内容，是否继续？',
      '确认应用模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      formData.content = template.content
      ElMessage.success('模板应用成功')
    }).catch(() => {
      // 用户取消
    })
  } else {
    formData.content = template.content
    ElMessage.success('模板应用成功')
  }
}

// 文件上传相关
const handleFileChange = (file, fileList) => {
  formData.attachments = fileList
}

const handleFileRemove = (file, fileList) => {
  formData.attachments = fileList
}

const beforeUpload = (file) => {
  const isValidType = [
    'image/jpeg', 'image/png', 'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ].includes(file.type)

  const isValidSize = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('不支持的文件格式')
    return false
  }

  if (!isValidSize) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }

  return true
}

// 工具函数
const getCategoryName = (categoryId) => {
  const category = categories.value.find(c => c.id === categoryId)
  return category ? category.name : ''
}

const formatTime = (time) => {
  return time
}
</script>

<style scoped>
.knowledge-editor {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.breadcrumb {
  margin-bottom: 20px;
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-container {
  max-width: 1400px;
  margin: 0 auto;
}

.knowledge-form {
  margin: 0;
}

/* 编辑器卡片样式 */
.editor-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 基本信息区域 */
.basic-info-section {
  margin-bottom: 32px;
}

/* 内容编辑器区域 */
.content-editor-section {
  margin-bottom: 32px;
}

.editor-toolbar {
  margin-bottom: 12px;
}

.rich-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.content-textarea {
  border: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.content-textarea :deep(.el-textarea__inner) {
  border: none;
  box-shadow: none;
  resize: vertical;
  min-height: 500px;
}

/* 附件区域 */
.attachments-section {
  margin-bottom: 32px;
}

/* 设置卡片样式 */
.settings-card,
.templates-card,
.version-card,
.history-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 模板列表样式 */
.templates-list {
  max-height: 300px;
  overflow-y: auto;
}

.template-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item:hover {
  border-color: #409EFF;
  background-color: #f0f8ff;
}

.template-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.template-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 版本信息样式 */
.version-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

.version-label {
  font-size: 14px;
  color: #606266;
  flex-shrink: 0;
  width: 80px;
}

.version-value {
  font-size: 14px;
  color: #303133;
  flex: 1;
}

/* 操作历史样式 */
.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-action {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.history-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

/* 预览对话框样式 */
.preview-content {
  padding: 20px;
}

.preview-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.preview-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.preview-author {
  font-size: 14px;
  color: #606266;
}

.preview-summary {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.preview-body {
  line-height: 1.8;
  font-size: 16px;
  color: #303133;
}

/* 模板选择对话框样式 */
.template-selection {
  padding: 20px 0;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.template-card:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.template-card.selected {
  border-color: #409EFF;
  background-color: #f0f8ff;
}

.template-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.template-info p {
  font-size: 14px;
  color: #606266;
  margin: 0;
  line-height: 1.4;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-select) {
  width: 100%;
}

/* 上传组件样式 */
:deep(.el-upload-dragger) {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-editor {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .content-textarea :deep(.el-textarea__inner) {
    min-height: 300px;
  }

  .template-selection .el-col {
    margin-bottom: 16px;
  }
}
</style>
