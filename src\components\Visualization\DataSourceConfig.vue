<template>
  <div class="data-source-config">
    <el-form :model="dataConfig" label-width="80px" size="small">
      <!-- 数据源类型选择 -->
      <el-form-item label="数据源">
        <el-select v-model="dataConfig.source" @change="handleSourceChange">
          <el-option label="静态数据" value="static" />
          <el-option label="API接口" value="api" />
          <el-option label="WebSocket" value="websocket" />
          <el-option label="Mock数据" value="mock" />
        </el-select>
      </el-form-item>

      <!-- 静态数据配置 -->
      <template v-if="dataConfig.source === 'static'">
        <el-form-item label="数据内容">
          <el-input
            v-model="dataConfig.data"
            type="textarea"
            :rows="6"
            placeholder="请输入JSON格式的数据"
          />
        </el-form-item>
      </template>

      <!-- API接口配置 -->
      <template v-if="dataConfig.source === 'api'">
        <el-form-item label="请求方法">
          <el-select v-model="dataConfig.method">
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" />
          </el-select>
        </el-form-item>

        <el-form-item label="接口地址">
          <el-input v-model="dataConfig.url" placeholder="https://api.example.com/data" />
        </el-form-item>

        <el-form-item label="请求头">
          <el-input
            v-model="dataConfig.headers"
            type="textarea"
            :rows="3"
            placeholder='{"Content-Type": "application/json"}'
          />
        </el-form-item>

        <el-form-item label="请求参数">
          <el-input
            v-model="dataConfig.params"
            type="textarea"
            :rows="3"
            placeholder='{"page": 1, "size": 10}'
          />
        </el-form-item>

        <el-form-item label="刷新间隔">
          <el-select v-model="dataConfig.interval">
            <el-option label="不刷新" :value="0" />
            <el-option label="1秒" :value="1000" />
            <el-option label="5秒" :value="5000" />
            <el-option label="10秒" :value="10000" />
            <el-option label="30秒" :value="30000" />
            <el-option label="1分钟" :value="60000" />
            <el-option label="5分钟" :value="300000" />
          </el-select>
        </el-form-item>
      </template>

      <!-- WebSocket配置 -->
      <template v-if="dataConfig.source === 'websocket'">
        <el-form-item label="WebSocket地址">
          <el-input v-model="dataConfig.url" placeholder="ws://localhost:8080/data" />
        </el-form-item>

        <el-form-item label="初始化消息">
          <el-input
            v-model="dataConfig.initMessage"
            type="textarea"
            :rows="3"
            placeholder='{"type": "subscribe", "channel": "data"}'
          />
        </el-form-item>

        <el-form-item label="自动重连">
          <el-switch v-model="dataConfig.autoReconnect" />
        </el-form-item>

        <el-form-item label="重连间隔" v-if="dataConfig.autoReconnect">
          <el-input-number v-model="dataConfig.reconnectInterval" :min="1000" :step="1000" />
          <span style="margin-left: 8px;">毫秒</span>
        </el-form-item>
      </template>

      <!-- Mock数据配置 -->
      <template v-if="dataConfig.source === 'mock'">
        <el-form-item label="数据类型">
          <el-select v-model="dataConfig.mockType">
            <el-option label="时间序列" value="timeSeries" />
            <el-option label="分类数据" value="category" />
            <el-option label="数字数据" value="number" />
            <el-option label="表格数据" value="table" />
            <el-option label="随机数据" value="random" />
          </el-select>
        </el-form-item>

        <el-form-item label="更新间隔">
          <el-select v-model="dataConfig.interval">
            <el-option label="不更新" :value="0" />
            <el-option label="1秒" :value="1000" />
            <el-option label="2秒" :value="2000" />
            <el-option label="5秒" :value="5000" />
            <el-option label="10秒" :value="10000" />
          </el-select>
        </el-form-item>

        <!-- Mock数据选项 -->
        <template v-if="dataConfig.mockType === 'timeSeries'">
          <el-form-item label="数据点数">
            <el-input-number v-model="dataConfig.mockOptions.count" :min="5" :max="100" />
          </el-form-item>
          <el-form-item label="基准值">
            <el-input-number v-model="dataConfig.mockOptions.baseValue" />
          </el-form-item>
          <el-form-item label="波动范围">
            <el-input-number v-model="dataConfig.mockOptions.variance" />
          </el-form-item>
        </template>

        <template v-if="dataConfig.mockType === 'category'">
          <el-form-item label="分类">
            <el-input v-model="dataConfig.mockOptions.categories" placeholder="A,B,C,D,E" />
          </el-form-item>
          <el-form-item label="最小值">
            <el-input-number v-model="dataConfig.mockOptions.minValue" />
          </el-form-item>
          <el-form-item label="最大值">
            <el-input-number v-model="dataConfig.mockOptions.maxValue" />
          </el-form-item>
        </template>

        <template v-if="dataConfig.mockType === 'number'">
          <el-form-item label="最小值">
            <el-input-number v-model="dataConfig.mockOptions.min" />
          </el-form-item>
          <el-form-item label="最大值">
            <el-input-number v-model="dataConfig.mockOptions.max" />
          </el-form-item>
          <el-form-item label="小数位">
            <el-input-number v-model="dataConfig.mockOptions.decimals" :min="0" :max="4" />
          </el-form-item>
        </template>

        <template v-if="dataConfig.mockType === 'table'">
          <el-form-item label="行数">
            <el-input-number v-model="dataConfig.mockOptions.rows" :min="1" :max="100" />
          </el-form-item>
          <el-form-item label="列名">
            <el-input v-model="dataConfig.mockOptions.columns" placeholder="name,value,status" />
          </el-form-item>
        </template>
      </template>

      <!-- 字段映射配置 -->
      <el-divider content-position="left">字段映射</el-divider>
      
      <el-form-item label="数据映射">
        <el-input
          v-model="dataConfig.mapping"
          type="textarea"
          :rows="4"
          placeholder='{"xAxis": "date", "yAxis": "value", "series": "category"}'
        />
        <div class="mapping-help">
          <el-text size="small" type="info">
            配置数据字段到图表属性的映射关系，支持嵌套字段（如：data.list.0.value）
          </el-text>
        </div>
      </el-form-item>

      <!-- 测试按钮 -->
      <el-form-item>
        <el-button type="primary" @click="testDataSource" :loading="testing">
          测试数据源
        </el-button>
        <el-button @click="previewData" v-if="lastTestData">
          预览数据
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 数据预览对话框 -->
    <el-dialog v-model="showPreview" title="数据预览" width="60%">
      <el-scrollbar height="400px">
        <pre class="data-preview">{{ formattedPreviewData }}</pre>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { dataSourceManager } from '@/utils/dataSourceManager'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  componentId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:modelValue'])

// 数据配置
const dataConfig = reactive({
  source: 'static',
  data: '[]',
  method: 'GET',
  url: '',
  headers: '{}',
  params: '{}',
  interval: 0,
  initMessage: '{}',
  autoReconnect: true,
  reconnectInterval: 5000,
  mockType: 'timeSeries',
  mockOptions: {
    count: 24,
    baseValue: 100,
    variance: 20,
    categories: 'A,B,C,D,E',
    minValue: 10,
    maxValue: 100,
    min: 0,
    max: 100,
    decimals: 0,
    rows: 10,
    columns: 'name,value,status'
  },
  mapping: '{}'
})

// 测试相关
const testing = ref(false)
const lastTestData = ref(null)
const showPreview = ref(false)

// 格式化预览数据
const formattedPreviewData = computed(() => {
  return lastTestData.value ? JSON.stringify(lastTestData.value, null, 2) : ''
})

// 监听配置变化
watch(dataConfig, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
}, { deep: true })

// 初始化配置
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(dataConfig, newValue)
  }
}, { immediate: true })

// 数据源类型变化处理
const handleSourceChange = (source) => {
  // 重置相关配置
  switch (source) {
    case 'static':
      dataConfig.data = '[]'
      break
    case 'api':
      dataConfig.method = 'GET'
      dataConfig.url = ''
      dataConfig.interval = 30000
      break
    case 'websocket':
      dataConfig.url = ''
      dataConfig.autoReconnect = true
      break
    case 'mock':
      dataConfig.mockType = 'timeSeries'
      dataConfig.interval = 2000
      break
  }
}

// 测试数据源
const testDataSource = async () => {
  testing.value = true
  
  try {
    // 准备测试配置
    const testConfig = { ...dataConfig }
    
    // 解析JSON字符串
    if (testConfig.headers) {
      testConfig.headers = JSON.parse(testConfig.headers)
    }
    if (testConfig.params) {
      testConfig.params = JSON.parse(testConfig.params)
    }
    if (testConfig.initMessage) {
      testConfig.initMessage = JSON.parse(testConfig.initMessage)
    }
    if (testConfig.mapping) {
      testConfig.mapping = JSON.parse(testConfig.mapping)
    }
    if (testConfig.data && typeof testConfig.data === 'string') {
      testConfig.data = JSON.parse(testConfig.data)
    }
    
    // 处理Mock数据选项
    if (testConfig.mockType && testConfig.mockOptions) {
      if (testConfig.mockOptions.categories && typeof testConfig.mockOptions.categories === 'string') {
        testConfig.mockOptions.categories = testConfig.mockOptions.categories.split(',')
      }
      if (testConfig.mockOptions.columns && typeof testConfig.mockOptions.columns === 'string') {
        testConfig.mockOptions.columns = testConfig.mockOptions.columns.split(',')
      }
    }

    // 创建临时数据源进行测试
    const testId = `test_${Date.now()}`
    
    // 监听数据更新
    const handleDataUpdate = (event) => {
      if (event.detail.componentId === testId) {
        lastTestData.value = event.detail.data
        ElMessage.success('数据源测试成功')
        window.removeEventListener('dataUpdate', handleDataUpdate)
        dataSourceManager.removeDataSource(testId)
      }
    }
    
    const handleDataError = (event) => {
      if (event.detail.componentId === testId) {
        ElMessage.error(`数据源测试失败: ${event.detail.error.message}`)
        window.removeEventListener('dataError', handleDataError)
        dataSourceManager.removeDataSource(testId)
      }
    }

    window.addEventListener('dataUpdate', handleDataUpdate)
    window.addEventListener('dataError', handleDataError)

    // 注册测试数据源
    dataSourceManager.registerDataSource(testId, testConfig)

    // 设置超时清理
    setTimeout(() => {
      window.removeEventListener('dataUpdate', handleDataUpdate)
      window.removeEventListener('dataError', handleDataError)
      dataSourceManager.removeDataSource(testId)
      if (testing.value) {
        ElMessage.warning('数据源测试超时')
      }
    }, 10000)

  } catch (error) {
    console.error('测试数据源失败:', error)
    ElMessage.error(`配置解析失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

// 预览数据
const previewData = () => {
  showPreview.value = true
}
</script>

<style scoped>
.data-source-config {
  padding: 16px;
}

.mapping-help {
  margin-top: 8px;
}

.data-preview {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

.el-divider {
  margin: 24px 0 16px 0;
}
</style>
