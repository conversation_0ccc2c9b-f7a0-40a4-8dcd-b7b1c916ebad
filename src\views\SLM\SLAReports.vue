<template>
  <div class="sla-reports">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>SLA报告</h2>
          <p>服务级别协议达成情况分析与报告生成</p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Document" @click="generateReport" :loading="generating">
            生成报告
          </el-button>
          <el-button :icon="Download" @click="exportReport">
            导出报告
          </el-button>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="报告周期">
            <el-select v-model="filterForm.period" @change="handlePeriodChange">
              <el-option label="本月" value="monthly" />
              <el-option label="本季度" value="quarterly" />
              <el-option label="本年度" value="yearly" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-form-item>
          <el-form-item label="服务" v-if="filterForm.period !== 'custom'">
            <el-select v-model="filterForm.serviceId" clearable placeholder="全部服务">
              <el-option
                v-for="service in services"
                :key="service.id"
                :label="service.name"
                :value="service.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="协议类型">
            <el-select v-model="filterForm.agreementType" clearable placeholder="全部类型">
              <el-option label="SLA" value="SLA" />
              <el-option label="OLA" value="OLA" />
              <el-option label="UC" value="UC" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="filterForm.period === 'custom'" label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadReports">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 报告概览 -->
    <div class="overview-section" v-if="reports.length > 0">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon">
                <el-icon :size="32" color="#409EFF">
                  <Document />
                </el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-value">{{ reportSummary.totalAgreements }}</div>
                <div class="overview-label">协议总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon">
                <el-icon :size="32" color="#67C23A">
                  <SuccessFilled />
                </el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-value">{{ reportSummary.avgCompliance }}%</div>
                <div class="overview-label">平均达成率</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon">
                <el-icon :size="32" color="#E6A23C">
                  <Warning />
                </el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-value">{{ reportSummary.atRiskCount }}</div>
                <div class="overview-label">风险协议</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon">
                <el-icon :size="32" color="#F56C6C">
                  <TrendCharts />
                </el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-value">{{ reportSummary.trendDirection }}</div>
                <div class="overview-label">整体趋势</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 报告列表 -->
    <div class="reports-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>SLA报告详情</span>
            <div class="header-actions">
              <el-button type="text" @click="toggleView">
                <el-icon><component :is="viewMode === 'list' ? 'Grid' : 'List'" /></el-icon>
                {{ viewMode === 'list' ? '卡片视图' : '列表视图' }}
              </el-button>
            </div>
          </div>
        </template>

        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="list-view">
          <el-table :data="reports" v-loading="loading" stripe>
            <el-table-column prop="serviceName" label="服务名称" width="150" />
            <el-table-column prop="agreementName" label="协议名称" min-width="200" />
            <el-table-column prop="type" label="类型" width="80">
              <template #default="{ row }">
                <el-tag :type="getAgreementTypeColor(row.type)">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="overallCompliance" label="达成率" width="100">
              <template #default="{ row }">
                <div class="compliance-cell">
                  <span :class="getComplianceClass(row.overallCompliance)">
                    {{ row.overallCompliance }}%
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="KPI详情" min-width="300">
              <template #default="{ row }">
                <div class="kpi-details">
                  <div
                    v-for="kpi in row.kpiReports.slice(0, 3)"
                    :key="kpi.name"
                    class="kpi-item"
                  >
                    <span class="kpi-name">{{ kpi.name }}:</span>
                    <span :class="getComplianceClass(kpi.compliance ? 100 : 0)">
                      {{ kpi.actualValue }}{{ kpi.unit }}
                    </span>
                    <span class="kpi-target">(目标: {{ kpi.targetValue }}{{ kpi.unit }})</span>
                  </div>
                  <span v-if="row.kpiReports.length > 3" class="more-kpis">
                    +{{ row.kpiReports.length - 3 }}个指标
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="generatedAt" label="生成时间" width="160" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="viewReportDetail(row)">
                  查看详情
                </el-button>
                <el-button type="text" size="small" @click="exportSingleReport(row)">
                  导出
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <el-row :gutter="20">
            <el-col
              v-for="report in reports"
              :key="report.agreementId"
              :xs="24"
              :sm="12"
              :lg="8"
            >
              <el-card class="report-card" @click="viewReportDetail(report)">
                <div class="report-header">
                  <div class="report-title">{{ report.serviceName }}</div>
                  <el-tag :type="getAgreementTypeColor(report.type)">{{ report.type }}</el-tag>
                </div>
                <div class="report-subtitle">{{ report.agreementName }}</div>

                <div class="compliance-section">
                  <div class="compliance-chart">
                    <el-progress
                      type="circle"
                      :percentage="report.overallCompliance"
                      :status="getProgressStatus(report.overallCompliance)"
                      :width="80"
                    />
                  </div>
                  <div class="compliance-info">
                    <div class="compliance-label">整体达成率</div>
                    <div class="compliance-value">{{ report.overallCompliance }}%</div>
                  </div>
                </div>

                <div class="kpi-summary">
                  <div
                    v-for="kpi in report.kpiReports.slice(0, 2)"
                    :key="kpi.name"
                    class="kpi-row"
                  >
                    <span class="kpi-name">{{ kpi.name }}</span>
                    <span :class="getComplianceClass(kpi.compliance ? 100 : 0)">
                      {{ kpi.actualValue }}{{ kpi.unit }}
                    </span>
                  </div>
                </div>

                <div class="report-footer">
                  <span class="report-time">{{ report.generatedAt }}</span>
                  <el-button type="text" size="small" @click.stop="exportSingleReport(report)">
                    导出
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 空状态 -->
        <div v-if="reports.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无报告数据">
            <el-button type="primary" @click="generateReport">生成报告</el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 报告详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="selectedReport?.serviceName + ' - SLA报告详情'"
      width="80%"
      top="5vh"
    >
      <div v-if="selectedReport" class="report-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="服务名称">{{ selectedReport.serviceName }}</el-descriptions-item>
            <el-descriptions-item label="协议名称">{{ selectedReport.agreementName }}</el-descriptions-item>
            <el-descriptions-item label="协议类型">
              <el-tag :type="getAgreementTypeColor(selectedReport.type)">{{ selectedReport.type }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="报告周期">{{ selectedReport.period }}</el-descriptions-item>
            <el-descriptions-item label="整体达成率">
              <span :class="getComplianceClass(selectedReport.overallCompliance)">
                {{ selectedReport.overallCompliance }}%
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="生成时间">{{ selectedReport.generatedAt }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- KPI详情 -->
        <div class="detail-section">
          <h3>KPI指标详情</h3>
          <el-table :data="selectedReport.kpiReports" border>
            <el-table-column prop="name" label="指标名称" width="150" />
            <el-table-column prop="targetValue" label="目标值" width="100">
              <template #default="{ row }">
                {{ row.targetValue }}{{ row.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="actualValue" label="实际值" width="100">
              <template #default="{ row }">
                {{ row.actualValue }}{{ row.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="deviation" label="偏差" width="100">
              <template #default="{ row }">
                <span :class="row.deviation >= 0 ? 'positive' : 'negative'">
                  {{ row.deviation > 0 ? '+' : '' }}{{ row.deviation }}{{ row.unit }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="compliance" label="达成状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.compliance ? 'success' : 'danger'">
                  {{ row.compliance ? '达成' : '未达成' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="趋势图" min-width="200">
              <template #default="{ row }">
                <div class="trend-mini-chart" :ref="el => setTrendChartRef(el, row.name)"></div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="exportSingleReport(selectedReport)">
            导出此报告
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document, Download, Refresh, SuccessFilled, Warning, TrendCharts,
  Grid, List
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getSLAReports,
  getServiceList
} from '@/api/slmApi.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const generating = ref(false)
const viewMode = ref('list') // list | card

// 筛选表单
const filterForm = reactive({
  period: 'monthly',
  serviceId: '',
  agreementType: '',
  dateRange: []
})

// 数据
const services = ref([])
const reports = ref([])
const selectedReport = ref(null)
const showDetailDialog = ref(false)

// 报告汇总
const reportSummary = reactive({
  totalAgreements: 0,
  avgCompliance: 0,
  atRiskCount: 0,
  trendDirection: '稳定'
})

// 趋势图表引用
const trendChartRefs = ref(new Map())

// 加载服务列表
const loadServices = async () => {
  try {
    const response = await getServiceList({ pageSize: 100 })
    if (response.success) {
      services.value = response.data.list
    }
  } catch (error) {
    ElMessage.error('加载服务列表失败')
    console.error('Load services error:', error)
  }
}

// 加载报告数据
const loadReports = async () => {
  loading.value = true
  try {
    const params = {
      period: filterForm.period,
      serviceId: filterForm.serviceId,
      agreementType: filterForm.agreementType
    }

    if (filterForm.period === 'custom' && filterForm.dateRange.length === 2) {
      params.startDate = filterForm.dateRange[0]
      params.endDate = filterForm.dateRange[1]
    }

    const response = await getSLAReports(params)
    if (response.success) {
      reports.value = response.data
      calculateSummary()
    }
  } catch (error) {
    ElMessage.error('加载报告数据失败')
    console.error('Load reports error:', error)
  } finally {
    loading.value = false
  }
}

// 计算汇总数据
const calculateSummary = () => {
  if (reports.value.length === 0) {
    Object.assign(reportSummary, {
      totalAgreements: 0,
      avgCompliance: 0,
      atRiskCount: 0,
      trendDirection: '稳定'
    })
    return
  }

  const totalCompliance = reports.value.reduce((sum, report) => sum + report.overallCompliance, 0)
  const avgCompliance = Math.round(totalCompliance / reports.value.length)
  const atRiskCount = reports.value.filter(report => report.overallCompliance < 95).length

  // 简单的趋势分析
  let trendDirection = '稳定'
  if (avgCompliance >= 98) {
    trendDirection = '优秀'
  } else if (avgCompliance >= 95) {
    trendDirection = '良好'
  } else if (avgCompliance >= 90) {
    trendDirection = '一般'
  } else {
    trendDirection = '需改进'
  }

  Object.assign(reportSummary, {
    totalAgreements: reports.value.length,
    avgCompliance,
    atRiskCount,
    trendDirection
  })
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadServices(),
    loadReports()
  ])
}

// 处理周期变化
const handlePeriodChange = () => {
  if (filterForm.period !== 'custom') {
    filterForm.dateRange = []
  }
}

// 重置筛选条件
const resetFilter = () => {
  Object.assign(filterForm, {
    period: 'monthly',
    serviceId: '',
    agreementType: '',
    dateRange: []
  })
  loadReports()
}

// 切换视图模式
const toggleView = () => {
  viewMode.value = viewMode.value === 'list' ? 'card' : 'list'
}

// 获取协议类型颜色
const getAgreementTypeColor = (type) => {
  switch (type) {
    case 'SLA':
      return 'primary'
    case 'OLA':
      return 'success'
    case 'UC':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取达成率样式类
const getComplianceClass = (compliance) => {
  if (compliance >= 98) return 'excellent'
  if (compliance >= 95) return 'good'
  if (compliance >= 90) return 'fair'
  return 'poor'
}

// 获取进度条状态
const getProgressStatus = (compliance) => {
  if (compliance >= 95) return 'success'
  if (compliance >= 90) return 'warning'
  return 'exception'
}

// 查看报告详情
const viewReportDetail = (report) => {
  selectedReport.value = report
  showDetailDialog.value = true

  // 延迟初始化趋势图表
  nextTick(() => {
    initTrendCharts()
  })
}

// 设置趋势图表引用
const setTrendChartRef = (el, kpiName) => {
  if (el) {
    trendChartRefs.value.set(kpiName, el)
  }
}

// 初始化趋势图表
const initTrendCharts = () => {
  if (!selectedReport.value) return

  selectedReport.value.kpiReports.forEach(kpi => {
    const chartEl = trendChartRefs.value.get(kpi.name)
    if (!chartEl || !kpi.trendData) return

    const chart = echarts.init(chartEl)

    const option = {
      grid: {
        left: 10,
        right: 10,
        top: 10,
        bottom: 20
      },
      xAxis: {
        type: 'category',
        data: kpi.trendData.map(item => item.month),
        axisLabel: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        show: false
      },
      series: [
        {
          type: 'line',
          data: kpi.trendData.map(item => item.value),
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: kpi.compliance ? '#67c23a' : '#f56c6c',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: kpi.compliance ? 'rgba(103, 194, 58, 0.3)' : 'rgba(245, 108, 108, 0.3)' },
                { offset: 1, color: kpi.compliance ? 'rgba(103, 194, 58, 0.1)' : 'rgba(245, 108, 108, 0.1)' }
              ]
            }
          }
        }
      ]
    }

    chart.setOption(option)

    // 响应式调整
    const resizeObserver = new ResizeObserver(() => {
      chart.resize()
    })
    resizeObserver.observe(chartEl)
  })
}

// 生成报告
const generateReport = async () => {
  generating.value = true
  try {
    // 模拟报告生成过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('报告生成成功')
    await loadReports()
  } catch (error) {
    ElMessage.error('报告生成失败')
    console.error('Generate report error:', error)
  } finally {
    generating.value = false
  }
}

// 导出报告
const exportReport = () => {
  if (reports.value.length === 0) {
    ElMessage.warning('暂无报告数据可导出')
    return
  }

  // 模拟导出功能
  ElMessage.success('报告导出成功')
}

// 导出单个报告
const exportSingleReport = (report) => {
  // 模拟单个报告导出
  ElMessage.success(`${report.serviceName} 报告导出成功`)
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.sla-reports {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

/* 概览区域 */
.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 8px;
}

.overview-icon {
  margin-right: 16px;
}

.overview-info {
  flex: 1;
}

.overview-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #666;
}

/* 报告区域 */
.reports-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 列表视图 */
.list-view {
  min-height: 400px;
}

.compliance-cell {
  text-align: center;
}

.kpi-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.kpi-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.kpi-name {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

.kpi-target {
  color: #999;
  font-size: 12px;
}

.more-kpis {
  color: #409eff;
  font-size: 12px;
  cursor: pointer;
}

/* 卡片视图 */
.card-view {
  min-height: 400px;
}

.report-card {
  height: 280px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.report-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.report-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.compliance-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.compliance-info {
  flex: 1;
}

.compliance-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.compliance-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.kpi-summary {
  margin-bottom: 16px;
}

.kpi-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
}

.kpi-row .kpi-name {
  color: #666;
}

.report-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.report-time {
  font-size: 12px;
  color: #999;
}

/* 达成率样式 */
.excellent {
  color: #67c23a;
  font-weight: 600;
}

.good {
  color: #409eff;
  font-weight: 600;
}

.fair {
  color: #e6a23c;
  font-weight: 600;
}

.poor {
  color: #f56c6c;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 报告详情 */
.report-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.trend-mini-chart {
  height: 40px;
  width: 100%;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sla-reports {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
  }

  .overview-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .overview-icon {
    margin-right: 0;
  }

  .compliance-section {
    flex-direction: column;
    text-align: center;
  }

  .report-footer {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}

/* 对话框样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-descriptions-item__label) {
  font-weight: 500;
}

:deep(.el-table .cell) {
  padding: 8px;
}
</style>
