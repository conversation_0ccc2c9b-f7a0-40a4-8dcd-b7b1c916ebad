:root {
  /* 颜色定义 */
  --primary-color: #1a3a8f;
  --secondary-color: #0c2461;
  --accent-color: #00F5FF;
  --warning-color: #FFAA33;
  --error-color: #FF3E3E;
  --success-color: #00FFA3;
  --text-primary: #FFFFFF;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --bg-primary: #0a1a3a;
  --bg-secondary: rgba(16, 42, 87, 0.7);
  
  /* 间距和尺寸 */
  --border-radius: 12px;
  --spacing-unit: 16px;
  --card-padding: 20px;
  --section-gap: 24px;
  
  /* 视觉效果 */
  --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  --transition-speed: 0.3s;
  --hover-elevation: 0 15px 30px rgba(0, 0, 0, 0.4);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  padding: var(--spacing-unit);
}

/* 布局容器 */
.layout-container {
  display: grid;
  gap: var(--section-gap);
  max-width: 1920px;
  margin: 0 auto;
}

/* 卡片样式 */
.card {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: var(--card-padding);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-speed) ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--hover-elevation);
}

/* 标题样式 */
.section-title {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-unit);
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--accent-color);
}

/* 响应式调整 */
@media (max-width: 1600px) {
  :root {
    --spacing-unit: 12px;
    --card-padding: 16px;
    --section-gap: 20px;
  }
}

@media (max-width: 1200px) {
  :root {
    --spacing-unit: 8px;
    --card-padding: 12px;
    --section-gap: 16px;
  }
  
  body {
    padding: 8px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}