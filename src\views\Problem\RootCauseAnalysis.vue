<template>
  <div class="root-cause-analysis">
    <div class="page-header">
      <div class="header-info">
        <h2>根本原因分析</h2>
        <p>深入分析问题根源，制定预防措施</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createAnalysis">
          <el-icon><Plus /></el-icon>
          新建分析
        </el-button>
        <el-button @click="showTemplateDialog = true">
          <el-icon><Document /></el-icon>
          分析模板
        </el-button>
        <el-button @click="showReportDialog = true">
          <el-icon><TrendCharts /></el-icon>
          分析报告
        </el-button>
      </div>
    </div>

    <!-- RCA工具选择 -->
    <div class="rca-tools-overview">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :lg="6" v-for="tool in rcaTools" :key="tool.key">
          <el-card class="tool-card" @click="openTool(tool.key)">
            <div class="tool-content">
              <div class="tool-icon">
                <el-icon :size="32" :color="tool.color">
                  <component :is="tool.icon" />
                </el-icon>
              </div>
              <div class="tool-info">
                <div class="tool-name">{{ tool.name }}</div>
                <div class="tool-description">{{ tool.description }}</div>
                <div class="tool-usage">已使用 {{ tool.usageCount }} 次</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分析进度跟踪 -->
    <div class="analysis-progress" v-if="currentAnalysis">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>当前分析: {{ currentAnalysis.title }}</span>
            <el-tag :type="getStatusColor(currentAnalysis.status)">{{ currentAnalysis.status }}</el-tag>
          </div>
        </template>
        <div class="progress-content">
          <el-steps :active="currentAnalysis.step" finish-status="success">
            <el-step title="问题定义" description="明确问题范围和影响" />
            <el-step title="数据收集" description="收集相关证据和信息" />
            <el-step title="原因分析" description="使用分析工具找出根因" />
            <el-step title="解决方案" description="制定预防和纠正措施" />
            <el-step title="验证跟踪" description="验证措施有效性" />
          </el-steps>
          <div class="progress-actions">
            <el-button @click="prevStep" :disabled="currentAnalysis.step === 0">上一步</el-button>
            <el-button type="primary" @click="nextStep" :disabled="currentAnalysis.step === 4">下一步</el-button>
            <el-button type="success" @click="completeAnalysis">完成分析</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <div class="analysis-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>分析历史与管理</span>
            <div class="header-controls">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索分析..."
                prefix-icon="Search"
                size="small"
                style="width: 200px; margin-right: 12px;"
              />
              <el-select v-model="filterType" placeholder="分析类型" size="small" style="width: 120px; margin-right: 12px;">
                <el-option label="全部" value="all" />
                <el-option label="鱼骨图" value="fishbone" />
                <el-option label="5Why" value="5why" />
                <el-option label="故障树" value="faulttree" />
                <el-option label="时间线" value="timeline" />
              </el-select>
              <el-button type="primary" size="small" @click="exportAnalyses">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </div>
        </template>

        <!-- 分析历史 -->
        <div class="analysis-history">
          <el-table :data="filteredAnalyses" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="分析ID" width="120" />
            <el-table-column prop="problemTitle" label="问题标题" min-width="200" />
            <el-table-column prop="analysisType" label="分析类型" width="120">
              <template #default="scope">
                <el-tag :type="getAnalysisTypeColor(scope.row.analysisType)" size="small">
                  {{ scope.row.analysisType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusColor(scope.row.status)" size="small">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="100">
              <template #default="scope">
                <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
                  {{ scope.row.priority }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="analyst" label="分析师" width="100" />
            <el-table-column prop="progress" label="进度" width="120">
              <template #default="scope">
                <el-progress :percentage="scope.row.progress" :show-text="false" />
                <span style="margin-left: 8px;">{{ scope.row.progress }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160" />
            <el-table-column label="操作" width="250">
              <template #default="scope">
                <el-button type="primary" size="small" text @click="viewAnalysis(scope.row)">
                  查看
                </el-button>
                <el-button type="primary" size="small" text @click="editAnalysis(scope.row)">
                  编辑
                </el-button>
                <el-button type="success" size="small" text @click="continueAnalysis(scope.row)" v-if="scope.row.status === '进行中'">
                  继续
                </el-button>
                <el-dropdown @command="handleAnalysisCommand">
                  <el-button type="primary" size="small" text>
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'duplicate', analysis: scope.row}">复制</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'export', analysis: scope.row}">导出</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'archive', analysis: scope.row}">归档</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', analysis: scope.row}" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50]"
              :total="filteredAnalyses.length"
              layout="total, sizes, prev, pager, next, jumper"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 鱼骨图分析对话框 -->
    <el-dialog v-model="showFishboneDialog" title="鱼骨图分析" width="80%" :before-close="handleClose">
      <div class="fishbone-analysis">
        <div class="problem-statement">
          <el-form :model="fishboneForm" label-width="100px">
            <el-form-item label="问题描述">
              <el-input v-model="fishboneForm.problem" placeholder="请输入要分析的问题" />
            </el-form-item>
          </el-form>
        </div>
        <div class="fishbone-categories">
          <h4>原因分类</h4>
          <el-row :gutter="20">
            <el-col :span="12" v-for="category in fishboneCategories" :key="category.name">
              <el-card class="category-card">
                <template #header>
                  <span>{{ category.name }}</span>
                  <el-button type="text" size="small" @click="addCause(category.name)">
                    <el-icon><Plus /></el-icon>
                  </el-button>
                </template>
                <div class="causes-list">
                  <el-tag
                    v-for="(cause, index) in category.causes"
                    :key="index"
                    closable
                    @close="removeCause(category.name, index)"
                    class="cause-tag"
                  >
                    {{ cause }}
                  </el-tag>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showFishboneDialog = false">取消</el-button>
          <el-button type="primary" @click="saveFishboneAnalysis">保存分析</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 5Why分析对话框 -->
    <el-dialog v-model="showFiveWhyDialog" title="5Why分析" width="60%" :before-close="handleClose">
      <div class="five-why-analysis">
        <el-form :model="fiveWhyForm" label-width="80px">
          <el-form-item label="问题">
            <el-input v-model="fiveWhyForm.problem" placeholder="请输入要分析的问题" />
          </el-form-item>
          <div v-for="(why, index) in fiveWhyForm.whys" :key="index" class="why-item">
            <el-form-item :label="`为什么${index + 1}`">
              <el-input v-model="why.question" placeholder="为什么会发生这个问题？" />
            </el-form-item>
            <el-form-item :label="`答案${index + 1}`">
              <el-input v-model="why.answer" placeholder="请输入答案" />
            </el-form-item>
          </div>
          <el-form-item label="根本原因">
            <el-input
              v-model="fiveWhyForm.rootCause"
              type="textarea"
              :rows="3"
              placeholder="基于以上分析，总结根本原因"
            />
          </el-form-item>
          <el-form-item label="预防措施">
            <el-input
              v-model="fiveWhyForm.preventiveMeasures"
              type="textarea"
              :rows="3"
              placeholder="制定预防措施"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showFiveWhyDialog = false">取消</el-button>
          <el-button type="primary" @click="saveFiveWhyAnalysis">保存分析</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 故障树分析对话框 -->
    <el-dialog v-model="showFaultTreeDialog" title="故障树分析" width="70%" :before-close="handleClose">
      <div class="fault-tree-analysis">
        <div class="tree-builder">
          <el-form :model="faultTreeForm" label-width="100px">
            <el-form-item label="顶层事件">
              <el-input v-model="faultTreeForm.topEvent" placeholder="请输入顶层故障事件" />
            </el-form-item>
            <el-form-item label="中间事件">
              <el-input
                v-model="faultTreeForm.intermediateEvents"
                type="textarea"
                :rows="3"
                placeholder="请输入中间事件（每行一个）"
              />
            </el-form-item>
            <el-form-item label="基本事件">
              <el-input
                v-model="faultTreeForm.basicEvents"
                type="textarea"
                :rows="3"
                placeholder="请输入基本事件（每行一个）"
              />
            </el-form-item>
            <el-form-item label="逻辑关系">
              <el-radio-group v-model="faultTreeForm.logicGate">
                <el-radio label="AND">AND门（所有事件都发生）</el-radio>
                <el-radio label="OR">OR门（任一事件发生）</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="分析结论">
              <el-input
                v-model="faultTreeForm.conclusion"
                type="textarea"
                :rows="3"
                placeholder="请输入分析结论"
              />
            </el-form-item>
          </el-form>

          <!-- 故障树可视化区域 -->
          <div class="fault-tree-visual" ref="faultTreeChart"></div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showFaultTreeDialog = false">取消</el-button>
          <el-button @click="generateFaultTree">生成故障树</el-button>
          <el-button type="primary" @click="saveFaultTreeAnalysis">保存分析</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 时间线分析对话框 -->
    <el-dialog v-model="showTimelineDialog" title="时间线分析" width="80%" :before-close="handleClose">
      <div class="timeline-analysis">
        <div class="timeline-controls">
          <el-form :model="timelineForm" :inline="true">
            <el-form-item label="问题描述">
              <el-input v-model="timelineForm.problem" placeholder="请输入问题描述" style="width: 300px;" />
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="timelineForm.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addTimelineEvent">
                <el-icon><Plus /></el-icon>
                添加事件
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="timeline-events">
          <el-timeline>
            <el-timeline-item
              v-for="(event, index) in timelineForm.events"
              :key="index"
              :timestamp="event.time"
              :type="getEventType(event.type)"
              :icon="getEventIcon(event.type)"
            >
              <div class="timeline-event">
                <div class="event-header">
                  <span class="event-title">{{ event.title }}</span>
                  <el-tag :type="getEventType(event.type)" size="small">{{ event.type }}</el-tag>
                  <el-button type="danger" size="small" text @click="removeTimelineEvent(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <div class="event-description">{{ event.description }}</div>
                <div class="event-impact" v-if="event.impact">
                  <strong>影响：</strong>{{ event.impact }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <div class="timeline-analysis-result" v-if="timelineForm.analysis">
          <h4>时间线分析结果</h4>
          <el-input
            v-model="timelineForm.analysis"
            type="textarea"
            :rows="4"
            placeholder="基于时间线分析，总结问题发生的时序关系和根本原因"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTimelineDialog = false">取消</el-button>
          <el-button @click="analyzeTimeline">分析时间线</el-button>
          <el-button type="primary" @click="saveTimelineAnalysis">保存分析</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加时间线事件对话框 -->
    <el-dialog v-model="showAddEventDialog" title="添加时间线事件" width="50%">
      <el-form :model="eventForm" :rules="eventRules" ref="eventFormRef" label-width="100px">
        <el-form-item label="事件时间" prop="time">
          <el-date-picker
            v-model="eventForm.time"
            type="datetime"
            placeholder="选择事件发生时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="事件标题" prop="title">
          <el-input v-model="eventForm.title" placeholder="请输入事件标题" />
        </el-form-item>
        <el-form-item label="事件类型" prop="type">
          <el-select v-model="eventForm.type" placeholder="选择事件类型" style="width: 100%">
            <el-option label="故障发生" value="failure" />
            <el-option label="告警触发" value="alert" />
            <el-option label="操作执行" value="operation" />
            <el-option label="配置变更" value="change" />
            <el-option label="恢复正常" value="recovery" />
          </el-select>
        </el-form-item>
        <el-form-item label="事件描述" prop="description">
          <el-input
            v-model="eventForm.description"
            type="textarea"
            :rows="3"
            placeholder="详细描述事件内容"
          />
        </el-form-item>
        <el-form-item label="影响范围">
          <el-input v-model="eventForm.impact" placeholder="描述事件的影响范围" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddEventDialog = false">取消</el-button>
          <el-button type="primary" @click="saveTimelineEvent">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分析模板对话框 -->
    <el-dialog v-model="showTemplateDialog" title="分析模板" width="60%">
      <div class="template-content">
        <el-tabs v-model="activeTemplateTab">
          <el-tab-pane label="模板库" name="library">
            <div class="template-library">
              <el-row :gutter="20">
                <el-col :span="8" v-for="template in analysisTemplates" :key="template.id">
                  <el-card class="template-card" @click="selectTemplate(template)">
                    <div class="template-info">
                      <div class="template-name">{{ template.name }}</div>
                      <div class="template-description">{{ template.description }}</div>
                      <div class="template-type">
                        <el-tag size="small">{{ template.type }}</el-tag>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="自定义模板" name="custom">
            <div class="custom-template">
              <el-form :model="customTemplate" label-width="100px">
                <el-form-item label="模板名称">
                  <el-input v-model="customTemplate.name" placeholder="请输入模板名称" />
                </el-form-item>
                <el-form-item label="模板类型">
                  <el-select v-model="customTemplate.type" placeholder="选择模板类型">
                    <el-option label="鱼骨图" value="fishbone" />
                    <el-option label="5Why" value="5why" />
                    <el-option label="故障树" value="faulttree" />
                    <el-option label="时间线" value="timeline" />
                  </el-select>
                </el-form-item>
                <el-form-item label="模板描述">
                  <el-input
                    v-model="customTemplate.description"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入模板描述"
                  />
                </el-form-item>
                <el-form-item label="模板内容">
                  <el-input
                    v-model="customTemplate.content"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入模板内容（JSON格式）"
                  />
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTemplateDialog = false">关闭</el-button>
          <el-button type="primary" @click="saveCustomTemplate" v-if="activeTemplateTab === 'custom'">
            保存模板
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分析报告对话框 -->
    <el-dialog v-model="showReportDialog" title="RCA分析报告" width="70%">
      <div class="report-content">
        <div class="report-summary">
          <h3>分析统计</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总分析数" :value="analysisHistory.length" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已完成" :value="completedAnalyses" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="进行中" :value="inProgressAnalyses" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="平均完成时间" :value="avgCompletionTime" suffix="天" />
            </el-col>
          </el-row>
        </div>

        <div class="report-charts">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container" ref="analysisTypeChart"></div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container" ref="analysisTrendChart"></div>
            </el-col>
          </el-row>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showReportDialog = false">关闭</el-button>
          <el-button type="primary" @click="exportReport">导出报告</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import * as echarts from 'echarts'

const router = useRouter()
const route = useRoute()

// 对话框状态
const showFishboneDialog = ref(false)
const showFiveWhyDialog = ref(false)
const showFaultTreeDialog = ref(false)
const showTimelineDialog = ref(false)
const showAddEventDialog = ref(false)
const showTemplateDialog = ref(false)
const showReportDialog = ref(false)

// 页面状态
const loading = ref(false)
const searchKeyword = ref('')
const filterType = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const activeTemplateTab = ref('library')

// 当前分析
const currentAnalysis = ref(null)

// RCA工具数据
const rcaTools = ref([
  {
    key: 'fishbone',
    name: '鱼骨图分析',
    description: '系统性分析问题的各种可能原因',
    icon: 'Share',
    color: '#1976D2',
    usageCount: 25
  },
  {
    key: '5why',
    name: '5Why分析',
    description: '通过连续提问找出根本原因',
    icon: 'QuestionFilled',
    color: '#4CAF50',
    usageCount: 18
  },
  {
    key: 'faulttree',
    name: '故障树分析',
    description: '逻辑分析故障发生的条件',
    icon: 'Connection',
    color: '#FF9800',
    usageCount: 12
  },
  {
    key: 'timeline',
    name: '时间线分析',
    description: '按时间顺序分析事件发生过程',
    icon: 'Clock',
    color: '#9C27B0',
    usageCount: 8
  }
])

// 分析历史数据
const analysisHistory = ref([
  {
    id: 'RCA-2025-001',
    problemTitle: '服务器频繁重启问题',
    analysisType: '鱼骨图',
    status: '已完成',
    priority: '高',
    analyst: '张工',
    progress: 100,
    createTime: '2025-01-30 14:30'
  },
  {
    id: 'RCA-2025-002',
    problemTitle: '网络连接不稳定',
    analysisType: '5Why',
    status: '进行中',
    priority: '中',
    analyst: '李工',
    progress: 60,
    createTime: '2025-01-29 16:20'
  },
  {
    id: 'RCA-2025-003',
    problemTitle: '数据库性能缓慢',
    analysisType: '故障树',
    status: '待开始',
    priority: '高',
    analyst: '王工',
    progress: 0,
    createTime: '2025-01-28 10:15'
  },
  {
    id: 'RCA-2025-004',
    problemTitle: '邮件系统故障',
    analysisType: '时间线',
    status: '已完成',
    priority: '中',
    analyst: '张工',
    progress: 100,
    createTime: '2025-01-27 15:30'
  }
])

// 分析模板数据
const analysisTemplates = ref([
  {
    id: 1,
    name: '硬件故障分析模板',
    type: '鱼骨图',
    description: '适用于硬件设备故障的根因分析'
  },
  {
    id: 2,
    name: '软件缺陷分析模板',
    type: '5Why',
    description: '适用于软件bug和缺陷的分析'
  },
  {
    id: 3,
    name: '网络故障分析模板',
    type: '故障树',
    description: '适用于网络连接和通信问题'
  },
  {
    id: 4,
    name: '性能问题分析模板',
    type: '时间线',
    description: '适用于系统性能和响应时间问题'
  }
])

// 鱼骨图表单
const fishboneForm = reactive({
  problem: ''
})

// 鱼骨图分类
const fishboneCategories = ref([
  { name: '人员(People)', causes: [] },
  { name: '流程(Process)', causes: [] },
  { name: '环境(Environment)', causes: [] },
  { name: '设备(Equipment)', causes: [] },
  { name: '材料(Material)', causes: [] },
  { name: '方法(Method)', causes: [] }
])

// 5Why表单
const fiveWhyForm = reactive({
  problem: '',
  whys: [
    { question: '', answer: '' },
    { question: '', answer: '' },
    { question: '', answer: '' },
    { question: '', answer: '' },
    { question: '', answer: '' }
  ],
  rootCause: '',
  preventiveMeasures: ''
})

// 故障树表单
const faultTreeForm = reactive({
  topEvent: '',
  intermediateEvents: '',
  basicEvents: '',
  logicGate: 'OR',
  conclusion: ''
})

// 时间线分析表单
const timelineForm = reactive({
  problem: '',
  dateRange: [],
  events: [],
  analysis: ''
})

// 事件表单
const eventForm = reactive({
  time: null,
  title: '',
  type: '',
  description: '',
  impact: ''
})

const eventRules = {
  time: [{ required: true, message: '请选择事件时间', trigger: 'change' }],
  title: [{ required: true, message: '请输入事件标题', trigger: 'blur' }],
  type: [{ required: true, message: '请选择事件类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入事件描述', trigger: 'blur' }]
}

// 自定义模板表单
const customTemplate = reactive({
  name: '',
  type: '',
  description: '',
  content: ''
})

// 图表引用
const faultTreeChart = ref()
const analysisTypeChart = ref()
const analysisTrendChart = ref()
const eventFormRef = ref()

// 计算属性
const filteredAnalyses = computed(() => {
  let filtered = analysisHistory.value

  // 按类型过滤
  if (filterType.value !== 'all') {
    const typeMap = {
      'fishbone': '鱼骨图',
      '5why': '5Why',
      'faulttree': '故障树',
      'timeline': '时间线'
    }
    const targetType = typeMap[filterType.value]
    if (targetType) {
      filtered = filtered.filter(a => a.analysisType === targetType)
    }
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(a =>
      a.id.toLowerCase().includes(keyword) ||
      a.problemTitle.toLowerCase().includes(keyword) ||
      a.analyst.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

const completedAnalyses = computed(() => {
  return analysisHistory.value.filter(a => a.status === '已完成').length
})

const inProgressAnalyses = computed(() => {
  return analysisHistory.value.filter(a => a.status === '进行中').length
})

const avgCompletionTime = computed(() => {
  // 模拟计算平均完成时间
  return 3.5
})

// 创建新分析
const createAnalysis = () => {
  ElMessageBox.prompt('请输入问题标题', '新建根因分析', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '问题标题不能为空'
  }).then(({ value }) => {
    // 创建新的分析记录
    const newAnalysis = {
      id: `RCA-2025-${String(analysisHistory.value.length + 1).padStart(3, '0')}`,
      problemTitle: value,
      analysisType: '待选择',
      status: '待开始',
      priority: '中',
      analyst: '当前用户',
      progress: 0,
      createTime: new Date().toLocaleString()
    }

    analysisHistory.value.unshift(newAnalysis)
    currentAnalysis.value = {
      ...newAnalysis,
      title: value,
      step: 0
    }

    ElMessage.success(`创建分析: ${value}`)
  }).catch(() => {
    ElMessage.info('已取消创建')
  })
}

// 打开分析工具
const openTool = (toolKey) => {
  switch (toolKey) {
    case 'fishbone':
      openFishboneAnalysis()
      break
    case '5why':
      openFiveWhyAnalysis()
      break
    case 'faulttree':
      openFaultTreeAnalysis()
      break
    case 'timeline':
      openTimelineAnalysis()
      break
  }
}

// 打开时间线分析
const openTimelineAnalysis = () => {
  showTimelineDialog.value = true
  // 重置表单
  timelineForm.problem = ''
  timelineForm.dateRange = []
  timelineForm.events = []
  timelineForm.analysis = ''
}

// 打开鱼骨图分析
const openFishboneAnalysis = () => {
  showFishboneDialog.value = true
  // 重置表单
  fishboneForm.problem = ''
  fishboneCategories.value.forEach(category => {
    category.causes = []
  })
}

// 打开5Why分析
const openFiveWhyAnalysis = () => {
  showFiveWhyDialog.value = true
  // 重置表单
  fiveWhyForm.problem = ''
  fiveWhyForm.whys.forEach(why => {
    why.question = ''
    why.answer = ''
  })
  fiveWhyForm.rootCause = ''
  fiveWhyForm.preventiveMeasures = ''
}

// 打开故障树分析
const openFaultTreeAnalysis = () => {
  showFaultTreeDialog.value = true
  // 重置表单
  faultTreeForm.topEvent = ''
  faultTreeForm.intermediateEvents = ''
  faultTreeForm.basicEvents = ''
  faultTreeForm.logicGate = 'OR'
  faultTreeForm.conclusion = ''
}

// 分析进度控制
const prevStep = () => {
  if (currentAnalysis.value && currentAnalysis.value.step > 0) {
    currentAnalysis.value.step--
  }
}

const nextStep = () => {
  if (currentAnalysis.value && currentAnalysis.value.step < 4) {
    currentAnalysis.value.step++
    // 更新进度
    currentAnalysis.value.progress = (currentAnalysis.value.step + 1) * 20
  }
}

const completeAnalysis = () => {
  if (currentAnalysis.value) {
    currentAnalysis.value.status = '已完成'
    currentAnalysis.value.progress = 100

    // 更新历史记录
    const index = analysisHistory.value.findIndex(a => a.id === currentAnalysis.value.id)
    if (index > -1) {
      analysisHistory.value[index] = { ...currentAnalysis.value }
    }

    ElMessage.success('分析已完成')
    currentAnalysis.value = null
  }
}

// 时间线分析相关函数
const addTimelineEvent = () => {
  showAddEventDialog.value = true
  // 重置事件表单
  eventForm.time = null
  eventForm.title = ''
  eventForm.type = ''
  eventForm.description = ''
  eventForm.impact = ''
}

const saveTimelineEvent = () => {
  eventFormRef.value.validate((valid) => {
    if (valid) {
      const newEvent = {
        time: eventForm.time.toLocaleString(),
        title: eventForm.title,
        type: eventForm.type,
        description: eventForm.description,
        impact: eventForm.impact
      }

      timelineForm.events.push(newEvent)
      // 按时间排序
      timelineForm.events.sort((a, b) => new Date(a.time) - new Date(b.time))

      showAddEventDialog.value = false
      ElMessage.success('事件已添加')
    }
  })
}

const removeTimelineEvent = (index) => {
  timelineForm.events.splice(index, 1)
  ElMessage.success('事件已删除')
}

const getEventType = (type) => {
  const typeMap = {
    'failure': 'danger',
    'alert': 'warning',
    'operation': 'primary',
    'change': 'info',
    'recovery': 'success'
  }
  return typeMap[type] || 'info'
}

const getEventIcon = (type) => {
  const iconMap = {
    'failure': 'CircleClose',
    'alert': 'Warning',
    'operation': 'Tools',
    'change': 'Edit',
    'recovery': 'CircleCheck'
  }
  return iconMap[type] || 'InfoFilled'
}

const analyzeTimeline = () => {
  if (timelineForm.events.length === 0) {
    ElMessage.warning('请先添加时间线事件')
    return
  }

  // 模拟AI分析
  setTimeout(() => {
    timelineForm.analysis = `基于时间线分析，发现以下关键时序关系：
1. 故障首次出现在 ${timelineForm.events[0]?.time}
2. 共发生 ${timelineForm.events.length} 个相关事件
3. 主要影响范围：${timelineForm.events.map(e => e.impact).filter(Boolean).join('、')}
4. 建议重点关注配置变更和操作执行的时间关联性`

    ElMessage.success('时间线分析完成')
  }, 2000)
}

const saveTimelineAnalysis = () => {
  if (!timelineForm.problem.trim()) {
    ElMessage.warning('请输入问题描述')
    return
  }

  if (timelineForm.events.length === 0) {
    ElMessage.warning('请添加至少一个时间线事件')
    return
  }

  ElMessage.success('时间线分析已保存')
  showTimelineDialog.value = false

  // 添加到历史记录
  const newAnalysis = {
    id: `RCA-${Date.now()}`,
    problemTitle: timelineForm.problem,
    analysisType: '时间线',
    status: '已完成',
    priority: '中',
    analyst: '当前用户',
    progress: 100,
    createTime: new Date().toLocaleString()
  }
  analysisHistory.value.unshift(newAnalysis)
}

// 生成故障树
const generateFaultTree = () => {
  if (!faultTreeForm.topEvent.trim()) {
    ElMessage.warning('请输入顶层事件')
    return
  }

  ElMessage.success('故障树生成中...')
  // 这里可以实现故障树的可视化生成
  setTimeout(() => {
    ElMessage.success('故障树已生成')
  }, 1500)
}

// 添加原因
const addCause = (categoryName) => {
  ElMessageBox.prompt('请输入原因', '添加原因', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '原因不能为空'
  }).then(({ value }) => {
    const category = fishboneCategories.value.find(cat => cat.name === categoryName)
    if (category) {
      category.causes.push(value)
    }
  }).catch(() => {
    // 用户取消
  })
}

// 移除原因
const removeCause = (categoryName, index) => {
  const category = fishboneCategories.value.find(cat => cat.name === categoryName)
  if (category) {
    category.causes.splice(index, 1)
  }
}

// 保存鱼骨图分析
const saveFishboneAnalysis = () => {
  if (!fishboneForm.problem.trim()) {
    ElMessage.warning('请输入问题描述')
    return
  }

  const hasAnyCause = fishboneCategories.value.some(category => category.causes.length > 0)
  if (!hasAnyCause) {
    ElMessage.warning('请至少添加一个原因')
    return
  }

  ElMessage.success('鱼骨图分析已保存')
  showFishboneDialog.value = false

  // 添加到历史记录
  const newAnalysis = {
    id: `RCA-${Date.now()}`,
    problemTitle: fishboneForm.problem,
    analysisType: '鱼骨图',
    status: '已完成',
    analyst: '当前用户',
    createTime: new Date().toLocaleString()
  }
  analysisHistory.value.unshift(newAnalysis)
}

// 保存5Why分析
const saveFiveWhyAnalysis = () => {
  if (!fiveWhyForm.problem.trim()) {
    ElMessage.warning('请输入问题描述')
    return
  }

  if (!fiveWhyForm.rootCause.trim()) {
    ElMessage.warning('请输入根本原因')
    return
  }

  ElMessage.success('5Why分析已保存')
  showFiveWhyDialog.value = false

  // 添加到历史记录
  const newAnalysis = {
    id: `RCA-${Date.now()}`,
    problemTitle: fiveWhyForm.problem,
    analysisType: '5Why',
    status: '已完成',
    analyst: '当前用户',
    createTime: new Date().toLocaleString()
  }
  analysisHistory.value.unshift(newAnalysis)
}

// 保存故障树分析
const saveFaultTreeAnalysis = () => {
  if (!faultTreeForm.topEvent.trim()) {
    ElMessage.warning('请输入顶层事件')
    return
  }

  if (!faultTreeForm.conclusion.trim()) {
    ElMessage.warning('请输入分析结论')
    return
  }

  ElMessage.success('故障树分析已保存')
  showFaultTreeDialog.value = false

  // 添加到历史记录
  const newAnalysis = {
    id: `RCA-${Date.now()}`,
    problemTitle: faultTreeForm.topEvent,
    analysisType: '故障树',
    status: '已完成',
    analyst: '当前用户',
    createTime: new Date().toLocaleString()
  }
  analysisHistory.value.unshift(newAnalysis)
}

// 查看分析
const viewAnalysis = (analysis) => {
  ElMessage.info(`查看分析: ${analysis.id}`)
  // 这里可以实现查看分析详情的逻辑
}

// 编辑分析
const editAnalysis = (analysis) => {
  router.push(`/problem/root-cause-analysis/edit/${analysis.id}`)
}

// 继续分析
const continueAnalysis = (analysis) => {
  currentAnalysis.value = {
    ...analysis,
    title: analysis.problemTitle,
    step: Math.floor(analysis.progress / 20) - 1
  }
  ElMessage.success(`继续分析: ${analysis.problemTitle}`)
}

// 分析操作命令处理
const handleAnalysisCommand = (command) => {
  const { action, analysis } = command

  switch (action) {
    case 'duplicate':
      duplicateAnalysis(analysis)
      break
    case 'export':
      exportAnalysis(analysis)
      break
    case 'archive':
      archiveAnalysis(analysis)
      break
    case 'delete':
      deleteAnalysis(analysis)
      break
  }
}

const duplicateAnalysis = (analysis) => {
  const newAnalysis = {
    ...analysis,
    id: `RCA-2025-${String(analysisHistory.value.length + 1).padStart(3, '0')}`,
    problemTitle: `${analysis.problemTitle} (副本)`,
    status: '待开始',
    progress: 0,
    createTime: new Date().toLocaleString()
  }

  analysisHistory.value.unshift(newAnalysis)
  ElMessage.success('分析已复制')
}

const exportAnalysis = (analysis) => {
  ElMessage.success(`正在导出分析 ${analysis.id}...`)
}

const archiveAnalysis = (analysis) => {
  const index = analysisHistory.value.findIndex(item => item.id === analysis.id)
  if (index > -1) {
    analysisHistory.value[index].status = '已归档'
    ElMessage.success('分析已归档')
  }
}

const deleteAnalysis = (analysis) => {
  ElMessageBox.confirm(
    `确定要删除分析 "${analysis.problemTitle}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = analysisHistory.value.findIndex(item => item.id === analysis.id)
    if (index > -1) {
      analysisHistory.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 导出分析列表
const exportAnalyses = () => {
  ElMessage.success('正在导出分析列表...')
}

// 模板相关函数
const selectTemplate = (template) => {
  ElMessage.success(`已选择模板: ${template.name}`)
  showTemplateDialog.value = false

  // 根据模板类型打开对应的分析工具
  openTool(template.type)
}

const saveCustomTemplate = () => {
  if (!customTemplate.name.trim()) {
    ElMessage.warning('请输入模板名称')
    return
  }

  if (!customTemplate.type) {
    ElMessage.warning('请选择模板类型')
    return
  }

  const newTemplate = {
    id: analysisTemplates.value.length + 1,
    name: customTemplate.name,
    type: customTemplate.type,
    description: customTemplate.description
  }

  analysisTemplates.value.push(newTemplate)
  ElMessage.success('自定义模板已保存')

  // 重置表单
  customTemplate.name = ''
  customTemplate.type = ''
  customTemplate.description = ''
  customTemplate.content = ''
}

// 导出报告
const exportReport = () => {
  ElMessage.success('正在导出RCA分析报告...')
}

// 获取分析类型颜色
const getAnalysisTypeColor = (type) => {
  const colorMap = {
    '鱼骨图': 'primary',
    '5Why': 'success',
    '故障树': 'warning',
    '时间线': 'info',
    '待选择': 'info'
  }
  return colorMap[type] || 'info'
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '待开始': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已归档': 'info'
  }
  return colorMap[status] || 'info'
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colorMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return colorMap[priority] || 'info'
}

// 初始化图表
const initCharts = () => {
  // 分析类型分布图
  if (analysisTypeChart.value) {
    const typeChartInstance = echarts.init(analysisTypeChart.value)
    typeChartInstance.setOption({
      title: {
        text: '分析类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '分析类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 8, name: '鱼骨图' },
            { value: 6, name: '5Why' },
            { value: 4, name: '故障树' },
            { value: 2, name: '时间线' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }

  // 分析趋势图
  if (analysisTrendChart.value) {
    const trendChartInstance = echarts.init(analysisTrendChart.value)
    trendChartInstance.setOption({
      title: {
        text: '分析趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '分析数量',
          type: 'line',
          data: [5, 8, 12, 15, 18, 20],
          itemStyle: { color: '#1976D2' }
        }
      ]
    })
  }
}

onMounted(() => {
  // 检查是否有问题ID参数
  const problemId = route.query.problemId
  if (problemId) {
    // 基于问题ID创建分析
    currentAnalysis.value = {
      id: `RCA-${Date.now()}`,
      title: `问题 ${problemId} 的根因分析`,
      step: 0,
      status: '进行中',
      progress: 20
    }
  }

  nextTick(() => {
    initCharts()
  })
})

// 对话框关闭处理
const handleClose = (done) => {
  ElMessageBox.confirm('确认关闭？未保存的内容将丢失。')
    .then(() => {
      done()
    })
    .catch(() => {
      // 取消关闭
    })
}
</script>

<style scoped>
.root-cause-analysis {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-info h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-info p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* RCA工具概览样式 */
.rca-tools-overview {
  margin-bottom: 30px;
}

.tool-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 120px;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tool-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.tool-icon {
  margin-right: 16px;
}

.tool-info {
  flex: 1;
}

.tool-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.tool-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.tool-usage {
  color: #999;
  font-size: 12px;
}

/* 分析进度样式 */
.analysis-progress {
  margin-bottom: 30px;
}

.progress-content {
  padding: 20px 0;
}

.progress-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 时间线分析样式 */
.timeline-analysis {
  padding: 20px 0;
}

.timeline-controls {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.timeline-events {
  margin: 20px 0;
  max-height: 400px;
  overflow-y: auto;
}

.timeline-event {
  padding: 8px 0;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-title {
  font-weight: 500;
  color: #333;
}

.event-description {
  color: #666;
  margin-bottom: 4px;
}

.event-impact {
  color: #999;
  font-size: 12px;
}

.timeline-analysis-result {
  margin-top: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #1976D2;
}

.timeline-analysis-result h4 {
  color: #333;
  margin-bottom: 12px;
}

/* 故障树可视化样式 */
.fault-tree-visual {
  height: 300px;
  margin-top: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

/* 模板相关样式 */
.template-content {
  padding: 20px 0;
}

.template-library {
  padding: 20px 0;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 120px;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-info {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.template-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.template-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.template-type {
  margin-top: auto;
}

.custom-template {
  padding: 20px 0;
}

/* 报告样式 */
.report-content {
  padding: 20px 0;
}

.report-summary {
  margin-bottom: 30px;
}

.report-summary h3 {
  color: #333;
  margin-bottom: 20px;
}

.report-charts {
  margin-top: 20px;
}

.chart-container {
  height: 300px;
}

.analysis-tools {
  margin-bottom: 30px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.analysis-tools .el-button {
  flex: 1;
  min-width: 150px;
}

.analysis-history {
  margin-top: 20px;
}

.analysis-history h3 {
  color: #333;
  margin-bottom: 16px;
  font-size: 16px;
}

/* 鱼骨图分析样式 */
.fishbone-analysis {
  padding: 20px 0;
}

.problem-statement {
  margin-bottom: 20px;
}

.fishbone-categories h4 {
  color: #333;
  margin-bottom: 16px;
}

.category-card {
  margin-bottom: 16px;
  min-height: 120px;
}

.category-card .el-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.causes-list {
  min-height: 60px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.cause-tag {
  margin: 2px;
}

/* 5Why分析样式 */
.five-why-analysis {
  padding: 20px 0;
}

.why-item {
  background: #f8f9fa;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  border-left: 4px solid #1976D2;
}

.why-item .el-form-item {
  margin-bottom: 12px;
}

.why-item:last-of-type {
  border-left-color: #4CAF50;
}

/* 故障树分析样式 */
.fault-tree-analysis {
  padding: 20px 0;
}

.tree-builder {
  text-align: center;
  margin-bottom: 20px;
}

.tree-builder p {
  color: #666;
  font-style: italic;
  margin-bottom: 20px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analysis-tools {
    flex-direction: column;
  }

  .analysis-tools .el-button {
    min-width: auto;
  }

  .fishbone-categories .el-col {
    margin-bottom: 16px;
  }

  .why-item {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .root-cause-analysis {
    padding: 12px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
