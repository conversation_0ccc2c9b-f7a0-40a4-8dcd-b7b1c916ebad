<template>
  <div class="service-level-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>服务级别管理</h2>
          <p>管理和监控服务级别协议，确保服务质量达标</p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
            新建协议
          </el-button>
          <el-button :icon="Document" @click="$router.push('/slm/reports')">
            SLA报告
          </el-button>
          <el-button :icon="Files" @click="$router.push('/slm/agreements')">
            协议管理
          </el-button>
          <el-button :icon="Monitor" @click="$router.push('/slm/monitoring')">
            SLA监控
          </el-button>
          <el-button :icon="TrendCharts" @click="$router.push('/slm/health')">
            服务健康
          </el-button>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon :size="32" color="#409EFF">
                  <Monitor />
                </el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ overview.totalServices }}</div>
                <div class="metric-label">服务总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon :size="32" color="#67C23A">
                  <Document />
                </el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ overview.activeAgreements }}</div>
                <div class="metric-label">活跃协议</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon :size="32" color="#E6A23C">
                  <TrendCharts />
                </el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ overview.avgHealthScore }}</div>
                <div class="metric-label">平均健康分</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon :size="32" color="#F56C6C">
                  <SuccessFilled />
                </el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ overview.slaComplianceRate }}%</div>
                <div class="metric-label">SLA达成率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧：服务健康评分 -->
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>服务健康评分</span>
                <el-button type="text" @click="viewAllServices">
                  查看全部 <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="health-scores">
              <div
                v-for="service in topServices"
                :key="service.id"
                class="health-item"
                @click="viewServiceDetail(service)"
              >
                <div class="service-info">
                  <div class="service-name">{{ service.name }}</div>
                  <div class="service-desc">{{ service.description }}</div>
                </div>
                <div class="health-score">
                  <div class="score-value" :style="{ color: getHealthColor(service.healthScore) }">
                    {{ service.healthScore }}
                  </div>
                  <div class="score-badge" :class="getHealthLevel(service.healthScore)">
                    {{ getHealthLevelText(service.healthScore) }}
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：SLA告警 -->
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>SLA告警</span>
                <el-button type="text" @click="viewAllAlerts">
                  查看全部 <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="alerts-list">
              <div
                v-for="alert in overview.recentAlerts"
                :key="alert.id"
                class="alert-item"
                :class="alert.type"
                @click="viewAlertDetail(alert)"
              >
                <div class="alert-icon">
                  <el-icon :size="20">
                    <component :is="getAlertIcon(alert.type)" />
                  </el-icon>
                </div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.service }}</div>
                  <div class="alert-message">{{ alert.message }}</div>
                  <div class="alert-time">{{ alert.time }}</div>
                </div>
              </div>
              <div v-if="overview.recentAlerts.length === 0" class="no-alerts">
                <el-icon :size="48" color="#C0C4CC">
                  <SuccessFilled />
                </el-icon>
                <p>暂无告警信息</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 趋势图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="8">
          <el-card>
            <template #header>
              <span>服务健康分布</span>
            </template>
            <div class="chart-container" ref="healthDistributionChart"></div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="16">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>关键指标趋势</span>
                <el-select v-model="selectedMetric" size="small" style="width: 120px">
                  <el-option label="可用性" value="availability" />
                  <el-option label="响应时间" value="responseTime" />
                  <el-option label="满意度" value="satisfaction" />
                </el-select>
              </div>
            </template>
            <div class="chart-container" ref="trendChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 新建协议对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建服务级别协议"
      width="600px"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="协议名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入协议名称" />
        </el-form-item>
        <el-form-item label="协议类型" prop="type">
          <el-select v-model="createForm.type" placeholder="请选择协议类型" style="width: 100%">
            <el-option
              v-for="type in agreementTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            >
              <div>
                <div>{{ type.label }}</div>
                <div style="font-size: 12px; color: #999;">{{ type.description }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联服务" prop="serviceId">
          <el-select v-model="createForm.serviceId" placeholder="请选择服务" style="width: 100%">
            <el-option
              v-for="service in services"
              :key="service.id"
              :label="service.name"
              :value="service.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" prop="owner">
          <el-input v-model="createForm.owner" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="生效日期" prop="effectiveDate">
          <el-date-picker
            v-model="createForm.effectiveDate"
            type="date"
            placeholder="选择生效日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="到期日期" prop="expiryDate">
          <el-date-picker
            v-model="createForm.expiryDate"
            type="date"
            placeholder="选择到期日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreateAgreement" :loading="createLoading">
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Document, Refresh, Monitor, TrendCharts, SuccessFilled, ArrowRight,
  Warning, CircleCloseFilled, InfoFilled, Files
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getSLMOverview,
  getServiceList,
  createAgreement,
  agreementTypes,
  healthScoreLevels
} from '@/api/slmApi.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showCreateDialog = ref(false)
const selectedMetric = ref('availability')

// 概览数据
const overview = reactive({
  totalServices: 0,
  activeAgreements: 0,
  avgHealthScore: 0,
  slaComplianceRate: 0,
  healthDistribution: [],
  recentAlerts: [],
  trendData: {}
})

// 服务列表
const services = ref([])
const topServices = ref([])

// 创建表单
const createForm = reactive({
  name: '',
  type: '',
  serviceId: '',
  owner: '',
  effectiveDate: '',
  expiryDate: ''
})

const createFormRef = ref()

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入协议名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择协议类型', trigger: 'change' }
  ],
  serviceId: [
    { required: true, message: '请选择关联服务', trigger: 'change' }
  ],
  owner: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  expiryDate: [
    { required: true, message: '请选择到期日期', trigger: 'change' }
  ]
}

// 图表引用
const healthDistributionChart = ref()
const trendChart = ref()

// 加载概览数据
const loadOverview = async () => {
  loading.value = true
  try {
    const response = await getSLMOverview()
    if (response.success) {
      Object.assign(overview, response.data)
    }
  } catch (error) {
    ElMessage.error('加载概览数据失败')
    console.error('Load overview error:', error)
  } finally {
    loading.value = false
  }
}

// 加载服务列表
const loadServices = async () => {
  try {
    const response = await getServiceList({ pageSize: 100 })
    if (response.success) {
      services.value = response.data.list
      // 按健康评分排序，取前5个
      topServices.value = [...response.data.list]
        .sort((a, b) => b.healthScore - a.healthScore)
        .slice(0, 5)
    }
  } catch (error) {
    ElMessage.error('加载服务列表失败')
    console.error('Load services error:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadOverview(),
    loadServices()
  ])

  // 刷新图表
  nextTick(() => {
    initHealthDistributionChart()
    initTrendChart()
  })
}

// 获取健康评分颜色
const getHealthColor = (score) => {
  const level = healthScoreLevels.find(l => score >= l.min && score <= l.max)
  return level?.color || '#999'
}

// 获取健康评分等级
const getHealthLevel = (score) => {
  const level = healthScoreLevels.find(l => score >= l.min && score <= l.max)
  return level?.level.toLowerCase() || 'unknown'
}

// 获取健康评分等级文本
const getHealthLevelText = (score) => {
  const level = healthScoreLevels.find(l => score >= l.min && score <= l.max)
  return level?.level || '未知'
}

// 获取告警图标
const getAlertIcon = (type) => {
  switch (type) {
    case 'critical':
      return CircleCloseFilled
    case 'warning':
      return Warning
    case 'info':
      return InfoFilled
    default:
      return InfoFilled
  }
}

// 查看服务详情
const viewServiceDetail = (service) => {
  router.push(`/slm/service/${service.id}`)
}

// 查看所有服务
const viewAllServices = () => {
  router.push('/slm/services')
}

// 查看告警详情
const viewAlertDetail = (alert) => {
  ElMessageBox.alert(
    `服务：${alert.service}\n消息：${alert.message}\n时间：${alert.time}`,
    '告警详情',
    {
      confirmButtonText: '确定'
    }
  )
}

// 查看所有告警
const viewAllAlerts = () => {
  router.push('/slm/alerts')
}

// 创建协议
const handleCreateAgreement = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()

    createLoading.value = true

    const formData = {
      ...createForm,
      effectiveDate: createForm.effectiveDate.toISOString(),
      expiryDate: createForm.expiryDate.toISOString()
    }

    const response = await createAgreement(formData)

    if (response.success) {
      ElMessage.success('协议创建成功')
      showCreateDialog.value = false
      resetCreateForm()
      refreshData()
    } else {
      ElMessage.error(response.msg || '创建失败')
    }
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('创建协议失败')
      console.error('Create agreement error:', error)
    }
  } finally {
    createLoading.value = false
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    type: '',
    serviceId: '',
    owner: '',
    effectiveDate: '',
    expiryDate: ''
  })
  createFormRef.value?.resetFields()
}

// 初始化健康分布图表
const initHealthDistributionChart = () => {
  if (!healthDistributionChart.value) return

  const chart = echarts.init(healthDistributionChart.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '服务健康分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: overview.healthDistribution.map(item => ({
          value: item.count,
          name: item.level,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChart.value) return

  const chart = echarts.init(trendChart.value)

  const getChartData = () => {
    const data = overview.trendData[selectedMetric.value] || []
    const labels = ['6个月前', '5个月前', '4个月前', '3个月前', '2个月前', '上个月']

    return {
      labels,
      data,
      unit: selectedMetric.value === 'availability' ? '%' :
            selectedMetric.value === 'responseTime' ? '分钟' : '分'
    }
  }

  const chartData = getChartData()

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value}${chartData.unit}`
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.labels,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: `{value}${chartData.unit}`
      }
    },
    series: [
      {
        name: selectedMetric.value === 'availability' ? '可用性' :
              selectedMetric.value === 'responseTime' ? '响应时间' : '满意度',
        type: 'line',
        data: chartData.data,
        smooth: true,
        lineStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 监听指标选择变化
watch(selectedMetric, () => {
  nextTick(() => {
    initTrendChart()
  })
})

// 生命周期
onMounted(async () => {
  await refreshData()

  nextTick(() => {
    initHealthDistributionChart()
    initTrendChart()
  })
})

// 导出数据
defineExpose({
  refreshData
})
</script>

<style scoped>
.service-level-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 指标卡片 */
.metrics-section {
  margin-bottom: 20px;
}

.metric-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 8px;
}

.metric-icon {
  margin-right: 16px;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #666;
}

/* 主要内容区域 */
.main-content {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 健康评分 */
.health-scores {
  max-height: 400px;
  overflow-y: auto;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.health-item:hover {
  background-color: #f8f9fa;
}

.health-item:last-child {
  border-bottom: none;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.service-desc {
  font-size: 14px;
  color: #666;
}

.health-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-value {
  font-size: 24px;
  font-weight: bold;
}

.score-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.score-badge.健康 {
  background-color: #f0f9ff;
  color: #67c23a;
}

.score-badge.良好 {
  background-color: #fef7e0;
  color: #e6a23c;
}

.score-badge.风险 {
  background-color: #fef2f2;
  color: #f56c6c;
}

.score-badge.危险 {
  background-color: #fef2f2;
  color: #f56c6c;
}

/* 告警列表 */
.alerts-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.alert-item:hover {
  background-color: #f8f9fa;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.alert-item.critical .alert-icon {
  color: #f56c6c;
}

.alert-item.warning .alert-icon {
  color: #e6a23c;
}

.alert-item.info .alert-icon {
  color: #409eff;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #999;
}

.no-alerts {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.no-alerts p {
  margin: 12px 0 0 0;
  font-size: 14px;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-level-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
  }

  .metric-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .metric-icon {
    margin-right: 0;
  }

  .health-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .health-score {
    align-self: flex-end;
  }
}

/* 对话框样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select .el-input__inner) {
  cursor: pointer;
}
</style>
