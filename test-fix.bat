@echo off
echo ========================================
echo 测试CMDB项目修复方案
echo ========================================
echo.

echo [1/5] 检查当前目录...
if not exist package.json (
    echo 错误: 未找到package.json文件
    echo 请确保在项目根目录中运行此脚本
    pause
    exit /b 1
)
echo ✓ 项目根目录确认

echo.
echo [2/5] 检查Node.js版本...
node --version
if %errorlevel% neq 0 (
    echo 错误: Node.js未安装或不在PATH中
    pause
    exit /b 1
)
echo ✓ Node.js版本检查通过

echo.
echo [3/5] 检查npm版本...
npm --version
if %errorlevel% neq 0 (
    echo 错误: npm未安装或不在PATH中
    pause
    exit /b 1
)
echo ✓ npm版本检查通过

echo.
echo [4/5] 检查关键依赖是否存在...
if exist node_modules\pinia (
    echo ✓ pinia 已安装
) else (
    echo ❌ pinia 未安装
)

if exist node_modules\axios (
    echo ✓ axios 已安装
) else (
    echo ❌ axios 未安装
)

if exist node_modules\mockjs (
    echo ✓ mockjs 已安装
) else (
    echo ❌ mockjs 未安装
)

echo.
echo [5/5] 检查项目文件结构...
if exist src\main.js (
    echo ✓ src\main.js 存在
) else (
    echo ❌ src\main.js 不存在
)

if exist src\App.vue (
    echo ✓ src\App.vue 存在
) else (
    echo ❌ src\App.vue 不存在
)

echo.
echo ========================================
echo 测试完成
echo ========================================
echo.
echo 如果看到任何 ❌ 标记，请运行：
echo fix-dependencies-now.bat
echo.
pause
