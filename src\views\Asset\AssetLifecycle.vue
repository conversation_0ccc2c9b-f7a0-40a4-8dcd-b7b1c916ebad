<template>
  <div class="asset-lifecycle">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>资产生命周期管理</h2>
          <p>管理资产从采购到报废的全生命周期状态流转</p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
            新增资产
          </el-button>
          <el-button :icon="Upload" @click="showBatchImportDialog = true">
            批量导入
          </el-button>
          <el-button :icon="Download" @click="exportAssets">
            导出资产
          </el-button>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 状态流程图 -->
    <el-card class="lifecycle-flow-card">
      <template #header>
        <div class="card-header">
          <span>资产生命周期流程</span>
          <el-icon><Share /></el-icon>
        </div>
      </template>
      <div class="lifecycle-flow">
        <div class="flow-steps">
          <div
            v-for="(step, index) in lifecycleSteps"
            :key="step.status"
            class="flow-step"
            :class="{ active: selectedStatus === step.status }"
            @click="filterByStatus(step.status)"
          >
            <div class="step-icon" :style="{ backgroundColor: step.color }">
              <el-icon>
                <component :is="step.icon" />
              </el-icon>
            </div>
            <div class="step-content">
              <div class="step-title">{{ step.label }}</div>
              <div class="step-count">{{ getStatusCount(step.status) }}</div>
            </div>
            <div v-if="index < lifecycleSteps.length - 1" class="step-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="搜索">
          <el-input
            v-model="filterForm.keyword"
            placeholder="资产名称/编号/序列号"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="filterForm.status"
            placeholder="全部状态"
            clearable
            @change="handleFilter"
            style="width: 150px"
          >
            <el-option
              v-for="status in assetStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类别">
          <el-select
            v-model="filterForm.category"
            placeholder="全部类别"
            clearable
            @change="handleFilter"
            style="width: 150px"
          >
            <el-option
              v-for="category in assetCategories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select
            v-model="filterForm.department"
            placeholder="全部部门"
            clearable
            @change="handleFilter"
            style="width: 150px"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 资产列表 -->
    <el-card class="asset-list-card">
      <template #header>
        <div class="card-header">
          <span>资产列表 ({{ pagination.total }})</span>
          <div class="header-actions">
            <el-button-group>
              <el-button
                :type="viewMode === 'table' ? 'primary' : ''"
                :icon="Grid"
                @click="viewMode = 'table'"
              >
                表格
              </el-button>
              <el-button
                :type="viewMode === 'card' ? 'primary' : ''"
                :icon="Menu"
                @click="viewMode = 'card'"
              >
                卡片
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table
          v-loading="loading"
          :data="assetList"
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="assetCode" label="资产编号" width="120" />
          <el-table-column prop="name" label="资产名称" min-width="150">
            <template #default="{ row }">
              <div class="asset-name-cell">
                <el-icon class="asset-icon">
                  <component :is="getCategoryIcon(row.category)" />
                </el-icon>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="categoryName" label="类别" width="100" />
          <el-table-column prop="brand" label="品牌" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignedUser" label="使用人" width="100" />
          <el-table-column prop="department" label="部门" width="120" />
          <el-table-column prop="location" label="位置" width="150" />
          <el-table-column prop="purchaseDate" label="采购日期" width="120" />
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewAssetDetail(row)">
                详情
              </el-button>
              <el-button type="text" size="small" @click="editAsset(row)">
                编辑
              </el-button>
              <el-button type="text" size="small" @click="showStatusChangeDialog(row)">
                状态变更
              </el-button>
              <el-dropdown @command="(command) => handleAssetAction(command, row)">
                <el-button type="text" size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="assign">分配</el-dropdown-item>
                    <el-dropdown-item command="maintenance">维修</el-dropdown-item>
                    <el-dropdown-item command="retire" divided>报废</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 批量操作栏 -->
      <div v-if="selectedAssets.length > 0" class="batch-actions">
        <el-alert
          :title="`已选择 ${selectedAssets.length} 项资产`"
          type="info"
          show-icon
          :closable="false"
        >
          <template #default>
            <div class="batch-buttons">
              <el-button size="small" @click="batchStatusChange">批量状态变更</el-button>
              <el-button size="small" @click="batchAssign">批量分配</el-button>
              <el-button size="small" @click="batchExport">批量导出</el-button>
              <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col
            v-for="asset in assetList"
            :key="asset.id"
            :xs="24" :sm="12" :md="8" :lg="6"
            class="asset-card-col"
          >
            <div class="asset-card" @click="viewAssetDetail(asset)">
              <div class="card-header">
                <div class="asset-icon">
                  <el-icon>
                    <component :is="getCategoryIcon(asset.category)" />
                  </el-icon>
                </div>
                <div class="asset-status">
                  <el-tag :type="getStatusType(asset.status)" size="small">
                    {{ asset.status }}
                  </el-tag>
                </div>
              </div>
              <div class="card-content">
                <h4 class="asset-name">{{ asset.name }}</h4>
                <p class="asset-code">{{ asset.assetCode }}</p>
                <div class="asset-info">
                  <div class="info-item">
                    <span class="label">品牌:</span>
                    <span class="value">{{ asset.brand }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">型号:</span>
                    <span class="value">{{ asset.model }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">使用人:</span>
                    <span class="value">{{ asset.assignedUser || '未分配' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">部门:</span>
                    <span class="value">{{ asset.department || '未分配' }}</span>
                  </div>
                </div>
              </div>
              <div class="card-actions">
                <el-button size="small" type="text" @click.stop="showStatusChangeDialog(asset)">
                  状态变更
                </el-button>
                <el-button size="small" type="text" @click.stop="handleAssetAction('assign', asset)">
                  分配
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <div v-if="assetList.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无资产数据">
            <el-button type="primary" @click="showCreateDialog = true">新增资产</el-button>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 状态变更对话框 -->
    <el-dialog
      v-model="showStatusDialog"
      title="状态变更"
      width="500px"
      :before-close="handleCloseStatusDialog"
    >
      <el-form
        ref="statusFormRef"
        :model="statusForm"
        :rules="statusFormRules"
        label-width="100px"
      >
        <el-form-item label="当前状态">
          <el-tag :type="getStatusType(statusForm.currentStatus)">
            {{ statusForm.currentStatus }}
          </el-tag>
        </el-form-item>
        <el-form-item label="目标状态" prop="newStatus">
          <el-select v-model="statusForm.newStatus" placeholder="请选择目标状态">
            <el-option
              v-for="status in getAvailableStatuses(statusForm.currentStatus)"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因" prop="reason">
          <el-input
            v-model="statusForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入状态变更原因"
          />
        </el-form-item>
        <el-form-item v-if="statusForm.newStatus === '维修中'" label="预计完成">
          <el-date-picker
            v-model="statusForm.expectedDate"
            type="date"
            placeholder="请选择预计完成日期"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showStatusDialog = false">取消</el-button>
          <el-button type="primary" @click="handleStatusChange" :loading="statusLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 资产分配对话框 -->
    <el-dialog
      v-model="showAssignDialog"
      title="资产分配"
      width="500px"
      :before-close="handleCloseAssignDialog"
    >
      <el-form
        ref="assignFormRef"
        :model="assignForm"
        :rules="assignFormRules"
        label-width="100px"
      >
        <el-form-item label="资产信息">
          <div class="asset-info-display">
            <span>{{ assignForm.assetName }} ({{ assignForm.assetCode }})</span>
          </div>
        </el-form-item>
        <el-form-item label="分配给" prop="userId">
          <el-select
            v-model="assignForm.userId"
            placeholder="请选择使用人"
            filterable
            @change="handleUserChange"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="`${user.name} (${user.department})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="assignForm.department" readonly />
        </el-form-item>
        <el-form-item label="位置" prop="location">
          <el-input v-model="assignForm.location" placeholder="请输入资产位置" />
        </el-form-item>
        <el-form-item label="分配说明">
          <el-input
            v-model="assignForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分配说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAssignDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAssign" :loading="assignLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 资产详情对话框 -->
    <!-- Debug: detailDialogVisible = {{ detailDialogVisible }} -->
    <el-dialog
      v-model="detailDialogVisible"
      title="资产详情"
      width="800px"
      :before-close="handleDetailDialogClose"
    >
      <div v-if="currentAssetDetail" class="asset-detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon><Document /></el-icon>
              <span>基本信息</span>
            </div>
          </template>
          <div class="asset-info-grid">
            <div class="info-item">
              <label>资产编号：</label>
              <span>{{ currentAssetDetail.id }}</span>
            </div>
            <div class="info-item">
              <label>资产名称：</label>
              <span>{{ currentAssetDetail.name }}</span>
            </div>
            <div class="info-item">
              <label>资产类型：</label>
              <el-tag :type="getAssetTypeTagType(currentAssetDetail.category)">
                {{ getAssetTypeName(currentAssetDetail.category) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>当前状态：</label>
              <el-tag :type="getStatusType(currentAssetDetail.status)">
                {{ currentAssetDetail.status }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>品牌型号：</label>
              <span>{{ currentAssetDetail.model }}</span>
            </div>
            <div class="info-item">
              <label>序列号：</label>
              <span>{{ currentAssetDetail.serialNumber }}</span>
            </div>
            <div class="info-item">
              <label>采购价格：</label>
              <span>¥{{ currentAssetDetail.price?.toLocaleString() }}</span>
            </div>
            <div class="info-item">
              <label>采购日期：</label>
              <span>{{ currentAssetDetail.purchaseDate }}</span>
            </div>
            <div class="info-item">
              <label>使用人：</label>
              <span>{{ currentAssetDetail.user }}</span>
            </div>
            <div class="info-item">
              <label>所属部门：</label>
              <span>{{ currentAssetDetail.department }}</span>
            </div>
            <div class="info-item">
              <label>存放位置：</label>
              <span>{{ currentAssetDetail.location }}</span>
            </div>
            <div class="info-item">
              <label>供应商：</label>
              <span>{{ currentAssetDetail.vendor }}</span>
            </div>
          </div>
        </el-card>

        <!-- 生命周期信息 -->
        <el-card class="detail-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon><Timer /></el-icon>
              <span>生命周期信息</span>
            </div>
          </template>
          <div class="lifecycle-history">
            <div
              v-for="record in currentAssetDetail.lifecycleHistory"
              :key="record.id"
              class="history-item"
            >
              <div class="history-dot" :class="getTimelineType(record.status)"></div>
              <div class="history-content">
                <div class="history-header">
                  <span class="history-title">{{ record.action }}</span>
                  <span class="history-time">{{ record.date }}</span>
                </div>
                <div class="history-desc">{{ record.description }}</div>
                <div class="history-operator">操作人：{{ record.operator }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 技术规格 -->
        <el-card class="detail-section" shadow="never" v-if="currentAssetDetail.specifications">
          <template #header>
            <div class="section-header">
              <el-icon><Tools /></el-icon>
              <span>技术规格</span>
            </div>
          </template>
          <div class="specifications-grid">
            <div
              v-for="(value, key) in currentAssetDetail.specifications"
              :key="key"
              class="spec-item"
            >
              <label>{{ getSpecLabel(key) }}：</label>
              <span>{{ value }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <div v-else class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="openEditDialog">编辑资产</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑资产对话框 -->
    <!-- Debug: editDialogVisible = {{ editDialogVisible }} -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑资产"
      width="600px"
      :before-close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="100px"
        v-loading="editLoading"
      >
        <el-form-item label="资产名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入资产名称" />
        </el-form-item>

        <el-form-item label="资产类型" prop="category">
          <el-select v-model="editForm.category" placeholder="请选择资产类型" style="width: 100%">
            <el-option
              v-for="category in assetCategories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="品牌型号" prop="model">
          <el-input v-model="editForm.model" placeholder="请输入品牌型号" />
        </el-form-item>

        <el-form-item label="序列号" prop="serialNumber">
          <el-input v-model="editForm.serialNumber" placeholder="请输入序列号" />
        </el-form-item>

        <el-form-item label="当前状态" prop="status">
          <el-select v-model="editForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="待入库" value="待入库" />
            <el-option label="库存中" value="库存中" />
            <el-option label="使用中" value="使用中" />
            <el-option label="维修中" value="维修中" />
            <el-option label="已报废" value="已报废" />
          </el-select>
        </el-form-item>

        <el-form-item label="使用人" prop="user">
          <el-input v-model="editForm.user" placeholder="请输入使用人" />
        </el-form-item>

        <el-form-item label="所属部门" prop="department">
          <el-select v-model="editForm.department" placeholder="请选择部门" style="width: 100%">
            <el-option label="IT部" value="IT部" />
            <el-option label="财务部" value="财务部" />
            <el-option label="人事部" value="人事部" />
            <el-option label="市场部" value="市场部" />
            <el-option label="研发部" value="研发部" />
          </el-select>
        </el-form-item>

        <el-form-item label="存放位置" prop="location">
          <el-input v-model="editForm.location" placeholder="请输入存放位置" />
        </el-form-item>

        <el-form-item label="供应商" prop="vendor">
          <el-input v-model="editForm.vendor" placeholder="请输入供应商" />
        </el-form-item>

        <el-form-item label="采购价格" prop="price">
          <el-input
            v-model.number="editForm.price"
            type="number"
            :min="0"
            step="0.01"
            placeholder="请输入采购价格"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAsset" :loading="editLoading">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Upload, Download, Refresh, Share, Search, Grid, Menu,
  ArrowRight, ArrowDown, Box, Monitor, Connection,
  FolderOpened, Printer, Cellphone, Tools, Close, Delete,
  Document, Timer, Loading
} from '@element-plus/icons-vue'
import {
  getAssetList,
  getAssetCategories,
  getAssetStatuses,
  changeAssetStatus,
  assignAsset,
  deleteAsset,
  getAssetDetail,
  updateAsset
} from '@/api/assetApi.js'
import { mockUsers, mockDepartments } from '@/api/index.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const statusLoading = ref(false)
const assignLoading = ref(false)
const viewMode = ref('table')
const selectedStatus = ref('')

// 对话框状态
const showCreateDialog = ref(false)
const showBatchImportDialog = ref(false)
const showStatusDialog = ref(false)
const showAssignDialog = ref(false)
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)

// 详情和编辑相关数据
const currentAssetDetail = ref(null)
const detailLoading = ref(false)
const editLoading = ref(false)

// 资产列表和分页
const assetList = ref([])
const selectedAssets = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  keyword: '',
  status: '',
  category: '',
  department: ''
})

// 基础数据
const assetCategories = ref([])
const assetStatuses = ref([])
const users = ref(mockUsers)
const departments = ref(mockDepartments)

// 生命周期步骤
const lifecycleSteps = ref([
  { status: '待入库', label: '待入库', icon: 'Box', color: '#909399' },
  { status: '库存中', label: '库存中', icon: 'FolderOpened', color: '#409EFF' },
  { status: '使用中', label: '使用中', icon: 'Monitor', color: '#67C23A' },
  { status: '维修中', label: '维修中', icon: 'Tools', color: '#E6A23C' },
  { status: '停用', label: '停用', icon: 'Close', color: '#C0C4CC' },
  { status: '已报废', label: '已报废', icon: 'Delete', color: '#F56C6C' }
])

// 状态变更表单
const statusFormRef = ref(null)
const statusForm = reactive({
  assetId: '',
  currentStatus: '',
  newStatus: '',
  reason: '',
  expectedDate: null
})

const statusFormRules = {
  newStatus: [
    { required: true, message: '请选择目标状态', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入变更原因', trigger: 'blur' }
  ]
}

// 分配表单
const assignFormRef = ref(null)
const assignForm = reactive({
  assetId: '',
  assetName: '',
  assetCode: '',
  userId: '',
  userName: '',
  department: '',
  location: '',
  description: ''
})

const assignFormRules = {
  userId: [
    { required: true, message: '请选择使用人', trigger: 'change' }
  ],
  location: [
    { required: true, message: '请输入资产位置', trigger: 'blur' }
  ]
}

// 编辑表单
const editFormRef = ref(null)
const editForm = reactive({
  id: '',
  name: '',
  category: '',
  model: '',
  serialNumber: '',
  status: '',
  user: '',
  department: '',
  location: '',
  vendor: '',
  price: 0
})

const editFormRules = {
  name: [
    { required: true, message: '请输入资产名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择资产类型', trigger: 'change' }
  ],
  model: [
    { required: true, message: '请输入品牌型号', trigger: 'blur' }
  ],
  serialNumber: [
    { required: true, message: '请输入序列号', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 计算属性
const statusCounts = computed(() => {
  const counts = {}
  lifecycleSteps.value.forEach(step => {
    counts[step.status] = assetList.value.filter(asset => asset.status === step.status).length
  })
  return counts
})

// 初始化数据
const initData = async () => {
  loading.value = true
  try {
    // 获取基础数据
    const [categoriesRes, statusesRes] = await Promise.all([
      getAssetCategories(),
      getAssetStatuses()
    ])

    assetCategories.value = categoriesRes.data
    assetStatuses.value = statusesRes.data

    // 获取资产列表
    await loadAssetList()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载资产列表
const loadAssetList = async () => {
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filterForm
    }

    const response = await getAssetList(params)
    assetList.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    console.error('加载资产列表失败:', error)
    ElMessage.error('加载资产列表失败')
  }
}

// 获取状态数量
const getStatusCount = (status) => {
  return statusCounts.value[status] || 0
}

// 按状态筛选
const filterByStatus = (status) => {
  if (selectedStatus.value === status) {
    selectedStatus.value = ''
    filterForm.status = ''
  } else {
    selectedStatus.value = status
    filterForm.status = status
  }
  handleFilter()
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadAssetList()
}

// 处理筛选
const handleFilter = () => {
  pagination.page = 1
  loadAssetList()
}

// 重置筛选
const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = ''
  })
  selectedStatus.value = ''
  pagination.page = 1
  loadAssetList()
}

// 刷新数据
const refreshData = async () => {
  await loadAssetList()
  ElMessage.success('数据刷新成功')
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadAssetList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadAssetList()
}

// 表格选择处理
const handleSelectionChange = (selection) => {
  selectedAssets.value = selection
}

// 获取分类图标
const getCategoryIcon = (category) => {
  const iconMap = {
    server: 'Monitor',
    laptop: 'Monitor', // 使用Monitor代替Laptop
    desktop: 'Monitor', // 使用Monitor代替Desktop
    network: 'Connection',
    storage: 'FolderOpened',
    printer: 'Printer',
    mobile: 'Cellphone',
    other: 'Box'
  }
  return iconMap[category] || 'Box'
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '待入库': 'info',
    '库存中': 'primary',
    '使用中': 'success',
    '维修中': 'warning',
    '借用中': 'danger',
    '停用': 'info',
    '已报废': 'danger'
  }
  return typeMap[status] || 'info'
}

// 查看资产详情
const viewAssetDetail = async (asset) => {
  console.log('查看资产详情:', asset)
  try {
    detailDialogVisible.value = true
    detailLoading.value = true
    currentAssetDetail.value = null

    console.log('调用API获取详情...')
    const response = await getAssetDetail(asset.id)
    console.log('API响应:', response)

    if (response && response.success) {
      currentAssetDetail.value = response.data
      console.log('设置详情数据:', response.data)
    } else {
      const errorMsg = response?.message || response?.msg || '获取资产详情失败'
      ElMessage.error(errorMsg)
      detailDialogVisible.value = false
    }
  } catch (error) {
    console.error('获取资产详情失败:', error)
    // 处理不同类型的错误
    let errorMsg = '获取资产详情失败'
    if (error && typeof error === 'object') {
      if (error.msg) {
        errorMsg = error.msg
      } else if (error.message) {
        errorMsg = error.message
      } else if (error.code === 404) {
        errorMsg = '资产不存在'
      }
    }
    ElMessage.error(errorMsg)
    detailDialogVisible.value = false
  } finally {
    detailLoading.value = false
  }
}

// 显示状态变更对话框
const showStatusChangeDialog = (asset) => {
  statusForm.assetId = asset.id
  statusForm.currentStatus = asset.status
  statusForm.newStatus = ''
  statusForm.reason = ''
  statusForm.expectedDate = null
  showStatusDialog.value = true
}

// 获取可用状态
const getAvailableStatuses = (currentStatus) => {
  // 定义状态流转规则
  const statusFlow = {
    '待入库': ['库存中'],
    '库存中': ['使用中', '维修中', '停用'],
    '使用中': ['维修中', '库存中', '停用', '已报废'],
    '维修中': ['使用中', '库存中', '停用', '已报废'],
    '借用中': ['使用中', '库存中'],
    '停用': ['库存中', '已报废'],
    '已报废': []
  }

  const availableStatuses = statusFlow[currentStatus] || []
  return assetStatuses.value.filter(status => availableStatuses.includes(status.value))
}

// 处理状态变更
const handleStatusChange = async () => {
  if (!statusFormRef.value) return

  try {
    await statusFormRef.value.validate()
    statusLoading.value = true

    await changeAssetStatus(statusForm.assetId, statusForm.newStatus, statusForm.reason)
    ElMessage.success('状态变更成功')
    showStatusDialog.value = false
    loadAssetList()
  } catch (error) {
    console.error('状态变更失败:', error)
    ElMessage.error('状态变更失败')
  } finally {
    statusLoading.value = false
  }
}

// 关闭状态对话框
const handleCloseStatusDialog = (done) => {
  statusForm.newStatus = ''
  statusForm.reason = ''
  statusForm.expectedDate = null
  done()
}

// 处理资产操作
const handleAssetAction = (command, asset) => {
  switch (command) {
    case 'edit':
      editAsset(asset)
      break
    case 'assign':
      showAssignAssetDialog(asset)
      break
    case 'maintenance':
      maintenanceAsset(asset)
      break
    case 'retire':
      retireAsset(asset)
      break
  }
}

// 编辑资产
const editAsset = async (asset) => {
  console.log('编辑资产:', asset)
  try {
    // 如果是从详情页面打开编辑，先关闭详情对话框
    detailDialogVisible.value = false

    // 获取最新的资产详情
    console.log('获取资产详情用于编辑...')
    const response = await getAssetDetail(asset.id)
    console.log('编辑用API响应:', response)

    if (response && response.success) {
      const assetData = response.data

      // 填充编辑表单
      Object.assign(editForm, {
        id: assetData.id,
        name: assetData.name,
        category: assetData.category,
        model: assetData.model,
        serialNumber: assetData.serialNumber,
        status: assetData.status,
        user: assetData.user,
        department: assetData.department,
        location: assetData.location,
        vendor: assetData.vendor,
        price: assetData.price
      })

      console.log('填充编辑表单:', editForm)
      editDialogVisible.value = true
      console.log('显示编辑对话框')
    } else {
      const errorMsg = response?.message || response?.msg || '获取资产信息失败'
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('获取资产信息失败:', error)
    // 处理不同类型的错误
    let errorMsg = '获取资产信息失败'
    if (error && typeof error === 'object') {
      if (error.msg) {
        errorMsg = error.msg
      } else if (error.message) {
        errorMsg = error.message
      } else if (error.code === 404) {
        errorMsg = '资产不存在'
      }
    }
    ElMessage.error(errorMsg)
  }
}

// 显示分配对话框
const showAssignAssetDialog = (asset) => {
  assignForm.assetId = asset.id
  assignForm.assetName = asset.name
  assignForm.assetCode = asset.assetCode
  assignForm.userId = ''
  assignForm.userName = ''
  assignForm.department = ''
  assignForm.location = ''
  assignForm.description = ''
  showAssignDialog.value = true
}

// 用户变更处理
const handleUserChange = (userId) => {
  const user = users.value.find(u => u.id === userId)
  if (user) {
    assignForm.userName = user.name
    assignForm.department = user.department
  }
}

// 处理分配
const handleAssign = async () => {
  if (!assignFormRef.value) return

  try {
    await assignFormRef.value.validate()
    assignLoading.value = true

    const assignData = {
      userId: assignForm.userId,
      userName: assignForm.userName,
      department: assignForm.department,
      location: assignForm.location
    }

    await assignAsset(assignForm.assetId, assignData)
    ElMessage.success('资产分配成功')
    showAssignDialog.value = false
    loadAssetList()
  } catch (error) {
    console.error('资产分配失败:', error)
    ElMessage.error('资产分配失败')
  } finally {
    assignLoading.value = false
  }
}

// 关闭分配对话框
const handleCloseAssignDialog = (done) => {
  Object.keys(assignForm).forEach(key => {
    assignForm[key] = ''
  })
  done()
}

// 维修资产
const maintenanceAsset = (asset) => {
  showStatusChangeDialog(asset)
  // 预设为维修状态
  statusForm.newStatus = '维修中'
}

// 报废资产
const retireAsset = (asset) => {
  ElMessageBox.confirm(
    `确定要报废资产 "${asset.name}" 吗？此操作不可逆。`,
    '确认报废',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await changeAssetStatus(asset.id, '已报废', '资产报废处理')
      ElMessage.success('资产报废成功')
      loadAssetList()
    } catch (error) {
      console.error('资产报废失败:', error)
      ElMessage.error('资产报废失败')
    }
  }).catch(() => {
    ElMessage.info('已取消报废')
  })
}

// 批量操作
const batchStatusChange = () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请先选择资产')
    return
  }
  ElMessage.info('批量状态变更功能开发中')
}

const batchAssign = () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请先选择资产')
    return
  }
  ElMessage.info('批量分配功能开发中')
}

const batchExport = () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请先选择资产')
    return
  }
  ElMessage.info('批量导出功能开发中')
}

const batchDelete = () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请先选择资产')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedAssets.value.length} 项资产吗？此操作不可逆。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const deletePromises = selectedAssets.value.map(asset => deleteAsset(asset.id))
      await Promise.all(deletePromises)
      ElMessage.success('批量删除成功')
      selectedAssets.value = []
      loadAssetList()
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 导出资产
const exportAssets = () => {
  ElMessage.info('导出功能开发中')
}

// 详情对话框相关函数
const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  currentAssetDetail.value = null
}

const openEditDialog = () => {
  if (currentAssetDetail.value) {
    editAsset(currentAssetDetail.value)
  }
}

// 编辑对话框相关函数
const handleEditDialogClose = () => {
  editDialogVisible.value = false
  // 重置表单
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

const saveAsset = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    editLoading.value = true

    const response = await updateAsset(editForm.id, editForm)
    if (response.success) {
      ElMessage.success('资产更新成功')
      editDialogVisible.value = false

      // 刷新列表
      await loadAssetList()

      // 如果详情对话框是打开的，也需要刷新详情
      if (detailDialogVisible.value && currentAssetDetail.value?.id === editForm.id) {
        const detailResponse = await getAssetDetail(editForm.id)
        if (detailResponse.success) {
          currentAssetDetail.value = detailResponse.data
        }
      }
    } else {
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('保存资产失败:', error)
    ElMessage.error('保存失败')
  } finally {
    editLoading.value = false
  }
}

// 辅助函数
const getAssetTypeTagType = (category) => {
  const typeMap = {
    'server': 'success',
    'laptop': 'primary',
    'desktop': 'primary',
    'network': 'warning',
    'storage': 'info',
    'printer': 'danger',
    'mobile': 'success',
    'other': ''
  }
  return typeMap[category] || ''
}

const getAssetTypeName = (category) => {
  const nameMap = {
    'server': '服务器',
    'laptop': '笔记本电脑',
    'desktop': '台式机',
    'network': '网络设备',
    'storage': '存储设备',
    'printer': '打印设备',
    'mobile': '移动设备',
    'other': '其他设备'
  }
  return nameMap[category] || category
}

const getTimelineType = (status) => {
  const typeMap = {
    '待入库': 'info',
    '库存中': 'primary',
    '使用中': 'success',
    '维修中': 'warning',
    '已报废': 'danger'
  }
  return typeMap[status] || 'info'
}

const getSpecLabel = (key) => {
  const labelMap = {
    'cpu': 'CPU',
    'memory': '内存',
    'storage': '存储',
    'os': '操作系统',
    'network': '网络',
    'display': '显示器',
    'warranty': '保修期',
    'power': '功率'
  }
  return labelMap[key] || key
}

// 生命周期
onMounted(() => {
  initData()
})
</script>

<style scoped>
.asset-lifecycle {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 生命周期流程卡片 */
.lifecycle-flow-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.lifecycle-flow {
  padding: 20px 0;
}

.flow-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.flow-step {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 12px;
  border-radius: 8px;
  min-width: 120px;
}

.flow-step:hover {
  background-color: #f8fbff;
  transform: translateY(-2px);
}

.flow-step.active {
  background-color: #e3f2fd;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin-right: 12px;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.step-count {
  font-size: 20px;
  font-weight: 700;
  color: #1976D2;
}

.step-arrow {
  margin-left: 12px;
  color: #c0c4cc;
  font-size: 16px;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 批量操作 */
.batch-actions {
  margin-bottom: 20px;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

/* 资产列表卡片 */
.asset-list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 表格样式 */
.asset-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.asset-icon {
  color: #1976D2;
  font-size: 16px;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.asset-card-col {
  margin-bottom: 20px;
}

.asset-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.asset-card:hover {
  border-color: #1976D2;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
  transform: translateY(-2px);
}

.asset-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.asset-card .asset-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1976D2;
  font-size: 18px;
}

.card-content {
  margin-bottom: 12px;
}

.asset-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.asset-code {
  font-size: 12px;
  color: #666;
  margin: 0 0 12px 0;
}

.asset-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.info-item .label {
  color: #666;
  font-weight: 500;
}

.info-item .value {
  color: #333;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 空状态 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 对话框 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.asset-info-display {
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #333;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .flow-steps {
    justify-content: center;
  }

  .flow-step {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .asset-lifecycle {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
    flex-wrap: wrap;
  }

  .flow-steps {
    flex-direction: column;
    align-items: stretch;
  }

  .flow-step {
    min-width: auto;
  }

  .step-arrow {
    display: none;
  }

  .filter-card .el-form {
    flex-direction: column;
  }

  .filter-card .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .asset-card-col {
    padding: 0 10px;
  }
}

/* 详情对话框样式 */
.asset-detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 资产信息网格样式 */
.asset-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.info-item span {
  color: #303133;
}

/* 生命周期历史样式 */
.lifecycle-history {
  position: relative;
  padding-left: 20px;
}

.history-item {
  position: relative;
  padding-bottom: 20px;
  border-left: 2px solid #e4e7ed;
}

.history-item:last-child {
  border-left: none;
  padding-bottom: 0;
}

.history-dot {
  position: absolute;
  left: -6px;
  top: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #e4e7ed;
}

.history-dot.primary {
  background-color: #409eff;
}

.history-dot.success {
  background-color: #67c23a;
}

.history-dot.warning {
  background-color: #e6a23c;
}

.history-dot.danger {
  background-color: #f56c6c;
}

.history-dot.info {
  background-color: #909399;
}

.history-content {
  padding-left: 20px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.history-title {
  font-weight: 600;
  color: #303133;
}

.history-time {
  font-size: 12px;
  color: #909399;
}

.history-desc {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.history-operator {
  color: #909399;
  font-size: 12px;
}

/* 技术规格网格样式 */
.specifications-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.spec-item label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.spec-item span {
  color: #303133;
}

.timeline-content {
  padding: 8px 0;
}

.timeline-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.timeline-operator {
  color: #909399;
  font-size: 12px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
}

.loading-container .el-icon {
  font-size: 32px;
  margin-bottom: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 编辑表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .asset-detail-content {
    max-height: 500px;
  }

  .asset-info-grid,
  .specifications-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .info-item,
  .spec-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .info-item label,
  .spec-item label {
    min-width: auto;
  }

  .history-content {
    padding-left: 15px;
  }

  .history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
