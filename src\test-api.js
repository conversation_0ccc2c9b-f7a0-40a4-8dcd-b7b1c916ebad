// 简单的API测试脚本
import { 
  getReleaseList, 
  getReleaseStatistics,
  validateReleaseChangeMapping 
} from './api/releaseApi.js'

console.log('开始测试API...')

// 测试获取发布列表
try {
  const listResult = await getReleaseList({ page: 1, pageSize: 10 })
  console.log('✅ getReleaseList 测试通过:', listResult.success)
} catch (error) {
  console.error('❌ getReleaseList 测试失败:', error)
}

// 测试获取统计数据
try {
  const statsResult = await getReleaseStatistics()
  console.log('✅ getReleaseStatistics 测试通过:', statsResult.success)
} catch (error) {
  console.error('❌ getReleaseStatistics 测试失败:', error)
}

// 测试变更关联验证
try {
  const validationResult = await validateReleaseChangeMapping('REL-001', 'CHG-001')
  console.log('✅ validateReleaseChangeMapping 测试通过:', validationResult.success)
} catch (error) {
  console.error('❌ validateReleaseChangeMapping 测试失败:', error)
}

console.log('API测试完成')
