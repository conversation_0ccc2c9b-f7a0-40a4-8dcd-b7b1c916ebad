<template>
  <div class="smart-routing-engine">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>智能路由引擎</span>
          <el-switch v-model="routingEnabled" @change="toggleRouting">
            <template #active-text>启用</template>
            <template #inactive-text>禁用</template>
          </el-switch>
        </div>
      </template>
      
      <!-- 路由配置 -->
      <div class="routing-config">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="路由规则" name="rules">
            <div class="rules-section">
              <div class="section-header">
                <h3>路由规则配置</h3>
                <el-button type="primary" size="small" @click="addRule">
                  <el-icon><Plus /></el-icon>
                  添加规则
                </el-button>
              </div>
              
              <el-table :data="routingRules" style="width: 100%">
                <el-table-column prop="name" label="规则名称" />
                <el-table-column prop="priority" label="优先级" width="80" />
                <el-table-column prop="condition" label="触发条件" />
                <el-table-column prop="action" label="路由动作" />
                <el-table-column prop="enabled" label="状态" width="80">
                  <template #default="scope">
                    <el-switch v-model="scope.row.enabled" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button type="primary" size="small" text @click="editRule(scope.row)">
                      编辑
                    </el-button>
                    <el-button type="danger" size="small" text @click="deleteRule(scope.row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="技能标签" name="skills">
            <div class="skills-section">
              <div class="section-header">
                <h3>技能标签管理</h3>
                <el-button type="primary" size="small" @click="addSkill">
                  <el-icon><Plus /></el-icon>
                  添加技能
                </el-button>
              </div>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-card>
                    <template #header>技能分类</template>
                    <el-tree
                      :data="skillCategories"
                      :props="{ children: 'children', label: 'name' }"
                      node-key="id"
                      :expand-on-click-node="false"
                    >
                      <template #default="{ node, data }">
                        <div class="skill-node">
                          <span>{{ data.name }}</span>
                          <span v-if="data.count" class="skill-count">({{ data.count }})</span>
                        </div>
                      </template>
                    </el-tree>
                  </el-card>
                </el-col>
                <el-col :span="12">
                  <el-card>
                    <template #header>工程师技能</template>
                    <div class="engineer-skills">
                      <div v-for="engineer in engineers" :key="engineer.id" class="engineer-item">
                        <div class="engineer-info">
                          <el-avatar :size="32">{{ engineer.name[0] }}</el-avatar>
                          <div class="engineer-details">
                            <div class="engineer-name">{{ engineer.name }}</div>
                            <div class="engineer-role">{{ engineer.role }}</div>
                          </div>
                        </div>
                        <div class="engineer-skills-tags">
                          <el-tag
                            v-for="skill in engineer.skills"
                            :key="skill"
                            size="small"
                            :type="getSkillLevel(skill)"
                          >
                            {{ skill }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="负载均衡" name="loadbalance">
            <div class="loadbalance-section">
              <div class="section-header">
                <h3>负载均衡配置</h3>
              </div>
              
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-card>
                    <template #header>均衡策略</template>
                    <el-radio-group v-model="loadBalanceStrategy" direction="vertical">
                      <el-radio label="round_robin">轮询分配</el-radio>
                      <el-radio label="least_loaded">最少负载</el-radio>
                      <el-radio label="skill_based">技能匹配</el-radio>
                      <el-radio label="weighted">权重分配</el-radio>
                    </el-radio-group>
                  </el-card>
                </el-col>
                <el-col :span="16">
                  <el-card>
                    <template #header>工程师负载状态</template>
                    <div class="load-status">
                      <div v-for="engineer in engineers" :key="engineer.id" class="load-item">
                        <div class="load-info">
                          <span class="engineer-name">{{ engineer.name }}</span>
                          <span class="load-percentage">{{ engineer.workload }}%</span>
                        </div>
                        <el-progress
                          :percentage="engineer.workload"
                          :status="getLoadStatus(engineer.workload)"
                          :stroke-width="8"
                        />
                        <div class="load-details">
                          <span>当前工单: {{ engineer.currentTickets }}</span>
                          <span>今日处理: {{ engineer.todayProcessed }}</span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="路由统计" name="statistics">
            <div class="statistics-section">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="自动路由成功率" :value="routingStats.successRate" suffix="%" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="平均路由时间" :value="routingStats.avgTime" suffix="ms" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="今日路由次数" :value="routingStats.todayCount" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="规则命中率" :value="routingStats.ruleHitRate" suffix="%" />
                </el-col>
              </el-row>
              
              <div class="routing-chart">
                <div ref="routingChart" style="height: 300px;"></div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
    
    <!-- 添加规则对话框 -->
    <el-dialog v-model="showRuleDialog" title="路由规则配置" width="60%">
      <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="120px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="ruleForm.priority" :min="1" :max="100" />
          <span class="form-help">数值越大优先级越高</span>
        </el-form-item>
        
        <el-form-item label="触发条件">
          <div class="condition-builder">
            <el-row :gutter="10" v-for="(condition, index) in ruleForm.conditions" :key="index">
              <el-col :span="6">
                <el-select v-model="condition.field" placeholder="选择字段">
                  <el-option label="工单类型" value="type" />
                  <el-option label="优先级" value="priority" />
                  <el-option label="分类" value="category" />
                  <el-option label="关键词" value="keyword" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="condition.operator" placeholder="操作符">
                  <el-option label="等于" value="equals" />
                  <el-option label="包含" value="contains" />
                  <el-option label="不等于" value="not_equals" />
                </el-select>
              </el-col>
              <el-col :span="10">
                <el-input v-model="condition.value" placeholder="条件值" />
              </el-col>
              <el-col :span="4">
                <el-button type="danger" size="small" @click="removeCondition(index)">删除</el-button>
              </el-col>
            </el-row>
            <el-button type="text" @click="addCondition">+ 添加条件</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="路由动作" prop="action">
          <el-radio-group v-model="ruleForm.actionType">
            <el-radio label="assign_to_user">分配给指定用户</el-radio>
            <el-radio label="assign_to_group">分配给指定组</el-radio>
            <el-radio label="auto_assign">自动分配</el-radio>
          </el-radio-group>
          
          <div v-if="ruleForm.actionType === 'assign_to_user'" class="action-config">
            <el-select v-model="ruleForm.targetUser" placeholder="选择用户">
              <el-option v-for="engineer in engineers" :key="engineer.id" :label="engineer.name" :value="engineer.id" />
            </el-select>
          </div>
          
          <div v-if="ruleForm.actionType === 'assign_to_group'" class="action-config">
            <el-select v-model="ruleForm.targetGroup" placeholder="选择组">
              <el-option label="桌面支持组" value="desktop" />
              <el-option label="网络运维组" value="network" />
              <el-option label="应用支持组" value="application" />
            </el-select>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showRuleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRule">保存规则</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const routingEnabled = ref(true)
const activeTab = ref('rules')
const showRuleDialog = ref(false)
const ruleFormRef = ref()

// 路由规则
const routingRules = ref([
  {
    id: 1,
    name: '网络问题自动路由',
    priority: 90,
    condition: '类型=事件 且 分类包含"网络"',
    action: '分配给网络运维组',
    enabled: true
  },
  {
    id: 2,
    name: '紧急工单升级',
    priority: 95,
    condition: '优先级=紧急',
    action: '分配给高级工程师',
    enabled: true
  }
])

// 技能分类
const skillCategories = ref([
  {
    id: 1,
    name: '硬件技能',
    count: 15,
    children: [
      { id: 11, name: '服务器维护', count: 5 },
      { id: 12, name: '网络设备', count: 8 },
      { id: 13, name: '存储设备', count: 2 }
    ]
  },
  {
    id: 2,
    name: '软件技能',
    count: 20,
    children: [
      { id: 21, name: '操作系统', count: 10 },
      { id: 22, name: '数据库', count: 6 },
      { id: 23, name: '应用软件', count: 4 }
    ]
  }
])

// 工程师列表
const engineers = ref([
  {
    id: 1,
    name: '张工',
    role: '高级网络工程师',
    skills: ['网络设备', '路由交换', 'TCP/IP'],
    workload: 75,
    currentTickets: 8,
    todayProcessed: 3
  },
  {
    id: 2,
    name: '李工',
    role: '系统管理员',
    skills: ['Windows', 'Linux', '虚拟化'],
    workload: 60,
    currentTickets: 6,
    todayProcessed: 5
  },
  {
    id: 3,
    name: '王工',
    role: '应用支持工程师',
    skills: ['Java', 'MySQL', 'Web应用'],
    workload: 45,
    currentTickets: 4,
    todayProcessed: 2
  }
])

// 负载均衡策略
const loadBalanceStrategy = ref('skill_based')

// 路由统计
const routingStats = ref({
  successRate: 92,
  avgTime: 150,
  todayCount: 45,
  ruleHitRate: 88
})

// 规则表单
const ruleForm = reactive({
  name: '',
  priority: 50,
  conditions: [
    { field: '', operator: '', value: '' }
  ],
  actionType: 'auto_assign',
  targetUser: '',
  targetGroup: ''
})

// 表单验证规则
const ruleRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  priority: [{ required: true, message: '请设置优先级', trigger: 'blur' }]
}

// 图表引用
const routingChart = ref()

// 切换路由状态
const toggleRouting = (enabled) => {
  ElMessage.success(enabled ? '智能路由已启用' : '智能路由已禁用')
}

// 添加规则
const addRule = () => {
  showRuleDialog.value = true
  resetRuleForm()
}

// 编辑规则
const editRule = (rule) => {
  ElMessage.info(`编辑规则: ${rule.name}`)
}

// 删除规则
const deleteRule = (rule) => {
  ElMessageBox.confirm(`确定要删除规则 "${rule.name}" 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = routingRules.value.findIndex(r => r.id === rule.id)
    if (index > -1) {
      routingRules.value.splice(index, 1)
      ElMessage.success('规则已删除')
    }
  })
}

// 添加技能
const addSkill = () => {
  ElMessage.info('添加技能功能')
}

// 获取技能等级
const getSkillLevel = (skill) => {
  // 根据技能返回不同的标签类型
  const levelMap = {
    '网络设备': 'success',
    '路由交换': 'warning',
    'TCP/IP': 'info'
  }
  return levelMap[skill] || ''
}

// 获取负载状态
const getLoadStatus = (workload) => {
  if (workload >= 80) return 'exception'
  if (workload >= 60) return 'warning'
  return 'success'
}

// 添加条件
const addCondition = () => {
  ruleForm.conditions.push({ field: '', operator: '', value: '' })
}

// 移除条件
const removeCondition = (index) => {
  if (ruleForm.conditions.length > 1) {
    ruleForm.conditions.splice(index, 1)
  }
}

// 重置规则表单
const resetRuleForm = () => {
  ruleForm.name = ''
  ruleForm.priority = 50
  ruleForm.conditions = [{ field: '', operator: '', value: '' }]
  ruleForm.actionType = 'auto_assign'
  ruleForm.targetUser = ''
  ruleForm.targetGroup = ''
}

// 保存规则
const saveRule = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const newRule = {
        id: Date.now(),
        name: ruleForm.name,
        priority: ruleForm.priority,
        condition: '自定义条件',
        action: '自定义动作',
        enabled: true
      }
      
      routingRules.value.unshift(newRule)
      ElMessage.success('规则已保存')
      showRuleDialog.value = false
    }
  })
}

// 初始化图表
const initChart = () => {
  if (!routingChart.value) return
  
  const chartInstance = echarts.init(routingChart.value)
  chartInstance.setOption({
    title: {
      text: '路由成功率趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100
    },
    series: [
      {
        name: '成功率',
        type: 'line',
        data: [88, 92, 89, 94, 91, 95, 92],
        itemStyle: {
          color: '#4CAF50'
        },
        areaStyle: {
          color: 'rgba(76, 175, 80, 0.1)'
        }
      }
    ]
  })
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.smart-routing-engine {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.routing-config {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.skill-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skill-count {
  color: #999;
  font-size: 12px;
}

.engineer-skills {
  max-height: 400px;
  overflow-y: auto;
}

.engineer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.engineer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.engineer-details {
  display: flex;
  flex-direction: column;
}

.engineer-name {
  font-weight: 500;
  color: #333;
}

.engineer-role {
  font-size: 12px;
  color: #666;
}

.engineer-skills-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.load-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.load-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.load-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.load-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.statistics-section {
  padding: 20px 0;
}

.routing-chart {
  margin-top: 30px;
}

.condition-builder {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 16px;
}

.action-config {
  margin-top: 12px;
}

.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

@media (max-width: 768px) {
  .engineer-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .load-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .load-details {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
