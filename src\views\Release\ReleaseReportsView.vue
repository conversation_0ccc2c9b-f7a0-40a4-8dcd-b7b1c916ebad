<template>
  <div class="release-reports-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>发布报表</h2>
          <p>发布数据分析、趋势洞察和绩效评估</p>
        </div>
        <div class="action-section">
          <el-button :icon="DataBoard" @click="$router.push('/release')">
            发布管理
          </el-button>
          <el-button :icon="Calendar" @click="$router.push('/release/dashboard')">
            发布看板
          </el-button>
          <el-button :icon="TrendCharts" @click="$router.push('/release/calendar')">
            发布日历
          </el-button>
        </div>
      </div>
    </div>

    <!-- 发布报表组件 -->
    <ReleaseReports />
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { 
  DataBoard, Calendar, TrendCharts
} from '@element-plus/icons-vue'
import ReleaseReports from '@/components/ReleaseReports.vue'

// 路由
const router = useRouter()
</script>

<style scoped>
.release-reports-view {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .release-reports-view {
    padding: 10px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .action-section {
    justify-content: center;
  }
}
</style>
