<template>
  <div class="cmdb-test">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Tools /></el-icon>
            配置管理模块测试
          </h1>
          <p class="page-description">测试配置管理模块的所有功能和页面导航</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="runAllTests">
            <el-icon><VideoPlay /></el-icon>
            运行所有测试
          </el-button>
          <el-button @click="resetTests">
            <el-icon><Refresh /></el-icon>
            重置测试
          </el-button>
        </div>
      </div>
    </div>

    <!-- 测试结果概览 -->
    <div class="test-overview">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="stat in testStats" :key="stat.key">
          <div class="test-card" :class="stat.status">
            <div class="card-content">
              <div class="card-icon">
                <el-icon :size="28">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ stat.value }}</div>
                <div class="card-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 功能测试列表 -->
    <div class="test-content">
      <el-row :gutter="20">
        <!-- 页面导航测试 -->
        <el-col :span="12">
          <el-card class="test-section">
            <template #header>
              <div class="section-header">
                <span>页面导航测试</span>
                <el-button type="text" size="small" @click="testAllPages">
                  <el-icon><Right /></el-icon>
                  测试所有页面
                </el-button>
              </div>
            </template>
            
            <div class="test-list">
              <div
                v-for="page in cmdbPages"
                :key="page.path"
                class="test-item"
                :class="getTestStatus(page.path)"
              >
                <div class="test-info">
                  <div class="test-name">{{ page.name }}</div>
                  <div class="test-path">{{ page.path }}</div>
                </div>
                <div class="test-actions">
                  <el-button size="small" @click="navigateToPage(page)">
                    <el-icon><View /></el-icon>
                    访问
                  </el-button>
                  <el-button size="small" @click="testPage(page)">
                    <el-icon><VideoPlay /></el-icon>
                    测试
                  </el-button>
                </div>
                <div class="test-status">
                  <el-icon v-if="pageTests[page.path] === 'success'" color="#4CAF50">
                    <CircleCheck />
                  </el-icon>
                  <el-icon v-else-if="pageTests[page.path] === 'error'" color="#F44336">
                    <CircleClose />
                  </el-icon>
                  <el-icon v-else-if="pageTests[page.path] === 'testing'" color="#FF9800">
                    <Loading />
                  </el-icon>
                  <el-icon v-else color="#C0C4CC">
                    <Minus />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 功能测试 -->
        <el-col :span="12">
          <el-card class="test-section">
            <template #header>
              <div class="section-header">
                <span>功能测试</span>
                <el-button type="text" size="small" @click="testAllFeatures">
                  <el-icon><Right /></el-icon>
                  测试所有功能
                </el-button>
              </div>
            </template>
            
            <div class="test-list">
              <div
                v-for="feature in cmdbFeatures"
                :key="feature.key"
                class="test-item"
                :class="getTestStatus(feature.key)"
              >
                <div class="test-info">
                  <div class="test-name">{{ feature.name }}</div>
                  <div class="test-desc">{{ feature.description }}</div>
                </div>
                <div class="test-actions">
                  <el-button size="small" @click="testFeature(feature)">
                    <el-icon><VideoPlay /></el-icon>
                    测试
                  </el-button>
                </div>
                <div class="test-status">
                  <el-icon v-if="featureTests[feature.key] === 'success'" color="#4CAF50">
                    <CircleCheck />
                  </el-icon>
                  <el-icon v-else-if="featureTests[feature.key] === 'error'" color="#F44336">
                    <CircleClose />
                  </el-icon>
                  <el-icon v-else-if="featureTests[feature.key] === 'testing'" color="#FF9800">
                    <Loading />
                  </el-icon>
                  <el-icon v-else color="#C0C4CC">
                    <Minus />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 测试日志 -->
    <div class="test-logs">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>测试日志</span>
            <el-button type="text" size="small" @click="clearLogs">
              <el-icon><Delete /></el-icon>
              清空日志
            </el-button>
          </div>
        </template>
        
        <div class="log-container">
          <div
            v-for="log in testLogs"
            :key="log.id"
            class="log-item"
            :class="`log-${log.level}`"
          >
            <div class="log-time">{{ log.time }}</div>
            <div class="log-level">{{ log.level.toUpperCase() }}</div>
            <div class="log-message">{{ log.message }}</div>
          </div>
          
          <div class="log-empty" v-if="testLogs.length === 0">
            <el-icon :size="32" color="#C0C4CC"><Document /></el-icon>
            <p>暂无测试日志</p>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const testing = ref(false)
const pageTests = reactive({})
const featureTests = reactive({})
const testLogs = ref([])

// CMDB页面配置
const cmdbPages = ref([
  { path: '/cmdb', name: 'CMDB概览', description: '配置管理数据库概览页面' },
  { path: '/cmdb/graph', name: '配置项图谱', description: '配置项关系可视化图谱' },
  { path: '/cmdb/topology', name: '拓扑图', description: 'IT基础设施拓扑图' },
  { path: '/cmdb/monitor', name: '拓扑监控', description: '实时拓扑监控中心' },
  { path: '/cmdb/discovery', name: '自动发现', description: '配置项自动发现管理' }
])

// CMDB功能配置
const cmdbFeatures = ref([
  {
    key: 'data-quality',
    name: '数据质量监控',
    description: '检查配置项数据的完整性和一致性',
    testFunction: 'testDataQuality'
  },
  {
    key: 'relationship-mapping',
    name: '关系映射',
    description: '测试配置项之间的关系映射功能',
    testFunction: 'testRelationshipMapping'
  },
  {
    key: 'auto-discovery',
    name: '自动发现',
    description: '测试配置项自动发现和识别功能',
    testFunction: 'testAutoDiscovery'
  },
  {
    key: 'topology-visualization',
    name: '拓扑可视化',
    description: '测试拓扑图的渲染和交互功能',
    testFunction: 'testTopologyVisualization'
  },
  {
    key: 'real-time-monitoring',
    name: '实时监控',
    description: '测试实时监控和告警联动功能',
    testFunction: 'testRealTimeMonitoring'
  },
  {
    key: 'impact-analysis',
    name: '影响分析',
    description: '测试故障影响范围分析功能',
    testFunction: 'testImpactAnalysis'
  }
])

// 测试统计
const testStats = computed(() => {
  const totalPages = cmdbPages.value.length
  const totalFeatures = cmdbFeatures.value.length
  const totalTests = totalPages + totalFeatures
  
  const successPages = Object.values(pageTests).filter(status => status === 'success').length
  const successFeatures = Object.values(featureTests).filter(status => status === 'success').length
  const totalSuccess = successPages + successFeatures
  
  const errorPages = Object.values(pageTests).filter(status => status === 'error').length
  const errorFeatures = Object.values(featureTests).filter(status => status === 'error').length
  const totalErrors = errorPages + errorFeatures
  
  const successRate = totalTests > 0 ? Math.round((totalSuccess / totalTests) * 100) : 0
  
  return [
    {
      key: 'total',
      label: '总测试数',
      value: totalTests,
      icon: 'Document',
      status: 'info'
    },
    {
      key: 'success',
      label: '成功测试',
      value: totalSuccess,
      icon: 'CircleCheck',
      status: 'success'
    },
    {
      key: 'error',
      label: '失败测试',
      value: totalErrors,
      icon: 'CircleClose',
      status: 'danger'
    },
    {
      key: 'rate',
      label: '成功率',
      value: `${successRate}%`,
      icon: 'TrendCharts',
      status: successRate >= 80 ? 'success' : successRate >= 60 ? 'warning' : 'danger'
    }
  ]
})

// 方法
const addLog = (level, message) => {
  const log = {
    id: Date.now(),
    time: new Date().toLocaleTimeString(),
    level,
    message
  }
  testLogs.value.unshift(log)
  
  // 限制日志数量
  if (testLogs.value.length > 100) {
    testLogs.value = testLogs.value.slice(0, 100)
  }
}

const getTestStatus = (key) => {
  const pageStatus = pageTests[key]
  const featureStatus = featureTests[key]
  const status = pageStatus || featureStatus
  
  return {
    'test-success': status === 'success',
    'test-error': status === 'error',
    'test-testing': status === 'testing'
  }
}

const navigateToPage = (page) => {
  router.push(page.path)
  addLog('info', `导航到页面: ${page.name} (${page.path})`)
}

const testPage = async (page) => {
  pageTests[page.path] = 'testing'
  addLog('info', `开始测试页面: ${page.name}`)
  
  try {
    // 模拟页面测试
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 检查页面是否可访问
    const canAccess = await checkPageAccess(page.path)
    
    if (canAccess) {
      pageTests[page.path] = 'success'
      addLog('success', `页面测试成功: ${page.name}`)
      ElMessage.success(`${page.name} 测试通过`)
    } else {
      pageTests[page.path] = 'error'
      addLog('error', `页面测试失败: ${page.name}`)
      ElMessage.error(`${page.name} 测试失败`)
    }
  } catch (error) {
    pageTests[page.path] = 'error'
    addLog('error', `页面测试异常: ${page.name} - ${error.message}`)
    ElMessage.error(`${page.name} 测试异常`)
  }
}

const checkPageAccess = async (path) => {
  // 模拟页面访问检查
  return new Promise(resolve => {
    setTimeout(() => {
      // 假设所有页面都可以访问
      resolve(true)
    }, 500)
  })
}

const testFeature = async (feature) => {
  featureTests[feature.key] = 'testing'
  addLog('info', `开始测试功能: ${feature.name}`)
  
  try {
    // 调用对应的测试函数
    const testFunction = window[feature.testFunction] || (() => Promise.resolve(true))
    const result = await testFunction()
    
    if (result) {
      featureTests[feature.key] = 'success'
      addLog('success', `功能测试成功: ${feature.name}`)
      ElMessage.success(`${feature.name} 测试通过`)
    } else {
      featureTests[feature.key] = 'error'
      addLog('error', `功能测试失败: ${feature.name}`)
      ElMessage.error(`${feature.name} 测试失败`)
    }
  } catch (error) {
    featureTests[feature.key] = 'error'
    addLog('error', `功能测试异常: ${feature.name} - ${error.message}`)
    ElMessage.error(`${feature.name} 测试异常`)
  }
}

const testAllPages = async () => {
  addLog('info', '开始批量测试所有页面')
  
  for (const page of cmdbPages.value) {
    await testPage(page)
    // 添加延迟避免过快执行
    await new Promise(resolve => setTimeout(resolve, 200))
  }
  
  addLog('info', '所有页面测试完成')
  ElMessage.success('所有页面测试完成')
}

const testAllFeatures = async () => {
  addLog('info', '开始批量测试所有功能')
  
  for (const feature of cmdbFeatures.value) {
    await testFeature(feature)
    // 添加延迟避免过快执行
    await new Promise(resolve => setTimeout(resolve, 300))
  }
  
  addLog('info', '所有功能测试完成')
  ElMessage.success('所有功能测试完成')
}

const runAllTests = async () => {
  if (testing.value) return
  
  testing.value = true
  addLog('info', '开始运行所有测试')
  
  try {
    await testAllPages()
    await testAllFeatures()
    addLog('success', '所有测试运行完成')
    ElMessage.success('所有测试运行完成')
  } catch (error) {
    addLog('error', `测试运行异常: ${error.message}`)
    ElMessage.error('测试运行异常')
  } finally {
    testing.value = false
  }
}

const resetTests = () => {
  Object.keys(pageTests).forEach(key => {
    delete pageTests[key]
  })
  Object.keys(featureTests).forEach(key => {
    delete featureTests[key]
  })
  testLogs.value = []
  addLog('info', '测试状态已重置')
  ElMessage.info('测试状态已重置')
}

const clearLogs = () => {
  testLogs.value = []
  ElMessage.info('测试日志已清空')
}

// 模拟测试函数
window.testDataQuality = () => Promise.resolve(true)
window.testRelationshipMapping = () => Promise.resolve(true)
window.testAutoDiscovery = () => Promise.resolve(true)
window.testTopologyVisualization = () => Promise.resolve(true)
window.testRealTimeMonitoring = () => Promise.resolve(true)
window.testImpactAnalysis = () => Promise.resolve(true)
</script>

<style scoped>
.cmdb-test {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 测试概览卡片 */
.test-overview {
  margin-bottom: 24px;
}

.test-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid #e5e7eb;
  transition: all 0.3s ease;
}

.test-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.test-card.info {
  border-left-color: #3b82f6;
}

.test-card.success {
  border-left-color: #10b981;
}

.test-card.warning {
  border-left-color: #f59e0b;
}

.test-card.danger {
  border-left-color: #ef4444;
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: #f3f4f6;
}

.test-card.info .card-icon {
  background: #dbeafe;
  color: #3b82f6;
}

.test-card.success .card-icon {
  background: #d1fae5;
  color: #10b981;
}

.test-card.warning .card-icon {
  background: #fef3c7;
  color: #f59e0b;
}

.test-card.danger .card-icon {
  background: #fee2e2;
  color: #ef4444;
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
}

/* 测试内容 */
.test-content {
  margin-bottom: 24px;
}

.test-section {
  height: 400px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-list {
  max-height: 320px;
  overflow-y: auto;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.test-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.test-item.test-success {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.test-item.test-error {
  background: #fef2f2;
  border-color: #fecaca;
}

.test-item.test-testing {
  background: #fffbeb;
  border-color: #fed7aa;
}

.test-info {
  flex: 1;
  margin-right: 12px;
}

.test-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.test-path,
.test-desc {
  font-size: 12px;
  color: #6b7280;
}

.test-actions {
  display: flex;
  gap: 8px;
  margin-right: 12px;
}

.test-status {
  width: 24px;
  display: flex;
  justify-content: center;
}

/* 测试日志 */
.test-logs {
  margin-bottom: 24px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #1f2937;
  border-radius: 4px;
  padding: 16px;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-time {
  color: #9ca3af;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
}

.log-message {
  flex: 1;
  color: #f3f4f6;
}

.log-info .log-level {
  color: #3b82f6;
}

.log-success .log-level {
  color: #10b981;
}

.log-error .log-level {
  color: #ef4444;
}

.log-empty {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.log-empty .el-icon {
  margin-bottom: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cmdb-test {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .test-section {
    height: auto;
    margin-bottom: 20px;
  }
  
  .test-list {
    max-height: 250px;
  }
  
  .test-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .test-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .log-container {
    max-height: 200px;
  }
  
  .log-item {
    flex-direction: column;
    gap: 4px;
  }
}

/* 自定义滚动条 */
.test-list::-webkit-scrollbar,
.log-container::-webkit-scrollbar {
  width: 6px;
}

.test-list::-webkit-scrollbar-track,
.log-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.test-list::-webkit-scrollbar-thumb,
.log-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.test-list::-webkit-scrollbar-thumb:hover,
.log-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
