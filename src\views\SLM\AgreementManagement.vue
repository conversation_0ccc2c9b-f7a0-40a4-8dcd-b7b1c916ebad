<template>
  <div class="agreement-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>协议管理</h2>
          <p>管理SLA/OLA/UC服务级别协议</p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
            新建协议
          </el-button>
          <el-button :icon="Upload" @click="showImportDialog = true">
            批量导入
          </el-button>
          <el-button :icon="Download" @click="exportAgreements">
            导出协议
          </el-button>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="协议类型">
            <el-select v-model="filterForm.type" clearable placeholder="全部类型">
              <el-option
                v-for="type in agreementTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" clearable placeholder="全部状态">
              <el-option label="草稿" value="draft" />
              <el-option label="活跃" value="active" />
              <el-option label="暂停" value="suspended" />
              <el-option label="过期" value="expired" />
            </el-select>
          </el-form-item>
          <el-form-item label="服务">
            <el-select v-model="filterForm.serviceId" clearable placeholder="全部服务">
              <el-option
                v-for="service in services"
                :key="service.id"
                :label="service.name"
                :value="service.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="filterForm.keyword"
              placeholder="搜索协议名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadAgreements">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 协议列表 -->
    <div class="agreements-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>协议列表</span>
            <div class="header-actions">
              <el-button type="text" @click="toggleView">
                <el-icon><component :is="viewMode === 'table' ? 'Grid' : 'List'" /></el-icon>
                {{ viewMode === 'table' ? '卡片视图' : '表格视图' }}
              </el-button>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-view">
          <el-table :data="agreements" v-loading="loading" stripe>
            <el-table-column prop="name" label="协议名称" min-width="200" />
            <el-table-column prop="type" label="类型" width="80">
              <template #default="{ row }">
                <el-tag :type="getTypeColor(row.type)">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="serviceName" label="关联服务" width="150" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)">{{ getStatusText(row.status) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="version" label="版本" width="80" />
            <el-table-column prop="owner" label="负责人" width="100" />
            <el-table-column prop="effectiveDate" label="生效日期" width="120" />
            <el-table-column prop="expiryDate" label="到期日期" width="120" />
            <el-table-column label="KPI数量" width="100">
              <template #default="{ row }">
                <span>{{ row.kpis?.length || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="viewAgreement(row)">
                  查看
                </el-button>
                <el-button type="text" size="small" @click="editAgreement(row)">
                  编辑
                </el-button>
                <el-button
                  v-if="row.status === 'draft'"
                  type="text"
                  size="small"
                  @click="activateAgreement(row)"
                >
                  激活
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  style="color: #f56c6c"
                  @click="deleteAgreement(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <el-row :gutter="20">
            <el-col
              v-for="agreement in agreements"
              :key="agreement.id"
              :xs="24"
              :sm="12"
              :lg="8"
            >
              <el-card class="agreement-card" @click="viewAgreement(agreement)">
                <div class="agreement-header">
                  <div class="agreement-title">{{ agreement.name }}</div>
                  <el-tag :type="getTypeColor(agreement.type)">{{ agreement.type }}</el-tag>
                </div>
                <div class="agreement-service">{{ agreement.serviceName }}</div>
                
                <div class="agreement-info">
                  <div class="info-item">
                    <span class="info-label">状态:</span>
                    <el-tag :type="getStatusColor(agreement.status)" size="small">
                      {{ getStatusText(agreement.status) }}
                    </el-tag>
                  </div>
                  <div class="info-item">
                    <span class="info-label">版本:</span>
                    <span>{{ agreement.version }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">负责人:</span>
                    <span>{{ agreement.owner }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">KPI数量:</span>
                    <span>{{ agreement.kpis?.length || 0 }}</span>
                  </div>
                </div>

                <div class="agreement-dates">
                  <div class="date-item">
                    <span class="date-label">生效:</span>
                    <span>{{ agreement.effectiveDate?.split(' ')[0] }}</span>
                  </div>
                  <div class="date-item">
                    <span class="date-label">到期:</span>
                    <span>{{ agreement.expiryDate?.split(' ')[0] }}</span>
                  </div>
                </div>

                <div class="agreement-actions">
                  <el-button type="text" size="small" @click.stop="editAgreement(agreement)">
                    编辑
                  </el-button>
                  <el-button
                    v-if="agreement.status === 'draft'"
                    type="text"
                    size="small"
                    @click.stop="activateAgreement(agreement)"
                  >
                    激活
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    style="color: #f56c6c"
                    @click.stop="deleteAgreement(agreement)"
                  >
                    删除
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 空状态 -->
        <div v-if="agreements.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无协议数据">
            <el-button type="primary" @click="showCreateDialog = true">新建协议</el-button>
          </el-empty>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > 0" class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadAgreements"
            @current-change="loadAgreements"
          />
        </div>
      </el-card>
    </div>

    <!-- 新建/编辑协议对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingAgreement ? '编辑协议' : '新建协议'"
      width="800px"
      top="5vh"
    >
      <el-form :model="agreementForm" :rules="agreementRules" ref="agreementFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="协议名称" prop="name">
              <el-input v-model="agreementForm.name" placeholder="请输入协议名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议类型" prop="type">
              <el-select v-model="agreementForm.type" placeholder="请选择协议类型" style="width: 100%">
                <el-option
                  v-for="type in agreementTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                >
                  <div>
                    <div>{{ type.label }}</div>
                    <div style="font-size: 12px; color: #999;">{{ type.description }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联服务" prop="serviceId">
              <el-select v-model="agreementForm.serviceId" placeholder="请选择服务" style="width: 100%">
                <el-option
                  v-for="service in services"
                  :key="service.id"
                  :label="service.name"
                  :value="service.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="owner">
              <el-input v-model="agreementForm.owner" placeholder="请输入负责人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="agreementForm.effectiveDate"
                type="date"
                placeholder="选择生效日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="到期日期" prop="expiryDate">
              <el-date-picker
                v-model="agreementForm.expiryDate"
                type="date"
                placeholder="选择到期日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="业务负责人">
          <el-input v-model="agreementForm.businessOwner" placeholder="请输入业务负责人" />
        </el-form-item>

        <el-form-item label="协议描述">
          <el-input
            v-model="agreementForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入协议描述"
          />
        </el-form-item>

        <!-- KPI配置 -->
        <el-form-item label="KPI指标">
          <div class="kpi-config">
            <div
              v-for="(kpi, index) in agreementForm.kpis"
              :key="index"
              class="kpi-item"
            >
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-select v-model="kpi.type" placeholder="选择指标类型">
                    <el-option
                      v-for="type in kpiTypes"
                      :key="type.key"
                      :label="type.name"
                      :value="type.key"
                    />
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-input-number
                    v-model="kpi.target"
                    :precision="2"
                    :min="0"
                    placeholder="目标值"
                    style="width: 100%"
                  />
                </el-col>
                <el-col :span="3">
                  <el-input v-model="kpi.unit" placeholder="单位" readonly />
                </el-col>
                <el-col :span="4">
                  <el-select v-model="kpi.period" placeholder="周期">
                    <el-option label="月度" value="monthly" />
                    <el-option label="季度" value="quarterly" />
                    <el-option label="年度" value="yearly" />
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-input v-model="kpi.name" placeholder="指标名称" readonly />
                </el-col>
                <el-col :span="2">
                  <el-button type="text" @click="removeKPI(index)" style="color: #f56c6c">
                    删除
                  </el-button>
                </el-col>
              </el-row>
            </div>
            <el-button type="text" @click="addKPI" style="margin-top: 10px">
              <el-icon><Plus /></el-icon>
              添加KPI指标
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAgreement" :loading="saveLoading">
            {{ editingAgreement ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Upload, Download, Refresh, Grid, List
} from '@element-plus/icons-vue'
import {
  getAgreementList,
  getServiceList,
  createAgreement,
  updateAgreement,
  deleteAgreement as deleteAgreementApi,
  activateAgreement as activateAgreementApi,
  agreementTypes,
  kpiTypes
} from '@/api/slmApi.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const showCreateDialog = ref(false)
const showImportDialog = ref(false)
const viewMode = ref('table') // table | card
const editingAgreement = ref(null)

// 筛选表单
const filterForm = reactive({
  type: '',
  status: '',
  serviceId: '',
  keyword: ''
})

// 协议表单
const agreementForm = reactive({
  name: '',
  type: '',
  serviceId: '',
  owner: '',
  businessOwner: '',
  effectiveDate: '',
  expiryDate: '',
  description: '',
  kpis: []
})

const agreementFormRef = ref()

// 表单验证规则
const agreementRules = {
  name: [
    { required: true, message: '请输入协议名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择协议类型', trigger: 'change' }
  ],
  serviceId: [
    { required: true, message: '请选择关联服务', trigger: 'change' }
  ],
  owner: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  expiryDate: [
    { required: true, message: '请选择到期日期', trigger: 'change' }
  ]
}

// 数据
const services = ref([])
const agreements = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 加载服务列表
const loadServices = async () => {
  try {
    const response = await getServiceList({ pageSize: 100 })
    if (response.success) {
      services.value = response.data.list
    }
  } catch (error) {
    ElMessage.error('加载服务列表失败')
    console.error('Load services error:', error)
  }
}

// 加载协议列表
const loadAgreements = async () => {
  loading.value = true
  try {
    const params = {
      ...filterForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    const response = await getAgreementList(params)
    if (response.success) {
      agreements.value = response.data.list
      pagination.total = response.data.total
    }
  } catch (error) {
    ElMessage.error('加载协议列表失败')
    console.error('Load agreements error:', error)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadServices(),
    loadAgreements()
  ])
}

// 重置筛选条件
const resetFilter = () => {
  Object.assign(filterForm, {
    type: '',
    status: '',
    serviceId: '',
    keyword: ''
  })
  pagination.page = 1
  loadAgreements()
}

// 切换视图模式
const toggleView = () => {
  viewMode.value = viewMode.value === 'table' ? 'card' : 'table'
}

// 获取类型颜色
const getTypeColor = (type) => {
  switch (type) {
    case 'SLA':
      return 'primary'
    case 'OLA':
      return 'success'
    case 'UC':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 'active':
      return 'success'
    case 'draft':
      return 'info'
    case 'suspended':
      return 'warning'
    case 'expired':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'active':
      return '活跃'
    case 'draft':
      return '草稿'
    case 'suspended':
      return '暂停'
    case 'expired':
      return '过期'
    default:
      return '未知'
  }
}

// 查看协议
const viewAgreement = (agreement) => {
  router.push(`/slm/agreement/${agreement.id}`)
}

// 编辑协议
const editAgreement = (agreement) => {
  editingAgreement.value = agreement

  // 填充表单数据
  Object.assign(agreementForm, {
    name: agreement.name,
    type: agreement.type,
    serviceId: agreement.serviceId,
    owner: agreement.owner,
    businessOwner: agreement.businessOwner || '',
    effectiveDate: agreement.effectiveDate ? new Date(agreement.effectiveDate) : '',
    expiryDate: agreement.expiryDate ? new Date(agreement.expiryDate) : '',
    description: agreement.description || '',
    kpis: agreement.kpis ? [...agreement.kpis] : []
  })

  showCreateDialog.value = true
}

// 激活协议
const activateAgreement = async (agreement) => {
  try {
    await ElMessageBox.confirm(
      `确定要激活协议 "${agreement.name}" 吗？`,
      '激活确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await activateAgreementApi(agreement.id)
    if (response.success) {
      ElMessage.success('协议激活成功')
      loadAgreements()
    } else {
      ElMessage.error(response.msg || '激活失败')
    }
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('激活协议失败')
    console.error('Activate agreement error:', error)
  }
}

// 删除协议
const deleteAgreement = async (agreement) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除协议 "${agreement.name}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteAgreementApi(agreement.id)
    if (response.success) {
      ElMessage.success('协议删除成功')
      loadAgreements()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('删除协议失败')
    console.error('Delete agreement error:', error)
  }
}

// 添加KPI指标
const addKPI = () => {
  agreementForm.kpis.push({
    type: '',
    name: '',
    target: 0,
    unit: '',
    period: 'monthly'
  })
}

// 删除KPI指标
const removeKPI = (index) => {
  agreementForm.kpis.splice(index, 1)
}

// 监听KPI类型变化，自动填充相关信息
watch(() => agreementForm.kpis, (newKpis) => {
  newKpis.forEach(kpi => {
    const kpiType = kpiTypes.find(type => type.key === kpi.type)
    if (kpiType) {
      kpi.name = kpiType.name
      kpi.unit = kpiType.unit
      if (!kpi.target) {
        kpi.target = kpiType.defaultTarget
      }
    }
  })
}, { deep: true })

// 保存协议
const saveAgreement = async () => {
  if (!agreementFormRef.value) return

  try {
    await agreementFormRef.value.validate()

    saveLoading.value = true

    const formData = {
      ...agreementForm,
      effectiveDate: agreementForm.effectiveDate.toISOString(),
      expiryDate: agreementForm.expiryDate.toISOString(),
      serviceName: services.value.find(s => s.id === agreementForm.serviceId)?.name || ''
    }

    let response
    if (editingAgreement.value) {
      response = await updateAgreement(editingAgreement.value.id, formData)
    } else {
      response = await createAgreement(formData)
    }

    if (response.success) {
      ElMessage.success(editingAgreement.value ? '协议更新成功' : '协议创建成功')
      showCreateDialog.value = false
      resetAgreementForm()
      loadAgreements()
    } else {
      ElMessage.error(response.msg || '保存失败')
    }
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('保存协议失败')
      console.error('Save agreement error:', error)
    }
  } finally {
    saveLoading.value = false
  }
}

// 重置协议表单
const resetAgreementForm = () => {
  editingAgreement.value = null
  Object.assign(agreementForm, {
    name: '',
    type: '',
    serviceId: '',
    owner: '',
    businessOwner: '',
    effectiveDate: '',
    expiryDate: '',
    description: '',
    kpis: []
  })
  agreementFormRef.value?.resetFields()
}

// 导出协议
const exportAgreements = () => {
  if (agreements.value.length === 0) {
    ElMessage.warning('暂无协议数据可导出')
    return
  }

  // 模拟导出功能
  ElMessage.success('协议导出成功')
}

// 生命周期
onMounted(async () => {
  await refreshData()
})

// 监听对话框关闭
watch(showCreateDialog, (newVal) => {
  if (!newVal) {
    resetAgreementForm()
  }
})
</script>

<style scoped>
.agreement-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

/* 协议区域 */
.agreements-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 表格视图 */
.table-view {
  min-height: 400px;
}

/* 卡片视图 */
.card-view {
  min-height: 400px;
}

.agreement-card {
  height: 280px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.agreement-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.agreement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.agreement-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.agreement-service {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.agreement-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
}

.info-label {
  color: #666;
  font-weight: 500;
}

.agreement-dates {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.date-item {
  font-size: 12px;
}

.date-label {
  color: #999;
  margin-right: 4px;
}

.agreement-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 分页 */
.pagination-section {
  margin-top: 20px;
  text-align: center;
}

/* KPI配置 */
.kpi-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.kpi-item {
  margin-bottom: 12px;
  padding: 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.kpi-item:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agreement-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
    flex-wrap: wrap;
  }

  .agreement-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .agreement-dates {
    flex-direction: column;
    gap: 4px;
  }

  .agreement-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}

/* 对话框样式 */
:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select .el-input__inner) {
  cursor: pointer;
}

:deep(.el-table .cell) {
  padding: 8px;
}
</style>
