<template>
  <div class="test-page">
    <h1>测试页面</h1>
    <p>如果您能看到这个页面，说明路由系统正在正常工作！</p>
    <el-button type="primary" @click="goToDashboard">前往仪表盘</el-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToDashboard = () => {
  router.push('/dashboard')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  text-align: center;
}

h1 {
  color: #409EFF;
  margin-bottom: 20px;
}

p {
  font-size: 16px;
  margin-bottom: 30px;
}
</style>
