<template>
  <div class="ux-enhancer">
    <!-- 全局加载遮罩 -->
    <div class="global-loading" v-if="globalLoading">
      <div class="loading-content">
        <el-icon class="is-loading" :size="48"><Loading /></el-icon>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>

    <!-- 网络状态指示器 -->
    <div class="network-status" :class="networkStatusClass" v-if="showNetworkStatus">
      <el-icon :size="16">
        <component :is="networkIcon" />
      </el-icon>
      <span>{{ networkStatusText }}</span>
    </div>

    <!-- 返回顶部按钮 -->
    <el-backtop
      :right="40"
      :bottom="40"
      :visibility-height="300"
      target=".main-content"
    >
      <div class="back-to-top">
        <el-icon :size="20"><Top /></el-icon>
      </div>
    </el-backtop>

    <!-- 快捷键提示 -->
    <div class="keyboard-shortcuts" v-if="showShortcuts">
      <div class="shortcuts-header">
        <span>快捷键</span>
        <el-button type="text" size="small" @click="hideShortcuts">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="shortcuts-list">
        <div class="shortcut-item" v-for="shortcut in shortcuts" :key="shortcut.key">
          <kbd>{{ shortcut.key }}</kbd>
          <span>{{ shortcut.description }}</span>
        </div>
      </div>
    </div>

    <!-- 性能监控面板 -->
    <div class="performance-monitor" v-if="showPerformanceMonitor && isDevelopment">
      <div class="monitor-header">
        <span>性能监控</span>
        <el-button type="text" size="small" @click="togglePerformanceMonitor">
          <el-icon><Minus /></el-icon>
        </el-button>
      </div>
      <div class="monitor-metrics">
        <div class="metric-item">
          <span class="metric-label">FPS:</span>
          <span class="metric-value">{{ performanceMetrics.fps }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">内存:</span>
          <span class="metric-value">{{ performanceMetrics.memory }}MB</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">加载时间:</span>
          <span class="metric-value">{{ performanceMetrics.loadTime }}ms</span>
        </div>
      </div>
    </div>

    <!-- 用户反馈浮动按钮 -->
    <div class="feedback-fab" @click="showFeedbackDialog = true">
      <el-icon :size="20"><ChatDotRound /></el-icon>
    </div>

    <!-- 反馈对话框 -->
    <el-dialog
      v-model="showFeedbackDialog"
      title="用户反馈"
      width="500px"
      @close="resetFeedback"
    >
      <el-form :model="feedbackForm" label-width="80px">
        <el-form-item label="反馈类型">
          <el-select v-model="feedbackForm.type" style="width: 100%;">
            <el-option label="功能建议" value="suggestion" />
            <el-option label="问题报告" value="bug" />
            <el-option label="使用体验" value="experience" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="页面路径">
          <el-input v-model="feedbackForm.page" readonly />
        </el-form-item>
        
        <el-form-item label="反馈内容">
          <el-input
            v-model="feedbackForm.content"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您的反馈..."
          />
        </el-form-item>
        
        <el-form-item label="联系方式">
          <el-input
            v-model="feedbackForm.contact"
            placeholder="邮箱或电话（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showFeedbackDialog = false">取消</el-button>
          <el-button type="primary" @click="submitFeedback">
            提交反馈
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { responsiveHelper, performanceHelper } from '@/utils/userExperience'

const route = useRoute()

// 响应式数据
const globalLoading = ref(false)
const loadingText = ref('加载中...')
const networkStatus = ref('online')
const showNetworkStatus = ref(false)
const showShortcuts = ref(false)
const showPerformanceMonitor = ref(false)
const showFeedbackDialog = ref(false)
const isDevelopment = ref(import.meta.env.DEV)

// 性能指标
const performanceMetrics = reactive({
  fps: 60,
  memory: 0,
  loadTime: 0
})

// 反馈表单
const feedbackForm = reactive({
  type: 'suggestion',
  page: '',
  content: '',
  contact: ''
})

// 快捷键配置
const shortcuts = ref([
  { key: 'Ctrl + K', description: '全局搜索' },
  { key: 'Ctrl + /', description: '显示快捷键' },
  { key: 'Ctrl + R', description: '刷新页面' },
  { key: 'Esc', description: '关闭对话框' },
  { key: 'F11', description: '全屏模式' }
])

// 计算属性
const networkStatusClass = computed(() => ({
  'status-online': networkStatus.value === 'online',
  'status-offline': networkStatus.value === 'offline',
  'status-slow': networkStatus.value === 'slow'
}))

const networkStatusText = computed(() => {
  const statusMap = {
    online: '网络正常',
    offline: '网络断开',
    slow: '网络缓慢'
  }
  return statusMap[networkStatus.value] || '未知状态'
})

const networkIcon = computed(() => {
  const iconMap = {
    online: 'Connection',
    offline: 'Close',
    slow: 'Warning'
  }
  return iconMap[networkStatus.value] || 'Connection'
})

// 方法
const showGlobalLoading = (text = '加载中...') => {
  globalLoading.value = true
  loadingText.value = text
}

const hideGlobalLoading = () => {
  globalLoading.value = false
}

const checkNetworkStatus = () => {
  if (navigator.onLine) {
    // 简单的网络速度检测
    const start = Date.now()
    fetch('/api/ping', { method: 'HEAD' })
      .then(() => {
        const duration = Date.now() - start
        if (duration > 3000) {
          networkStatus.value = 'slow'
          showNetworkStatus.value = true
        } else {
          networkStatus.value = 'online'
          showNetworkStatus.value = false
        }
      })
      .catch(() => {
        networkStatus.value = 'offline'
        showNetworkStatus.value = true
      })
  } else {
    networkStatus.value = 'offline'
    showNetworkStatus.value = true
  }
}

const hideShortcuts = () => {
  showShortcuts.value = false
}

const togglePerformanceMonitor = () => {
  showPerformanceMonitor.value = !showPerformanceMonitor.value
}

const updatePerformanceMetrics = () => {
  // 更新FPS
  let lastTime = performance.now()
  let frames = 0
  
  const countFPS = () => {
    frames++
    const currentTime = performance.now()
    if (currentTime >= lastTime + 1000) {
      performanceMetrics.fps = Math.round((frames * 1000) / (currentTime - lastTime))
      frames = 0
      lastTime = currentTime
    }
    requestAnimationFrame(countFPS)
  }
  requestAnimationFrame(countFPS)
  
  // 更新内存使用情况
  if (performance.memory) {
    performanceMetrics.memory = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
  }
  
  // 更新页面加载时间
  if (performance.timing) {
    performanceMetrics.loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
  }
}

const resetFeedback = () => {
  Object.assign(feedbackForm, {
    type: 'suggestion',
    page: route.path,
    content: '',
    contact: ''
  })
}

const submitFeedback = () => {
  if (!feedbackForm.content.trim()) {
    ElMessage.warning('请填写反馈内容')
    return
  }
  
  // 模拟提交反馈
  ElMessage.success('反馈已提交，感谢您的建议！')
  showFeedbackDialog.value = false
  resetFeedback()
}

const handleKeyboardShortcuts = (event) => {
  // Ctrl + K: 全局搜索
  if (event.ctrlKey && event.key === 'k') {
    event.preventDefault()
    // 触发全局搜索
    document.querySelector('.global-search input')?.focus()
  }
  
  // Ctrl + /: 显示快捷键
  if (event.ctrlKey && event.key === '/') {
    event.preventDefault()
    showShortcuts.value = !showShortcuts.value
  }
  
  // Esc: 关闭对话框
  if (event.key === 'Escape') {
    showShortcuts.value = false
    showFeedbackDialog.value = false
  }
}

// 生命周期
onMounted(() => {
  // 初始化反馈表单
  feedbackForm.page = route.path
  
  // 检查网络状态
  checkNetworkStatus()
  setInterval(checkNetworkStatus, 30000) // 每30秒检查一次
  
  // 监听网络状态变化
  window.addEventListener('online', checkNetworkStatus)
  window.addEventListener('offline', checkNetworkStatus)
  
  // 监听键盘快捷键
  document.addEventListener('keydown', handleKeyboardShortcuts)
  
  // 更新性能指标
  if (isDevelopment.value) {
    updatePerformanceMetrics()
  }
})

onUnmounted(() => {
  window.removeEventListener('online', checkNetworkStatus)
  window.removeEventListener('offline', checkNetworkStatus)
  document.removeEventListener('keydown', handleKeyboardShortcuts)
})

// 暴露方法给父组件
defineExpose({
  showGlobalLoading,
  hideGlobalLoading,
  checkNetworkStatus
})
</script>

<style scoped>
.ux-enhancer {
  position: relative;
}

/* 全局加载遮罩 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
}

/* 网络状态指示器 */
.network-status {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  z-index: 1000;
  transition: all 0.3s ease;
}

.network-status.status-online {
  border-left: 3px solid #4CAF50;
  color: #4CAF50;
}

.network-status.status-offline {
  border-left: 3px solid #F44336;
  color: #F44336;
}

.network-status.status-slow {
  border-left: 3px solid #FF9800;
  color: #FF9800;
}

/* 返回顶部按钮 */
.back-to-top {
  width: 40px;
  height: 40px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-to-top:hover {
  background: #337ecc;
  transform: scale(1.1);
}

/* 快捷键提示 */
.keyboard-shortcuts {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 16px;
  min-width: 200px;
  z-index: 1000;
}

.shortcuts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
  color: #1f2937;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.shortcut-item kbd {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 11px;
}

/* 性能监控面板 */
.performance-monitor {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 8px;
  padding: 12px;
  font-family: monospace;
  font-size: 12px;
  z-index: 1000;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
}

.monitor-metrics {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  min-width: 120px;
}

.metric-label {
  color: #9ca3af;
}

.metric-value {
  color: #10b981;
  font-weight: 600;
}

/* 用户反馈浮动按钮 */
.feedback-fab {
  position: fixed;
  bottom: 100px;
  right: 40px;
  width: 56px;
  height: 56px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 999;
}

.feedback-fab:hover {
  background: #337ecc;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .network-status {
    top: 10px;
    right: 10px;
    font-size: 11px;
    padding: 6px 12px;
  }
  
  .keyboard-shortcuts {
    bottom: 10px;
    left: 10px;
    right: 10px;
    min-width: auto;
  }
  
  .performance-monitor {
    top: 10px;
    left: 10px;
    font-size: 11px;
  }
  
  .feedback-fab {
    bottom: 80px;
    right: 20px;
    width: 48px;
    height: 48px;
  }
}
</style>
