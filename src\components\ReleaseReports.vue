<template>
  <div class="release-reports">
    <el-card class="reports-card">
      <template #header>
        <div class="card-header">
          <span>发布报表分析</span>
          <div class="header-actions">
            <el-select v-model="timeRange" @change="loadReportData" style="width: 120px">
              <el-option label="近7天" value="7d" />
              <el-option label="近30天" value="30d" />
              <el-option label="近90天" value="90d" />
              <el-option label="近1年" value="1y" />
            </el-select>
            <el-button :icon="Refresh" @click="refreshData" :loading="loading">
              刷新
            </el-button>
            <el-button :icon="Download" @click="exportReport">
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 关键指标概览 -->
      <div class="metrics-overview" v-if="reportData">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon total">
                <el-icon><Upload /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ reportData.totalReleases }}</div>
                <div class="metric-label">总发布数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon success">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ reportData.successRate }}%</div>
                <div class="metric-label">成功率</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon cycle">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ reportData.avgCycle }}</div>
                <div class="metric-label">平均周期(天)</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon rollback">
                <el-icon><RefreshLeft /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ reportData.rollbackRate }}%</div>
                <div class="metric-label">回滚率</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 趋势图表 -->
      <div class="trend-charts" v-if="reportData">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>成功率趋势</span>
              </template>
              <div ref="successRateChartRef" class="chart-container"></div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>发布周期趋势</span>
              </template>
              <div ref="cycleChartRef" class="chart-container"></div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>回滚率趋势</span>
              </template>
              <div ref="rollbackChartRef" class="chart-container"></div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <span>月度发布统计</span>
              </template>
              <div ref="monthlyChartRef" class="chart-container"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分类统计 -->
      <div class="category-stats" v-if="reportData">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="stats-card">
              <template #header>
                <span>按类型统计</span>
              </template>
              <div class="stats-table">
                <el-table :data="typeStatsData" style="width: 100%">
                  <el-table-column prop="type" label="发布类型" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getTypeTagType(row.type)" size="small">
                        {{ getTypeLabel(row.type) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="total" label="总数" width="80" />
                  <el-table-column prop="success" label="成功" width="80" />
                  <el-table-column prop="failed" label="失败" width="80" />
                  <el-table-column prop="successRate" label="成功率" width="100">
                    <template #default="{ row }">
                      <el-progress 
                        :percentage="row.successRate" 
                        :stroke-width="8"
                        :show-text="false"
                      />
                      <span class="progress-text">{{ row.successRate }}%</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="avgCycle" label="平均周期" />
                </el-table>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="stats-card">
              <template #header>
                <span>按风险等级统计</span>
              </template>
              <div class="stats-table">
                <el-table :data="riskStatsData" style="width: 100%">
                  <el-table-column prop="risk" label="风险等级" width="120">
                    <template #default="{ row }">
                      <el-tag :type="getRiskTagType(row.risk)" size="small">
                        {{ getRiskLabel(row.risk) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="total" label="总数" width="80" />
                  <el-table-column prop="success" label="成功" width="80" />
                  <el-table-column prop="failed" label="失败" width="80" />
                  <el-table-column prop="successRate" label="成功率" width="100">
                    <template #default="{ row }">
                      <el-progress 
                        :percentage="row.successRate" 
                        :stroke-width="8"
                        :show-text="false"
                      />
                      <span class="progress-text">{{ row.successRate }}%</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="avgCycle" label="平均周期" />
                </el-table>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 团队绩效 -->
      <div class="team-performance" v-if="reportData?.teamStats">
        <el-card class="stats-card">
          <template #header>
            <span>团队绩效排行</span>
          </template>
          <div class="team-stats">
            <el-table :data="reportData.teamStats" style="width: 100%">
              <el-table-column prop="name" label="团队" width="150" />
              <el-table-column prop="total" label="发布总数" width="100" />
              <el-table-column prop="success" label="成功数" width="100" />
              <el-table-column prop="failed" label="失败数" width="100" />
              <el-table-column prop="successRate" label="成功率" width="120">
                <template #default="{ row }">
                  <el-progress 
                    :percentage="row.successRate" 
                    :stroke-width="8"
                    :show-text="false"
                  />
                  <span class="progress-text">{{ row.successRate }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="avgCycle" label="平均周期(天)" />
            </el-table>
          </div>
        </el-card>
      </div>

      <!-- 洞察建议 -->
      <div class="insights" v-if="reportData?.insights?.length > 0">
        <el-card class="insights-card">
          <template #header>
            <span>数据洞察与建议</span>
          </template>
          <div class="insights-list">
            <div 
              v-for="insight in reportData.insights" 
              :key="insight.title"
              class="insight-item"
            >
              <el-alert
                :title="insight.title"
                :type="insight.type"
                :description="insight.description"
                show-icon
                :closable="false"
              />
              <div class="insight-recommendation">
                <strong>建议：</strong>{{ insight.recommendation }}
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, Download, Upload, SuccessFilled, Timer, RefreshLeft
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getReleaseReports, releaseTypes, riskLevels } from '@/api/releaseApi.js'

// 响应式数据
const loading = ref(false)
const timeRange = ref('30d')
const reportData = ref(null)

// 图表引用
const successRateChartRef = ref(null)
const cycleChartRef = ref(null)
const rollbackChartRef = ref(null)
const monthlyChartRef = ref(null)

let successRateChart = null
let cycleChart = null
let rollbackChart = null
let monthlyChart = null

// 计算属性
const typeStatsData = computed(() => {
  if (!reportData.value?.typeStats) return []
  
  return Object.entries(reportData.value.typeStats).map(([type, stats]) => ({
    type,
    ...stats,
    successRate: stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0
  }))
})

const riskStatsData = computed(() => {
  if (!reportData.value?.riskStats) return []
  
  return Object.entries(reportData.value.riskStats).map(([risk, stats]) => ({
    risk,
    ...stats,
    successRate: stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0
  }))
})

// 生命周期
onMounted(() => {
  loadReportData()
})

// 方法
const loadReportData = async () => {
  loading.value = true
  try {
    const response = await getReleaseReports(timeRange.value)
    reportData.value = response.data
    
    // 等待DOM更新后初始化图表
    await nextTick()
    initCharts()
  } catch (error) {
    ElMessage.error('加载报表数据失败')
    console.error('Load report data error:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadReportData()
}

const exportReport = () => {
  ElMessage.info('导出报表功能开发中...')
}

// 初始化图表
const initCharts = () => {
  initSuccessRateChart()
  initCycleChart()
  initRollbackChart()
  initMonthlyChart()
}

const initSuccessRateChart = () => {
  if (!successRateChartRef.value || !reportData.value?.trends?.successRate) return
  
  successRateChart = echarts.init(successRateChartRef.value)
  
  const data = reportData.value.trends.successRate
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>成功率: {c}%'
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [{
      data: data.map(item => item.value),
      type: 'line',
      smooth: true,
      itemStyle: { color: '#4CAF50' },
      areaStyle: { color: 'rgba(76, 175, 80, 0.1)' }
    }]
  }
  
  successRateChart.setOption(option)
}

const initCycleChart = () => {
  if (!cycleChartRef.value || !reportData.value?.trends?.cycle) return
  
  cycleChart = echarts.init(cycleChartRef.value)
  
  const data = reportData.value.trends.cycle
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>周期: {c}天'
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}天'
      }
    },
    series: [{
      data: data.map(item => item.value),
      type: 'line',
      smooth: true,
      itemStyle: { color: '#2196F3' },
      areaStyle: { color: 'rgba(33, 150, 243, 0.1)' }
    }]
  }
  
  cycleChart.setOption(option)
}

const initRollbackChart = () => {
  if (!rollbackChartRef.value || !reportData.value?.trends?.rollbackRate) return
  
  rollbackChart = echarts.init(rollbackChartRef.value)
  
  const data = reportData.value.trends.rollbackRate
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>回滚率: {c}%'
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      min: 0,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [{
      data: data.map(item => item.value),
      type: 'line',
      smooth: true,
      itemStyle: { color: '#FF5722' },
      areaStyle: { color: 'rgba(255, 87, 34, 0.1)' }
    }]
  }
  
  rollbackChart.setOption(option)
}

const initMonthlyChart = () => {
  if (!monthlyChartRef.value || !reportData.value?.monthlyStats) return
  
  monthlyChart = echarts.init(monthlyChartRef.value)
  
  const data = reportData.value.monthlyStats
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          result += param.seriesName + ': ' + param.value + '<br/>'
        })
        return result
      }
    },
    legend: {
      data: ['总数', '成功', '失败', '回滚']
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.month)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '总数',
        type: 'bar',
        data: data.map(item => item.total),
        itemStyle: { color: '#2196F3' }
      },
      {
        name: '成功',
        type: 'bar',
        data: data.map(item => item.success),
        itemStyle: { color: '#4CAF50' }
      },
      {
        name: '失败',
        type: 'bar',
        data: data.map(item => item.failed),
        itemStyle: { color: '#FF9800' }
      },
      {
        name: '回滚',
        type: 'bar',
        data: data.map(item => item.rollback),
        itemStyle: { color: '#F44336' }
      }
    ]
  }
  
  monthlyChart.setOption(option)
}

// 工具方法
const getTypeLabel = (type) => {
  const typeMap = {
    major: '重大发布',
    minor: '次要发布',
    patch: '补丁发布',
    hotfix: '热修复'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    major: 'danger',
    minor: 'warning',
    patch: 'info',
    hotfix: 'danger'
  }
  return typeMap[type] || 'info'
}

const getRiskLabel = (risk) => {
  const riskMap = {
    low: '低风险',
    medium: '中风险',
    high: '高风险',
    critical: '极高风险'
  }
  return riskMap[risk] || risk
}

const getRiskTagType = (risk) => {
  const riskMap = {
    low: 'success',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return riskMap[risk] || 'info'
}
</script>

<style scoped>
.release-reports {
  padding: 20px;
}

.reports-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 关键指标概览 */
.metrics-overview {
  margin-bottom: 32px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 28px;
  color: white;
}

.metric-icon.total {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
}

.metric-icon.success {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.metric-icon.cycle {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.metric-icon.rollback {
  background: linear-gradient(135deg, #F44336, #EF5350);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #666;
}

/* 趋势图表 */
.trend-charts {
  margin-bottom: 32px;
}

.chart-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 分类统计 */
.category-stats {
  margin-bottom: 32px;
}

.stats-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-table {
  padding: 0;
}

.progress-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

/* 团队绩效 */
.team-performance {
  margin-bottom: 32px;
}

.team-stats {
  padding: 0;
}

/* 洞察建议 */
.insights {
  margin-bottom: 32px;
}

.insights-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  padding: 16px;
  border-radius: 8px;
  background: #fafafa;
}

.insight-recommendation {
  margin-top: 12px;
  padding: 12px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
  border-left: 4px solid #1976D2;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-overview .el-col {
    margin-bottom: 16px;
  }

  .trend-charts .el-col {
    margin-bottom: 20px;
  }

  .category-stats .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .release-reports {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .metric-card {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .metric-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .metric-value {
    font-size: 24px;
  }

  .chart-container {
    height: 250px;
  }

  .stats-table .el-table {
    font-size: 12px;
  }

  .insight-recommendation {
    font-size: 12px;
  }
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
}

.el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.el-table tr:hover td {
  background-color: #f8fbff;
}

/* 进度条样式 */
.el-progress {
  display: inline-block;
  width: 60px;
}

.el-progress-bar__outer {
  border-radius: 4px;
}

.el-progress-bar__inner {
  border-radius: 4px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 卡片标题样式 */
.el-card__header {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #333;
}

/* 警告框样式 */
.el-alert {
  border-radius: 8px;
  border: none;
}

.el-alert--success {
  background-color: #f0f9ff;
  color: #0d7377;
}

.el-alert--warning {
  background-color: #fffbf0;
  color: #b45309;
}

.el-alert--error {
  background-color: #fef2f2;
  color: #b91c1c;
}

.el-alert--info {
  background-color: #f0f9ff;
  color: #1e40af;
}
</style>
