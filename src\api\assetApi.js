import request from '@/utils/request'
import { createApiResponse, createApiError, paginate, generateId, formatDateTime, mockUsers, mockDepartments } from './index.js'

// 资产管理 API

/**
 * 获取资产统计信息
 * @returns {Promise}
 */
export function getAssetStatistics() {
  const stats = {
    totalAssets: 1248,
    activeAssets: 1156,
    inMaintenanceAssets: 45,
    retiredAssets: 47,
    totalValue: 12580000,
    monthlyDepreciation: 85600,
    licenseCompliance: 0.95,
    expiringLicenses: 8,
    assetsByCategory: [
      { name: '服务器', value: 156, percentage: 12.5 },
      { name: '笔记本电脑', value: 425, percentage: 34.1 },
      { name: '台式机', value: 298, percentage: 23.9 },
      { name: '网络设备', value: 89, percentage: 7.1 },
      { name: '存储设备', value: 67, percentage: 5.4 },
      { name: '其他', value: 213, percentage: 17.0 }
    ],
    assetsByStatus: [
      { name: '使用中', value: 1156, color: '#67C23A' },
      { name: '维修中', value: 45, color: '#E6A23C' },
      { name: '库存中', value: 0, color: '#909399' },
      { name: '已报废', value: 47, color: '#F56C6C' }
    ],
    monthlyTrend: [
      { month: '1月', assets: 1180, value: 11800000 },
      { month: '2月', assets: 1195, value: 11950000 },
      { month: '3月', assets: 1210, value: 12100000 },
      { month: '4月', assets: 1225, value: 12250000 },
      { month: '5月', assets: 1240, value: 12400000 },
      { month: '6月', assets: 1248, value: 12580000 }
    ]
  }
  return createApiResponse(stats)
}

/**
 * 获取资产列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAssetList(params = {}) {
  const { page = 1, pageSize = 20, keyword = '', category = '', status = '', department = '' } = params
  
  let assets = mockAssets.filter(asset => {
    return (!keyword || asset.name.includes(keyword) || asset.assetCode.includes(keyword)) &&
           (!category || asset.category === category) &&
           (!status || asset.status === status) &&
           (!department || asset.department === department)
  })

  const result = paginate(assets, page, pageSize)
  return createApiResponse(result)
}

/**
 * 获取资产详情
 * @param {string} id - 资产ID
 * @returns {Promise}
 */
export function getAssetDetail(id) {
  const asset = mockAssets.find(item => item.id === id)
  if (!asset) {
    return createApiError('资产不存在', 404)
  }

  // 添加详细信息，并确保字段名与前端期望一致
  const detailAsset = {
    ...asset,
    // 确保前端期望的字段名
    price: asset.purchasePrice, // 前端期望 price
    user: asset.assignedUser,   // 前端期望 user
    vendor: asset.supplier,     // 前端期望 vendor
    specifications: {
      cpu: 'Intel Core i7-10700',
      memory: '16GB DDR4',
      storage: '512GB SSD',
      graphics: 'NVIDIA GTX 1650',
      network: 'Gigabit Ethernet'
    },
    maintenanceHistory: [
      {
        id: 'maint001',
        date: '2024-05-15',
        type: '定期维护',
        description: '清理灰尘，更新驱动程序',
        technician: '张工',
        cost: 0
      },
      {
        id: 'maint002',
        date: '2024-03-20',
        type: '故障维修',
        description: '更换硬盘',
        technician: '李工',
        cost: 800
      }
    ],
    relatedTickets: [
      {
        id: 'ticket001',
        title: '电脑运行缓慢',
        status: '已解决',
        createTime: '2024-06-01'
      }
    ],
    lifecycleHistory: [
      {
        id: 'lc001',
        date: '2024-01-15 10:30:00',
        action: '资产入库',
        status: '库存中',
        description: '新采购资产入库登记',
        operator: '张工'
      },
      {
        id: 'lc002',
        date: '2024-02-01 09:15:00',
        action: '资产分配',
        status: '使用中',
        description: '分配给研发部门使用',
        operator: '李工'
      },
      {
        id: 'lc003',
        date: '2024-03-20 14:30:00',
        action: '维修处理',
        status: '维修中',
        description: '硬盘故障，送修处理',
        operator: '王工'
      },
      {
        id: 'lc004',
        date: '2024-03-25 16:45:00',
        action: '维修完成',
        status: '使用中',
        description: '硬盘更换完成，恢复使用',
        operator: '王工'
      }
    ]
  }

  return createApiResponse(detailAsset)
}

/**
 * 创建资产
 * @param {Object} data - 资产数据
 * @returns {Promise}
 */
export function createAsset(data) {
  const newAsset = {
    id: generateId(),
    assetCode: `AST${Date.now()}`,
    createTime: formatDateTime(new Date()),
    updateTime: formatDateTime(new Date()),
    ...data
  }
  
  mockAssets.unshift(newAsset)
  return createApiResponse(newAsset, '资产创建成功')
}

/**
 * 更新资产
 * @param {string} id - 资产ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateAsset(id, data) {
  const index = mockAssets.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('资产不存在', 404)
  }
  
  mockAssets[index] = {
    ...mockAssets[index],
    ...data,
    updateTime: formatDateTime(new Date())
  }
  
  return createApiResponse(mockAssets[index], '资产更新成功')
}

/**
 * 删除资产
 * @param {string} id - 资产ID
 * @returns {Promise}
 */
export function deleteAsset(id) {
  const index = mockAssets.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('资产不存在', 404)
  }
  
  mockAssets.splice(index, 1)
  return createApiResponse(null, '资产删除成功')
}

/**
 * 资产状态变更
 * @param {string} id - 资产ID
 * @param {string} status - 新状态
 * @param {string} reason - 变更原因
 * @returns {Promise}
 */
export function changeAssetStatus(id, status, reason = '') {
  const asset = mockAssets.find(item => item.id === id)
  if (!asset) {
    return createApiError('资产不存在', 404)
  }
  
  // 记录状态变更历史
  if (!asset.statusHistory) {
    asset.statusHistory = []
  }
  
  asset.statusHistory.push({
    id: generateId(),
    fromStatus: asset.status,
    toStatus: status,
    reason,
    operator: '当前用户',
    changeTime: formatDateTime(new Date())
  })
  
  asset.status = status
  asset.updateTime = formatDateTime(new Date())
  
  return createApiResponse(asset, '状态变更成功')
}

/**
 * 资产分配
 * @param {string} id - 资产ID
 * @param {Object} assignData - 分配信息
 * @returns {Promise}
 */
export function assignAsset(id, assignData) {
  const asset = mockAssets.find(item => item.id === id)
  if (!asset) {
    return createApiError('资产不存在', 404)
  }
  
  asset.assignedTo = assignData.userId
  asset.assignedUser = assignData.userName
  asset.department = assignData.department
  asset.location = assignData.location
  asset.assignTime = formatDateTime(new Date())
  asset.status = '使用中'
  asset.updateTime = formatDateTime(new Date())
  
  return createApiResponse(asset, '资产分配成功')
}

/**
 * 获取资产类别列表
 * @returns {Promise}
 */
export function getAssetCategories() {
  const categories = [
    { id: 'server', name: '服务器', icon: 'Monitor' },
    { id: 'laptop', name: '笔记本电脑', icon: 'Monitor' }, // 使用Monitor代替Laptop
    { id: 'desktop', name: '台式机', icon: 'Monitor' }, // 使用Monitor代替Desktop
    { id: 'network', name: '网络设备', icon: 'Connection' },
    { id: 'storage', name: '存储设备', icon: 'FolderOpened' },
    { id: 'printer', name: '打印设备', icon: 'Printer' },
    { id: 'mobile', name: '移动设备', icon: 'Cellphone' },
    { id: 'other', name: '其他设备', icon: 'Box' }
  ]
  return createApiResponse(categories)
}

/**
 * 获取资产状态列表
 * @returns {Promise}
 */
export function getAssetStatuses() {
  const statuses = [
    { value: '待入库', label: '待入库', color: '#909399' },
    { value: '库存中', label: '库存中', color: '#409EFF' },
    { value: '使用中', label: '使用中', color: '#67C23A' },
    { value: '维修中', label: '维修中', color: '#E6A23C' },
    { value: '借用中', label: '借用中', color: '#F56C6C' },
    { value: '停用', label: '停用', color: '#C0C4CC' },
    { value: '已报废', label: '已报废', color: '#F56C6C' }
  ]
  return createApiResponse(statuses)
}

// 许可证管理 API

/**
 * 获取许可证统计信息
 * @returns {Promise}
 */
export function getLicenseStatistics() {
  const stats = {
    totalLicenses: 156,
    activeLicenses: 142,
    expiredLicenses: 8,
    expiringLicenses: 6,
    complianceRate: 0.95,
    totalCost: 2580000,
    monthlyCost: 45600,
    licensesByType: [
      { name: '永久许可证', value: 89, percentage: 57.1 },
      { name: '订阅许可证', value: 52, percentage: 33.3 },
      { name: '并发许可证', value: 15, percentage: 9.6 }
    ],
    complianceStatus: [
      { name: '合规', value: 142, color: '#67C23A' },
      { name: '超限', value: 6, color: '#F56C6C' },
      { name: '即将到期', value: 8, color: '#E6A23C' }
    ]
  }
  return createApiResponse(stats)
}

/**
 * 获取许可证列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getLicenseList(params = {}) {
  const { page = 1, pageSize = 20, keyword = '', type = '', status = '' } = params

  let licenses = mockLicenses.filter(license => {
    return (!keyword || license.softwareName.includes(keyword) || license.licenseKey.includes(keyword)) &&
           (!type || license.type === type) &&
           (!status || license.status === status)
  })

  const result = paginate(licenses, page, pageSize)
  return createApiResponse(result)
}

/**
 * 获取许可证详情
 * @param {string} id - 许可证ID
 * @returns {Promise}
 */
export function getLicenseDetail(id) {
  const license = mockLicenses.find(item => item.id === id)
  if (!license) {
    return createApiError('许可证不存在', 404)
  }

  // 添加使用详情
  const detailLicense = {
    ...license,
    usageDetails: [
      {
        id: 'usage001',
        computerName: 'PC-DEV-001',
        userName: '张工',
        department: '研发部',
        installDate: '2024-01-15',
        lastUsed: '2024-06-15'
      },
      {
        id: 'usage002',
        computerName: 'PC-DEV-002',
        userName: '李工',
        department: '研发部',
        installDate: '2024-02-01',
        lastUsed: '2024-06-14'
      }
    ],
    renewalHistory: [
      {
        id: 'renewal001',
        renewalDate: '2023-01-15',
        expiryDate: '2024-01-15',
        cost: 5000,
        operator: '管理员'
      }
    ]
  }

  return createApiResponse(detailLicense)
}

/**
 * 创建许可证
 * @param {Object} data - 许可证数据
 * @returns {Promise}
 */
export function createLicense(data) {
  const newLicense = {
    id: generateId(),
    createTime: formatDateTime(new Date()),
    updateTime: formatDateTime(new Date()),
    ...data
  }

  mockLicenses.unshift(newLicense)
  return createApiResponse(newLicense, '许可证创建成功')
}

/**
 * 更新许可证
 * @param {string} id - 许可证ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateLicense(id, data) {
  const index = mockLicenses.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('许可证不存在', 404)
  }

  mockLicenses[index] = {
    ...mockLicenses[index],
    ...data,
    updateTime: formatDateTime(new Date())
  }

  return createApiResponse(mockLicenses[index], '许可证更新成功')
}

/**
 * 删除许可证
 * @param {string} id - 许可证ID
 * @returns {Promise}
 */
export function deleteLicense(id) {
  const index = mockLicenses.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('许可证不存在', 404)
  }

  mockLicenses.splice(index, 1)
  return createApiResponse(null, '许可证删除成功')
}

/**
 * 许可证续费
 * @param {string} id - 许可证ID
 * @param {Object} renewalData - 续费信息
 * @returns {Promise}
 */
export function renewLicense(id, renewalData) {
  const license = mockLicenses.find(item => item.id === id)
  if (!license) {
    return createApiError('许可证不存在', 404)
  }

  license.expiryDate = renewalData.newExpiryDate
  license.cost = renewalData.cost
  license.status = '有效'
  license.updateTime = formatDateTime(new Date())

  return createApiResponse(license, '许可证续费成功')
}

/**
 * 获取即将到期的许可证
 * @param {number} days - 天数阈值
 * @returns {Promise}
 */
export function getExpiringLicenses(days = 30) {
  const today = new Date()
  const threshold = new Date(today.getTime() + days * 24 * 60 * 60 * 1000)

  const expiringLicenses = mockLicenses.filter(license => {
    const expiryDate = new Date(license.expiryDate)
    return expiryDate <= threshold && expiryDate >= today
  })

  return createApiResponse(expiringLicenses)
}

// Mock 资产数据
const mockAssets = [
  {
    id: 'asset001',
    assetCode: 'AST20240001',
    name: 'ThinkPad X1 Carbon',
    category: 'laptop',
    categoryName: '笔记本电脑',
    brand: '联想',
    model: 'X1 Carbon Gen 9',
    serialNumber: 'PC123456',
    status: '使用中',
    purchaseDate: '2024-01-15',
    purchasePrice: 12800,
    currentValue: 10240,
    supplier: '联想集团',
    warrantyExpiry: '2027-01-15',
    assignedTo: 'user001',
    assignedUser: '张工',
    department: '研发部',
    location: '北京总部-3楼-301',
    createTime: '2024-01-15 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'asset002',
    assetCode: 'AST20240002',
    name: 'Dell OptiPlex 7090',
    category: 'desktop',
    categoryName: '台式机',
    brand: '戴尔',
    model: 'OptiPlex 7090',
    serialNumber: 'DL789012',
    status: '库存中',
    purchaseDate: '2024-02-20',
    purchasePrice: 8500,
    currentValue: 7225,
    supplier: '戴尔科技',
    warrantyExpiry: '2027-02-20',
    assignedTo: null,
    assignedUser: null,
    department: null,
    location: '北京总部-仓库',
    createTime: '2024-02-20 10:15:00',
    updateTime: '2024-02-20 10:15:00'
  },
  {
    id: 'asset003',
    assetCode: 'AST20240003',
    name: 'MacBook Pro 14',
    category: 'laptop',
    categoryName: '笔记本电脑',
    brand: '苹果',
    model: 'MacBook Pro 14" M2',
    serialNumber: 'AP345678',
    status: '使用中',
    purchaseDate: '2024-03-10',
    purchasePrice: 18800,
    currentValue: 16920,
    supplier: '苹果授权经销商',
    warrantyExpiry: '2027-03-10',
    assignedTo: 'user002',
    assignedUser: '李工',
    department: '设计部',
    location: '北京总部-2楼-205',
    createTime: '2024-03-10 11:20:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'asset004',
    assetCode: 'AST20240004',
    name: 'HP ProDesk 600 G6',
    category: 'desktop',
    categoryName: '台式机',
    brand: '惠普',
    model: 'ProDesk 600 G6',
    serialNumber: 'HP901234',
    status: '维修中',
    purchaseDate: '2023-12-05',
    purchasePrice: 7200,
    currentValue: 5760,
    supplier: '惠普中国',
    warrantyExpiry: '2026-12-05',
    assignedTo: 'user003',
    assignedUser: '王工',
    department: '财务部',
    location: '维修中心',
    createTime: '2023-12-05 09:30:00',
    updateTime: '2024-06-10 16:45:00'
  },
  {
    id: 'asset005',
    assetCode: 'AST20240005',
    name: 'Cisco Catalyst 2960',
    category: 'network',
    categoryName: '网络设备',
    brand: '思科',
    model: 'Catalyst 2960-X',
    serialNumber: 'CS567890',
    status: '使用中',
    purchaseDate: '2024-01-20',
    purchasePrice: 15600,
    currentValue: 14040,
    supplier: '思科系统',
    warrantyExpiry: '2029-01-20',
    assignedTo: 'user004',
    assignedUser: '赵工',
    department: 'IT部门',
    location: '北京总部-机房',
    createTime: '2024-01-20 14:15:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'asset006',
    assetCode: 'AST20240006',
    name: 'Dell PowerEdge R740',
    category: 'server',
    categoryName: '服务器',
    brand: '戴尔',
    model: 'PowerEdge R740',
    serialNumber: 'DL112233',
    status: '使用中',
    purchaseDate: '2023-11-15',
    purchasePrice: 45000,
    currentValue: 36000,
    supplier: '戴尔科技',
    warrantyExpiry: '2026-11-15',
    assignedTo: 'user005',
    assignedUser: '刘工',
    department: 'IT部门',
    location: '北京总部-数据中心',
    createTime: '2023-11-15 10:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'asset007',
    assetCode: 'AST20240007',
    name: 'iPad Pro 12.9',
    category: 'mobile',
    categoryName: '移动设备',
    brand: '苹果',
    model: 'iPad Pro 12.9" M2',
    serialNumber: 'IP789012',
    status: '借用中',
    purchaseDate: '2024-04-08',
    purchasePrice: 8800,
    currentValue: 8360,
    supplier: '苹果授权经销商',
    warrantyExpiry: '2027-04-08',
    assignedTo: 'user006',
    assignedUser: '孙工',
    department: '市场部',
    location: '上海分部',
    createTime: '2024-04-08 13:45:00',
    updateTime: '2024-06-05 09:20:00'
  },
  {
    id: 'asset008',
    assetCode: 'AST20240008',
    name: 'HP LaserJet Pro M404n',
    category: 'printer',
    categoryName: '打印设备',
    brand: '惠普',
    model: 'LaserJet Pro M404n',
    serialNumber: 'HP445566',
    status: '使用中',
    purchaseDate: '2024-02-28',
    purchasePrice: 2800,
    currentValue: 2380,
    supplier: '惠普中国',
    warrantyExpiry: '2027-02-28',
    assignedTo: null,
    assignedUser: null,
    department: '行政部',
    location: '北京总部-1楼-打印室',
    createTime: '2024-02-28 15:30:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'asset009',
    assetCode: 'AST20240009',
    name: 'Surface Studio 2+',
    category: 'desktop',
    categoryName: '台式机',
    brand: '微软',
    model: 'Surface Studio 2+',
    serialNumber: 'MS778899',
    status: '停用',
    purchaseDate: '2023-08-12',
    purchasePrice: 28000,
    currentValue: 19600,
    supplier: '微软中国',
    warrantyExpiry: '2026-08-12',
    assignedTo: null,
    assignedUser: null,
    department: null,
    location: '北京总部-仓库',
    createTime: '2023-08-12 11:10:00',
    updateTime: '2024-05-20 10:15:00'
  },
  {
    id: 'asset010',
    assetCode: 'AST20240010',
    name: 'ASUS ROG Strix G15',
    category: 'laptop',
    categoryName: '笔记本电脑',
    brand: '华硕',
    model: 'ROG Strix G15',
    serialNumber: 'AS990011',
    status: '已报废',
    purchaseDate: '2022-06-20',
    purchasePrice: 9800,
    currentValue: 0,
    supplier: '华硕电脑',
    warrantyExpiry: '2025-06-20',
    assignedTo: null,
    assignedUser: null,
    department: null,
    location: '已处置',
    createTime: '2022-06-20 16:20:00',
    updateTime: '2024-05-15 09:30:00'
  }
]

// Mock 许可证数据
const mockLicenses = [
  {
    id: 'license001',
    softwareName: 'Microsoft Office 365',
    version: 'E3',
    type: '订阅许可证',
    licenseKey: 'XXXXX-XXXXX-XXXXX-XXXXX-XXXXX',
    totalQuantity: 100,
    usedQuantity: 95,
    availableQuantity: 5,
    purchaseDate: '2024-01-01',
    expiryDate: '2025-01-01',
    cost: 50000,
    supplier: '微软中国',
    status: '有效',
    complianceStatus: '合规',
    department: '全公司',
    createTime: '2024-01-01 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license002',
    softwareName: 'AutoCAD',
    version: '2024',
    type: '并发许可证',
    licenseKey: 'YYYYY-YYYYY-YYYYY-YYYYY-YYYYY',
    totalQuantity: 20,
    usedQuantity: 23,
    availableQuantity: -3,
    purchaseDate: '2023-06-15',
    expiryDate: '2024-06-15',
    cost: 80000,
    supplier: 'Autodesk',
    status: '即将到期',
    complianceStatus: '超限',
    department: '设计部',
    createTime: '2023-06-15 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license003',
    softwareName: 'Windows Server 2022',
    version: 'Standard',
    type: '永久许可证',
    licenseKey: 'ZZZZZ-ZZZZZ-ZZZZZ-ZZZZZ-ZZZZZ',
    totalQuantity: 10,
    usedQuantity: 8,
    availableQuantity: 2,
    purchaseDate: '2024-01-10',
    expiryDate: '永久',
    cost: 120000,
    supplier: '微软中国',
    status: '有效',
    complianceStatus: '合规',
    department: 'IT部门',
    createTime: '2024-01-10 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license004',
    softwareName: 'Adobe Creative Cloud',
    version: 'All Apps',
    type: '订阅许可证',
    licenseKey: 'AAAAA-BBBBB-CCCCC-DDDDD-EEEEE',
    totalQuantity: 25,
    usedQuantity: 22,
    availableQuantity: 3,
    purchaseDate: '2024-03-01',
    expiryDate: '2025-03-01',
    cost: 75000,
    supplier: 'Adobe Systems',
    status: '有效',
    complianceStatus: '合规',
    department: '设计部',
    createTime: '2024-03-01 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license005',
    softwareName: 'VMware vSphere',
    version: '8.0',
    type: '永久许可证',
    licenseKey: 'VVVVV-MMMMM-WWWWW-AAAAA-RRRRR',
    totalQuantity: 5,
    usedQuantity: 5,
    availableQuantity: 0,
    purchaseDate: '2023-09-20',
    expiryDate: '永久',
    cost: 200000,
    supplier: 'VMware',
    status: '有效',
    complianceStatus: '合规',
    department: 'IT部门',
    createTime: '2023-09-20 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license006',
    softwareName: 'JetBrains IntelliJ IDEA',
    version: 'Ultimate',
    type: '订阅许可证',
    licenseKey: 'JJJJJ-EEEEE-TTTTT-BBBBB-RRRRR',
    totalQuantity: 15,
    usedQuantity: 12,
    availableQuantity: 3,
    purchaseDate: '2024-02-15',
    expiryDate: '2025-02-15',
    cost: 30000,
    supplier: 'JetBrains',
    status: '有效',
    complianceStatus: '合规',
    department: '研发部',
    createTime: '2024-02-15 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license007',
    softwareName: 'Antivirus Enterprise',
    version: '2024',
    type: '订阅许可证',
    licenseKey: 'ANTIV-IRUS-ENTER-PRISE-2024X',
    totalQuantity: 500,
    usedQuantity: 485,
    availableQuantity: 15,
    purchaseDate: '2024-01-01',
    expiryDate: '2024-12-31',
    cost: 25000,
    supplier: '卡巴斯基',
    status: '即将到期',
    complianceStatus: '合规',
    department: '全公司',
    createTime: '2024-01-01 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license008',
    softwareName: 'Oracle Database',
    version: '19c Enterprise',
    type: '永久许可证',
    licenseKey: 'ORACL-DATAB-ASE19-ENTER-PRISE',
    totalQuantity: 2,
    usedQuantity: 2,
    availableQuantity: 0,
    purchaseDate: '2023-05-10',
    expiryDate: '永久',
    cost: 500000,
    supplier: 'Oracle',
    status: '有效',
    complianceStatus: '合规',
    department: 'IT部门',
    createTime: '2023-05-10 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license009',
    softwareName: 'Slack Business+',
    version: 'Business+',
    type: '订阅许可证',
    licenseKey: 'SLACK-BUSIN-ESS-PLUS-2024X',
    totalQuantity: 200,
    usedQuantity: 178,
    availableQuantity: 22,
    purchaseDate: '2024-04-01',
    expiryDate: '2025-04-01',
    cost: 60000,
    supplier: 'Slack Technologies',
    status: '有效',
    complianceStatus: '合规',
    department: '全公司',
    createTime: '2024-04-01 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  },
  {
    id: 'license010',
    softwareName: 'SolidWorks Professional',
    version: '2024',
    type: '永久许可证',
    licenseKey: 'SOLID-WORKS-PROFE-SSION-AL24',
    totalQuantity: 8,
    usedQuantity: 10,
    availableQuantity: -2,
    purchaseDate: '2023-12-01',
    expiryDate: '永久',
    cost: 160000,
    supplier: 'Dassault Systèmes',
    status: '有效',
    complianceStatus: '超限',
    department: '设计部',
    createTime: '2023-12-01 09:00:00',
    updateTime: '2024-06-01 14:30:00'
  }
]
