<template>
  <div class="automation-engine">
    <!-- 自动化状态展示 -->
    <div class="automation-status">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>自动化处理状态</span>
            <el-tag :type="getStatusType(automationStatus)">
              {{ getStatusText(automationStatus) }}
            </el-tag>
          </div>
        </template>
        
        <div class="status-content">
          <div class="status-info">
            <div class="info-item">
              <span class="label">请求编号：</span>
              <span class="value">{{ requestData.requestNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">服务类型：</span>
              <span class="value">{{ requestData.serviceName }}</span>
            </div>
            <div class="info-item">
              <span class="label">自动化类型：</span>
              <span class="value">{{ automationType }}</span>
            </div>
            <div class="info-item">
              <span class="label">开始时间：</span>
              <span class="value">{{ startTime }}</span>
            </div>
            <div class="info-item" v-if="endTime">
              <span class="label">完成时间：</span>
              <span class="value">{{ endTime }}</span>
            </div>
          </div>
          
          <!-- 进度条 -->
          <div class="progress-section">
            <div class="progress-info">
              <span>执行进度</span>
              <span class="progress-percent">{{ progress }}%</span>
            </div>
            <el-progress 
              :percentage="progress" 
              :stroke-width="8"
              :status="getProgressStatus()"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 执行步骤 -->
    <div class="execution-steps">
      <el-card>
        <template #header>
          <span>执行步骤</span>
        </template>
        
        <el-timeline>
          <el-timeline-item
            v-for="(step, index) in executionSteps"
            :key="index"
            :timestamp="step.timestamp"
            :type="step.type"
            :icon="step.icon"
          >
            <div class="timeline-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-description">{{ step.description }}</div>
              <div v-if="step.details" class="step-details">
                <el-collapse>
                  <el-collapse-item title="查看详情" name="details">
                    <pre class="step-log">{{ step.details }}</pre>
                  </el-collapse-item>
                </el-collapse>
              </div>
              <div v-if="step.error" class="step-error">
                <el-alert
                  :title="step.error"
                  type="error"
                  :closable="false"
                  show-icon
                />
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel" v-if="showControls">
      <el-card>
        <template #header>
          <span>控制操作</span>
        </template>
        
        <div class="control-actions">
          <el-button 
            type="primary" 
            @click="startAutomation"
            :disabled="automationStatus === 'running'"
            :loading="starting"
          >
            <el-icon><VideoPlay /></el-icon>
            开始执行
          </el-button>
          
          <el-button 
            type="warning" 
            @click="pauseAutomation"
            :disabled="automationStatus !== 'running'"
          >
            <el-icon><VideoPause /></el-icon>
            暂停执行
          </el-button>
          
          <el-button 
            type="danger" 
            @click="stopAutomation"
            :disabled="!['running', 'paused'].includes(automationStatus)"
          >
            <el-icon><VideoStop /></el-icon>
            停止执行
          </el-button>
          
          <el-button 
            @click="retryAutomation"
            :disabled="automationStatus !== 'failed'"
          >
            <el-icon><Refresh /></el-icon>
            重试执行
          </el-button>
          
          <el-button 
            type="info" 
            @click="viewLogs"
          >
            <el-icon><Document /></el-icon>
            查看日志
          </el-button>
        </div>
        
        <!-- 参数配置 -->
        <div class="automation-params" v-if="showParams">
          <h4>执行参数</h4>
          <el-form :model="automationParams" label-width="120px">
            <el-form-item 
              v-for="param in parameterConfig" 
              :key="param.name"
              :label="param.label"
            >
              <el-input 
                v-if="param.type === 'text'"
                v-model="automationParams[param.name]"
                :placeholder="param.placeholder"
              />
              <el-select 
                v-else-if="param.type === 'select'"
                v-model="automationParams[param.name]"
                :placeholder="param.placeholder"
              >
                <el-option 
                  v-for="option in param.options" 
                  :key="option.value"
                  :label="option.label" 
                  :value="option.value" 
                />
              </el-select>
              <el-switch 
                v-else-if="param.type === 'boolean'"
                v-model="automationParams[param.name]"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 日志查看对话框 -->
    <el-dialog v-model="showLogDialog" title="执行日志" width="800px">
      <div class="log-content">
        <div class="log-header">
          <el-button @click="refreshLogs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="downloadLogs">
            <el-icon><Download /></el-icon>
            下载日志
          </el-button>
        </div>
        <div class="log-viewer">
          <pre class="log-text">{{ executionLogs }}</pre>
        </div>
      </div>
    </el-dialog>

    <!-- 脚本编辑器 -->
    <div class="script-editor" v-if="showScriptEditor">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>自动化脚本</span>
            <div class="header-actions">
              <el-button @click="validateScript">验证脚本</el-button>
              <el-button type="primary" @click="saveScript">保存脚本</el-button>
            </div>
          </div>
        </template>
        
        <div class="editor-container">
          <el-tabs v-model="activeScriptTab">
            <el-tab-pane label="PowerShell" name="powershell">
              <el-input
                v-model="scripts.powershell"
                type="textarea"
                :rows="15"
                placeholder="输入PowerShell脚本..."
                class="script-textarea"
              />
            </el-tab-pane>
            
            <el-tab-pane label="Python" name="python">
              <el-input
                v-model="scripts.python"
                type="textarea"
                :rows="15"
                placeholder="输入Python脚本..."
                class="script-textarea"
              />
            </el-tab-pane>
            
            <el-tab-pane label="API调用" name="api">
              <el-form :model="apiConfig" label-width="100px">
                <el-form-item label="请求URL">
                  <el-input v-model="apiConfig.url" placeholder="https://api.example.com/endpoint" />
                </el-form-item>
                <el-form-item label="请求方法">
                  <el-select v-model="apiConfig.method">
                    <el-option label="GET" value="GET" />
                    <el-option label="POST" value="POST" />
                    <el-option label="PUT" value="PUT" />
                    <el-option label="DELETE" value="DELETE" />
                  </el-select>
                </el-form-item>
                <el-form-item label="请求头">
                  <el-input
                    v-model="apiConfig.headers"
                    type="textarea"
                    :rows="3"
                    placeholder='{"Content-Type": "application/json"}'
                  />
                </el-form-item>
                <el-form-item label="请求体">
                  <el-input
                    v-model="apiConfig.body"
                    type="textarea"
                    :rows="5"
                    placeholder='{"key": "value"}'
                  />
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  VideoPlay, VideoPause, VideoStop, Refresh, Document, Download,
  CircleCheck, Clock, Warning, CircleClose
} from '@element-plus/icons-vue'

const props = defineProps({
  requestId: {
    type: String,
    required: true
  },
  requestData: {
    type: Object,
    default: () => ({})
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showParams: {
    type: Boolean,
    default: false
  },
  showScriptEditor: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['start', 'pause', 'stop', 'retry', 'complete', 'error'])

// 响应式数据
const automationStatus = ref('pending') // pending, running, paused, completed, failed
const progress = ref(0)
const startTime = ref('')
const endTime = ref('')
const starting = ref(false)
const showLogDialog = ref(false)
const activeScriptTab = ref('powershell')

// 自动化参数
const automationParams = reactive({
  timeout: 300,
  retryCount: 3,
  notifyOnComplete: true
})

// 脚本配置
const scripts = reactive({
  powershell: '',
  python: '',
})

const apiConfig = reactive({
  url: '',
  method: 'POST',
  headers: '{}',
  body: '{}'
})

// 执行步骤
const executionSteps = ref([
  {
    title: '初始化自动化环境',
    description: '准备执行环境和参数',
    timestamp: '2024-01-15 10:30:00',
    type: 'success',
    icon: CircleCheck
  },
  {
    title: '执行密码重置脚本',
    description: '调用Active Directory API重置用户密码',
    timestamp: '2024-01-15 10:30:30',
    type: 'primary',
    icon: Clock,
    details: 'PowerShell脚本执行中...\n连接到域控制器...\n重置用户密码...'
  }
])

const executionLogs = ref(`[2024-01-15 10:30:00] 开始执行自动化流程
[2024-01-15 10:30:01] 初始化PowerShell环境
[2024-01-15 10:30:02] 连接到Active Directory
[2024-01-15 10:30:03] 验证用户权限
[2024-01-15 10:30:04] 执行密码重置操作
[2024-01-15 10:30:05] 发送通知邮件
[2024-01-15 10:30:06] 自动化流程执行完成`)

// 参数配置
const parameterConfig = ref([
  {
    name: 'timeout',
    label: '超时时间(秒)',
    type: 'text',
    placeholder: '300'
  },
  {
    name: 'retryCount',
    label: '重试次数',
    type: 'select',
    options: [
      { label: '1次', value: 1 },
      { label: '3次', value: 3 },
      { label: '5次', value: 5 }
    ]
  },
  {
    name: 'notifyOnComplete',
    label: '完成时通知',
    type: 'boolean'
  }
])

// 计算属性
const automationType = computed(() => {
  const typeMap = {
    'password-reset': 'PowerShell脚本',
    'vpn': 'API调用',
    'email': 'Exchange API',
    'software': '软件部署脚本'
  }
  return typeMap[props.requestData.serviceId] || '自定义脚本'
})

// 方法定义
const getStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    running: 'warning',
    paused: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '等待执行',
    running: '执行中',
    paused: '已暂停',
    completed: '执行完成',
    failed: '执行失败'
  }
  return statusMap[status] || '未知状态'
}

const getProgressStatus = () => {
  if (automationStatus.value === 'failed') return 'exception'
  if (automationStatus.value === 'completed') return 'success'
  return undefined
}

const startAutomation = async () => {
  try {
    starting.value = true
    automationStatus.value = 'running'
    startTime.value = new Date().toLocaleString()
    
    // 模拟自动化执行过程
    await simulateAutomation()
    
    emit('start', {
      requestId: props.requestId,
      params: automationParams
    })
  } catch (error) {
    automationStatus.value = 'failed'
    ElMessage.error('启动自动化失败')
    emit('error', error)
  } finally {
    starting.value = false
  }
}

const pauseAutomation = () => {
  automationStatus.value = 'paused'
  emit('pause', props.requestId)
  ElMessage.info('自动化执行已暂停')
}

const stopAutomation = async () => {
  try {
    await ElMessageBox.confirm('确定要停止自动化执行吗？', '确认停止', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    automationStatus.value = 'failed'
    endTime.value = new Date().toLocaleString()
    emit('stop', props.requestId)
    ElMessage.success('自动化执行已停止')
  } catch (error) {
    // 用户取消
  }
}

const retryAutomation = () => {
  automationStatus.value = 'pending'
  progress.value = 0
  startTime.value = ''
  endTime.value = ''
  startAutomation()
}

const viewLogs = () => {
  showLogDialog.value = true
}

const refreshLogs = () => {
  // 刷新日志内容
  ElMessage.success('日志已刷新')
}

const downloadLogs = () => {
  // 下载日志文件
  const blob = new Blob([executionLogs.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `automation-log-${props.requestId}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const validateScript = () => {
  // 验证脚本语法
  ElMessage.success('脚本验证通过')
}

const saveScript = () => {
  // 保存脚本
  ElMessage.success('脚本保存成功')
}

// 模拟自动化执行
const simulateAutomation = async () => {
  const steps = [
    { progress: 20, message: '初始化环境' },
    { progress: 40, message: '连接目标系统' },
    { progress: 60, message: '执行操作' },
    { progress: 80, message: '验证结果' },
    { progress: 100, message: '完成处理' }
  ]
  
  for (const step of steps) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    progress.value = step.progress
    
    if (step.progress === 100) {
      automationStatus.value = 'completed'
      endTime.value = new Date().toLocaleString()
      emit('complete', props.requestId)
    }
  }
}

// 生命周期
onMounted(() => {
  // 初始化自动化引擎
  if (props.requestData.automationEnabled) {
    // 自动开始执行
    setTimeout(() => {
      startAutomation()
    }, 1000)
  }
})
</script>

<style scoped>
.automation-engine {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  color: #606266;
  font-size: 14px;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-percent {
  font-weight: 600;
  color: #409EFF;
}

.timeline-content {
  padding-left: 12px;
}

.step-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.step-description {
  color: #606266;
  margin-bottom: 8px;
}

.step-details {
  margin-top: 8px;
}

.step-log {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.step-error {
  margin-top: 8px;
}

.control-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.automation-params h4 {
  margin: 0 0 16px 0;
  color: #303133;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.log-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.log-header {
  display: flex;
  gap: 12px;
}

.log-viewer {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.log-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #303133;
  white-space: pre-wrap;
  margin: 0;
}

.editor-container {
  margin-top: 16px;
}

.script-textarea :deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

@media (max-width: 768px) {
  .status-info {
    grid-template-columns: 1fr;
  }
  
  .control-actions {
    flex-direction: column;
  }
  
  .control-actions .el-button {
    width: 100%;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-item .label {
    min-width: auto;
  }
}
</style>
