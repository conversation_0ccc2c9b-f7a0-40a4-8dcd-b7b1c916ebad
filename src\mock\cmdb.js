import Mock from 'mockjs'

// 模拟数据生成器
const Random = Mock.Random

// 配置项类型
const CI_TYPES = ['server', 'database', 'network', 'application', 'storage']
const CI_STATUS = ['运行中', '维护中', '故障', '离线']
const RELATION_TYPES = ['depends', 'connects', 'contains', 'monitors']

// 生成配置项数据
const generateCI = (id) => {
  const type = Random.pick(CI_TYPES)
  const status = Random.pick(CI_STATUS)
  
  return {
    id: id || `CI-${Random.string('number', 3)}`,
    name: `${type.toUpperCase()}-${Random.string('upper', 3)}-${Random.string('number', 2)}`,
    type,
    status,
    ip: Random.ip(),
    mac: Random.string('upper', 2) + ':' + Random.string('upper', 2) + ':' + Random.string('upper', 2) + ':' + Random.string('upper', 2) + ':' + Random.string('upper', 2) + ':' + Random.string('upper', 2),
    os: Random.pick(['CentOS 7.9', 'Ubuntu 20.04', 'Windows Server 2019', 'RHEL 8.5']),
    version: `v${Random.integer(1, 5)}.${Random.integer(0, 9)}.${Random.integer(0, 9)}`,
    owner: Random.cname(),
    department: Random.pick(['IT运维部', '开发部', '测试部', '产品部']),
    location: `机房${Random.pick(['A', 'B', 'C'])}-${Random.string('number', 2)}`,
    vendor: Random.pick(['Dell', 'HP', 'IBM', 'Huawei', 'Lenovo']),
    description: Random.sentence(5, 10),
    createTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
    updateTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
    healthScore: Random.integer(60, 100),
    importance: Random.integer(1, 5)
  }
}

// 生成关系数据
const generateRelation = (sourceId, targetId) => {
  const type = Random.pick(RELATION_TYPES)
  
  return {
    id: `REL-${Random.string('number', 6)}`,
    sourceId,
    targetId,
    type,
    description: `${sourceId} ${type} ${targetId}`,
    confidence: Random.integer(70, 100),
    createTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
    creator: Random.cname(),
    status: Random.pick(['normal', 'abnormal', 'pending'])
  }
}

// 生成版本数据
const generateVersion = (ciId) => {
  return {
    id: `VER-${Random.string('number', 6)}`,
    ciId,
    version: `v${Random.integer(1, 3)}.${Random.integer(0, 9)}.${Random.integer(0, 9)}`,
    type: Random.pick(['auto', 'manual', 'change', 'backup']),
    description: Random.sentence(3, 8),
    createTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
    creator: Random.cname(),
    size: Random.integer(1000000, 10000000),
    isCurrent: Random.boolean()
  }
}

// 生成质量问题数据
const generateQualityIssue = () => {
  return {
    id: `QI-${Random.string('number', 3)}`,
    ciId: `CI-${Random.string('number', 3)}`,
    ciName: `${Random.pick(CI_TYPES).toUpperCase()}-${Random.string('upper', 3)}-${Random.string('number', 2)}`,
    category: Random.pick(['completeness', 'consistency', 'accuracy', 'freshness']),
    severity: Random.pick(['critical', 'warning', 'info']),
    description: Random.sentence(5, 10),
    detectTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
    status: Random.pick(['待处理', '处理中', '已修复', '已忽略'])
  }
}

// 生成发现任务数据
const generateDiscoveryTask = () => {
  return {
    id: `TASK-${Random.string('number', 3)}`,
    name: `${Random.pick(['办公网络', '服务器', '数据库', '应用系统'])}${Random.pick(['扫描', '发现', '检测'])}`,
    type: Random.pick(['network', 'snmp', 'wmi', 'ssh', 'api']),
    protocol: Random.pick(['SNMP', 'WMI', 'SSH', 'HTTP']),
    range: Random.ip() + '/24',
    status: Random.pick(['running', 'completed', 'failed', 'stopped']),
    progress: Random.integer(0, 100),
    lastRun: Random.datetime('yyyy-MM-dd HH:mm:ss'),
    nextRun: Random.datetime('yyyy-MM-dd HH:mm:ss'),
    discovered: Random.integer(10, 100),
    newDiscovered: Random.integer(0, 10),
    duration: `${Random.integer(1, 30)}分钟`,
    creator: Random.cname()
  }
}

// Mock API 接口
Mock.mock(/\/api\/cmdb\/ci\/list/, 'get', (options) => {
  const params = new URLSearchParams(options.url.split('?')[1])
  const page = parseInt(params.get('page')) || 1
  const pageSize = parseInt(params.get('pageSize')) || 20
  const type = params.get('type')
  const status = params.get('status')
  const keyword = params.get('keyword')

  // 生成配置项列表
  let items = []
  for (let i = 0; i < 100; i++) {
    items.push(generateCI())
  }

  // 筛选
  if (type) {
    items = items.filter(item => item.type === type)
  }
  if (status) {
    items = items.filter(item => item.status === status)
  }
  if (keyword) {
    items = items.filter(item => 
      item.name.toLowerCase().includes(keyword.toLowerCase()) ||
      item.description.toLowerCase().includes(keyword.toLowerCase())
    )
  }

  // 分页
  const total = items.length
  const start = (page - 1) * pageSize
  const end = start + pageSize
  items = items.slice(start, end)

  return {
    code: 200,
    message: 'success',
    data: {
      items,
      total,
      page,
      pageSize,
      stats: {
        total: 156,
        running: 142,
        maintenance: 8,
        error: 4,
        offline: 2
      }
    }
  }
})

Mock.mock(/\/api\/cmdb\/ci\/(\w+)/, 'get', (options) => {
  const id = options.url.match(/\/api\/cmdb\/ci\/(\w+)/)[1]
  const ci = generateCI(id)
  
  // 添加详细信息
  ci.relatedEvents = Array.from({ length: Random.integer(3, 8) }, () => ({
    id: `INC-${Random.string('number', 3)}`,
    title: Random.sentence(3, 6),
    type: Random.pick(['事件', '请求', '问题', '变更']),
    status: Random.pick(['已解决', '处理中', '已完成', '待处理']),
    createTime: Random.datetime('yyyy-MM-dd HH:mm:ss')
  }))
  
  ci.versions = Array.from({ length: Random.integer(3, 10) }, () => generateVersion(id))
  
  return {
    code: 200,
    message: 'success',
    data: ci
  }
})

Mock.mock(/\/api\/cmdb\/ci/, 'post', () => {
  return {
    code: 200,
    message: '配置项创建成功',
    data: generateCI()
  }
})

Mock.mock(/\/api\/cmdb\/ci\/(\w+)/, 'put', (options) => {
  const id = options.url.match(/\/api\/cmdb\/ci\/(\w+)/)[1]
  return {
    code: 200,
    message: '配置项更新成功',
    data: generateCI(id)
  }
})

Mock.mock(/\/api\/cmdb\/ci\/(\w+)/, 'delete', () => {
  return {
    code: 200,
    message: '配置项删除成功'
  }
})

Mock.mock(/\/api\/cmdb\/topology/, 'get', () => {
  const nodes = Array.from({ length: 20 }, () => {
    const ci = generateCI()
    return {
      ...ci,
      x: Random.integer(50, 750),
      y: Random.integer(50, 550)
    }
  })
  
  const connections = []
  for (let i = 0; i < 15; i++) {
    const source = Random.pick(nodes)
    const target = Random.pick(nodes.filter(n => n.id !== source.id))
    connections.push(generateRelation(source.id, target.id))
  }
  
  return {
    code: 200,
    message: 'success',
    data: { nodes, connections }
  }
})

Mock.mock(/\/api\/cmdb\/discovery\/tasks/, 'get', () => {
  const tasks = Array.from({ length: 20 }, generateDiscoveryTask)
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: tasks,
      total: tasks.length
    }
  }
})

Mock.mock(/\/api\/cmdb\/discovery\/tasks/, 'post', () => {
  return {
    code: 200,
    message: '发现任务创建成功',
    data: generateDiscoveryTask()
  }
})

Mock.mock(/\/api\/cmdb\/discovery\/tasks\/(\w+)\/run/, 'post', () => {
  return {
    code: 200,
    message: '发现任务已启动'
  }
})

Mock.mock(/\/api\/cmdb\/relations/, 'get', () => {
  const relations = Array.from({ length: 30 }, () => {
    const sourceId = `CI-${Random.string('number', 3)}`
    const targetId = `CI-${Random.string('number', 3)}`
    const relation = generateRelation(sourceId, targetId)
    
    return {
      ...relation,
      sourceName: `${Random.pick(CI_TYPES).toUpperCase()}-${Random.string('upper', 3)}-${Random.string('number', 2)}`,
      targetName: `${Random.pick(CI_TYPES).toUpperCase()}-${Random.string('upper', 3)}-${Random.string('number', 2)}`,
      relationTypeName: Random.pick(['依赖于', '连接到', '运行于', '包含', '监控']),
      relationTypeColor: Random.color()
    }
  })
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: relations,
      total: relations.length
    }
  }
})

Mock.mock(/\/api\/cmdb\/relations/, 'post', () => {
  return {
    code: 200,
    message: '关系创建成功',
    data: generateRelation(`CI-${Random.string('number', 3)}`, `CI-${Random.string('number', 3)}`)
  }
})

Mock.mock(/\/api\/cmdb\/versions/, 'get', () => {
  const versions = Array.from({ length: 50 }, () => generateVersion(`CI-${Random.string('number', 3)}`))
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: versions,
      total: versions.length
    }
  }
})

Mock.mock(/\/api\/cmdb\/versions\/snapshot/, 'post', () => {
  return {
    code: 200,
    message: '版本快照创建成功',
    data: generateVersion(`CI-${Random.string('number', 3)}`)
  }
})

Mock.mock(/\/api\/cmdb\/versions\/compare/, 'post', () => {
  return {
    code: 200,
    message: 'success',
    data: {
      fieldChanges: [
        {
          field: 'IP地址',
          type: 'modified',
          oldValue: '************',
          newValue: '************'
        },
        {
          field: '内存配置',
          type: 'modified',
          oldValue: '8GB',
          newValue: '16GB'
        }
      ],
      relationChanges: [
        {
          type: 'added',
          title: '新增关系',
          changes: [
            { relation: '依赖于', target: 'CACHE-SRV-01', action: '新增' }
          ]
        }
      ],
      stats: {
        totalChanges: 3,
        fieldChanges: 2,
        relationChanges: 1,
        similarity: 87
      }
    }
  }
})

Mock.mock(/\/api\/cmdb\/quality\/report/, 'get', () => {
  return {
    code: 200,
    message: 'success',
    data: {
      overallScore: Random.integer(70, 95),
      metrics: {
        completeness: { score: Random.integer(80, 100), issueCount: Random.integer(5, 25) },
        consistency: { score: Random.integer(75, 95), issueCount: Random.integer(3, 20) },
        accuracy: { score: Random.integer(85, 100), issueCount: Random.integer(2, 15) },
        freshness: { score: Random.integer(60, 90), issueCount: Random.integer(10, 50) }
      }
    }
  }
})

Mock.mock(/\/api\/cmdb\/quality\/issues/, 'get', () => {
  const issues = Array.from({ length: 30 }, generateQualityIssue)
  
  return {
    code: 200,
    message: 'success',
    data: {
      items: issues,
      total: issues.length
    }
  }
})

Mock.mock(/\/api\/cmdb\/stats/, 'get', () => {
  return {
    code: 200,
    message: 'success',
    data: {
      totalCIs: Random.integer(1000, 5000),
      totalRelations: Random.integer(2000, 8000),
      totalVersions: Random.integer(5000, 20000),
      qualityScore: Random.integer(70, 95),
      trends: {
        ciGrowth: Random.integer(-5, 15),
        relationGrowth: Random.integer(-3, 20),
        qualityTrend: Random.integer(-10, 10)
      }
    }
  }
})

export default Mock
