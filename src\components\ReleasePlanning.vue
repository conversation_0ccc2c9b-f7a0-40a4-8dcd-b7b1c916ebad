<template>
  <div class="release-planning">
    <el-card class="planning-card">
      <template #header>
        <div class="card-header">
          <span>发布计划 - {{ release.name }}</span>
          <div class="header-actions">
            <el-button type="text" :icon="Edit" @click="editMode = !editMode">
              {{ editMode ? '保存' : '编辑' }}
            </el-button>
            <el-button type="text" :icon="Download" @click="exportPlan">
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 发布基本信息 -->
      <div class="release-info">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>发布类型：</label>
              <el-tag :type="getTypeTagType(release.type)">
                {{ getTypeLabel(release.type) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>风险等级：</label>
              <el-tag :type="getRiskTagType(release.riskLevel)">
                {{ getRiskLabel(release.riskLevel) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>负责人：</label>
              <span>{{ release.owner }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 12px;">
          <el-col :span="12">
            <div class="info-item">
              <label>计划开始：</label>
              <span>{{ release.plannedStartDate }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>计划结束：</label>
              <span>{{ release.plannedEndDate }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 阶段式发布流程 -->
      <div class="stages-section">
        <h3>发布阶段</h3>
        <div class="stages-timeline">
          <div 
            v-for="(stage, index) in release.stages" 
            :key="stage.key"
            class="stage-item"
            :class="{ 
              'active': stage.status === 'in-progress',
              'completed': stage.status === 'completed',
              'failed': stage.status === 'failed'
            }"
          >
            <!-- 阶段连接线 -->
            <div v-if="index < release.stages.length - 1" class="stage-connector"></div>
            
            <!-- 阶段图标 -->
            <div class="stage-icon">
              <el-icon v-if="stage.status === 'completed'">
                <SuccessFilled />
              </el-icon>
              <el-icon v-else-if="stage.status === 'in-progress'">
                <Loading />
              </el-icon>
              <el-icon v-else-if="stage.status === 'failed'">
                <CircleCloseFilled />
              </el-icon>
              <span v-else class="stage-number">{{ index + 1 }}</span>
            </div>

            <!-- 阶段内容 -->
            <div class="stage-content">
              <div class="stage-header">
                <h4>{{ stage.name }}</h4>
                <el-tag 
                  :type="getStageStatusTagType(stage.status)" 
                  size="small"
                >
                  {{ getStageStatusLabel(stage.status) }}
                </el-tag>
              </div>
              
              <div class="stage-details">
                <p class="stage-responsible">负责人：{{ stage.responsible }}</p>
                <p class="stage-criteria">出口准则：{{ stage.exitCriteria }}</p>
                
                <div class="stage-timeline-info">
                  <div v-if="stage.startDate" class="timeline-item">
                    <span class="timeline-label">开始时间：</span>
                    <span class="timeline-value">{{ stage.startDate }}</span>
                  </div>
                  <div v-if="stage.endDate" class="timeline-item">
                    <span class="timeline-label">结束时间：</span>
                    <span class="timeline-value">{{ stage.endDate }}</span>
                  </div>
                  <div v-if="stage.notes" class="timeline-item">
                    <span class="timeline-label">备注：</span>
                    <span class="timeline-value">{{ stage.notes }}</span>
                  </div>
                </div>

                <!-- 阶段操作 -->
                <div class="stage-actions" v-if="!editMode">
                  <el-button 
                    v-if="canPromoteStage(stage, index)"
                    type="primary" 
                    size="small"
                    @click="promoteStage(stage)"
                  >
                    开始阶段
                  </el-button>
                  <el-button 
                    v-if="canCompleteStage(stage)"
                    type="success" 
                    size="small"
                    @click="completeStage(stage)"
                  >
                    完成阶段
                  </el-button>
                  <el-button 
                    v-if="stage.status === 'in-progress'"
                    type="info" 
                    size="small"
                    @click="addStageNote(stage)"
                  >
                    添加备注
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 甘特图视图 -->
      <div class="gantt-section" v-if="showGantt">
        <h3>甘特图视图</h3>
        <div ref="ganttChartRef" class="gantt-chart"></div>
      </div>

      <!-- 依赖关系 -->
      <div class="dependencies-section">
        <h3>依赖关系</h3>
        <div class="dependencies-list">
          <div 
            v-for="dep in dependencies" 
            :key="dep.id"
            class="dependency-item"
          >
            <div class="dependency-info">
              <span class="dependency-name">{{ dep.name }}</span>
              <el-tag :type="dep.status === 'resolved' ? 'success' : 'warning'" size="small">
                {{ dep.status === 'resolved' ? '已解决' : '待解决' }}
              </el-tag>
            </div>
            <p class="dependency-desc">{{ dep.description }}</p>
          </div>
        </div>
      </div>

      <!-- 发布窗口建议 -->
      <div class="window-suggestions">
        <h3>发布窗口建议</h3>
        <div class="suggestions-list">
          <div 
            v-for="suggestion in windowSuggestions" 
            :key="suggestion.id"
            class="suggestion-item"
            :class="{ 'recommended': suggestion.recommended }"
          >
            <div class="suggestion-header">
              <span class="suggestion-time">{{ suggestion.timeWindow }}</span>
              <el-tag 
                :type="suggestion.riskLevel === 'low' ? 'success' : 'warning'" 
                size="small"
              >
                {{ suggestion.riskLevel === 'low' ? '低风险' : '中风险' }}
              </el-tag>
            </div>
            <p class="suggestion-reason">{{ suggestion.reason }}</p>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 添加备注对话框 -->
    <el-dialog
      v-model="showNoteDialog"
      title="添加阶段备注"
      width="500px"
    >
      <el-form :model="noteForm" label-width="80px">
        <el-form-item label="备注内容">
          <el-input 
            v-model="noteForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入备注内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showNoteDialog = false">取消</el-button>
          <el-button type="primary" @click="saveStageNote">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Edit, Download, SuccessFilled, Loading, CircleCloseFilled 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  release: {
    type: Object,
    required: true
  }
})

// 响应式数据
const editMode = ref(false)
const showGantt = ref(false)
const showNoteDialog = ref(false)
const ganttChartRef = ref(null)
let ganttChart = null

// 当前操作的阶段
const currentStage = ref(null)

// 备注表单
const noteForm = reactive({
  content: ''
})

// 模拟依赖关系数据
const dependencies = ref([
  {
    id: 1,
    name: '数据库脚本准备',
    description: '数据库升级脚本必须在应用部署前完成',
    status: 'resolved'
  },
  {
    id: 2,
    name: '第三方API测试',
    description: '确保第三方接口兼容性测试通过',
    status: 'pending'
  }
])

// 发布窗口建议
const windowSuggestions = ref([
  {
    id: 1,
    timeWindow: '周二 02:00-04:00',
    riskLevel: 'low',
    reason: '标准维护窗口，用户访问量最低',
    recommended: true
  },
  {
    id: 2,
    timeWindow: '周六 10:00-12:00',
    riskLevel: 'medium',
    reason: '月度发布窗口，适合重大更新',
    recommended: false
  }
])

// 生命周期
onMounted(() => {
  if (showGantt.value) {
    nextTick(() => {
      initGanttChart()
    })
  }
})

// 方法
const getTypeLabel = (type) => {
  const typeMap = {
    major: '重大发布',
    minor: '次要发布',
    patch: '补丁发布',
    hotfix: '热修复'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    major: 'danger',
    minor: 'warning',
    patch: 'info',
    hotfix: 'danger'
  }
  return typeMap[type] || 'info'
}

const getRiskLabel = (risk) => {
  const riskMap = {
    low: '低风险',
    medium: '中风险',
    high: '高风险',
    critical: '极高风险'
  }
  return riskMap[risk] || risk
}

const getRiskTagType = (risk) => {
  const riskMap = {
    low: 'success',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return riskMap[risk] || 'info'
}

const getStageStatusLabel = (status) => {
  const statusMap = {
    pending: '待开始',
    'in-progress': '进行中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || status
}

const getStageStatusTagType = (status) => {
  const statusMap = {
    pending: 'info',
    'in-progress': 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const canPromoteStage = (stage, index) => {
  // 只有前一个阶段完成且当前阶段为待开始状态才能开始
  if (index === 0) return stage.status === 'pending'
  const prevStage = props.release.stages[index - 1]
  return prevStage.status === 'completed' && stage.status === 'pending'
}

const canCompleteStage = (stage) => {
  return stage.status === 'in-progress'
}

const promoteStage = (stage) => {
  ElMessage.success(`开始${stage.name}阶段`)
  // 这里应该调用API更新阶段状态
}

const completeStage = (stage) => {
  ElMessage.success(`完成${stage.name}阶段`)
  // 这里应该调用API更新阶段状态
}

const addStageNote = (stage) => {
  currentStage.value = stage
  noteForm.content = stage.notes || ''
  showNoteDialog.value = true
}

const saveStageNote = () => {
  if (currentStage.value) {
    currentStage.value.notes = noteForm.content
    ElMessage.success('备注保存成功')
  }
  showNoteDialog.value = false
}

const exportPlan = () => {
  ElMessage.info('导出发布计划功能开发中...')
}

const initGanttChart = () => {
  if (!ganttChartRef.value) return
  
  ganttChart = echarts.init(ganttChartRef.value)
  
  // 甘特图配置
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'time'
    },
    yAxis: {
      type: 'category',
      data: props.release.stages.map(stage => stage.name)
    },
    series: [{
      name: '发布阶段',
      type: 'bar',
      data: props.release.stages.map(stage => ({
        name: stage.name,
        value: [stage.startDate, stage.endDate],
        itemStyle: {
          color: stage.status === 'completed' ? '#4CAF50' : 
                 stage.status === 'in-progress' ? '#FF9800' : '#e0e0e0'
        }
      }))
    }]
  }
  
  ganttChart.setOption(option)
}
</script>

<style scoped>
.release-planning {
  padding: 20px;
}

.planning-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 发布信息 */
.release-info {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 600;
  color: #666;
  min-width: 80px;
}

/* 阶段部分 */
.stages-section {
  margin-bottom: 32px;
}

.stages-section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
}

.stages-timeline {
  position: relative;
}

.stage-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32px;
  position: relative;
}

.stage-connector {
  position: absolute;
  left: 24px;
  top: 48px;
  width: 2px;
  height: 60px;
  background-color: #e0e0e0;
  z-index: 1;
}

.stage-item.completed .stage-connector {
  background-color: #4CAF50;
}

.stage-item.active .stage-connector {
  background-color: #FF9800;
}

.stage-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e0e0;
  color: #666;
  font-size: 20px;
  font-weight: 600;
  margin-right: 20px;
  z-index: 2;
  position: relative;
}

.stage-item.completed .stage-icon {
  background-color: #4CAF50;
  color: white;
}

.stage-item.active .stage-icon {
  background-color: #FF9800;
  color: white;
  animation: pulse 2s infinite;
}

.stage-item.failed .stage-icon {
  background-color: #F44336;
  color: white;
}

.stage-number {
  font-size: 16px;
}

.stage-content {
  flex: 1;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stage-item.active .stage-content {
  border-color: #FF9800;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
}

.stage-item.completed .stage-content {
  border-color: #4CAF50;
  background-color: #f8fff8;
}

.stage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stage-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.stage-details {
  color: #666;
  font-size: 14px;
}

.stage-responsible,
.stage-criteria {
  margin: 8px 0;
}

.stage-timeline-info {
  margin: 12px 0;
}

.timeline-item {
  display: flex;
  margin: 4px 0;
}

.timeline-label {
  font-weight: 600;
  min-width: 80px;
  color: #666;
}

.timeline-value {
  color: #333;
}

.stage-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 甘特图 */
.gantt-section {
  margin-bottom: 32px;
}

.gantt-section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
}

.gantt-chart {
  height: 300px;
  width: 100%;
}

/* 依赖关系 */
.dependencies-section {
  margin-bottom: 32px;
}

.dependencies-section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
}

.dependencies-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dependency-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.dependency-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.dependency-name {
  font-weight: 600;
  color: #333;
}

.dependency-desc {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* 发布窗口建议 */
.window-suggestions h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  transition: all 0.2s;
}

.suggestion-item.recommended {
  border-color: #4CAF50;
  background-color: #f8fff8;
}

.suggestion-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-time {
  font-weight: 600;
  color: #333;
}

.suggestion-reason {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .release-planning {
    padding: 10px;
  }

  .release-info .el-row {
    flex-direction: column;
  }

  .release-info .el-col {
    margin-bottom: 12px;
  }

  .stage-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .stage-icon {
    margin-bottom: 12px;
    margin-right: 0;
  }

  .stage-connector {
    display: none;
  }

  .stage-content {
    width: 100%;
  }

  .stage-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .stage-actions {
    flex-wrap: wrap;
  }
}
</style>
