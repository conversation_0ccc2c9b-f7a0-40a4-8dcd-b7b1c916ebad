<template>
  <div class="release-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>发布管理</h2>
          <p>管理软件发布和部署流程，确保发布安全可控</p>
        </div>
        <div class="action-section">
          <el-tooltip content="快捷键: Ctrl+N" placement="bottom">
            <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
              新建发布
            </el-button>
          </el-tooltip>
          <el-button :icon="Calendar" @click="$router.push('/release/dashboard')">
            发布看板
          </el-button>
          <el-tooltip content="快捷键: F5" placement="bottom">
            <el-button :icon="Refresh" @click="refreshData" :loading="loading">
              刷新
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Upload /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">总发布数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.successRate }}%</div>
                <div class="stat-label">成功率</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon processing">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.byStatus['prod'] || 0 }}</div>
                <div class="stat-label">进行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon duration">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.avgDuration }}</div>
                <div class="stat-label">平均周期(天)</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作和状态分布 -->
    <el-row :gutter="20" class="content-row">
      <!-- 快速操作 -->
      <el-col :span="8">
        <el-card class="quick-actions-card">
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
              <el-icon><Operation /></el-icon>
            </div>
          </template>
          <div class="quick-actions">
            <div class="action-item" @click="showCreateDialog = true">
              <el-icon class="action-icon"><Plus /></el-icon>
              <div class="action-content">
                <div class="action-title">创建发布</div>
                <div class="action-desc">新建发布计划</div>
              </div>
            </div>
            <div class="action-item" @click="$router.push('/release/dashboard')">
              <el-icon class="action-icon"><DataBoard /></el-icon>
              <div class="action-content">
                <div class="action-title">发布看板</div>
                <div class="action-desc">查看发布进度</div>
              </div>
            </div>
            <div class="action-item" @click="showTemplateDialog = true">
              <el-icon class="action-icon"><Document /></el-icon>
              <div class="action-content">
                <div class="action-title">发布模板</div>
                <div class="action-desc">管理发布模板</div>
              </div>
            </div>
            <div class="action-item" @click="$router.push('/release/calendar')">
              <el-icon class="action-icon"><Calendar /></el-icon>
              <div class="action-content">
                <div class="action-title">发布日历</div>
                <div class="action-desc">查看发布排期</div>
              </div>
            </div>
            <div class="action-item" @click="$router.push('/release/reports')">
              <el-icon class="action-icon"><TrendCharts /></el-icon>
              <div class="action-content">
                <div class="action-title">发布报表</div>
                <div class="action-desc">数据分析统计</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 状态分布图 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>状态分布</span>
              <el-icon><PieChart /></el-icon>
            </div>
          </template>
          <div ref="statusChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 类型分布图 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>类型分布</span>
              <el-icon><TrendCharts /></el-icon>
            </div>
          </template>
          <div ref="typeChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近发布 -->
    <el-card class="recent-releases-card">
      <template #header>
        <div class="card-header">
          <span>最近发布</span>
          <el-button type="text" @click="$router.push('/release/dashboard')">
            查看全部 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </template>
      <el-table :data="statistics.recentReleases" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="发布ID" width="140" />
        <el-table-column prop="name" label="发布名称" min-width="200" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)" size="small">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="currentStage" label="当前阶段" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ getStageLabel(row.currentStage) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="owner" label="负责人" width="100" />
        <el-table-column prop="updatedAt" label="更新时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewRelease(row)">
              查看
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="promoteStage(row)"
              v-if="canPromote(row.status)"
            >
              推进
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="rollbackRelease(row)"
              v-if="canRollback(row.status)"
              style="color: #f56c6c"
            >
              回滚
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 创建发布对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建发布"
      width="600px"
      :before-close="handleCreateDialogClose"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="发布名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入发布名称" />
        </el-form-item>
        <el-form-item label="发布描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入发布描述"
          />
        </el-form-item>
        <el-form-item label="发布类型" prop="type">
          <el-select v-model="createForm.type" placeholder="请选择发布类型" style="width: 100%">
            <el-option
              v-for="type in releaseTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联变更" prop="relatedChange">
          <el-input v-model="createForm.relatedChange" placeholder="请输入关联的变更请求ID" />
        </el-form-item>
        <el-form-item label="负责人" prop="owner">
          <el-select v-model="createForm.owner" placeholder="请选择负责人" style="width: 100%">
            <el-option
              v-for="user in mockUsers"
              :key="user.id"
              :label="user.name"
              :value="user.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计划时间" prop="plannedDate">
          <el-date-picker
            v-model="createForm.plannedDate"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreateRelease" :loading="createLoading">
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模板管理对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      title="发布模板"
      width="800px"
    >
      <div class="template-list">
        <div
          v-for="template in releaseTemplates"
          :key="template.id"
          class="template-item"
          @click="selectTemplate(template)"
        >
          <div class="template-header">
            <h4>{{ template.name }}</h4>
            <el-button type="text" size="small">使用模板</el-button>
          </div>
          <p class="template-desc">{{ template.description }}</p>
          <div class="template-stages">
            <el-tag
              v-for="stage in template.stages"
              :key="stage.name"
              size="small"
              class="stage-tag"
            >
              {{ stage.name }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Calendar, Refresh, Upload, SuccessFilled, Loading, Timer,
  Operation, DataBoard, Document, PieChart, TrendCharts, ArrowRight
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getReleaseStatistics,
  createRelease,
  promoteReleaseStage,
  rollbackRelease as rollbackReleaseApi,
  releaseTypes
} from '@/api/releaseApi.js'
import { mockUsers } from '@/api/index.js'
import { releaseTemplates } from '@/mock/release.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showCreateDialog = ref(false)
const showTemplateDialog = ref(false)

// 统计数据
const statistics = reactive({
  total: 0,
  successRate: 0,
  avgDuration: 0,
  byStatus: {},
  byType: {},
  byRisk: {},
  recentReleases: []
})

// 图表引用
const statusChartRef = ref(null)
const typeChartRef = ref(null)
let statusChart = null
let typeChart = null

// 创建表单
const createForm = reactive({
  name: '',
  description: '',
  type: '',
  relatedChange: '',
  owner: '',
  plannedDate: []
})

const createFormRef = ref(null)

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入发布名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入发布描述', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择发布类型', trigger: 'change' }
  ],
  relatedChange: [
    { required: true, message: '请输入关联的变更请求ID', trigger: 'blur' }
  ],
  owner: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ],
  plannedDate: [
    { required: true, message: '请选择计划时间', trigger: 'change' }
  ]
}

// 生命周期
onMounted(() => {
  loadStatistics()

  // 添加键盘快捷键支持
  const handleKeydown = (event) => {
    // Ctrl/Cmd + N: 新建发布
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
      event.preventDefault()
      showCreateDialog.value = true
    }
    // F5: 刷新数据
    if (event.key === 'F5') {
      event.preventDefault()
      refreshData()
    }
    // Escape: 关闭对话框
    if (event.key === 'Escape') {
      showCreateDialog.value = false
      showTemplateDialog.value = false
    }
  }

  document.addEventListener('keydown', handleKeydown)

  // 清理事件监听器
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})

// 方法
const loadStatistics = async () => {
  loading.value = true
  try {
    const response = await getReleaseStatistics()
    Object.assign(statistics, response.data)

    // 等待DOM更新后初始化图表
    await nextTick()
    initCharts()
  } catch (error) {
    ElMessage.error('加载统计数据失败')
    console.error('Load statistics error:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadStatistics()
}

// 初始化图表
const initCharts = () => {
  initStatusChart()
  initTypeChart()
}

const initStatusChart = () => {
  if (!statusChartRef.value) return

  statusChart = echarts.init(statusChartRef.value)

  const statusLabels = {
    planning: '计划中',
    dev: '开发',
    test: '测试',
    uat: '预生产',
    prod: '生产',
    verified: '已验证',
    failed: '失败',
    rollback: '已回滚',
    closed: '已关闭'
  }

  const statusColors = {
    planning: '#909399',
    dev: '#1976D2',
    test: '#FF9800',
    uat: '#9C27B0',
    prod: '#4CAF50',
    verified: '#4CAF50',
    failed: '#F44336',
    rollback: '#F44336',
    closed: '#909399'
  }

  const data = Object.entries(statistics.byStatus).map(([key, value]) => ({
    name: statusLabels[key] || key,
    value,
    itemStyle: { color: statusColors[key] || '#909399' }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '发布状态',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  statusChart.setOption(option)
}

const initTypeChart = () => {
  if (!typeChartRef.value) return

  typeChart = echarts.init(typeChartRef.value)

  const typeLabels = {
    major: '重大发布',
    minor: '次要发布',
    patch: '补丁发布',
    hotfix: '热修复'
  }

  const typeColors = ['#1976D2', '#4CAF50', '#FF9800', '#F44336']

  const data = Object.entries(statistics.byType).map(([key, value], index) => ({
    name: typeLabels[key] || key,
    value,
    itemStyle: { color: typeColors[index % typeColors.length] }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '发布类型',
      type: 'pie',
      radius: '60%',
      center: ['50%', '50%'],
      data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  typeChart.setOption(option)
}

// 创建发布
const handleCreateRelease = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    createLoading.value = true

    const releaseData = {
      ...createForm,
      plannedStartDate: createForm.plannedDate[0],
      plannedEndDate: createForm.plannedDate[1]
    }

    const response = await createRelease(releaseData)
    ElMessage.success('发布创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    loadStatistics()
  } catch (error) {
    if (error.fields) {
      // 表单验证错误
      return
    }
    ElMessage.error('创建发布失败')
    console.error('Create release error:', error)
  } finally {
    createLoading.value = false
  }
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    description: '',
    type: '',
    relatedChange: '',
    owner: '',
    plannedDate: []
  })
  if (createFormRef.value) {
    createFormRef.value.clearValidate()
  }
}

const handleCreateDialogClose = (done) => {
  resetCreateForm()
  done()
}

// 选择模板
const selectTemplate = (template) => {
  createForm.name = template.name
  createForm.description = template.description
  showTemplateDialog.value = false
  showCreateDialog.value = true
}

// 查看发布详情
const viewRelease = (release) => {
  // 跳转到发布详情页面
  router.push(`/release/detail/${release.id}`)
}

// 推进阶段
const promoteStage = async (release) => {
  const stageMap = {
    'planning': 'dev',
    'dev': 'test',
    'test': 'uat',
    'uat': 'prod'
  }

  const nextStage = stageMap[release.status]
  const stageNames = {
    'dev': '开发阶段',
    'test': '测试阶段',
    'uat': '预生产阶段',
    'prod': '生产阶段'
  }

  if (!nextStage) {
    ElMessage.warning('当前阶段无法推进')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将发布 "${release.name}" 推进到${stageNames[nextStage]}吗？`,
      '推进确认',
      {
        confirmButtonText: '确定推进',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '推进中...'
            setTimeout(() => {
              done()
            }, 1000)
          } else {
            done()
          }
        }
      }
    )

    const response = await promoteReleaseStage(release.id, nextStage)
    ElMessage.success(response.msg || `已推进到${stageNames[nextStage]}`)
    loadStatistics()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('推进阶段失败：' + (error.msg || error.message || '未知错误'))
    console.error('Promote stage error:', error)
  }
}

// 回滚发布
const rollbackRelease = async (release) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      `确定要回滚发布 "${release.name}" 吗？请输入回滚原因：`,
      '回滚确认',
      {
        confirmButtonText: '确定回滚',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入回滚原因'
      }
    )

    const response = await rollbackReleaseApi(release.id, reason)
    ElMessage.success(response.msg)
    loadStatistics()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('回滚失败')
    console.error('Rollback error:', error)
  }
}

// 工具方法
const getTypeLabel = (type) => {
  const typeMap = {
    major: '重大发布',
    minor: '次要发布',
    patch: '补丁发布',
    hotfix: '热修复'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    major: 'danger',
    minor: 'warning',
    patch: 'info',
    hotfix: 'danger'
  }
  return typeMap[type] || 'info'
}

const getStatusLabel = (status) => {
  const statusMap = {
    planning: '计划中',
    dev: '开发',
    test: '测试',
    uat: '预生产',
    prod: '生产',
    verified: '已验证',
    failed: '失败',
    rollback: '已回滚',
    closed: '已关闭'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const statusMap = {
    planning: 'info',
    dev: 'primary',
    test: 'warning',
    uat: 'warning',
    prod: 'success',
    verified: 'success',
    failed: 'danger',
    rollback: 'danger',
    closed: 'info'
  }
  return statusMap[status] || 'info'
}

const getStageLabel = (stage) => {
  const stageMap = {
    dev: '开发',
    test: '测试',
    uat: '预生产',
    prod: '生产'
  }
  return stageMap[stage] || stage
}

const canPromote = (status) => {
  return ['planning', 'dev', 'test', 'uat'].includes(status)
}

const canRollback = (status) => {
  return ['prod', 'verified'].includes(status)
}
</script>

<style scoped>
.release-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
}

.stat-icon.success {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.stat-icon.duration {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 内容行 */
.content-row {
  margin-bottom: 20px;
}

/* 快速操作卡片 */
.quick-actions-card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-item:hover {
  border-color: #1976D2;
  background-color: #f8fbff;
  transform: translateX(4px);
}

.action-icon {
  font-size: 20px;
  color: #1976D2;
  margin-right: 12px;
}

.action-content {
  flex: 1;
}

.action-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #666;
}

/* 图表卡片 */
.chart-card {
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-container {
  height: 200px;
  width: 100%;
}

/* 最近发布卡片 */
.recent-releases-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 模板列表 */
.template-list {
  max-height: 400px;
  overflow-y: auto;
}

.template-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.template-item:hover {
  border-color: #1976D2;
  background-color: #f8fbff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.template-desc {
  color: #666;
  font-size: 14px;
  margin: 0 0 12px 0;
}

.template-stages {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stage-tag {
  margin: 0;
}

/* 日历占位符 */
.calendar-placeholder {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .release-management {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .content-row .el-col {
    margin-bottom: 20px;
  }
}
</style>
