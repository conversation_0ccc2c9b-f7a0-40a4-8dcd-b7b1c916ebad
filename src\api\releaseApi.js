// 发布管理 API
import { 
  createApiResponse, 
  createApiError, 
  paginate, 
  generateId, 
  formatDateTime, 
  getRandomUser,
  getRandomStatus,
  mockUsers
} from './index.js'

/**
 * 发布阶段配置
 */
export const releaseStages = [
  { 
    key: 'dev', 
    name: '开发', 
    description: '完成功能开发与单元测试',
    responsible: '开发团队',
    exitCriteria: '代码合并至主干',
    color: '#1976D2'
  },
  { 
    key: 'test', 
    name: '测试', 
    description: '功能测试、集成测试',
    responsible: '测试团队',
    exitCriteria: '测试用例100%通过',
    color: '#FF9800'
  },
  { 
    key: 'uat', 
    name: '预生产', 
    description: '用户验收测试',
    responsible: '业务代表',
    exitCriteria: '签字确认',
    color: '#9C27B0'
  },
  { 
    key: 'prod', 
    name: '生产', 
    description: '正式上线',
    responsible: '运维团队',
    exitCriteria: '健康检查通过',
    color: '#4CAF50'
  }
]

/**
 * 发布类型配置
 */
export const releaseTypes = [
  { value: 'major', label: '重大发布', description: '包含重要功能更新或架构变更', color: 'danger' },
  { value: 'minor', label: '次要发布', description: '功能增强和改进', color: 'warning' },
  { value: 'patch', label: '补丁发布', description: '错误修复和小幅调整', color: 'info' },
  { value: 'hotfix', label: '热修复', description: '紧急修复关键问题', color: 'danger' }
]

/**
 * 发布风险等级
 */
export const riskLevels = [
  { value: 'low', label: '低风险', color: 'success', score: 1 },
  { value: 'medium', label: '中风险', color: 'warning', score: 2 },
  { value: 'high', label: '高风险', color: 'danger', score: 3 },
  { value: 'critical', label: '极高风险', color: 'danger', score: 4 }
]

/**
 * 生成模拟发布数据
 */
function generateMockRelease(id) {
  const releaseId = `REL-2025-${String(id).padStart(3, '0')}`
  const changeId = `CHG-2025-${String(id + 100).padStart(4, '0')}`
  const type = releaseTypes[Math.floor(Math.random() * releaseTypes.length)]
  const risk = riskLevels[Math.floor(Math.random() * riskLevels.length)]
  const owner = getRandomUser()
  const status = getRandomStatus('release')
  
  // 根据状态确定当前阶段
  let currentStage = 'dev'
  if (status === 'test') currentStage = 'test'
  else if (status === 'uat') currentStage = 'uat'
  else if (status === 'prod' || status === 'verified') currentStage = 'prod'
  
  const applications = ['ERP系统', 'CRM系统', 'OA系统', 'BI平台', 'API网关']
  const app = applications[Math.floor(Math.random() * applications.length)]
  
  const baseDate = new Date()
  baseDate.setDate(baseDate.getDate() - Math.floor(Math.random() * 30))
  
  return {
    id: releaseId,
    name: `${app} v${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 100)}`,
    description: `${app}功能升级和性能优化`,
    type: type.value,
    typeLabel: type.label,
    status,
    currentStage,
    riskLevel: risk.value,
    riskScore: risk.score,
    owner: owner.name,
    ownerId: owner.id,
    department: owner.department,
    relatedChange: changeId,
    application: app,
    version: `v${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 100)}`,
    plannedStartDate: formatDateTime(baseDate),
    plannedEndDate: formatDateTime(new Date(baseDate.getTime() + 7 * 24 * 60 * 60 * 1000)),
    actualStartDate: status !== 'planning' ? formatDateTime(baseDate) : null,
    actualEndDate: ['verified', 'closed', 'rollback'].includes(status) ? 
      formatDateTime(new Date(baseDate.getTime() + Math.floor(Math.random() * 5 + 2) * 24 * 60 * 60 * 1000)) : null,
    createdAt: formatDateTime(new Date(baseDate.getTime() - 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date()),
    
    // 阶段详情
    stages: releaseStages.map(stage => ({
      key: stage.key,
      name: stage.name,
      status: stage.key === currentStage ? 'in-progress' : 
              releaseStages.findIndex(s => s.key === stage.key) < releaseStages.findIndex(s => s.key === currentStage) ? 'completed' : 'pending',
      responsible: stage.responsible,
      startDate: stage.key === currentStage || releaseStages.findIndex(s => s.key === stage.key) < releaseStages.findIndex(s => s.key === currentStage) ? 
        formatDateTime(new Date(baseDate.getTime() + releaseStages.findIndex(s => s.key === stage.key) * 24 * 60 * 60 * 1000)) : null,
      endDate: releaseStages.findIndex(s => s.key === stage.key) < releaseStages.findIndex(s => s.key === currentStage) ? 
        formatDateTime(new Date(baseDate.getTime() + (releaseStages.findIndex(s => s.key === stage.key) + 1) * 24 * 60 * 60 * 1000)) : null,
      exitCriteria: stage.exitCriteria,
      notes: stage.key === currentStage ? '正在进行中...' : ''
    })),
    
    // 回滚信息
    rollbackPlan: {
      hasScript: Math.random() > 0.3,
      scriptPath: Math.random() > 0.3 ? `/scripts/rollback_${releaseId.toLowerCase()}.sh` : null,
      checklist: [
        '停止新版本服务',
        '恢复旧版本镜像',
        '回退数据库版本',
        '配置文件还原',
        '通知相关方'
      ],
      estimatedTime: Math.floor(Math.random() * 60 + 30) + '分钟'
    },
    
    // 验证检查
    healthChecks: [
      { name: '服务可达性', status: Math.random() > 0.2 ? 'passed' : 'failed', lastCheck: formatDateTime(new Date()) },
      { name: 'API连通性', status: Math.random() > 0.2 ? 'passed' : 'failed', lastCheck: formatDateTime(new Date()) },
      { name: '性能指标', status: Math.random() > 0.2 ? 'passed' : 'failed', lastCheck: formatDateTime(new Date()) },
      { name: '日志检查', status: Math.random() > 0.2 ? 'passed' : 'failed', lastCheck: formatDateTime(new Date()) }
    ]
  }
}

// 生成模拟数据
const mockReleases = Array.from({ length: 50 }, (_, index) => generateMockRelease(index + 1))

/**
 * 获取发布列表
 */
export function getReleaseList(params = {}) {
  const { page = 1, pageSize = 20, status, type, owner, keyword } = params
  
  let filteredData = [...mockReleases]
  
  // 状态筛选
  if (status) {
    filteredData = filteredData.filter(item => item.status === status)
  }
  
  // 类型筛选
  if (type) {
    filteredData = filteredData.filter(item => item.type === type)
  }
  
  // 负责人筛选
  if (owner) {
    filteredData = filteredData.filter(item => item.owner.includes(owner))
  }
  
  // 关键词搜索
  if (keyword) {
    filteredData = filteredData.filter(item => 
      item.name.includes(keyword) || 
      item.description.includes(keyword) ||
      item.application.includes(keyword)
    )
  }
  
  // 按创建时间倒序排列
  filteredData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  
  const result = paginate(filteredData, page, pageSize)
  return createApiResponse(result)
}

/**
 * 获取发布详情
 */
export function getReleaseDetail(id) {
  const release = mockReleases.find(item => item.id === id)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }
  return createApiResponse(release)
}

/**
 * 创建发布
 */
export function createRelease(data) {
  const newRelease = {
    ...generateMockRelease(mockReleases.length + 1),
    ...data,
    id: generateId('REL'),
    status: 'planning',
    currentStage: 'dev',
    createdAt: formatDateTime(new Date()),
    updatedAt: formatDateTime(new Date())
  }
  
  mockReleases.unshift(newRelease)
  return createApiResponse(newRelease, '发布创建成功')
}

/**
 * 更新发布
 */
export function updateRelease(id, data) {
  const index = mockReleases.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('发布记录不存在', 404)
  }
  
  mockReleases[index] = {
    ...mockReleases[index],
    ...data,
    updatedAt: formatDateTime(new Date())
  }
  
  return createApiResponse(mockReleases[index], '发布更新成功')
}

/**
 * 删除发布
 */
export function deleteRelease(id) {
  const index = mockReleases.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('发布记录不存在', 404)
  }

  mockReleases.splice(index, 1)
  return createApiResponse(null, '发布删除成功')
}

/**
 * 推进发布阶段
 */
export function promoteReleaseStage(id, targetStage) {
  const index = mockReleases.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('发布记录不存在', 404)
  }

  const release = mockReleases[index]
  const stageIndex = releaseStages.findIndex(stage => stage.key === targetStage)

  if (stageIndex === -1) {
    return createApiError('无效的发布阶段', 400)
  }

  // 更新发布状态和当前阶段
  release.currentStage = targetStage
  release.status = targetStage
  release.updatedAt = formatDateTime(new Date())

  // 更新阶段状态
  release.stages.forEach((stage, index) => {
    if (index < stageIndex) {
      stage.status = 'completed'
      if (!stage.endDate) {
        stage.endDate = formatDateTime(new Date())
      }
    } else if (index === stageIndex) {
      stage.status = 'in-progress'
      if (!stage.startDate) {
        stage.startDate = formatDateTime(new Date())
      }
    } else {
      stage.status = 'pending'
    }
  })

  return createApiResponse(release, `发布已推进至${releaseStages[stageIndex].name}阶段`)
}

/**
 * 回滚发布
 */
export function rollbackRelease(id, reason) {
  const index = mockReleases.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('发布记录不存在', 404)
  }

  const release = mockReleases[index]
  release.status = 'rollback'
  release.updatedAt = formatDateTime(new Date())
  release.rollbackReason = reason
  release.rollbackTime = formatDateTime(new Date())

  return createApiResponse(release, '发布回滚成功')
}

/**
 * 执行健康检查
 */
export function performHealthCheck(id) {
  const index = mockReleases.findIndex(item => item.id === id)
  if (index === -1) {
    return createApiError('发布记录不存在', 404)
  }

  const release = mockReleases[index]

  // 模拟健康检查结果
  release.healthChecks.forEach(check => {
    check.status = Math.random() > 0.1 ? 'passed' : 'failed'
    check.lastCheck = formatDateTime(new Date())
  })

  const allPassed = release.healthChecks.every(check => check.status === 'passed')

  if (allPassed && release.status === 'prod') {
    release.status = 'verified'
  }

  release.updatedAt = formatDateTime(new Date())

  return createApiResponse({
    release,
    healthCheckResult: {
      passed: allPassed,
      checks: release.healthChecks,
      timestamp: formatDateTime(new Date())
    }
  }, allPassed ? '健康检查通过' : '健康检查发现问题')
}

/**
 * 获取发布统计数据
 */
export function getReleaseStatistics() {
  const stats = {
    total: mockReleases.length,
    byStatus: {},
    byType: {},
    byRisk: {},
    successRate: 0,
    avgDuration: 0,
    recentReleases: mockReleases.slice(0, 5)
  }

  // 按状态统计
  mockReleases.forEach(release => {
    stats.byStatus[release.status] = (stats.byStatus[release.status] || 0) + 1
    stats.byType[release.type] = (stats.byType[release.type] || 0) + 1
    stats.byRisk[release.riskLevel] = (stats.byRisk[release.riskLevel] || 0) + 1
  })

  // 计算成功率
  const successCount = (stats.byStatus.verified || 0) + (stats.byStatus.closed || 0)
  const completedCount = successCount + (stats.byStatus.failed || 0) + (stats.byStatus.rollback || 0)
  stats.successRate = completedCount > 0 ? Math.round((successCount / completedCount) * 100) : 0

  // 计算平均持续时间（天）
  const completedReleases = mockReleases.filter(r => r.actualEndDate && r.actualStartDate)
  if (completedReleases.length > 0) {
    const totalDuration = completedReleases.reduce((sum, release) => {
      const start = new Date(release.actualStartDate)
      const end = new Date(release.actualEndDate)
      return sum + (end - start) / (1000 * 60 * 60 * 24)
    }, 0)
    stats.avgDuration = Math.round(totalDuration / completedReleases.length * 10) / 10
  }

  return createApiResponse(stats)
}

/**
 * 获取发布日历数据
 */
export function getReleaseCalendar(year, month) {
  const calendarData = mockReleases
    .filter(release => {
      const startDate = new Date(release.plannedStartDate)
      return startDate.getFullYear() === year && startDate.getMonth() === month - 1
    })
    .map(release => ({
      id: release.id,
      title: release.name,
      start: release.plannedStartDate,
      end: release.plannedEndDate,
      type: release.type,
      status: release.status,
      owner: release.owner,
      riskLevel: release.riskLevel
    }))

  return createApiResponse(calendarData)
}

/**
 * 检查发布冲突
 */
export function checkReleaseConflicts(startDate, endDate, excludeId = null) {
  const conflicts = mockReleases
    .filter(release => {
      if (excludeId && release.id === excludeId) return false

      const releaseStart = new Date(release.plannedStartDate)
      const releaseEnd = new Date(release.plannedEndDate)
      const checkStart = new Date(startDate)
      const checkEnd = new Date(endDate)

      // 检查时间重叠
      return (checkStart <= releaseEnd && checkEnd >= releaseStart) &&
             ['planning', 'dev', 'test', 'uat', 'prod'].includes(release.status)
    })
    .map(release => ({
      id: release.id,
      name: release.name,
      startDate: release.plannedStartDate,
      endDate: release.plannedEndDate,
      type: release.type,
      riskLevel: release.riskLevel,
      conflictType: release.riskLevel === 'high' || release.riskLevel === 'critical' ? 'high-risk' : 'time-overlap'
    }))

  return createApiResponse({
    hasConflicts: conflicts.length > 0,
    conflicts,
    recommendations: conflicts.length > 0 ? [
      '建议错峰安排发布时间',
      '考虑将低风险发布延后',
      '确保有足够的回滚时间窗口'
    ] : []
  })
}

/**
 * 验证发布与变更的关联关系
 */
export function validateReleaseChangeMapping(releaseId, changeId) {
  // 模拟验证逻辑
  const isValid = Math.random() > 0.2 // 80%的概率验证通过

  if (!isValid) {
    return createApiError('发布与变更关联验证失败：变更状态不符合发布要求', 400)
  }

  return createApiResponse({
    valid: true,
    changeStatus: 'approved',
    approver: '张三',
    approvalDate: formatDateTime(new Date()),
    riskAssessment: {
      level: 'medium',
      factors: [
        '涉及核心业务模块',
        '需要数据库结构变更',
        '影响多个系统接口'
      ],
      mitigations: [
        '已完成充分测试',
        '准备回滚方案',
        '安排专人监控'
      ]
    }
  }, '变更关联验证通过')
}

/**
 * 获取发布相关的变更列表
 */
export function getReleaseChanges(releaseId) {
  // 模拟变更数据
  const changes = [
    {
      id: 'CHG-2025-0001',
      title: '用户管理模块升级',
      description: '升级用户管理模块，增加新的权限控制功能',
      status: 'approved',
      priority: 'high',
      requester: '李四',
      approver: '王五',
      requestDate: formatDateTime(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
      approvalDate: formatDateTime(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)),
      implementationDate: formatDateTime(new Date()),
      category: 'enhancement',
      impact: 'medium',
      urgency: 'medium',
      riskLevel: 'medium'
    },
    {
      id: 'CHG-2025-0002',
      title: '数据库性能优化',
      description: '优化数据库查询性能，添加必要的索引',
      status: 'approved',
      priority: 'medium',
      requester: '赵六',
      approver: '王五',
      requestDate: formatDateTime(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
      approvalDate: formatDateTime(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
      implementationDate: formatDateTime(new Date()),
      category: 'optimization',
      impact: 'low',
      urgency: 'low',
      riskLevel: 'low'
    }
  ]

  return createApiResponse(changes)
}

/**
 * 关联变更到发布
 */
export function linkChangeToRelease(releaseId, changeId) {
  const release = mockReleases.find(r => r.id === releaseId)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }

  // 验证变更关联
  const validation = validateReleaseChangeMapping(releaseId, changeId)
  if (!validation.success) {
    return validation
  }

  // 添加关联
  if (!release.relatedChanges) {
    release.relatedChanges = []
  }

  if (!release.relatedChanges.includes(changeId)) {
    release.relatedChanges.push(changeId)
  }

  release.updatedAt = formatDateTime(new Date())

  return createApiResponse(release, '变更关联成功')
}

/**
 * 取消变更关联
 */
export function unlinkChangeFromRelease(releaseId, changeId) {
  const release = mockReleases.find(r => r.id === releaseId)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }

  if (release.relatedChanges) {
    release.relatedChanges = release.relatedChanges.filter(id => id !== changeId)
  }

  release.updatedAt = formatDateTime(new Date())

  return createApiResponse(release, '取消变更关联成功')
}

/**
 * 检查发布前置条件
 */
export function checkReleasePrerequisites(releaseId) {
  const release = mockReleases.find(r => r.id === releaseId)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }

  const checks = [
    {
      name: '变更请求已批准',
      status: release.relatedChange ? 'passed' : 'failed',
      required: true,
      description: '所有相关变更请求必须获得批准'
    },
    {
      name: '代码审查完成',
      status: Math.random() > 0.2 ? 'passed' : 'failed',
      required: true,
      description: '代码必须通过同行评审'
    },
    {
      name: '测试用例通过',
      status: Math.random() > 0.1 ? 'passed' : 'failed',
      required: true,
      description: '所有测试用例必须通过'
    },
    {
      name: '安全扫描通过',
      status: Math.random() > 0.15 ? 'passed' : 'failed',
      required: true,
      description: '安全漏洞扫描必须通过'
    },
    {
      name: '性能测试通过',
      status: Math.random() > 0.3 ? 'passed' : 'warning',
      required: false,
      description: '性能测试建议通过'
    },
    {
      name: '文档已更新',
      status: Math.random() > 0.4 ? 'passed' : 'warning',
      required: false,
      description: '相关文档建议更新'
    }
  ]

  const requiredChecks = checks.filter(check => check.required)
  const passedRequired = requiredChecks.filter(check => check.status === 'passed').length
  const allRequired = requiredChecks.length

  const canProceed = passedRequired === allRequired

  return createApiResponse({
    canProceed,
    checks,
    summary: {
      total: checks.length,
      passed: checks.filter(check => check.status === 'passed').length,
      failed: checks.filter(check => check.status === 'failed').length,
      warning: checks.filter(check => check.status === 'warning').length,
      requiredPassed: passedRequired,
      requiredTotal: allRequired
    }
  })
}

/**
 * 强制关联验证
 */
export function enforceChangeAssociation(releaseId) {
  const release = mockReleases.find(r => r.id === releaseId)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }

  // 检查是否有关联的变更请求
  if (!release.relatedChange && (!release.relatedChanges || release.relatedChanges.length === 0)) {
    return createApiError('发布必须关联至少一个已批准的变更请求', 400)
  }

  // 验证关联的变更状态
  const invalidChanges = []
  if (release.relatedChanges) {
    release.relatedChanges.forEach(changeId => {
      // 模拟变更状态检查
      if (Math.random() > 0.9) { // 10%概率变更状态无效
        invalidChanges.push({
          id: changeId,
          reason: '变更请求未获得批准或已过期'
        })
      }
    })
  }

  if (invalidChanges.length > 0) {
    return createApiError('存在无效的变更关联', 400, { invalidChanges })
  }

  return createApiResponse({
    valid: true,
    associatedChanges: release.relatedChanges || [release.relatedChange],
    message: '变更关联验证通过'
  })
}

/**
 * 获取回滚计划详情
 */
export function getRollbackPlan(releaseId) {
  const release = mockReleases.find(r => r.id === releaseId)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }

  const rollbackPlan = {
    ...release.rollbackPlan,
    steps: [
      {
        id: 1,
        name: '停止新版本服务',
        description: '停止当前运行的新版本应用服务',
        estimatedTime: '2分钟',
        status: 'pending',
        automated: true,
        script: '/scripts/stop_service.sh'
      },
      {
        id: 2,
        name: '切换负载均衡',
        description: '将流量切换到旧版本服务',
        estimatedTime: '1分钟',
        status: 'pending',
        automated: true,
        script: '/scripts/switch_lb.sh'
      },
      {
        id: 3,
        name: '回退数据库',
        description: '执行数据库回滚脚本（如需要）',
        estimatedTime: '5分钟',
        status: 'pending',
        automated: false,
        script: '/scripts/rollback_db.sh',
        requiresConfirmation: true
      },
      {
        id: 4,
        name: '恢复配置文件',
        description: '恢复应用配置到旧版本',
        estimatedTime: '1分钟',
        status: 'pending',
        automated: true,
        script: '/scripts/restore_config.sh'
      },
      {
        id: 5,
        name: '验证服务状态',
        description: '验证回滚后服务是否正常运行',
        estimatedTime: '3分钟',
        status: 'pending',
        automated: false,
        manual: true
      },
      {
        id: 6,
        name: '通知相关方',
        description: '通知相关团队回滚完成',
        estimatedTime: '1分钟',
        status: 'pending',
        automated: true,
        script: '/scripts/notify.sh'
      }
    ],
    riskAssessment: {
      dataLoss: release.type === 'major' ? 'medium' : 'low',
      downtime: '预计5-10分钟',
      complexity: release.rollbackPlan?.hasScript ? 'low' : 'high',
      dependencies: [
        '数据库备份可用',
        '旧版本镜像存在',
        '配置文件已备份'
      ]
    },
    approvals: [
      {
        role: '发布负责人',
        required: true,
        approved: false,
        approver: null,
        approvalTime: null
      },
      {
        role: '技术经理',
        required: release.riskLevel === 'high' || release.riskLevel === 'critical',
        approved: false,
        approver: null,
        approvalTime: null
      }
    ]
  }

  return createApiResponse(rollbackPlan)
}

/**
 * 执行回滚步骤
 */
export function executeRollbackStep(releaseId, stepId) {
  const release = mockReleases.find(r => r.id === releaseId)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }

  // 模拟步骤执行
  const success = Math.random() > 0.1 // 90%成功率

  if (!success) {
    return createApiError('回滚步骤执行失败', 500)
  }

  return createApiResponse({
    stepId,
    status: 'completed',
    executionTime: formatDateTime(new Date()),
    output: '步骤执行成功',
    nextStep: stepId + 1
  }, '回滚步骤执行成功')
}

/**
 * 一键回滚
 */
export function executeOneClickRollback(releaseId, reason, approvals) {
  const release = mockReleases.find(r => r.id === releaseId)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }

  // 检查必要的审批
  const requiredApprovals = approvals.filter(a => a.required && !a.approved)
  if (requiredApprovals.length > 0) {
    return createApiError('缺少必要的回滚审批', 400, { requiredApprovals })
  }

  // 执行回滚
  release.status = 'rollback'
  release.rollbackReason = reason
  release.rollbackTime = formatDateTime(new Date())
  release.rollbackApprovals = approvals
  release.updatedAt = formatDateTime(new Date())

  return createApiResponse({
    rollbackId: generateId('RB'),
    status: 'in-progress',
    estimatedCompletion: formatDateTime(new Date(Date.now() + 10 * 60 * 1000)), // 10分钟后
    steps: 6,
    completedSteps: 0
  }, '回滚已启动')
}

/**
 * 验证回滚结果
 */
export function validateRollbackResult(releaseId) {
  const release = mockReleases.find(r => r.id === releaseId)
  if (!release) {
    return createApiError('发布记录不存在', 404)
  }

  const validationChecks = [
    {
      name: '服务可用性',
      status: Math.random() > 0.1 ? 'passed' : 'failed',
      description: '检查服务是否正常响应',
      details: '所有关键接口响应正常'
    },
    {
      name: '数据完整性',
      status: Math.random() > 0.05 ? 'passed' : 'failed',
      description: '验证数据是否完整',
      details: '数据库连接正常，关键数据完整'
    },
    {
      name: '性能指标',
      status: Math.random() > 0.2 ? 'passed' : 'warning',
      description: '检查系统性能是否正常',
      details: '响应时间在正常范围内'
    },
    {
      name: '业务功能',
      status: Math.random() > 0.15 ? 'passed' : 'failed',
      description: '验证关键业务功能',
      details: '核心业务流程正常'
    }
  ]

  const allPassed = validationChecks.every(check => check.status === 'passed')
  const hasFailed = validationChecks.some(check => check.status === 'failed')

  let overallStatus = 'passed'
  if (hasFailed) {
    overallStatus = 'failed'
  } else if (validationChecks.some(check => check.status === 'warning')) {
    overallStatus = 'warning'
  }

  return createApiResponse({
    overallStatus,
    checks: validationChecks,
    summary: {
      total: validationChecks.length,
      passed: validationChecks.filter(c => c.status === 'passed').length,
      failed: validationChecks.filter(c => c.status === 'failed').length,
      warning: validationChecks.filter(c => c.status === 'warning').length
    },
    recommendation: overallStatus === 'passed' ?
      '回滚验证通过，系统运行正常' :
      '回滚验证发现问题，建议进一步检查'
  })
}

/**
 * 获取发布报表数据
 */
export function getReleaseReports(timeRange = '30d') {
  const now = new Date()
  let startDate = new Date()

  // 根据时间范围设置开始日期
  switch (timeRange) {
    case '7d':
      startDate.setDate(now.getDate() - 7)
      break
    case '30d':
      startDate.setDate(now.getDate() - 30)
      break
    case '90d':
      startDate.setDate(now.getDate() - 90)
      break
    case '1y':
      startDate.setFullYear(now.getFullYear() - 1)
      break
    default:
      startDate.setDate(now.getDate() - 30)
  }

  // 筛选时间范围内的发布
  const filteredReleases = mockReleases.filter(release => {
    const releaseDate = new Date(release.createdAt)
    return releaseDate >= startDate && releaseDate <= now
  })

  // 计算成功率趋势
  const successRateTrend = generateTrendData(filteredReleases, startDate, now, 'successRate')

  // 计算发布周期趋势
  const cycleTrend = generateTrendData(filteredReleases, startDate, now, 'cycle')

  // 计算回滚率趋势
  const rollbackRateTrend = generateTrendData(filteredReleases, startDate, now, 'rollbackRate')

  // 按类型统计
  const typeStats = {}
  releaseTypes.forEach(type => {
    const typeReleases = filteredReleases.filter(r => r.type === type.value)
    typeStats[type.value] = {
      total: typeReleases.length,
      success: typeReleases.filter(r => ['verified', 'closed'].includes(r.status)).length,
      failed: typeReleases.filter(r => ['failed', 'rollback'].includes(r.status)).length,
      avgCycle: calculateAvgCycle(typeReleases)
    }
  })

  // 按风险等级统计
  const riskStats = {}
  riskLevels.forEach(risk => {
    const riskReleases = filteredReleases.filter(r => r.riskLevel === risk.value)
    riskStats[risk.value] = {
      total: riskReleases.length,
      success: riskReleases.filter(r => ['verified', 'closed'].includes(r.status)).length,
      failed: riskReleases.filter(r => ['failed', 'rollback'].includes(r.status)).length,
      avgCycle: calculateAvgCycle(riskReleases)
    }
  })

  // 按月份统计
  const monthlyStats = generateMonthlyStats(filteredReleases, startDate, now)

  // 团队绩效统计
  const teamStats = generateTeamStats(filteredReleases)

  return createApiResponse({
    timeRange,
    totalReleases: filteredReleases.length,
    successRate: calculateSuccessRate(filteredReleases),
    avgCycle: calculateAvgCycle(filteredReleases),
    rollbackRate: calculateRollbackRate(filteredReleases),
    trends: {
      successRate: successRateTrend,
      cycle: cycleTrend,
      rollbackRate: rollbackRateTrend
    },
    typeStats,
    riskStats,
    monthlyStats,
    teamStats,
    insights: generateInsights(filteredReleases)
  })
}

/**
 * 生成趋势数据
 */
function generateTrendData(releases, startDate, endDate, metric) {
  const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
  const data = []

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + i)
    const dateStr = date.toISOString().split('T')[0]

    const dayReleases = releases.filter(release => {
      const releaseDate = new Date(release.createdAt).toISOString().split('T')[0]
      return releaseDate === dateStr
    })

    let value = 0
    if (dayReleases.length > 0) {
      switch (metric) {
        case 'successRate':
          value = calculateSuccessRate(dayReleases)
          break
        case 'cycle':
          value = calculateAvgCycle(dayReleases)
          break
        case 'rollbackRate':
          value = calculateRollbackRate(dayReleases)
          break
      }
    }

    data.push({
      date: dateStr,
      value,
      count: dayReleases.length
    })
  }

  return data
}

/**
 * 生成月度统计
 */
function generateMonthlyStats(releases, startDate, endDate) {
  const months = {}

  releases.forEach(release => {
    const date = new Date(release.createdAt)
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

    if (!months[monthKey]) {
      months[monthKey] = {
        month: monthKey,
        total: 0,
        success: 0,
        failed: 0,
        rollback: 0
      }
    }

    months[monthKey].total++
    if (['verified', 'closed'].includes(release.status)) {
      months[monthKey].success++
    } else if (release.status === 'failed') {
      months[monthKey].failed++
    } else if (release.status === 'rollback') {
      months[monthKey].rollback++
    }
  })

  return Object.values(months).sort((a, b) => a.month.localeCompare(b.month))
}

/**
 * 生成团队绩效统计
 */
function generateTeamStats(releases) {
  const teams = {}

  releases.forEach(release => {
    const team = release.department || '未知团队'

    if (!teams[team]) {
      teams[team] = {
        name: team,
        total: 0,
        success: 0,
        failed: 0,
        avgCycle: 0,
        releases: []
      }
    }

    teams[team].total++
    teams[team].releases.push(release)

    if (['verified', 'closed'].includes(release.status)) {
      teams[team].success++
    } else if (['failed', 'rollback'].includes(release.status)) {
      teams[team].failed++
    }
  })

  // 计算平均周期
  Object.values(teams).forEach(team => {
    team.avgCycle = calculateAvgCycle(team.releases)
    team.successRate = team.total > 0 ? Math.round((team.success / team.total) * 100) : 0
    delete team.releases // 删除详细数据，减少响应大小
  })

  return Object.values(teams).sort((a, b) => b.total - a.total)
}

/**
 * 生成洞察建议
 */
function generateInsights(releases) {
  const insights = []

  const successRate = calculateSuccessRate(releases)
  const rollbackRate = calculateRollbackRate(releases)
  const avgCycle = calculateAvgCycle(releases)

  // 成功率洞察
  if (successRate < 80) {
    insights.push({
      type: 'warning',
      title: '发布成功率偏低',
      description: `当前发布成功率为 ${successRate}%，建议加强测试和质量控制`,
      recommendation: '增加自动化测试覆盖率，完善发布前检查清单'
    })
  } else if (successRate > 95) {
    insights.push({
      type: 'success',
      title: '发布成功率优秀',
      description: `当前发布成功率为 ${successRate}%，保持良好的发布质量`,
      recommendation: '继续保持现有的质量控制流程'
    })
  }

  // 回滚率洞察
  if (rollbackRate > 10) {
    insights.push({
      type: 'error',
      title: '回滚率过高',
      description: `当前回滚率为 ${rollbackRate}%，需要重点关注发布质量`,
      recommendation: '加强发布前验证，完善回滚预案和监控告警'
    })
  }

  // 发布周期洞察
  if (avgCycle > 7) {
    insights.push({
      type: 'info',
      title: '发布周期较长',
      description: `平均发布周期为 ${avgCycle} 天，可考虑优化发布流程`,
      recommendation: '评估发布流程，识别瓶颈环节，推进自动化部署'
    })
  }

  return insights
}

// 辅助计算函数
function calculateSuccessRate(releases) {
  if (releases.length === 0) return 0
  const successCount = releases.filter(r => ['verified', 'closed'].includes(r.status)).length
  return Math.round((successCount / releases.length) * 100)
}

function calculateRollbackRate(releases) {
  if (releases.length === 0) return 0
  const rollbackCount = releases.filter(r => r.status === 'rollback').length
  return Math.round((rollbackCount / releases.length) * 100)
}

function calculateAvgCycle(releases) {
  const completedReleases = releases.filter(r => r.actualEndDate && r.actualStartDate)
  if (completedReleases.length === 0) return 0

  const totalDays = completedReleases.reduce((sum, release) => {
    const start = new Date(release.actualStartDate)
    const end = new Date(release.actualEndDate)
    return sum + (end - start) / (1000 * 60 * 60 * 24)
  }, 0)

  return Math.round((totalDays / completedReleases.length) * 10) / 10
}


