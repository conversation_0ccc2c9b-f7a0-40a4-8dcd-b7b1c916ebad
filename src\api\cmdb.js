import request from '@/utils/request'

// CMDB 配置项管理 API

/**
 * 获取配置项列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getCIList(params) {
  return request({
    url: '/api/cmdb/ci/list',
    method: 'get',
    params
  })
}

/**
 * 获取配置项详情
 * @param {string} id - 配置项ID
 * @returns {Promise}
 */
export function getCIDetail(id) {
  return request({
    url: `/api/cmdb/ci/${id}`,
    method: 'get'
  })
}

/**
 * 创建配置项
 * @param {Object} data - 配置项数据
 * @returns {Promise}
 */
export function createCI(data) {
  return request({
    url: '/api/cmdb/ci',
    method: 'post',
    data
  })
}

/**
 * 更新配置项
 * @param {string} id - 配置项ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateCI(id, data) {
  return request({
    url: `/api/cmdb/ci/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除配置项
 * @param {string} id - 配置项ID
 * @returns {Promise}
 */
export function deleteCI(id) {
  return request({
    url: `/api/cmdb/ci/${id}`,
    method: 'delete'
  })
}

/**
 * 批量操作配置项
 * @param {Object} data - 批量操作数据
 * @returns {Promise}
 */
export function batchOperateCI(data) {
  return request({
    url: '/api/cmdb/ci/batch',
    method: 'post',
    data
  })
}

// 拓扑图相关 API

/**
 * 获取拓扑图数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getTopologyData(params) {
  return request({
    url: '/api/cmdb/topology',
    method: 'get',
    params
  })
}

/**
 * 更新拓扑图布局
 * @param {Object} data - 布局数据
 * @returns {Promise}
 */
export function updateTopologyLayout(data) {
  return request({
    url: '/api/cmdb/topology/layout',
    method: 'post',
    data
  })
}

// 自动发现相关 API

/**
 * 获取发现任务列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getDiscoveryTasks(params) {
  return request({
    url: '/api/cmdb/discovery/tasks',
    method: 'get',
    params
  })
}

/**
 * 创建发现任务
 * @param {Object} data - 任务数据
 * @returns {Promise}
 */
export function createDiscoveryTask(data) {
  return request({
    url: '/api/cmdb/discovery/tasks',
    method: 'post',
    data
  })
}

/**
 * 运行发现任务
 * @param {string} taskId - 任务ID
 * @returns {Promise}
 */
export function runDiscoveryTask(taskId) {
  return request({
    url: `/api/cmdb/discovery/tasks/${taskId}/run`,
    method: 'post'
  })
}

/**
 * 获取发现结果
 * @param {string} taskId - 任务ID
 * @returns {Promise}
 */
export function getDiscoveryResults(taskId) {
  return request({
    url: `/api/cmdb/discovery/tasks/${taskId}/results`,
    method: 'get'
  })
}

// 关系映射相关 API

/**
 * 获取关系列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getRelationList(params) {
  return request({
    url: '/api/cmdb/relations',
    method: 'get',
    params
  })
}

/**
 * 创建关系
 * @param {Object} data - 关系数据
 * @returns {Promise}
 */
export function createRelation(data) {
  return request({
    url: '/api/cmdb/relations',
    method: 'post',
    data
  })
}

/**
 * 删除关系
 * @param {string} id - 关系ID
 * @returns {Promise}
 */
export function deleteRelation(id) {
  return request({
    url: `/api/cmdb/relations/${id}`,
    method: 'delete'
  })
}

/**
 * 验证关系
 * @param {string} id - 关系ID
 * @returns {Promise}
 */
export function validateRelation(id) {
  return request({
    url: `/api/cmdb/relations/${id}/validate`,
    method: 'post'
  })
}

/**
 * 获取关系推荐
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getRelationRecommendations(params) {
  return request({
    url: '/api/cmdb/relations/recommendations',
    method: 'get',
    params
  })
}

// 版本控制相关 API

/**
 * 获取版本列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getVersionList(params) {
  return request({
    url: '/api/cmdb/versions',
    method: 'get',
    params
  })
}

/**
 * 创建版本快照
 * @param {Object} data - 快照数据
 * @returns {Promise}
 */
export function createSnapshot(data) {
  return request({
    url: '/api/cmdb/versions/snapshot',
    method: 'post',
    data
  })
}

/**
 * 恢复版本
 * @param {string} versionId - 版本ID
 * @returns {Promise}
 */
export function restoreVersion(versionId) {
  return request({
    url: `/api/cmdb/versions/${versionId}/restore`,
    method: 'post'
  })
}

/**
 * 版本对比
 * @param {Object} data - 对比数据
 * @returns {Promise}
 */
export function compareVersions(data) {
  return request({
    url: '/api/cmdb/versions/compare',
    method: 'post',
    data
  })
}

// 数据质量相关 API

/**
 * 获取数据质量报告
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getQualityReport(params) {
  return request({
    url: '/api/cmdb/quality/report',
    method: 'get',
    params
  })
}

/**
 * 执行质量检查
 * @param {Object} data - 检查参数
 * @returns {Promise}
 */
export function runQualityCheck(data) {
  return request({
    url: '/api/cmdb/quality/check',
    method: 'post',
    data
  })
}

/**
 * 获取质量问题列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getQualityIssues(params) {
  return request({
    url: '/api/cmdb/quality/issues',
    method: 'get',
    params
  })
}

/**
 * 修复质量问题
 * @param {string} issueId - 问题ID
 * @returns {Promise}
 */
export function fixQualityIssue(issueId) {
  return request({
    url: `/api/cmdb/quality/issues/${issueId}/fix`,
    method: 'post'
  })
}

// 统计分析相关 API

/**
 * 获取CMDB统计数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getCMDBStats(params) {
  return request({
    url: '/api/cmdb/stats',
    method: 'get',
    params
  })
}

/**
 * 获取趋势数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getTrendData(params) {
  return request({
    url: '/api/cmdb/trends',
    method: 'get',
    params
  })
}

// 导入导出相关 API

/**
 * 导出配置项数据
 * @param {Object} params - 导出参数
 * @returns {Promise}
 */
export function exportCIData(params) {
  return request({
    url: '/api/cmdb/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 导入配置项数据
 * @param {FormData} data - 导入数据
 * @returns {Promise}
 */
export function importCIData(data) {
  return request({
    url: '/api/cmdb/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 与其他模块集成的 API

/**
 * 获取关联的事件列表
 * @param {string} ciId - 配置项ID
 * @returns {Promise}
 */
export function getRelatedEvents(ciId) {
  return request({
    url: `/api/cmdb/ci/${ciId}/events`,
    method: 'get'
  })
}

/**
 * 获取关联的变更记录
 * @param {string} ciId - 配置项ID
 * @returns {Promise}
 */
export function getRelatedChanges(ciId) {
  return request({
    url: `/api/cmdb/ci/${ciId}/changes`,
    method: 'get'
  })
}

/**
 * 获取关联的发布记录
 * @param {string} ciId - 配置项ID
 * @returns {Promise}
 */
export function getRelatedReleases(ciId) {
  return request({
    url: `/api/cmdb/ci/${ciId}/releases`,
    method: 'get'
  })
}

/**
 * 同步配置项状态
 * @param {string} ciId - 配置项ID
 * @param {Object} data - 状态数据
 * @returns {Promise}
 */
export function syncCIStatus(ciId, data) {
  return request({
    url: `/api/cmdb/ci/${ciId}/sync`,
    method: 'post',
    data
  })
}
