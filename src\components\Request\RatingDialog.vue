<template>
  <el-dialog
    v-model="visible"
    title="服务评价"
    width="500px"
    :before-close="handleClose"
    class="rating-dialog"
  >
    <div v-if="request" class="rating-content">
      <!-- 服务信息 -->
      <div class="service-info">
        <div class="service-header">
          <h4>{{ request.serviceName }}</h4>
          <el-tag type="success">已完成</el-tag>
        </div>
        <div class="service-meta">
          <span>请求编号：{{ request.requestNumber }}</span>
          <span>完成时间：{{ request.updatedAt }}</span>
        </div>
      </div>

      <!-- 评价表单 -->
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="80px"
        class="rating-form"
      >
        <!-- 总体评分 -->
        <el-form-item label="总体评分" prop="rating" required>
          <div class="rating-section">
            <el-rate 
              v-model="formData.rating" 
              :max="5"
              :colors="ratingColors"
              :texts="ratingTexts"
              show-text
              text-color="#ff9900"
              size="large"
            />
            <div class="rating-description">
              {{ getRatingDescription(formData.rating) }}
            </div>
          </div>
        </el-form-item>

        <!-- 详细评价 -->
        <el-form-item label="处理速度" prop="speedRating">
          <el-rate 
            v-model="formData.speedRating" 
            :max="5"
            show-text
            :texts="['很慢', '较慢', '一般', '较快', '很快']"
          />
        </el-form-item>

        <el-form-item label="服务质量" prop="qualityRating">
          <el-rate 
            v-model="formData.qualityRating" 
            :max="5"
            show-text
            :texts="['很差', '较差', '一般', '较好', '很好']"
          />
        </el-form-item>

        <el-form-item label="沟通效果" prop="communicationRating">
          <el-rate 
            v-model="formData.communicationRating" 
            :max="5"
            show-text
            :texts="['很差', '较差', '一般', '较好', '很好']"
          />
        </el-form-item>

        <!-- 满意度标签 -->
        <el-form-item label="服务标签">
          <div class="satisfaction-tags">
            <el-checkbox-group v-model="formData.tags">
              <el-checkbox value="响应及时">响应及时</el-checkbox>
              <el-checkbox value="专业高效">专业高效</el-checkbox>
              <el-checkbox value="态度友好">态度友好</el-checkbox>
              <el-checkbox value="解决彻底">解决彻底</el-checkbox>
              <el-checkbox value="流程清晰">流程清晰</el-checkbox>
              <el-checkbox value="超出预期">超出预期</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>

        <!-- 文字评价 -->
        <el-form-item label="详细评价">
          <el-input
            v-model="formData.feedback"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您对本次服务的评价和建议..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 改进建议 -->
        <el-form-item label="改进建议">
          <el-input
            v-model="formData.suggestions"
            type="textarea"
            :rows="3"
            placeholder="您认为我们还可以在哪些方面改进？"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>

        <!-- 推荐度 -->
        <el-form-item label="推荐度">
          <div class="recommendation-section">
            <div class="recommendation-question">
              您是否愿意向同事推荐我们的服务？
            </div>
            <el-radio-group v-model="formData.recommendation" class="recommendation-options">
              <el-radio value="highly">非常愿意</el-radio>
              <el-radio value="willing">愿意</el-radio>
              <el-radio value="neutral">一般</el-radio>
              <el-radio value="unwilling">不太愿意</el-radio>
              <el-radio value="never">绝不推荐</el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
      </el-form>

      <!-- 评价统计 -->
      <div class="rating-stats" v-if="showStats">
        <h5>服务评价统计</h5>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">4.6</div>
            <div class="stat-label">平均评分</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">95%</div>
            <div class="stat-label">满意度</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">156</div>
            <div class="stat-label">评价数量</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="toggleStats">
          {{ showStats ? '隐藏' : '查看' }}统计
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          提交评价
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  request: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const formRef = ref()
const submitting = ref(false)
const showStats = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
  rating: 5,
  speedRating: 5,
  qualityRating: 5,
  communicationRating: 5,
  tags: [],
  feedback: '',
  suggestions: '',
  recommendation: 'highly'
})

// 表单验证规则
const formRules = {
  rating: [
    { required: true, message: '请选择总体评分', trigger: 'change' }
  ]
}

// 评分配置
const ratingColors = ['#F56C6C', '#E6A23C', '#E6A23C', '#67C23A', '#67C23A']
const ratingTexts = ['很差', '较差', '一般', '较好', '很好']

const getRatingDescription = (rating) => {
  const descriptions = {
    1: '服务体验很差，需要大幅改进',
    2: '服务体验较差，有较多不满意的地方',
    3: '服务体验一般，基本满足需求',
    4: '服务体验较好，比较满意',
    5: '服务体验很好，非常满意'
  }
  return descriptions[rating] || ''
}

const toggleStats = () => {
  showStats.value = !showStats.value
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const ratingData = {
      requestId: props.request.id,
      rating: formData.rating,
      speedRating: formData.speedRating,
      qualityRating: formData.qualityRating,
      communicationRating: formData.communicationRating,
      tags: formData.tags,
      feedback: formData.feedback,
      suggestions: formData.suggestions,
      recommendation: formData.recommendation,
      submittedAt: new Date().toISOString()
    }
    
    emit('submit', ratingData)
    ElMessage.success('评价提交成功，感谢您的反馈！')
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置表单数据
  Object.assign(formData, {
    rating: 5,
    speedRating: 5,
    qualityRating: 5,
    communicationRating: 5,
    tags: [],
    feedback: '',
    suggestions: '',
    recommendation: 'highly'
  })
  
  showStats.value = false
}
</script>

<style scoped>
.rating-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.rating-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.service-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.service-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.service-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #606266;
}

.rating-form {
  background: white;
}

.rating-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rating-description {
  font-size: 14px;
  color: #606266;
  font-style: italic;
}

.satisfaction-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.satisfaction-tags :deep(.el-checkbox) {
  margin-right: 0;
  margin-bottom: 8px;
}

.recommendation-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-question {
  font-weight: 500;
  color: #303133;
}

.recommendation-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.recommendation-options :deep(.el-radio) {
  margin-right: 0;
}

.rating-stats {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-top: 3px solid #409EFF;
}

.rating-stats h5 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #409EFF;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义评分组件样式 */
:deep(.el-rate) {
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.el-rate__text) {
  margin-left: 8px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rating-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .service-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .service-meta {
    flex-direction: column;
    gap: 4px;
  }
  
  .satisfaction-tags {
    flex-direction: column;
  }
  
  .recommendation-options {
    flex-direction: column;
    gap: 8px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
  
  .dialog-footer .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .rating-dialog :deep(.el-form-item__label) {
    width: 60px !important;
  }
  
  :deep(.el-rate) {
    flex-wrap: wrap;
  }
  
  :deep(.el-rate__item) {
    margin-right: 4px;
  }
}
</style>
