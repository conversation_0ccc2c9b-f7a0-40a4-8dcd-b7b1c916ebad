import { 
  createApiResponse, 
  createApiError, 
  paginate, 
  generateId, 
  formatDateTime,
  getRandomStatus,
  getRandomPriority,
  getRandomUser,
  getRandomCategory,
  getRandomDate
} from './index.js'

// 模拟事件数据
let mockIncidents = []

// 初始化模拟数据
function initMockData() {
  const incidentTitles = [
    '服务器CPU使用率过高',
    '数据库连接超时',
    '网络设备故障',
    '应用程序无响应',
    '存储空间不足',
    '邮件服务中断',
    '网站访问缓慢',
    '系统内存泄漏',
    '防火墙规则异常',
    '备份任务失败',
    '负载均衡器故障',
    'SSL证书过期',
    '域名解析异常',
    '虚拟机宕机',
    '集群节点离线'
  ]

  const priorities = ['P1', 'P2', 'P3', 'P4']
  const statuses = ['new', 'assigned', 'in-progress', 'waiting', 'resolved', 'closed']
  const categories = [
    '网络/连接问题',
    '服务器/性能',
    '应用系统/故障',
    '数据库/连接',
    '安全/权限',
    '存储/备份',
    '邮件/通讯',
    '桌面支持/硬件'
  ]

  for (let i = 1; i <= 100; i++) {
    const title = incidentTitles[Math.floor(Math.random() * incidentTitles.length)]
    const user = getRandomUser()
    const assignee = Math.random() > 0.2 ? getRandomUser() : null
    const priority = priorities[Math.floor(Math.random() * priorities.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const category = categories[Math.floor(Math.random() * categories.length)]
    
    mockIncidents.push({
      id: generateId('INC'),
      title: `${title} - ${i}`,
      description: `这是一个${priority}级别的事件，需要及时处理...`,
      priority,
      status,
      reporter: user.name,
      reporterDept: user.department,
      assignee: assignee ? assignee.name : '',
      assigneeDept: assignee ? assignee.department : '',
      category,
      impact: Math.random() > 0.5 ? '高' : Math.random() > 0.5 ? '中' : '低',
      urgency: Math.random() > 0.5 ? '高' : Math.random() > 0.5 ? '中' : '低',
      createTime: getRandomDate(30),
      updateTime: getRandomDate(7),
      resolveTime: status === 'resolved' || status === 'closed' ? getRandomDate(3) : null,
      slaProgress: Math.floor(Math.random() * 40) + 60, // 60-100%
      escalationLevel: Math.floor(Math.random() * 3), // 0-2
      affectedUsers: Math.floor(Math.random() * 100) + 1,
      businessService: ['ERP系统', 'OA系统', '邮件服务', '文件服务'][Math.floor(Math.random() * 4)]
    })
  }
}

// 初始化数据
initMockData()

/**
 * 获取事件列表
 * @param {Object} params 查询参数
 * @returns {Promise} 事件列表
 */
export function getIncidentList(params = {}) {
  const { 
    page = 1, 
    pageSize = 20, 
    incidentId = '', 
    title = '', 
    status = '', 
    priority = '', 
    assignee = '',
    category = ''
  } = params

  let filteredIncidents = mockIncidents.filter(incident => {
    return (!incidentId || incident.id.includes(incidentId)) &&
           (!title || incident.title.includes(title)) &&
           (!status || incident.status === status) &&
           (!priority || incident.priority === priority) &&
           (!assignee || incident.assignee.includes(assignee)) &&
           (!category || incident.category.includes(category))
  })

  // 按优先级和创建时间排序
  filteredIncidents.sort((a, b) => {
    const priorityOrder = { 'P1': 4, 'P2': 3, 'P3': 2, 'P4': 1 }
    const aPriority = priorityOrder[a.priority] || 0
    const bPriority = priorityOrder[b.priority] || 0
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority
    }
    return new Date(b.createTime) - new Date(a.createTime)
  })

  const paginatedData = paginate(filteredIncidents, page, pageSize)
  
  return createApiResponse(paginatedData, '获取事件列表成功')
}

/**
 * 获取事件详情
 * @param {string} incidentId 事件ID
 * @returns {Promise} 事件详情
 */
export function getIncidentDetail(incidentId) {
  const incident = mockIncidents.find(i => i.id === incidentId)
  
  if (!incident) {
    return createApiError('事件不存在', 404)
  }

  // 添加详细信息
  const detailIncident = {
    ...incident,
    timeline: [
      {
        id: 1,
        action: '事件创建',
        operator: incident.reporter,
        time: incident.createTime,
        description: '用户报告了新的事件'
      },
      {
        id: 2,
        action: '自动分类',
        operator: '系统',
        time: incident.createTime,
        description: `事件已自动分类为: ${incident.category}`
      },
      {
        id: 3,
        action: '分配处理人',
        operator: '系统管理员',
        time: incident.updateTime,
        description: `事件已分配给 ${incident.assignee}`
      }
    ],
    relatedIncidents: [
      {
        id: 'INC-2025-002',
        title: '相关事件1',
        status: 'resolved',
        similarity: 85
      }
    ],
    affectedCIs: [
      {
        id: 'CI-001',
        name: 'Web服务器-01',
        type: '服务器',
        status: '故障'
      },
      {
        id: 'CI-002',
        name: '负载均衡器',
        type: '网络设备',
        status: '正常'
      }
    ],
    workNotes: [
      {
        id: 1,
        author: incident.assignee,
        content: '开始调查问题原因，检查服务器日志',
        time: incident.updateTime,
        type: 'work_note'
      }
    ]
  }

  return createApiResponse(detailIncident, '获取事件详情成功')
}

/**
 * 创建事件
 * @param {Object} incidentData 事件数据
 * @returns {Promise} 创建结果
 */
export function createIncident(incidentData) {
  const newIncident = {
    id: generateId('INC'),
    ...incidentData,
    status: 'new',
    createTime: formatDateTime(),
    updateTime: formatDateTime(),
    slaProgress: 100,
    escalationLevel: 0
  }

  mockIncidents.unshift(newIncident)
  
  return createApiResponse(newIncident, '事件创建成功')
}

/**
 * 更新事件
 * @param {string} incidentId 事件ID
 * @param {Object} updateData 更新数据
 * @returns {Promise} 更新结果
 */
export function updateIncident(incidentId, updateData) {
  const incidentIndex = mockIncidents.findIndex(i => i.id === incidentId)
  
  if (incidentIndex === -1) {
    return createApiError('事件不存在', 404)
  }

  mockIncidents[incidentIndex] = {
    ...mockIncidents[incidentIndex],
    ...updateData,
    updateTime: formatDateTime()
  }

  return createApiResponse(mockIncidents[incidentIndex], '事件更新成功')
}

/**
 * 分配事件
 * @param {string} incidentId 事件ID
 * @param {Object} assignData 分配数据
 * @returns {Promise} 分配结果
 */
export function assignIncident(incidentId, assignData) {
  const incidentIndex = mockIncidents.findIndex(i => i.id === incidentId)
  
  if (incidentIndex === -1) {
    return createApiError('事件不存在', 404)
  }

  mockIncidents[incidentIndex] = {
    ...mockIncidents[incidentIndex],
    assignee: assignData.assignee,
    assigneeDept: assignData.assigneeDept,
    status: 'assigned',
    updateTime: formatDateTime()
  }

  return createApiResponse(mockIncidents[incidentIndex], '事件分配成功')
}

/**
 * 升级事件
 * @param {string} incidentId 事件ID
 * @param {Object} escalationData 升级数据
 * @returns {Promise} 升级结果
 */
export function escalateIncident(incidentId, escalationData) {
  const incidentIndex = mockIncidents.findIndex(i => i.id === incidentId)
  
  if (incidentIndex === -1) {
    return createApiError('事件不存在', 404)
  }

  mockIncidents[incidentIndex] = {
    ...mockIncidents[incidentIndex],
    escalationLevel: mockIncidents[incidentIndex].escalationLevel + 1,
    priority: escalationData.newPriority || mockIncidents[incidentIndex].priority,
    assignee: escalationData.newAssignee || mockIncidents[incidentIndex].assignee,
    updateTime: formatDateTime()
  }

  return createApiResponse(mockIncidents[incidentIndex], '事件升级成功')
}

/**
 * 解决事件
 * @param {string} incidentId 事件ID
 * @param {Object} resolutionData 解决方案数据
 * @returns {Promise} 解决结果
 */
export function resolveIncident(incidentId, resolutionData) {
  const incidentIndex = mockIncidents.findIndex(i => i.id === incidentId)
  
  if (incidentIndex === -1) {
    return createApiError('事件不存在', 404)
  }

  mockIncidents[incidentIndex] = {
    ...mockIncidents[incidentIndex],
    status: 'resolved',
    resolveTime: formatDateTime(),
    resolution: resolutionData.resolution,
    resolutionCode: resolutionData.resolutionCode,
    updateTime: formatDateTime()
  }

  return createApiResponse(mockIncidents[incidentIndex], '事件解决成功')
}

/**
 * 获取事件统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 统计数据
 */
export function getIncidentStats(params = {}) {
  const { timeRange = 'month' } = params
  
  const stats = {
    total: mockIncidents.length,
    p1: mockIncidents.filter(i => i.priority === 'P1').length,
    p2: mockIncidents.filter(i => i.priority === 'P2').length,
    p3: mockIncidents.filter(i => i.priority === 'P3').length,
    p4: mockIncidents.filter(i => i.priority === 'P4').length,
    overdue: mockIncidents.filter(i => i.slaProgress < 50).length,
    resolved: mockIncidents.filter(i => i.status === 'resolved').length,
    byCategory: {},
    byStatus: {},
    slaCompliance: {
      P1: 95,
      P2: 88,
      P3: 92,
      P4: 98
    },
    avgResolutionTime: {
      P1: '2.3小时',
      P2: '6.8小时',
      P3: '18.5小时',
      P4: '45.2小时'
    }
  }

  // 按分类统计
  mockIncidents.forEach(incident => {
    stats.byCategory[incident.category] = (stats.byCategory[incident.category] || 0) + 1
  })

  // 按状态统计
  mockIncidents.forEach(incident => {
    stats.byStatus[incident.status] = (stats.byStatus[incident.status] || 0) + 1
  })

  return createApiResponse(stats, '获取事件统计成功')
}

/**
 * 获取事件分类树
 * @returns {Promise} 分类树数据
 */
export function getIncidentCategories() {
  const categories = [
    {
      id: 1,
      label: '网络',
      children: [
        { id: 11, label: '接入问题' },
        { id: 12, label: '路由器故障' },
        { id: 13, label: '带宽拥塞' }
      ]
    },
    {
      id: 2,
      label: '服务器',
      children: [
        { id: 21, label: '物理服务器' },
        { id: 22, label: '虚拟机' },
        { id: 23, label: '集群故障' }
      ]
    },
    {
      id: 3,
      label: '应用系统',
      children: [
        { id: 31, label: 'ERP' },
        { id: 32, label: 'OA' },
        { id: 33, label: '邮件系统' }
      ]
    },
    {
      id: 4,
      label: '桌面支持',
      children: [
        { id: 41, label: '打印机' },
        { id: 42, label: '电脑蓝屏' },
        { id: 43, label: '外设故障' }
      ]
    }
  ]

  return createApiResponse(categories, '获取事件分类成功')
}

export default {
  getIncidentList,
  getIncidentDetail,
  createIncident,
  updateIncident,
  assignIncident,
  escalateIncident,
  resolveIncident,
  getIncidentStats,
  getIncidentCategories
}
