<template>
  <div class="knowledge-detail">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator="/">
      <el-breadcrumb-item :to="{ path: '/knowledge' }">知识概览</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/knowledge/base' }">知识库</el-breadcrumb-item>
      <el-breadcrumb-item>{{ knowledgeDetail.title }}</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="detail-container">
      <el-row :gutter="20">
        <!-- 主要内容区域 -->
        <el-col :xs="24" :lg="18">
          <!-- 文章头部 -->
          <el-card class="article-header">
            <div class="header-content">
              <div class="title-section">
                <h1 class="article-title">{{ knowledgeDetail.title }}</h1>
                <div class="article-meta">
                  <el-tag :type="getCategoryType(knowledgeDetail.category)">
                    {{ knowledgeDetail.category }}
                  </el-tag>
                  <el-tag :type="getStatusType(knowledgeDetail.status)">
                    {{ getStatusText(knowledgeDetail.status) }}
                  </el-tag>
                  <span class="meta-item">
                    <el-icon><User /></el-icon>
                    {{ knowledgeDetail.author }}
                  </span>
                  <span class="meta-item">
                    <el-icon><Calendar /></el-icon>
                    {{ formatTime(knowledgeDetail.updateTime) }}
                  </span>
                  <span class="meta-item">
                    <el-icon><View /></el-icon>
                    {{ knowledgeDetail.views }} 次浏览
                  </span>
                </div>
              </div>
              <div class="action-section">
                <el-button-group>
                  <el-button @click="editKnowledge" v-if="canEdit">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button @click="shareKnowledge">
                    <el-icon><Share /></el-icon>
                    分享
                  </el-button>
                  <el-button @click="exportKnowledge">
                    <el-icon><Download /></el-icon>
                    导出
                  </el-button>
                  <el-dropdown @command="handleMoreAction">
                    <el-button>
                      更多<el-icon><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="copy">复制链接</el-dropdown-item>
                        <el-dropdown-item command="print">打印</el-dropdown-item>
                        <el-dropdown-item command="report">举报</el-dropdown-item>
                        <el-dropdown-item command="archive" divided v-if="canManage">归档</el-dropdown-item>
                        <el-dropdown-item command="delete" class="danger" v-if="canManage">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-button-group>
              </div>
            </div>
          </el-card>

          <!-- 文章内容 -->
          <el-card class="article-content">
            <div class="content-body" v-html="knowledgeDetail.content"></div>
            
            <!-- 附件列表 -->
            <div v-if="knowledgeDetail.attachments && knowledgeDetail.attachments.length > 0" class="attachments-section">
              <el-divider content-position="left">附件</el-divider>
              <div class="attachments-list">
                <div 
                  v-for="attachment in knowledgeDetail.attachments" 
                  :key="attachment.id"
                  class="attachment-item"
                  @click="downloadAttachment(attachment)"
                >
                  <el-icon><Document /></el-icon>
                  <span class="attachment-name">{{ attachment.name }}</span>
                  <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
                </div>
              </div>
            </div>

            <!-- 标签 -->
            <div v-if="knowledgeDetail.tags && knowledgeDetail.tags.length > 0" class="tags-section">
              <el-divider content-position="left">标签</el-divider>
              <div class="tags-list">
                <el-tag
                  v-for="tag in knowledgeDetail.tags"
                  :key="tag.id"
                  class="tag-item"
                  @click="searchByTag(tag)"
                >
                  {{ tag.name }}
                </el-tag>
              </div>
            </div>
          </el-card>

          <!-- 评价区域 -->
          <el-card class="rating-section">
            <template #header>
              <div class="section-header">
                <span>评价与反馈</span>
                <div class="rating-summary">
                  <el-rate
                    v-model="knowledgeDetail.rating"
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value} 分"
                  />
                  <span class="rating-count">({{ knowledgeDetail.ratingCount }} 人评价)</span>
                </div>
              </div>
            </template>

            <!-- 用户评价 -->
            <div class="user-rating">
              <div class="rating-form">
                <p>您觉得这篇知识对您有帮助吗？</p>
                <el-rate
                  v-model="userRating.score"
                  :colors="['#F7BA2A', '#F7BA2A', '#F7BA2A']"
                  @change="handleRatingChange"
                />
                <el-input
                  v-model="userRating.comment"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入您的评价和建议..."
                  maxlength="500"
                  show-word-limit
                  class="rating-comment"
                />
                <div class="rating-actions">
                  <el-button type="primary" @click="submitRating">提交评价</el-button>
                </div>
              </div>
            </div>

            <!-- 评价列表 -->
            <div class="ratings-list">
              <div 
                v-for="rating in ratingsList" 
                :key="rating.id"
                class="rating-item"
              >
                <div class="rating-header">
                  <el-avatar :size="32">{{ rating.userName.charAt(0) }}</el-avatar>
                  <div class="rating-info">
                    <div class="rating-user">{{ rating.userName }}</div>
                    <div class="rating-meta">
                      <el-rate
                        v-model="rating.score"
                        disabled
                        size="small"
                      />
                      <span class="rating-time">{{ formatTime(rating.createTime) }}</span>
                    </div>
                  </div>
                </div>
                <div class="rating-content">{{ rating.comment }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 侧边栏 -->
        <el-col :xs="24" :lg="6">
          <!-- 目录导航 -->
          <el-card class="toc-card">
            <template #header>
              <span>目录</span>
            </template>
            <div class="table-of-contents">
              <div 
                v-for="item in tocItems" 
                :key="item.id"
                :class="['toc-item', `toc-level-${item.level}`, { active: item.id === activeTocId }]"
                @click="scrollToHeading(item)"
              >
                {{ item.text }}
              </div>
            </div>
          </el-card>

          <!-- 知识统计 -->
          <el-card class="stats-card">
            <template #header>
              <span>统计信息</span>
            </template>
            <div class="knowledge-stats">
              <div class="stat-item">
                <span class="stat-label">浏览次数</span>
                <span class="stat-value">{{ knowledgeDetail.views }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">收藏次数</span>
                <span class="stat-value">{{ knowledgeDetail.favorites }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">引用次数</span>
                <span class="stat-value">{{ knowledgeDetail.references }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最后更新</span>
                <span class="stat-value">{{ formatTime(knowledgeDetail.updateTime) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">创建时间</span>
                <span class="stat-value">{{ formatTime(knowledgeDetail.createTime) }}</span>
              </div>
            </div>
          </el-card>

          <!-- 版本历史 -->
          <el-card class="version-card">
            <template #header>
              <div class="section-header">
                <span>版本历史</span>
                <el-button type="text" size="small" @click="showVersionHistory">
                  查看全部
                </el-button>
              </div>
            </template>
            <div class="version-list">
              <div 
                v-for="version in versionHistory" 
                :key="version.id"
                class="version-item"
                @click="viewVersion(version)"
              >
                <div class="version-info">
                  <div class="version-number">v{{ version.version }}</div>
                  <div class="version-meta">
                    <div class="version-author">{{ version.author }}</div>
                    <div class="version-time">{{ formatTime(version.createTime) }}</div>
                  </div>
                </div>
                <div class="version-description">{{ version.description }}</div>
              </div>
            </div>
          </el-card>

          <!-- 相关知识推荐 -->
          <el-card class="related-card">
            <template #header>
              <span>相关知识</span>
            </template>
            <div class="related-list">
              <div 
                v-for="item in relatedKnowledge" 
                :key="item.id"
                class="related-item"
                @click="viewRelatedKnowledge(item)"
              >
                <div class="related-title">{{ item.title }}</div>
                <div class="related-meta">
                  <el-tag size="small" :type="getCategoryType(item.category)">
                    {{ item.category }}
                  </el-tag>
                  <span class="related-views">{{ item.views }} 浏览</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User, Calendar, View, Edit, Share, Download, ArrowDown,
  Document
} from '@element-plus/icons-vue'
import { 
  getKnowledgeDetail,
  getKnowledgeVersions,
  getRelatedKnowledge,
  rateKnowledge,
  incrementKnowledgeViews
} from '@/api/knowledgeApi'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeTocId = ref('')
const canEdit = ref(true)
const canManage = ref(true)

// 知识详情
const knowledgeDetail = ref({
  id: 1,
  title: '服务器性能监控配置完整指南',
  content: `
    <h2>概述</h2>
    <p>本文档详细介绍如何配置服务器性能监控系统，包括CPU、内存、磁盘等关键指标的监控设置。</p>
    
    <h2>监控指标</h2>
    <h3>CPU监控</h3>
    <p>CPU使用率是服务器性能的重要指标...</p>
    
    <h3>内存监控</h3>
    <p>内存使用情况直接影响系统性能...</p>
    
    <h2>配置步骤</h2>
    <p>以下是详细的配置步骤...</p>
  `,
  category: '技术文档',
  author: '张工',
  status: 'published',
  views: 2456,
  favorites: 89,
  references: 23,
  rating: 4.8,
  ratingCount: 156,
  updateTime: '2025-01-30 14:30',
  createTime: '2025-01-25 09:15',
  attachments: [
    { id: 1, name: '监控配置模板.xlsx', size: 1024000 },
    { id: 2, name: '告警规则示例.json', size: 2048 }
  ],
  tags: [
    { id: 1, name: '服务器' },
    { id: 2, name: '监控' },
    { id: 3, name: '性能' }
  ]
})

// 用户评价
const userRating = reactive({
  score: 0,
  comment: ''
})

// 评价列表
const ratingsList = ref([
  {
    id: 1,
    userName: '李工',
    score: 5,
    comment: '非常详细的指南，按照步骤配置成功了！',
    createTime: '2025-01-29 16:30'
  },
  {
    id: 2,
    userName: '王工',
    score: 4,
    comment: '内容很全面，建议增加一些截图说明。',
    createTime: '2025-01-28 10:15'
  }
])

// 目录项
const tocItems = ref([
  { id: 'overview', text: '概述', level: 2 },
  { id: 'metrics', text: '监控指标', level: 2 },
  { id: 'cpu', text: 'CPU监控', level: 3 },
  { id: 'memory', text: '内存监控', level: 3 },
  { id: 'config', text: '配置步骤', level: 2 }
])

// 版本历史
const versionHistory = ref([
  {
    id: 1,
    version: '2.1',
    author: '张工',
    createTime: '2025-01-30 14:30',
    description: '更新了告警配置部分'
  },
  {
    id: 2,
    version: '2.0',
    author: '张工',
    createTime: '2025-01-28 09:15',
    description: '重构了整体结构，增加了实例说明'
  }
])

// 相关知识
const relatedKnowledge = ref([
  {
    id: 2,
    title: '网络故障快速排除流程',
    category: '流程规范',
    views: 1892
  },
  {
    id: 3,
    title: '数据库性能优化指南',
    category: '技术文档',
    views: 1654
  },
  {
    id: 4,
    title: '系统告警处理规范',
    category: '操作指南',
    views: 1432
  }
])

// 生命周期
onMounted(() => {
  loadKnowledgeDetail()
  incrementViews()
  generateTOC()
})

// 加载知识详情
const loadKnowledgeDetail = async () => {
  loading.value = true
  try {
    const id = route.params.id
    // const response = await getKnowledgeDetail(id)
    // knowledgeDetail.value = response.data
    
    ElMessage.success('知识详情加载完成')
  } catch (error) {
    ElMessage.error('加载知识详情失败')
  } finally {
    loading.value = false
  }
}

// 增加浏览次数
const incrementViews = async () => {
  try {
    const id = route.params.id
    // await incrementKnowledgeViews(id)
  } catch (error) {
    console.error('增加浏览次数失败:', error)
  }
}

// 生成目录
const generateTOC = () => {
  nextTick(() => {
    // 这里可以解析content中的标题生成目录
    // 实际项目中可以使用更复杂的解析逻辑
  })
}

// 事件处理
const editKnowledge = () => {
  router.push(`/knowledge/articles/${route.params.id}/edit`)
}

const shareKnowledge = () => {
  // 复制链接到剪贴板
  const url = window.location.href
  navigator.clipboard.writeText(url).then(() => {
    ElMessage.success('链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

const exportKnowledge = () => {
  ElMessage.info('导出功能开发中...')
}

const handleMoreAction = (command) => {
  switch (command) {
    case 'copy':
      shareKnowledge()
      break
    case 'print':
      window.print()
      break
    case 'report':
      ElMessage.info('举报功能开发中...')
      break
    case 'archive':
      handleArchive()
      break
    case 'delete':
      handleDelete()
      break
  }
}

const handleArchive = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要归档这篇知识吗？',
      '确认归档',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('归档成功')
    router.push('/knowledge/base')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('归档失败')
    }
  }
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这篇知识吗？删除后无法恢复！',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    ElMessage.success('删除成功')
    router.push('/knowledge/base')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 附件下载
const downloadAttachment = (attachment) => {
  ElMessage.info(`下载附件: ${attachment.name}`)
}

// 标签搜索
const searchByTag = (tag) => {
  router.push(`/knowledge/base?tag=${tag.name}`)
}

// 评价相关
const handleRatingChange = (value) => {
  console.log('评分变化:', value)
}

const submitRating = async () => {
  if (userRating.score === 0) {
    ElMessage.warning('请先给出评分')
    return
  }

  try {
    // await rateKnowledge(route.params.id, {
    //   score: userRating.score,
    //   comment: userRating.comment
    // })

    ElMessage.success('评价提交成功')
    userRating.score = 0
    userRating.comment = ''
    // 重新加载评价列表
  } catch (error) {
    ElMessage.error('评价提交失败')
  }
}

// 目录导航
const scrollToHeading = (item) => {
  activeTocId.value = item.id
  const element = document.getElementById(item.id)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 版本相关
const showVersionHistory = () => {
  router.push(`/knowledge/articles/${route.params.id}/versions`)
}

const viewVersion = (version) => {
  router.push(`/knowledge/articles/${route.params.id}/versions/${version.id}`)
}

// 相关知识
const viewRelatedKnowledge = (item) => {
  router.push(`/knowledge/articles/${item.id}`)
}

// 工具函数
const getCategoryType = (category) => {
  const types = {
    '技术文档': 'primary',
    '流程规范': 'success',
    'FAQ': 'warning',
    '解决方案': 'danger',
    '操作指南': 'info'
  }
  return types[category] || ''
}

const getStatusType = (status) => {
  const types = {
    'published': 'success',
    'draft': 'info',
    'review': 'warning',
    'archived': 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    'published': '已发布',
    'draft': '草稿',
    'review': '审核中',
    'archived': '已归档'
  }
  return texts[status] || status
}

const formatTime = (time) => {
  return time
}

const formatFileSize = (size) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
}
</script>

<style scoped>
.knowledge-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.breadcrumb {
  margin-bottom: 20px;
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* 文章头部样式 */
.article-header {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.title-section {
  flex: 1;
}

.article-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.action-section {
  flex-shrink: 0;
}

/* 文章内容样式 */
.article-content {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-body {
  line-height: 1.8;
  font-size: 16px;
  color: #303133;
}

.content-body h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1976D2;
  margin: 32px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.content-body h3 {
  font-size: 20px;
  font-weight: 500;
  color: #409EFF;
  margin: 24px 0 12px 0;
}

.content-body p {
  margin: 16px 0;
  text-align: justify;
}

/* 附件样式 */
.attachments-section {
  margin-top: 32px;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.attachment-item:hover {
  background: #e9ecef;
}

.attachment-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.attachment-size {
  font-size: 12px;
  color: #909399;
}

/* 标签样式 */
.tags-section {
  margin-top: 32px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag-item:hover {
  transform: scale(1.05);
}

/* 评价区域样式 */
.rating-section {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.rating-summary {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-count {
  font-size: 14px;
  color: #909399;
}

.user-rating {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.rating-form p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
}

.rating-comment {
  margin: 16px 0;
}

.rating-actions {
  text-align: right;
}

.ratings-list {
  max-height: 400px;
  overflow-y: auto;
}

.rating-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.rating-item:last-child {
  border-bottom: none;
}

.rating-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.rating-info {
  flex: 1;
}

.rating-user {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.rating-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 4px;
}

.rating-time {
  font-size: 12px;
  color: #909399;
}

.rating-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}

/* 侧边栏样式 */
.toc-card,
.stats-card,
.version-card,
.related-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 目录样式 */
.table-of-contents {
  max-height: 300px;
  overflow-y: auto;
}

.toc-item {
  padding: 8px 0;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  border-left: 2px solid transparent;
  padding-left: 12px;
  transition: all 0.3s ease;
}

.toc-item:hover {
  color: #409EFF;
  background-color: #f8f9fa;
}

.toc-item.active {
  color: #409EFF;
  border-left-color: #409EFF;
  background-color: #f0f8ff;
}

.toc-level-3 {
  padding-left: 24px;
  font-size: 13px;
}

/* 统计信息样式 */
.knowledge-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

/* 版本历史样式 */
.version-list {
  max-height: 200px;
  overflow-y: auto;
}

.version-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.version-item:hover {
  background-color: #f8f9fa;
}

.version-item:last-child {
  border-bottom: none;
}

.version-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.version-number {
  font-size: 14px;
  font-weight: 600;
  color: #409EFF;
}

.version-meta {
  flex: 1;
}

.version-author {
  font-size: 12px;
  color: #303133;
}

.version-time {
  font-size: 12px;
  color: #909399;
}

.version-description {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

/* 相关知识样式 */
.related-list {
  max-height: 300px;
  overflow-y: auto;
}

.related-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.related-item:hover {
  background-color: #f8f9fa;
}

.related-item:last-child {
  border-bottom: none;
}

.related-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.related-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.related-views {
  font-size: 12px;
  color: #909399;
}

/* 下拉菜单危险项样式 */
:deep(.el-dropdown-menu__item.danger) {
  color: #F56C6C;
}

:deep(.el-dropdown-menu__item.danger:hover) {
  background-color: #fef0f0;
  color: #F56C6C;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-detail {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .article-title {
    font-size: 24px;
  }

  .article-meta {
    gap: 12px;
  }

  .action-section {
    margin-top: 16px;
  }

  .content-body {
    font-size: 15px;
  }

  .content-body h2 {
    font-size: 20px;
  }

  .content-body h3 {
    font-size: 18px;
  }
}
</style>
