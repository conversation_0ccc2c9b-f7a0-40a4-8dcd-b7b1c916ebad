<template>
  <div class="auto-discovery">
    <div class="page-header">
      <div class="header-content">
        <h2>自动发现</h2>
        <p>智能发现和识别IT基础设施配置项，支持多种协议和发现策略</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="startDiscovery">
          <el-icon><Refresh /></el-icon>
          开始发现
        </el-button>
        <el-button @click="showDiscoveryWizard = true">
          <el-icon><MagicStick /></el-icon>
          发现向导
        </el-button>
        <el-button @click="showScheduleDialog = true">
          <el-icon><Timer /></el-icon>
          计划任务
        </el-button>
        <el-button @click="viewDiscoveryReport">
          <el-icon><TrendCharts /></el-icon>
          发现报告
        </el-button>
        <el-button @click="showSettingsDialog = true">
          <el-icon><Setting /></el-icon>
          发现设置
        </el-button>
      </div>
    </div>

    <!-- 发现状态概览 -->
    <div class="discovery-overview">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in discoveryStats" :key="stat.key">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24" :color="stat.color">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 发现协议和策略 -->
    <div class="discovery-protocols">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>支持的发现协议</span>
                <el-button type="text" size="small" @click="configureProtocols">配置</el-button>
              </div>
            </template>
            <div class="protocol-grid">
              <div
                class="protocol-item"
                v-for="protocol in supportedProtocols"
                :key="protocol.name"
                :class="{ active: protocol.enabled }"
                @click="toggleProtocol(protocol)"
              >
                <div class="protocol-icon">
                  <el-icon :size="20" :color="protocol.enabled ? protocol.color : '#ccc'">
                    <component :is="protocol.icon" />
                  </el-icon>
                </div>
                <div class="protocol-info">
                  <div class="protocol-name">{{ protocol.name }}</div>
                  <div class="protocol-desc">{{ protocol.description }}</div>
                  <div class="protocol-stats" v-if="protocol.enabled">
                    <span>成功率: {{ protocol.successRate }}%</span>
                  </div>
                </div>
                <div class="protocol-status">
                  <el-switch
                    v-model="protocol.enabled"
                    @change="updateProtocol(protocol)"
                    size="small"
                  />
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>发现策略</span>
                <el-button type="text" size="small" @click="manageStrategies">管理</el-button>
              </div>
            </template>
            <div class="strategy-list">
              <div class="strategy-item" v-for="strategy in discoveryStrategies" :key="strategy.id">
                <div class="strategy-header">
                  <div class="strategy-name">{{ strategy.name }}</div>
                  <el-tag :type="strategy.active ? 'success' : 'info'" size="small">
                    {{ strategy.active ? '启用' : '禁用' }}
                  </el-tag>
                </div>
                <div class="strategy-desc">{{ strategy.description }}</div>
                <div class="strategy-stats">
                  <div class="stat-item">
                    <span class="stat-label">成功率:</span>
                    <span class="stat-value">{{ strategy.successRate }}%</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">发现数:</span>
                    <span class="stat-value">{{ strategy.discoveredCount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">最后运行:</span>
                    <span class="stat-value">{{ strategy.lastRun }}</span>
                  </div>
                </div>
                <div class="strategy-actions">
                  <el-button size="small" type="text" @click="editStrategy(strategy)">编辑</el-button>
                  <el-button size="small" type="text" @click="runStrategy(strategy)">运行</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 发现任务 -->
    <div class="discovery-tasks">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>发现任务</span>
            <div class="header-controls">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索任务..."
                style="width: 200px; margin-right: 12px"
                prefix-icon="Search"
                @input="handleSearch"
              />
              <el-select
                v-model="selectedStatus"
                placeholder="状态筛选"
                style="width: 120px; margin-right: 12px"
                @change="handleStatusFilter"
              >
                <el-option label="全部状态" value="" />
                <el-option label="运行中" value="running" />
                <el-option label="已完成" value="completed" />
                <el-option label="失败" value="failed" />
                <el-option label="已停止" value="stopped" />
              </el-select>
              <el-select
                v-model="selectedType"
                placeholder="类型筛选"
                style="width: 120px; margin-right: 12px"
                @change="handleTypeFilter"
              >
                <el-option label="全部类型" value="" />
                <el-option label="网络扫描" value="network" />
                <el-option label="Agent发现" value="agent" />
                <el-option label="SNMP发现" value="snmp" />
                <el-option label="WMI发现" value="wmi" />
                <el-option label="API发现" value="api" />
              </el-select>
              <el-button type="primary" size="small" @click="createDiscoveryTask">
                <el-icon><Plus /></el-icon>
                新建任务
              </el-button>
            </div>
          </div>
        </template>

        <el-table :data="filteredTasks" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="任务名称" min-width="150">
            <template #default="scope">
              <el-button type="text" @click="viewTaskDetails(scope.row)">
                {{ scope.row.name }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="发现类型" width="120">
            <template #default="scope">
              <el-tag :type="getTypeColor(scope.row.type)" size="small">
                {{ getTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="protocol" label="协议" width="100">
            <template #default="scope">
              <el-tag size="small" type="info">{{ scope.row.protocol }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="range" label="发现范围" min-width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="进度" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.progress || 0"
                :status="scope.row.status === 'failed' ? 'exception' : ''"
                :show-text="false"
                style="width: 80px"
              />
              <span style="margin-left: 8px; font-size: 12px">{{ scope.row.progress || 0 }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="lastRun" label="最后运行" width="160" />
          <el-table-column prop="nextRun" label="下次运行" width="160" />
          <el-table-column prop="discovered" label="发现数量" width="100">
            <template #default="scope">
              <span class="discovery-count" :class="{ 'has-new': scope.row.newDiscovered > 0 }">
                {{ scope.row.discovered }}
                <span v-if="scope.row.newDiscovered > 0" class="new-badge">+{{ scope.row.newDiscovered }}</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="80" />
          <el-table-column label="操作" width="250">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                text
                @click="runTask(scope.row)"
                :disabled="scope.row.status === 'running'"
                :loading="scope.row.status === 'running'"
              >
                {{ scope.row.status === 'running' ? '运行中' : '运行' }}
              </el-button>
              <el-button type="primary" size="small" text @click="viewResults(scope.row)">
                结果
              </el-button>
              <el-button type="primary" size="small" text @click="editTask(scope.row)">
                编辑
              </el-button>
              <el-button type="primary" size="small" text @click="cloneTask(scope.row)">
                克隆
              </el-button>
              <el-dropdown @command="handleTaskAction" trigger="click">
                <el-button type="primary" size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDownBold /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'schedule', task: scope.row}">
                      <el-icon><Timer /></el-icon>
                      计划任务
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'export', task: scope.row}">
                      <el-icon><Download /></el-icon>
                      导出配置
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'logs', task: scope.row}">
                      <el-icon><Document /></el-icon>
                      查看日志
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{action: 'stop', task: scope.row}"
                      v-if="scope.row.status === 'running'"
                    >
                      <el-icon><VideoPause /></el-icon>
                      停止任务
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{action: 'delete', task: scope.row}"
                      v-if="scope.row.status !== 'running'"
                      divided
                    >
                      <el-icon><DeleteFilled /></el-icon>
                      删除任务
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalItems"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 发现结果 -->
    <div class="discovery-results">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最新发现结果</span>
            <el-button type="text" size="small" @click="viewAllResults">查看全部</el-button>
          </div>
        </template>

        <el-table :data="recentResults" style="width: 100%">
          <el-table-column prop="name" label="设备名称" />
          <el-table-column prop="ip" label="IP地址" width="140" />
          <el-table-column prop="type" label="设备类型" width="120">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="os" label="操作系统" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === '在线' ? 'success' : 'danger'" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="discoveredTime" label="发现时间" width="160" />
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="addToCMDB(scope.row)">
                添加到CMDB
              </el-button>
              <el-button type="primary" size="small" text @click="viewDetails(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 创建发现任务对话框 -->
    <el-dialog v-model="showTaskDialog" title="创建发现任务" width="60%" :before-close="handleClose">
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="120px">
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
        </el-form-item>

        <el-form-item label="发现类型" prop="type">
          <el-select v-model="taskForm.type" placeholder="请选择发现类型" style="width: 100%">
            <el-option label="网络扫描" value="network" />
            <el-option label="SNMP发现" value="snmp" />
            <el-option label="WMI发现" value="wmi" />
            <el-option label="SSH发现" value="ssh" />
          </el-select>
        </el-form-item>

        <el-form-item label="IP范围" prop="ipRange">
          <el-input v-model="taskForm.ipRange" placeholder="例如: ***********-*************" />
        </el-form-item>

        <el-form-item label="端口范围" prop="portRange">
          <el-input v-model="taskForm.portRange" placeholder="例如: 22,80,443,3389" />
        </el-form-item>

        <el-form-item label="认证信息">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-input v-model="taskForm.username" placeholder="用户名" />
            </el-col>
            <el-col :span="12">
              <el-input v-model="taskForm.password" type="password" placeholder="密码" show-password />
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="调度设置">
          <el-radio-group v-model="taskForm.scheduleType">
            <el-radio label="once">立即执行</el-radio>
            <el-radio label="daily">每日执行</el-radio>
            <el-radio label="weekly">每周执行</el-radio>
            <el-radio label="monthly">每月执行</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="执行时间" v-if="taskForm.scheduleType !== 'once'">
          <el-time-picker v-model="taskForm.scheduleTime" placeholder="选择执行时间" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTaskDialog = false">取消</el-button>
          <el-button type="primary" @click="saveTask">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 计划任务对话框 -->
    <el-dialog v-model="showScheduleDialog" title="计划任务" width="50%">
      <div class="schedule-content">
        <p>计划任务功能允许您设置自动发现的时间表</p>
        <el-table :data="scheduledTasks" style="width: 100%">
          <el-table-column prop="name" label="任务名称" />
          <el-table-column prop="schedule" label="执行计划" />
          <el-table-column prop="enabled" label="状态" width="80">
            <template #default="scope">
              <el-switch v-model="scope.row.enabled" @change="toggleSchedule(scope.row)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showScheduleDialog = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 发现设置对话框 -->
    <el-dialog v-model="showSettingsDialog" title="发现设置" width="50%">
      <el-form :model="discoverySettings" label-width="120px">
        <el-form-item label="并发线程数">
          <el-input-number v-model="discoverySettings.threads" :min="1" :max="50" />
          <span class="form-help">同时进行发现的线程数量</span>
        </el-form-item>

        <el-form-item label="连接超时">
          <el-input-number v-model="discoverySettings.timeout" :min="1" :max="300" />
          <span class="form-help">连接超时时间（秒）</span>
        </el-form-item>

        <el-form-item label="重试次数">
          <el-input-number v-model="discoverySettings.retries" :min="0" :max="10" />
        </el-form-item>

        <el-form-item label="自动添加到CMDB">
          <el-switch v-model="discoverySettings.autoAddToCMDB" />
          <span class="form-help">发现的设备自动添加到CMDB</span>
        </el-form-item>

        <el-form-item label="发现日志级别">
          <el-select v-model="discoverySettings.logLevel" style="width: 100%">
            <el-option label="错误" value="error" />
            <el-option label="警告" value="warning" />
            <el-option label="信息" value="info" />
            <el-option label="调试" value="debug" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSettingsDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 对话框状态
const showTaskDialog = ref(false)
const showScheduleDialog = ref(false)
const showSettingsDialog = ref(false)
const showDiscoveryWizard = ref(false)
const loading = ref(false)
const taskFormRef = ref()

// 搜索和筛选
const searchKeyword = ref('')
const selectedStatus = ref('')
const selectedType = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)

// 发现统计数据
const discoveryStats = ref([
  {
    key: 'total',
    label: '总设备数',
    value: '156',
    icon: 'Monitor',
    color: '#1976D2',
    trend: 5.2
  },
  {
    key: 'online',
    label: '在线设备',
    value: '142',
    icon: 'CircleCheck',
    color: '#4CAF50',
    trend: 2.1
  },
  {
    key: 'offline',
    label: '离线设备',
    value: '14',
    icon: 'CircleClose',
    color: '#F44336',
    trend: -1.3
  },
  {
    key: 'new',
    label: '新发现',
    value: '8',
    icon: 'Plus',
    color: '#FF9800',
    trend: 15.6
  }
])

// 支持的协议
const supportedProtocols = ref([
  {
    name: 'SNMP',
    description: '简单网络管理协议',
    icon: 'Connection',
    color: '#4CAF50',
    enabled: true,
    successRate: 92
  },
  {
    name: 'WMI',
    description: 'Windows管理规范',
    icon: 'Monitor',
    color: '#2196F3',
    enabled: true,
    successRate: 88
  },
  {
    name: 'SSH',
    description: '安全外壳协议',
    icon: 'Key',
    color: '#FF9800',
    enabled: true,
    successRate: 95
  },
  {
    name: 'Agent',
    description: '代理程序发现',
    icon: 'Service',
    color: '#9C27B0',
    enabled: false,
    successRate: 98
  },
  {
    name: 'API',
    description: '应用程序接口',
    icon: 'Link',
    color: '#607D8B',
    enabled: true,
    successRate: 85
  },
  {
    name: 'Ping',
    description: '网络连通性检测',
    icon: 'Connection',
    color: '#795548',
    enabled: true,
    successRate: 99
  }
])

// 发现策略
const discoveryStrategies = ref([
  {
    id: 1,
    name: '智能网络扫描',
    description: '基于网络拓扑的智能扫描策略',
    active: true,
    successRate: 94,
    discoveredCount: 128,
    lastRun: '2小时前'
  },
  {
    id: 2,
    name: '增量发现',
    description: '仅发现新增或变更的设备',
    active: true,
    successRate: 87,
    discoveredCount: 23,
    lastRun: '30分钟前'
  },
  {
    id: 3,
    name: '深度扫描',
    description: '全面扫描设备详细信息',
    active: false,
    successRate: 76,
    discoveredCount: 89,
    lastRun: '1天前'
  },
  {
    id: 4,
    name: '关系发现',
    description: '自动识别设备间的依赖关系',
    active: true,
    successRate: 82,
    discoveredCount: 156,
    lastRun: '4小时前'
  }
])

// 发现任务数据
const discoveryTasks = ref([
  {
    id: 1,
    name: '办公网络扫描',
    type: 'network',
    protocol: 'SNMP',
    range: '***********/24',
    status: 'completed',
    progress: 100,
    lastRun: '2025-01-30 14:30',
    nextRun: '2025-01-31 14:30',
    discovered: 45,
    newDiscovered: 3,
    duration: '5分钟'
  },
  {
    id: 2,
    name: '服务器SNMP发现',
    type: 'snmp',
    protocol: 'SNMP',
    range: '********/24',
    status: 'running',
    progress: 65,
    lastRun: '2025-01-30 16:00',
    nextRun: '-',
    discovered: 12,
    newDiscovered: 0,
    duration: '运行中'
  },
  {
    id: 3,
    name: 'Windows服务器WMI扫描',
    type: 'wmi',
    protocol: 'WMI',
    range: '********/24',
    status: 'failed',
    progress: 25,
    lastRun: '2025-01-30 12:15',
    nextRun: '2025-01-30 18:15',
    discovered: 8,
    newDiscovered: 0,
    duration: '失败'
  },
  {
    id: 4,
    name: 'Linux服务器SSH发现',
    type: 'ssh',
    protocol: 'SSH',
    range: '10.0.3.1-50',
    status: 'completed',
    progress: 100,
    lastRun: '2025-01-30 10:00',
    nextRun: '2025-01-31 10:00',
    discovered: 23,
    newDiscovered: 1,
    duration: '8分钟'
  },
  {
    id: 5,
    name: 'API接口发现',
    type: 'api',
    protocol: 'HTTP',
    range: 'cloud.example.com',
    status: 'stopped',
    progress: 0,
    lastRun: '2025-01-29 20:00',
    nextRun: '手动触发',
    discovered: 67,
    newDiscovered: 0,
    duration: '12分钟'
  }
])

// 最新发现结果
const recentResults = ref([
  {
    id: 1,
    name: 'PC-001',
    ip: '***********00',
    type: '工作站',
    os: 'Windows 10',
    status: '在线',
    discoveredTime: '2025-01-30 16:45'
  },
  {
    id: 2,
    name: 'SRV-DB-01',
    ip: '*********',
    type: '服务器',
    os: 'Linux CentOS 7',
    status: '在线',
    discoveredTime: '2025-01-30 16:30'
  }
])

// 计划任务数据
const scheduledTasks = ref([
  { name: '办公网络扫描', schedule: '每日 14:30', enabled: true },
  { name: '服务器健康检查', schedule: '每小时', enabled: true },
  { name: '网络设备发现', schedule: '每周一 02:00', enabled: false }
])

// 任务表单
const taskForm = reactive({
  name: '',
  type: '',
  ipRange: '',
  portRange: '',
  username: '',
  password: '',
  scheduleType: 'once',
  scheduleTime: null
})

// 表单验证规则
const taskRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择发现类型', trigger: 'change' }
  ],
  ipRange: [
    { required: true, message: '请输入IP范围', trigger: 'blur' }
  ]
}

// 发现设置
const discoverySettings = reactive({
  threads: 10,
  timeout: 30,
  retries: 3,
  autoAddToCMDB: false,
  logLevel: 'info'
})

// 计算属性
const filteredTasks = computed(() => {
  let tasks = discoveryTasks.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    tasks = tasks.filter(task =>
      task.name.toLowerCase().includes(keyword) ||
      task.range.toLowerCase().includes(keyword)
    )
  }

  // 按状态筛选
  if (selectedStatus.value) {
    tasks = tasks.filter(task => task.status === selectedStatus.value)
  }

  // 按类型筛选
  if (selectedType.value) {
    tasks = tasks.filter(task => task.type === selectedType.value)
  }

  totalItems.value = tasks.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tasks.slice(start, end)
})

// 开始发现
const startDiscovery = () => {
  ElMessageBox.confirm(
    '确定要开始自动发现吗？这可能需要一些时间。',
    '确认发现',
    {
      confirmButtonText: '开始',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    loading.value = true
    ElMessage.success('发现任务已启动')

    // 模拟发现过程
    setTimeout(() => {
      loading.value = false
      ElMessage.success('发现任务完成，发现了3个新设备')
      // 更新统计数据
      discoveryStats.value[3].value = '11'
    }, 3000)
  }).catch(() => {
    ElMessage.info('已取消发现')
  })
}

// 创建发现任务
const createDiscoveryTask = () => {
  showTaskDialog.value = true
  // 重置表单
  Object.keys(taskForm).forEach(key => {
    if (key === 'scheduleType') {
      taskForm[key] = 'once'
    } else if (key === 'scheduleTime') {
      taskForm[key] = null
    } else {
      taskForm[key] = ''
    }
  })
}

// 新增方法
const viewDiscoveryReport = () => {
  ElMessage.info('打开发现报告页面')
  // 这里可以实现跳转到报告页面
}

const toggleProtocol = (protocol) => {
  protocol.enabled = !protocol.enabled
  updateProtocol(protocol)
}

const updateProtocol = (protocol) => {
  ElMessage.success(`${protocol.name} 协议已${protocol.enabled ? '启用' : '禁用'}`)
  // 这里可以实现协议配置更新逻辑
}

const configureProtocols = () => {
  ElMessage.info('打开协议配置页面')
  // 这里可以实现协议配置逻辑
}

const manageStrategies = () => {
  ElMessage.info('打开策略管理页面')
  // 这里可以实现策略管理逻辑
}

const editStrategy = (strategy) => {
  ElMessage.info(`编辑策略: ${strategy.name}`)
  // 这里可以实现策略编辑逻辑
}

const runStrategy = (strategy) => {
  ElMessage.success(`策略 "${strategy.name}" 已启动`)
  // 这里可以实现策略运行逻辑
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleStatusFilter = () => {
  currentPage.value = 1
}

const handleTypeFilter = () => {
  currentPage.value = 1
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const viewTaskDetails = (task) => {
  // 打开任务详情对话框
  showTaskDetailsDialog.value = true
  selectedTask.value = task
}

// 添加任务详情相关的响应式数据
const showTaskDetailsDialog = ref(false)
const selectedTask = ref({})

const cloneTask = (task) => {
  ElMessage.success(`任务 "${task.name}" 已克隆`)
  // 这里可以实现任务克隆逻辑
}

const handleTaskAction = (command) => {
  const { action, task } = command

  switch (action) {
    case 'schedule':
      ElMessage.info(`设置计划任务: ${task.name}`)
      break
    case 'export':
      ElMessage.success(`导出任务配置: ${task.name}`)
      break
    case 'logs':
      ElMessage.info(`查看任务日志: ${task.name}`)
      break
    case 'stop':
      task.status = 'stopped'
      ElMessage.warning(`任务 "${task.name}" 已停止`)
      break
    case 'delete':
      ElMessageBox.confirm(
        `确定要删除任务 "${task.name}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        const index = discoveryTasks.value.findIndex(t => t.id === task.id)
        if (index > -1) {
          discoveryTasks.value.splice(index, 1)
          ElMessage.success(`任务 "${task.name}" 已删除`)
        }
      }).catch(() => {
        ElMessage.info('已取消删除')
      })
      break
  }
}

// 运行任务
const runTask = (task) => {
  if (task.status === 'running') {
    ElMessage.warning('任务正在运行中')
    return
  }

  task.status = 'running'
  task.progress = 0
  ElMessage.success(`任务 "${task.name}" 已启动`)

  // 模拟任务进度
  const progressInterval = setInterval(() => {
    task.progress += Math.random() * 20
    if (task.progress >= 100) {
      task.progress = 100
      task.status = 'completed'
      task.lastRun = new Date().toLocaleString()
      task.newDiscovered = Math.floor(Math.random() * 5)
      clearInterval(progressInterval)
      ElMessage.success(`任务 "${task.name}" 已完成`)
    }
  }, 1000)

  // 模拟任务执行
  setTimeout(() => {
    task.status = '已完成'
    task.lastRun = new Date().toLocaleString()
    task.discovered += Math.floor(Math.random() * 5) + 1
    ElMessage.success(`任务 "${task.name}" 执行完成`)
  }, 2000)
}

// 查看结果
const viewResults = (task) => {
  // 跳转到CMDB概览页面，显示该任务发现的配置项
  window.open(`/cmdb?discoveryTask=${task.id}`, '_blank')
}

// 编辑任务
const editTask = (task) => {
  ElMessage.info(`编辑任务: ${task.name}`)
  // 这里可以实现编辑任务的逻辑
}

// 删除任务
const deleteTask = (task) => {
  ElMessageBox.confirm(
    `确定要删除任务 "${task.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = discoveryTasks.value.findIndex(item => item.id === task.id)
    if (index > -1) {
      discoveryTasks.value.splice(index, 1)
      ElMessage.success('任务已删除')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 辅助函数
const getTypeText = (type) => {
  const typeMap = {
    'network': '网络扫描',
    'snmp': 'SNMP发现',
    'wmi': 'WMI发现',
    'ssh': 'SSH发现',
    'api': 'API发现',
    'agent': 'Agent发现'
  }
  return typeMap[type] || type
}

const getStatusText = (status) => {
  const statusMap = {
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败',
    'stopped': '已停止'
  }
  return statusMap[status] || status
}

const getTypeColor = (type) => {
  const colorMap = {
    'network': 'primary',
    'snmp': 'success',
    'wmi': 'warning',
    'ssh': 'info',
    'api': 'danger',
    'agent': ''
  }
  return colorMap[type] || 'info'
}

const getStatusColor = (status) => {
  const colorMap = {
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'stopped': 'info'
  }
  return colorMap[status] || 'info'
}

// 查看全部结果
const viewAllResults = () => {
  ElMessage.info('跳转到发现结果页面')
  // 这里可以实现路由跳转
}

// 添加到CMDB
const addToCMDB = (device) => {
  ElMessageBox.confirm(
    `确定要将设备 "${device.name}" 添加到CMDB吗？`,
    '确认添加',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    ElMessage.success(`设备 "${device.name}" 已添加到CMDB`)
    // 这里可以实现添加到CMDB的逻辑
  }).catch(() => {
    ElMessage.info('已取消添加')
  })
}

// 查看设备详情
const viewDetails = (device) => {
  ElMessage.info(`查看设备详情: ${device.name}`)
  // 这里可以实现查看详情的逻辑
}

// 保存任务
const saveTask = () => {
  taskFormRef.value.validate((valid) => {
    if (valid) {
      const newTask = {
        id: Date.now(),
        name: taskForm.name,
        type: taskForm.type,
        range: taskForm.ipRange,
        status: '待运行',
        lastRun: '-',
        nextRun: taskForm.scheduleType === 'once' ? '-' : '计划中',
        discovered: 0
      }

      discoveryTasks.value.unshift(newTask)
      ElMessage.success('发现任务已创建')
      showTaskDialog.value = false
    }
  })
}

// 切换计划任务状态
const toggleSchedule = (task) => {
  const status = task.enabled ? '启用' : '禁用'
  ElMessage.success(`计划任务 "${task.name}" 已${status}`)
}

// 保存设置
const saveSettings = () => {
  ElMessage.success('发现设置已保存')
  showSettingsDialog.value = false
  // 这里可以实现保存设置的逻辑
}



// 对话框关闭处理
const handleClose = (done) => {
  ElMessageBox.confirm('确认关闭？未保存的内容将丢失。')
    .then(() => {
      done()
    })
    .catch(() => {
      // 取消关闭
    })
}
</script>

<style scoped>
.auto-discovery {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-content h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-content p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 发现概览样式 */
.discovery-overview {
  margin-bottom: 20px;
}

.stat-card {
  height: 90px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 12px;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
}

/* 协议和策略样式 */
.discovery-protocols {
  margin-bottom: 20px;
}

.protocol-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.protocol-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.protocol-item:hover {
  background: #f8f9fa;
  border-color: #1976D2;
}

.protocol-item.active {
  background: #e3f2fd;
  border-color: #1976D2;
}

.protocol-icon {
  margin-right: 12px;
}

.protocol-info {
  flex: 1;
}

.protocol-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.protocol-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.protocol-stats {
  font-size: 11px;
  color: #4CAF50;
}

.protocol-status {
  margin-left: 12px;
}

.strategy-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.strategy-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.strategy-name {
  font-weight: 500;
  color: #333;
}

.strategy-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.strategy-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  gap: 4px;
  font-size: 12px;
}

.stat-label {
  color: #666;
}

.stat-value {
  color: #333;
  font-weight: 500;
}

.strategy-actions {
  display: flex;
  gap: 8px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 12px;
}

/* 发现任务样式 */
.discovery-tasks {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 发现数量样式 */
.discovery-count {
  position: relative;
  font-weight: 500;
}

.discovery-count.has-new {
  color: #4CAF50;
}

.new-badge {
  position: absolute;
  top: -8px;
  right: -12px;
  background: #FF5722;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
}

/* 发现结果样式 */
.discovery-results {
  margin-bottom: 20px;
}

/* 计划任务内容样式 */
.schedule-content {
  padding: 20px 0;
}

.schedule-content p {
  color: #666;
  margin-bottom: 20px;
}

/* 表单帮助文本 */
.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-button--text {
  padding: 4px 8px;
}

.el-progress {
  margin: 0;
}

.el-progress__text {
  font-size: 12px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .protocol-grid {
    grid-template-columns: 1fr;
  }

  .strategy-stats {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .auto-discovery {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stat-card {
    margin-bottom: 12px;
    height: 80px;
  }

  .stat-value {
    font-size: 16px;
  }

  .header-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .header-controls .el-input,
  .header-controls .el-select {
    width: 100% !important;
  }

  .protocol-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .protocol-info {
    text-align: center;
  }

  .strategy-item {
    padding: 12px;
  }

  .strategy-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .strategy-stats {
    flex-direction: column;
    gap: 4px;
  }

  .strategy-actions {
    justify-content: center;
  }

  /* 表格在移动端的优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* 对话框在移动端的优化 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .protocol-item {
    padding: 8px;
  }

  .strategy-item {
    padding: 8px;
  }
}

/* 动画效果 */
.stat-card,
.protocol-item,
.strategy-item {
  transition: all 0.3s ease;
}

.protocol-item:hover,
.strategy-item:hover {
  transform: translateY(-1px);
}

/* 加载状态 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa;
}

/* 进度条样式 */
.el-progress--line {
  margin: 0;
}

.el-progress-bar__outer {
  border-radius: 10px;
}

.el-progress-bar__inner {
  border-radius: 10px;
}

/* 表格操作按钮样式 */
.el-table .el-button[disabled] {
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auto-discovery {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stat-card {
    margin-bottom: 12px;
  }

  /* 表格在移动端的优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-button {
    padding: 4px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .stat-value {
    font-size: 16px;
  }

  .stat-label {
    font-size: 11px;
  }
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 18px;
}

.el-input-number {
  width: 120px;
}

/* 标签样式 */
.el-tag {
  font-weight: 500;
}

/* 开关样式 */
.el-switch {
  --el-switch-on-color: #4CAF50;
}

/* 加载状态 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa;
}

/* 按钮组样式 */
.el-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

/* 时间选择器样式 */
.el-time-picker {
  width: 100%;
}
</style>
