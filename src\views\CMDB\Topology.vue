<template>
  <div class="topology">
    <div class="page-header">
      <h2>拓扑图</h2>
      <p>IT基础设施拓扑关系图</p>
      <div class="header-actions">
        <el-button type="primary" @click="refreshTopology">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="exportTopology">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-button @click="showSettings = true">
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="topology-toolbar">
      <el-card>
        <div class="toolbar-content">
          <div class="view-controls">
            <el-button-group>
              <el-button
                :type="viewMode === 'network' ? 'primary' : ''"
                @click="setViewMode('network')"
              >
                <el-icon><Link /></el-icon>
                网络视图
              </el-button>
              <el-button
                :type="viewMode === 'service' ? 'primary' : ''"
                @click="setViewMode('service')"
              >
                <el-icon><Tools /></el-icon>
                服务视图
              </el-button>
              <el-button
                :type="viewMode === 'application' ? 'primary' : ''"
                @click="setViewMode('application')"
              >
                <el-icon><Monitor /></el-icon>
                应用视图
              </el-button>
            </el-button-group>
          </div>

          <div class="filter-controls">
            <el-select v-model="selectedLayer" placeholder="选择层级" style="width: 150px" @change="filterByLayer">
              <el-option label="全部层级" value="" />
              <el-option label="业务层" value="business" />
              <el-option label="应用层" value="application" />
              <el-option label="中间件层" value="middleware" />
              <el-option label="操作系统层" value="os" />
              <el-option label="硬件层" value="hardware" />
            </el-select>

            <el-input
              v-model="searchKeyword"
              placeholder="搜索节点..."
              style="width: 200px"
              prefix-icon="Search"
              @input="searchNodes"
            />
          </div>

          <div class="zoom-controls">
            <el-button-group>
              <el-button @click="zoomIn">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
              <el-button @click="zoomOut">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetZoom">
                <el-icon><FullScreen /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 拓扑图主体 -->
    <div class="topology-main">
      <el-row :gutter="20">
        <!-- 拓扑图画布 -->
        <el-col :span="18">
          <el-card class="topology-canvas">
            <div ref="topologyContainer" class="topology-container" @click="handleCanvasClick">
              <!-- 这里将渲染拓扑图 -->
              <div class="topology-placeholder" v-if="!topologyData.nodes.length">
                <el-icon :size="64" color="#C0C4CC">
                  <Connection />
                </el-icon>
                <p>暂无拓扑数据</p>
                <el-button type="primary" @click="loadSampleData">加载示例数据</el-button>
              </div>

              <!-- 增强的拓扑图展示 -->
              <div v-else class="topology-graph" :style="{ transform: `scale(${zoomLevel}) translate(${panX}px, ${panY}px)` }">
                <!-- 节点 -->
                <div
                  v-for="node in filteredNodes"
                  :key="node.id"
                  class="topology-node"
                  :class="[
                    `node-${node.type}`,
                    {
                      'node-selected': selectedNode?.id === node.id,
                      'node-highlighted': highlightedNodes.includes(node.id),
                      'node-dimmed': isDimmed && !highlightedNodes.includes(node.id)
                    }
                  ]"
                  :style="{
                    left: node.x + 'px',
                    top: node.y + 'px',
                    zIndex: selectedNode?.id === node.id ? 1000 : node.layer * 10
                  }"
                  @click="selectNode(node)"
                  @mouseenter="handleNodeHover(node, true)"
                  @mouseleave="handleNodeHover(node, false)"
                >
                  <div class="node-icon">
                    <el-icon :size="getNodeSize(node)">
                      <component :is="getNodeIcon(node.type)" />
                    </el-icon>
                  </div>
                  <div class="node-label" v-if="settings.showLabels">{{ node.name }}</div>
                  <div class="node-status" :class="`status-${node.status}`"></div>

                  <!-- 节点详情悬浮提示 -->
                  <div class="node-tooltip" v-if="hoveredNode?.id === node.id">
                    <div class="tooltip-title">{{ node.name }}</div>
                    <div class="tooltip-info">
                      <div>类型: {{ getTypeText(node.type) }}</div>
                      <div>状态: {{ node.status }}</div>
                      <div v-if="node.ip">IP: {{ node.ip }}</div>
                      <div v-if="node.owner">负责人: {{ node.owner }}</div>
                    </div>
                  </div>
                </div>

                <!-- 连接线 -->
                <svg class="topology-connections" :width="canvasWidth" :height="canvasHeight" v-if="settings.showConnections">
                  <defs>
                    <!-- 箭头标记 -->
                    <marker
                      v-for="type in connectionTypes"
                      :key="`arrow-${type}`"
                      :id="`arrow-${type}`"
                      markerWidth="10"
                      markerHeight="10"
                      refX="8"
                      refY="3"
                      orient="auto"
                      markerUnits="strokeWidth"
                    >
                      <path d="M0,0 L0,6 L9,3 z" :fill="getConnectionColor(type)" />
                    </marker>
                  </defs>

                  <line
                    v-for="connection in visibleConnections"
                    :key="`${connection.source}-${connection.target}`"
                    :x1="connection.x1"
                    :y1="connection.y1"
                    :x2="connection.x2"
                    :y2="connection.y2"
                    :class="[
                      `connection-${connection.type}`,
                      {
                        'connection-highlighted': highlightedConnections.includes(connection.id),
                        'connection-dimmed': isDimmed && !highlightedConnections.includes(connection.id)
                      }
                    ]"
                    :marker-end="`url(#arrow-${connection.type})`"
                    :stroke-width="connection.highlighted ? 3 : 2"
                  />

                  <!-- 连接线标签 -->
                  <text
                    v-for="connection in visibleConnections"
                    :key="`label-${connection.source}-${connection.target}`"
                    :x="(connection.x1 + connection.x2) / 2"
                    :y="(connection.y1 + connection.y2) / 2"
                    class="connection-label"
                    v-if="connection.label && settings.showConnectionLabels"
                  >
                    {{ connection.label }}
                  </text>
                </svg>

                <!-- 影响分析高亮区域 -->
                <div
                  v-if="impactAnalysis.active"
                  class="impact-overlay"
                  :style="{
                    left: impactAnalysis.x + 'px',
                    top: impactAnalysis.y + 'px',
                    width: impactAnalysis.width + 'px',
                    height: impactAnalysis.height + 'px'
                  }"
                >
                  <div class="impact-title">影响分析</div>
                  <div class="impact-stats">
                    <span>影响节点: {{ impactAnalysis.affectedNodes }}</span>
                    <span>影响服务: {{ impactAnalysis.affectedServices }}</span>
                  </div>
                </div>
              </div>

              <!-- 小地图 -->
              <div class="topology-minimap" v-if="settings.showMinimap">
                <div class="minimap-viewport" :style="minimapViewportStyle"></div>
                <svg class="minimap-content" width="150" height="100">
                  <circle
                    v-for="node in topologyData.nodes"
                    :key="`mini-${node.id}`"
                    :cx="node.x * 0.15"
                    :cy="node.y * 0.15"
                    r="2"
                    :fill="getNodeColor(node.type)"
                  />
                </svg>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 侧边栏 -->
        <el-col :span="6">
          <el-card class="topology-sidebar">
            <template #header>
              <div class="sidebar-header">
                <span>{{ selectedNode ? '节点详情' : '图例与工具' }}</span>
                <el-button-group size="small" v-if="selectedNode">
                  <el-button @click="analyzeImpact(selectedNode)">
                    <el-icon><TrendCharts /></el-icon>
                  </el-button>
                  <el-button @click="showRelatedNodes(selectedNode)">
                    <el-icon><Link /></el-icon>
                  </el-button>
                </el-button-group>
              </div>
            </template>

            <!-- 节点详情 -->
            <div v-if="selectedNode" class="node-details">
              <div class="detail-header">
                <div class="node-avatar">
                  <el-icon :size="32" :color="getNodeColor(selectedNode.type)">
                    <component :is="getNodeIcon(selectedNode.type)" />
                  </el-icon>
                </div>
                <div class="node-basic-info">
                  <div class="node-name">{{ selectedNode.name }}</div>
                  <el-tag :type="getStatusColor(selectedNode.status)" size="small">
                    {{ selectedNode.status }}
                  </el-tag>
                </div>
              </div>

              <el-divider />

              <div class="detail-sections">
                <el-collapse v-model="activeDetailSections">
                  <el-collapse-item title="基本信息" name="basic">
                    <div class="detail-item">
                      <label>类型:</label>
                      <el-tag size="small">{{ getTypeText(selectedNode.type) }}</el-tag>
                    </div>
                    <div class="detail-item">
                      <label>IP地址:</label>
                      <span>{{ selectedNode.ip || 'N/A' }}</span>
                    </div>
                    <div class="detail-item">
                      <label>负责人:</label>
                      <span>{{ selectedNode.owner || 'N/A' }}</span>
                    </div>
                    <div class="detail-item">
                      <label>层级:</label>
                      <span>{{ getLayerText(selectedNode.layer) }}</span>
                    </div>
                    <div class="detail-item">
                      <label>描述:</label>
                      <span>{{ selectedNode.description || 'N/A' }}</span>
                    </div>
                  </el-collapse-item>

                  <el-collapse-item title="关系信息" name="relations">
                    <div class="relations-info">
                      <div class="relation-stats">
                        <div class="stat-item">
                          <span class="stat-label">上游依赖:</span>
                          <span class="stat-value">{{ getUpstreamCount(selectedNode.id) }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-label">下游影响:</span>
                          <span class="stat-value">{{ getDownstreamCount(selectedNode.id) }}</span>
                        </div>
                      </div>
                      <el-button type="text" size="small" @click="showRelationDetails(selectedNode)">
                        查看详细关系
                      </el-button>
                    </div>
                  </el-collapse-item>

                  <el-collapse-item title="健康状态" name="health">
                    <div class="health-info">
                      <div class="health-score">
                        <el-progress
                          type="circle"
                          :percentage="selectedNode.healthScore || 85"
                          :width="60"
                          :color="getHealthColor(selectedNode.healthScore || 85)"
                        />
                        <div class="health-label">健康分</div>
                      </div>
                      <div class="health-metrics">
                        <div class="metric-item">
                          <span>可用性:</span>
                          <span>{{ selectedNode.availability || '99.9%' }}</span>
                        </div>
                        <div class="metric-item">
                          <span>性能:</span>
                          <span>{{ selectedNode.performance || '良好' }}</span>
                        </div>
                        <div class="metric-item">
                          <span>最后检查:</span>
                          <span>{{ selectedNode.lastCheck || '2分钟前' }}</span>
                        </div>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>

              <div class="detail-actions">
                <el-button type="primary" size="small" @click="editNode(selectedNode)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button size="small" @click="viewNodeHistory(selectedNode)">
                  <el-icon><Timer /></el-icon>
                  历史
                </el-button>
                <el-button size="small" @click="analyzeImpact(selectedNode)">
                  <el-icon><TrendCharts /></el-icon>
                  影响分析
                </el-button>
              </div>
            </div>

            <!-- 图例和工具 -->
            <div v-else class="topology-legend">
              <el-tabs v-model="activeLegendTab" size="small">
                <el-tab-pane label="图例" name="legend">
                  <div class="legend-section">
                    <h4>节点类型</h4>
                    <div class="legend-item" v-for="type in nodeTypes" :key="type.value">
                      <el-icon :size="16" :color="getNodeColor(type.value)">
                        <component :is="type.icon" />
                      </el-icon>
                      <span>{{ type.label }}</span>
                    </div>
                  </div>

                  <div class="legend-section">
                    <h4>状态说明</h4>
                    <div class="legend-item">
                      <div class="status-indicator status-normal"></div>
                      <span>正常</span>
                    </div>
                    <div class="legend-item">
                      <div class="status-indicator status-warning"></div>
                      <span>警告</span>
                    </div>
                    <div class="legend-item">
                      <div class="status-indicator status-error"></div>
                      <span>故障</span>
                    </div>
                  </div>

                  <div class="legend-section">
                    <h4>连接类型</h4>
                    <div class="legend-item" v-for="type in connectionTypes" :key="type">
                      <div class="connection-sample" :class="`connection-${type}`"></div>
                      <span>{{ getConnectionText(type) }}</span>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="统计" name="stats">
                  <div class="topology-stats">
                    <div class="stat-card">
                      <div class="stat-number">{{ topologyData.nodes.length }}</div>
                      <div class="stat-label">总节点数</div>
                    </div>
                    <div class="stat-card">
                      <div class="stat-number">{{ topologyData.connections.length }}</div>
                      <div class="stat-label">连接数</div>
                    </div>
                    <div class="stat-card">
                      <div class="stat-number">{{ getLayerCount() }}</div>
                      <div class="stat-label">层级数</div>
                    </div>
                    <div class="stat-card">
                      <div class="stat-number">{{ getHealthyNodeCount() }}</div>
                      <div class="stat-label">健康节点</div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="工具" name="tools">
                  <div class="topology-tools">
                    <el-button size="small" @click="autoLayout" style="width: 100%; margin-bottom: 8px">
                      <el-icon><Refresh /></el-icon>
                      自动布局
                    </el-button>
                    <el-button size="small" @click="centerView" style="width: 100%; margin-bottom: 8px">
                      <el-icon><Aim /></el-icon>
                      居中显示
                    </el-button>
                    <el-button size="small" @click="fitToScreen" style="width: 100%; margin-bottom: 8px">
                      <el-icon><FullScreen /></el-icon>
                      适应屏幕
                    </el-button>
                    <el-button size="small" @click="exportTopology" style="width: 100%; margin-bottom: 8px">
                      <el-icon><Download /></el-icon>
                      导出图片
                    </el-button>

                    <el-divider />

                    <div class="tool-section">
                      <h5>智能推荐</h5>
                      <el-button size="small" type="text" @click="suggestRelations">
                        <el-icon><Link /></el-icon>
                        关系推荐
                      </el-button>
                      <el-button size="small" type="text" @click="detectAnomalies">
                        <el-icon><Warning /></el-icon>
                        异常检测
                      </el-button>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 设置对话框 -->
    <el-dialog v-model="showSettings" title="拓扑图设置" width="50%">
      <el-form :model="settings" label-width="120px">
        <el-form-item label="自动刷新">
          <el-switch v-model="settings.autoRefresh" />
          <span class="form-help">每5分钟自动刷新拓扑数据</span>
        </el-form-item>
        <el-form-item label="显示连接线">
          <el-switch v-model="settings.showConnections" />
        </el-form-item>
        <el-form-item label="显示节点标签">
          <el-switch v-model="settings.showLabels" />
        </el-form-item>
        <el-form-item label="布局算法">
          <el-select v-model="settings.layoutAlgorithm" style="width: 100%">
            <el-option label="力导向布局" value="force" />
            <el-option label="层次布局" value="hierarchical" />
            <el-option label="圆形布局" value="circular" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSettings = false">取消</el-button>
          <el-button type="primary" @click="saveSettings">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 视图状态
const viewMode = ref('network')
const selectedLayer = ref('')
const searchKeyword = ref('')
const selectedNode = ref(null)
const hoveredNode = ref(null)
const showSettings = ref(false)

// 画布尺寸和视图控制
const canvasWidth = ref(800)
const canvasHeight = ref(600)
const topologyContainer = ref()
const zoomLevel = ref(1)
const panX = ref(0)
const panY = ref(0)
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })

// 高亮和影响分析
const highlightedNodes = ref([])
const highlightedConnections = ref([])
const isDimmed = ref(false)
const impactAnalysis = reactive({
  active: false,
  x: 0,
  y: 0,
  width: 0,
  height: 0,
  affectedNodes: 0,
  affectedServices: 0
})

// 侧边栏状态
const activeDetailSections = ref(['basic'])
const activeLegendTab = ref('legend')

// 拓扑数据
const topologyData = reactive({
  nodes: [],
  connections: []
})

// 设置
const settings = reactive({
  autoRefresh: true,
  showConnections: true,
  showLabels: true,
  showConnectionLabels: false,
  showMinimap: true,
  layoutAlgorithm: 'force',
  animationEnabled: true
})

// 连接类型
const connectionTypes = ['depends', 'connects', 'contains', 'monitors']

// 节点类型定义
const nodeTypes = [
  { value: 'server', label: '服务器', icon: 'Monitor' },
  { value: 'database', label: '数据库', icon: 'Coin' },
  { value: 'network', label: '网络设备', icon: 'Share' },
  { value: 'application', label: '应用系统', icon: 'Service' },
  { value: 'storage', label: '存储设备', icon: 'Box' }
]

// 过滤后的节点
const filteredNodes = computed(() => {
  let nodes = topologyData.nodes

  // 按层级过滤
  if (selectedLayer.value) {
    nodes = nodes.filter(node => node.layer === selectedLayer.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    nodes = nodes.filter(node =>
      node.name.toLowerCase().includes(keyword) ||
      node.type.toLowerCase().includes(keyword)
    )
  }

  return nodes
})

// 可见的连接线
const visibleConnections = computed(() => {
  return topologyData.connections.filter(conn => {
    const sourceNode = topologyData.nodes.find(n => n.id === conn.source)
    const targetNode = topologyData.nodes.find(n => n.id === conn.target)
    return sourceNode && targetNode &&
           filteredNodes.value.includes(sourceNode) &&
           filteredNodes.value.includes(targetNode)
  })
})

// 连接线数据（包含坐标）
const connections = computed(() => {
  return visibleConnections.value.map(conn => {
    const sourceNode = topologyData.nodes.find(n => n.id === conn.source)
    const targetNode = topologyData.nodes.find(n => n.id === conn.target)

    if (!sourceNode || !targetNode) return null

    return {
      ...conn,
      x1: sourceNode.x + 30, // 节点中心偏移
      y1: sourceNode.y + 30,
      x2: targetNode.x + 30,
      y2: targetNode.y + 30,
      highlighted: highlightedConnections.value.includes(conn.id)
    }
  }).filter(Boolean)
})

// 小地图视口样式
const minimapViewportStyle = computed(() => {
  return {
    left: (-panX.value * 0.15) + 'px',
    top: (-panY.value * 0.15) + 'px',
    width: (canvasWidth.value * 0.15 / zoomLevel.value) + 'px',
    height: (canvasHeight.value * 0.15 / zoomLevel.value) + 'px'
  }
})



// 刷新拓扑
const refreshTopology = () => {
  ElMessage.success('拓扑数据已刷新')
  loadSampleData()
}

// 导出拓扑
const exportTopology = () => {
  ElMessage.success('拓扑图导出中...')
  // 这里可以实现导出逻辑
}

// 设置视图模式
const setViewMode = (mode) => {
  viewMode.value = mode
  ElMessage.info(`切换到${mode === 'network' ? '网络' : mode === 'service' ? '服务' : '应用'}视图`)
  // 根据视图模式重新布局
  layoutNodes()
}

// 按层级过滤
const filterByLayer = () => {
  ElMessage.info(`过滤层级: ${selectedLayer.value || '全部'}`)
}

// 搜索节点
const searchNodes = () => {
  // 搜索逻辑已在computed中实现
}

// 缩放控制
const zoomIn = () => {
  ElMessage.info('放大')
}

const zoomOut = () => {
  ElMessage.info('缩小')
}

const resetZoom = () => {
  zoomLevel.value = 1
  panX.value = 0
  panY.value = 0
  ElMessage.info('重置缩放')
}

// 新增的增强方法
const handleNodeHover = (node, isEnter) => {
  if (isEnter) {
    hoveredNode.value = node
    // 高亮相关节点和连接
    highlightRelatedNodes(node.id)
  } else {
    hoveredNode.value = null
    clearHighlight()
  }
}

const highlightRelatedNodes = (nodeId) => {
  const relatedNodes = new Set([nodeId])
  const relatedConnections = []

  // 找到所有相关的连接和节点
  topologyData.connections.forEach(conn => {
    if (conn.source === nodeId || conn.target === nodeId) {
      relatedConnections.push(conn.id)
      relatedNodes.add(conn.source)
      relatedNodes.add(conn.target)
    }
  })

  highlightedNodes.value = Array.from(relatedNodes)
  highlightedConnections.value = relatedConnections
  isDimmed.value = true
}

const clearHighlight = () => {
  highlightedNodes.value = []
  highlightedConnections.value = []
  isDimmed.value = false
}

const analyzeImpact = (node) => {
  ElMessage.info(`分析节点 ${node.name} 的影响范围...`)

  // 模拟影响分析
  const affectedNodes = getDownstreamNodes(node.id)
  const affectedServices = affectedNodes.filter(n => n.type === 'application').length

  impactAnalysis.active = true
  impactAnalysis.x = node.x - 50
  impactAnalysis.y = node.y - 100
  impactAnalysis.width = 200
  impactAnalysis.height = 80
  impactAnalysis.affectedNodes = affectedNodes.length
  impactAnalysis.affectedServices = affectedServices

  // 高亮影响的节点
  highlightedNodes.value = affectedNodes.map(n => n.id)
  isDimmed.value = true

  // 3秒后自动清除
  setTimeout(() => {
    impactAnalysis.active = false
    clearHighlight()
  }, 3000)
}

const showRelatedNodes = (node) => {
  const relatedNodes = getRelatedNodes(node.id)
  highlightedNodes.value = relatedNodes.map(n => n.id)
  isDimmed.value = true

  ElMessage.success(`已高亮显示与 ${node.name} 相关的 ${relatedNodes.length} 个节点`)
}

const getUpstreamCount = (nodeId) => {
  return topologyData.connections.filter(conn => conn.target === nodeId).length
}

const getDownstreamCount = (nodeId) => {
  return topologyData.connections.filter(conn => conn.source === nodeId).length
}

const getDownstreamNodes = (nodeId) => {
  const downstream = []
  const visited = new Set()

  const traverse = (id) => {
    if (visited.has(id)) return
    visited.add(id)

    topologyData.connections.forEach(conn => {
      if (conn.source === id) {
        const targetNode = topologyData.nodes.find(n => n.id === conn.target)
        if (targetNode) {
          downstream.push(targetNode)
          traverse(conn.target)
        }
      }
    })
  }

  traverse(nodeId)
  return downstream
}

const getRelatedNodes = (nodeId) => {
  const related = []
  topologyData.connections.forEach(conn => {
    if (conn.source === nodeId) {
      const targetNode = topologyData.nodes.find(n => n.id === conn.target)
      if (targetNode) related.push(targetNode)
    }
    if (conn.target === nodeId) {
      const sourceNode = topologyData.nodes.find(n => n.id === conn.source)
      if (sourceNode) related.push(sourceNode)
    }
  })
  return related
}

const showRelationDetails = (node) => {
  ElMessage.info(`查看节点 ${node.name} 的详细关系信息`)
  // 这里可以打开关系详情对话框
}

const getLayerText = (layer) => {
  const layerMap = {
    1: '应用层',
    2: '服务层',
    3: '数据层',
    4: '基础设施层'
  }
  return layerMap[layer] || `第${layer}层`
}

const getHealthColor = (score) => {
  if (score >= 90) return '#4CAF50'
  if (score >= 70) return '#FF9800'
  return '#F44336'
}

const getNodeSize = (node) => {
  // 根据节点重要性调整大小
  const baseSize = 24
  const importance = node.importance || 1
  return baseSize + (importance - 1) * 4
}

const getNodeColor = (type) => {
  const colorMap = {
    'server': '#4CAF50',
    'database': '#2196F3',
    'network': '#FF9800',
    'application': '#9C27B0',
    'storage': '#607D8B'
  }
  return colorMap[type] || '#666'
}

const getConnectionColor = (type) => {
  const colorMap = {
    'depends': '#F44336',
    'connects': '#2196F3',
    'contains': '#4CAF50',
    'monitors': '#FF9800'
  }
  return colorMap[type] || '#666'
}

const getConnectionText = (type) => {
  const textMap = {
    'depends': '依赖关系',
    'connects': '连接关系',
    'contains': '包含关系',
    'monitors': '监控关系'
  }
  return textMap[type] || type
}

const getLayerCount = () => {
  const layers = new Set(topologyData.nodes.map(n => n.layer))
  return layers.size
}

const getHealthyNodeCount = () => {
  return topologyData.nodes.filter(n => n.status === 'normal').length
}

// 工具方法
const autoLayout = () => {
  ElMessage.info('正在自动布局...')
  layoutNodes()
}

const centerView = () => {
  panX.value = 0
  panY.value = 0
  zoomLevel.value = 1
  ElMessage.success('视图已居中')
}

const fitToScreen = () => {
  // 计算合适的缩放比例
  const containerRect = topologyContainer.value?.getBoundingClientRect()
  if (!containerRect) return

  const nodesBounds = getNodesBounds()
  const scaleX = containerRect.width / nodesBounds.width
  const scaleY = containerRect.height / nodesBounds.height
  const scale = Math.min(scaleX, scaleY, 2) * 0.8 // 留一些边距

  zoomLevel.value = scale
  panX.value = (containerRect.width - nodesBounds.width * scale) / 2
  panY.value = (containerRect.height - nodesBounds.height * scale) / 2

  ElMessage.success('已适应屏幕大小')
}

const getNodesBounds = () => {
  if (!topologyData.nodes.length) return { width: 800, height: 600 }

  const xs = topologyData.nodes.map(n => n.x)
  const ys = topologyData.nodes.map(n => n.y)

  return {
    width: Math.max(...xs) - Math.min(...xs) + 100,
    height: Math.max(...ys) - Math.min(...ys) + 100
  }
}

const suggestRelations = () => {
  ElMessage.info('正在分析可能的关系...')

  // 模拟智能关系推荐
  setTimeout(() => {
    ElMessage.success('发现3个可能的关系，请查看推荐面板')
    // 这里可以显示推荐的关系
  }, 2000)
}

const detectAnomalies = () => {
  ElMessage.info('正在检测异常...')

  // 模拟异常检测
  setTimeout(() => {
    const anomalies = topologyData.nodes.filter(n => n.status === 'error')
    if (anomalies.length > 0) {
      highlightedNodes.value = anomalies.map(n => n.id)
      isDimmed.value = true
      ElMessage.warning(`检测到${anomalies.length}个异常节点`)
    } else {
      ElMessage.success('未检测到异常')
    }
  }, 1500)
}

const getTypeText = (type) => {
  const textMap = {
    'server': '服务器',
    'database': '数据库',
    'network': '网络设备',
    'application': '应用系统',
    'storage': '存储设备'
  }
  return textMap[type] || type
}

// 处理画布点击
const handleCanvasClick = (event) => {
  // 如果点击的不是节点，则取消选择
  if (event.target.classList.contains('topology-container')) {
    selectedNode.value = null
  }
}

// 选择节点
const selectNode = (node) => {
  selectedNode.value = node
  ElMessage.info(`选择节点: ${node.name}`)
}

// 获取节点图标
const getNodeIcon = (type) => {
  const iconMap = {
    'server': 'Monitor',
    'database': 'Coin',
    'network': 'Share',
    'application': 'Service',
    'storage': 'Box'
  }
  return iconMap[type] || 'Box'
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'normal': 'success',
    'warning': 'warning',
    'error': 'danger'
  }
  return colorMap[status] || 'info'
}

// 编辑节点
const editNode = (node) => {
  // 跳转到CI详情页面进行编辑
  window.open(`/cmdb/ci/${node.id}`, '_blank')
}

// 查看节点历史
const viewNodeHistory = (node) => {
  // 跳转到版本控制页面查看该节点的历史
  window.open(`/cmdb/versions?ci=${node.id}`, '_blank')
}

// 保存设置
const saveSettings = () => {
  ElMessage.success('设置已保存')
  showSettings.value = false
  // 这里可以实现保存设置的逻辑
}

// 加载示例数据
const loadSampleData = () => {
  topologyData.nodes = [
    {
      id: 'web-server-1',
      name: 'WEB-SRV-01',
      type: 'server',
      status: 'normal',
      layer: 1,
      x: 200,
      y: 100,
      ip: '************',
      owner: '张工',
      description: 'Apache Web服务器',
      healthScore: 95,
      availability: '99.9%',
      performance: '良好',
      lastCheck: '2分钟前',
      importance: 3
    },
    {
      id: 'web-server-2',
      name: 'WEB-SRV-02',
      type: 'server',
      status: 'warning',
      layer: 1,
      x: 400,
      y: 100,
      ip: '************',
      owner: '张工',
      description: 'Nginx Web服务器',
      healthScore: 78,
      availability: '99.5%',
      performance: '一般',
      lastCheck: '5分钟前',
      importance: 2
    },
    {
      id: 'db-server-1',
      name: 'DB-PROD-01',
      type: 'database',
      status: 'normal',
      layer: 3,
      x: 300,
      y: 300,
      ip: '***********0',
      owner: '李工',
      description: 'MySQL数据库服务器',
      healthScore: 92,
      availability: '99.8%',
      performance: '优秀',
      lastCheck: '1分钟前',
      importance: 4
    },
    {
      id: 'load-balancer',
      name: 'LB-MAIN-01',
      type: 'network',
      status: 'warning',
      layer: 4,
      x: 300,
      y: 50,
      ip: '***********',
      owner: '王工',
      description: 'F5负载均衡器',
      healthScore: 85,
      availability: '99.7%',
      performance: '良好',
      lastCheck: '3分钟前',
      importance: 5
    },
    {
      id: 'app-server-1',
      name: 'APP-SRV-01',
      type: 'application',
      status: 'normal',
      layer: 2,
      x: 150,
      y: 200,
      ip: '************',
      owner: '赵工',
      description: 'ERP应用系统',
      healthScore: 88,
      availability: '99.6%',
      performance: '良好',
      lastCheck: '2分钟前',
      importance: 3
    },
    {
      id: 'app-server-2',
      name: 'APP-SRV-02',
      type: 'application',
      status: 'error',
      layer: 2,
      x: 450,
      y: 200,
      ip: '************',
      owner: '赵工',
      description: 'CRM应用系统',
      healthScore: 45,
      availability: '95.2%',
      performance: '差',
      lastCheck: '10分钟前',
      importance: 2
    },
    {
      id: 'storage-1',
      name: 'SAN-STOR-01',
      type: 'storage',
      status: 'normal',
      layer: 4,
      x: 300,
      y: 400,
      ip: '************',
      owner: '孙工',
      description: 'SAN存储设备',
      healthScore: 96,
      availability: '99.9%',
      performance: '优秀',
      lastCheck: '1分钟前',
      importance: 4
    },
    {
      id: 'firewall-1',
      name: 'FW-EDGE-01',
      type: 'network',
      status: 'normal',
      layer: 4,
      x: 100,
      y: 50,
      ip: '***********',
      owner: '周工',
      description: '边界防火墙',
      healthScore: 94,
      availability: '99.9%',
      performance: '优秀',
      lastCheck: '1分钟前',
      importance: 5
    },
    {
      id: 'switch-1',
      name: 'SW-CORE-01',
      type: 'network',
      status: 'normal',
      layer: 4,
      x: 500,
      y: 50,
      ip: '***********',
      owner: '吴工',
      description: '核心交换机',
      healthScore: 91,
      availability: '99.8%',
      performance: '良好',
      lastCheck: '2分钟前',
      importance: 4
    }
  ]

  topologyData.connections = [
    { id: 'conn-1', source: 'firewall-1', target: 'load-balancer', type: 'connects', label: '网络连接' },
    { id: 'conn-2', source: 'load-balancer', target: 'web-server-1', type: 'connects', label: '负载分发' },
    { id: 'conn-3', source: 'load-balancer', target: 'web-server-2', type: 'connects', label: '负载分发' },
    { id: 'conn-4', source: 'web-server-1', target: 'app-server-1', type: 'depends', label: '服务调用' },
    { id: 'conn-5', source: 'web-server-2', target: 'app-server-2', type: 'depends', label: '服务调用' },
    { id: 'conn-6', source: 'app-server-1', target: 'db-server-1', type: 'depends', label: '数据访问' },
    { id: 'conn-7', source: 'app-server-2', target: 'db-server-1', type: 'depends', label: '数据访问' },
    { id: 'conn-8', source: 'db-server-1', target: 'storage-1', type: 'contains', label: '数据存储' },
    { id: 'conn-9', source: 'switch-1', target: 'load-balancer', type: 'connects', label: '网络连接' },
    { id: 'conn-10', source: 'switch-1', target: 'web-server-1', type: 'connects', label: '网络连接' },
    { id: 'conn-11', source: 'switch-1', target: 'web-server-2', type: 'connects', label: '网络连接' }
  ]

  ElMessage.success('示例数据已加载')
}

// 节点布局
const layoutNodes = () => {
  // 简单的布局算法，实际项目中可以使用更复杂的布局算法
  const nodes = topologyData.nodes
  const centerX = canvasWidth.value / 2
  const centerY = canvasHeight.value / 2
  const radius = 150

  nodes.forEach((node, index) => {
    const angle = (2 * Math.PI * index) / nodes.length
    node.x = centerX + radius * Math.cos(angle) - 30
    node.y = centerY + radius * Math.sin(angle) - 30
  })
}

// 组件挂载时初始化
onMounted(() => {
  canvasWidth.value = topologyContainer.value?.offsetWidth || 800
  canvasHeight.value = topologyContainer.value?.offsetHeight || 600
})
</script>

<style scoped>
.topology {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 工具栏样式 */
.topology-toolbar {
  margin-bottom: 20px;
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.view-controls,
.filter-controls,
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 拓扑图主体样式 */
.topology-main {
  height: calc(100vh - 280px);
}

.topology-canvas {
  height: 100%;
}

.topology-container {
  position: relative;
  width: 100%;
  height: 500px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.topology-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.topology-placeholder p {
  margin: 16px 0;
  font-size: 16px;
}

/* 拓扑图节点样式 */
.topology-graph {
  position: relative;
  width: 100%;
  height: 100%;
}

.topology-node {
  position: absolute;
  width: 60px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.topology-node:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.topology-node.node-selected {
  border-color: #1976D2;
  background: #e3f2fd;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
}

.topology-node.node-highlighted {
  border-color: #FF5722;
  background: #fff3e0;
  box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.3);
}

.topology-node.node-dimmed {
  opacity: 0.3;
  filter: grayscale(50%);
}

.node-server {
  border-color: #4CAF50;
}

.node-database {
  border-color: #FF9800;
}

.node-network {
  border-color: #2196F3;
}

.node-application {
  border-color: #9C27B0;
}

.node-storage {
  border-color: #607D8B;
}

.node-icon {
  margin-bottom: 4px;
}

.node-label {
  font-size: 10px;
  text-align: center;
  line-height: 1.2;
  max-width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 节点提示框 */
.node-tooltip {
  position: absolute;
  top: -80px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

.node-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
}

.tooltip-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.tooltip-info div {
  margin: 2px 0;
}

.node-status {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-normal {
  background: #4CAF50;
}

.status-warning {
  background: #FF9800;
}

.status-error {
  background: #F44336;
}

/* 连接线样式 */
.topology-connections {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.topology-connections line {
  stroke: #bdbdbd;
  stroke-width: 2;
  transition: all 0.3s ease;
}

.connection-depends {
  stroke: #F44336;
  stroke-dasharray: 5,5;
}

.connection-connects {
  stroke: #2196F3;
}

.connection-contains {
  stroke: #4CAF50;
  stroke-width: 3;
}

.connection-monitors {
  stroke: #FF9800;
  stroke-dasharray: 3,3;
}

.connection-highlighted {
  stroke-width: 4;
  filter: drop-shadow(0 0 3px currentColor);
}

.connection-dimmed {
  opacity: 0.2;
}

.connection-label {
  font-size: 10px;
  fill: #666;
  text-anchor: middle;
  pointer-events: none;
}

/* 影响分析覆盖层 */
.impact-overlay {
  position: absolute;
  background: rgba(255, 87, 34, 0.1);
  border: 2px dashed #FF5722;
  border-radius: 8px;
  padding: 8px;
  z-index: 100;
  pointer-events: none;
}

.impact-title {
  font-size: 12px;
  font-weight: bold;
  color: #FF5722;
  margin-bottom: 4px;
}

.impact-stats {
  font-size: 10px;
  color: #666;
}

.impact-stats span {
  display: block;
  margin: 2px 0;
}

/* 小地图样式 */
.topology-minimap {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 150px;
  height: 100px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.minimap-viewport {
  position: absolute;
  border: 2px solid #1976D2;
  background: rgba(25, 118, 210, 0.1);
  pointer-events: none;
}

.minimap-content {
  width: 100%;
  height: 100%;
}

/* 侧边栏样式 */
.topology-sidebar {
  height: 100%;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.node-details {
  padding: 16px 0;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.node-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #f5f5f5;
  border-radius: 8px;
}

.node-basic-info {
  flex: 1;
}

.node-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.detail-sections {
  margin-top: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item label {
  font-weight: 500;
  color: #666;
  min-width: 60px;
}

.relations-info {
  padding: 12px 0;
}

.relation-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #1976D2;
}

.health-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
}

.health-score {
  text-align: center;
}

.health-label {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.health-metrics {
  flex: 1;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.detail-actions {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 图例样式 */
.topology-legend {
  padding: 16px 0;
}

.legend-section {
  margin-bottom: 24px;
}

.topology-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-number {
  font-size: 20px;
  font-weight: bold;
  color: #1976D2;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.topology-tools {
  padding: 12px 0;
}

.tool-section {
  margin-top: 16px;
}

.tool-section h5 {
  font-size: 14px;
  color: #333;
  margin: 0 0 8px 0;
}

.connection-sample {
  width: 20px;
  height: 2px;
  margin-right: 8px;
  display: inline-block;
}

.legend-section h4 {
  color: #333;
  margin-bottom: 12px;
  font-size: 14px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

/* 表单帮助文本 */
.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .topology-main .el-col:first-child {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .topology {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .toolbar-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .topology-container {
    height: 400px;
  }

  .topology-node {
    width: 50px;
    height: 50px;
  }

  .node-label {
    font-size: 9px;
    max-width: 40px;
  }
}
</style>
