<template>
  <div class="escalation-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Top /></el-icon>
            升级管理
          </h1>
          <p class="page-description">管理事件升级流程、规则和通知设置</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showCreateRuleDialog">
            <el-icon><Plus /></el-icon>
            新建升级规则
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="stat in escalationStats" :key="stat.key">
          <div class="stat-card" :class="stat.type">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="28">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trend">
                  <el-icon :size="12">
                    <component :is="stat.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                  {{ stat.change }}
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" class="escalation-tabs">
        <!-- 升级规则 -->
        <el-tab-pane label="升级规则" name="rules">
          <div class="rules-section">
            <div class="section-header">
              <h3>升级规则配置</h3>
              <el-button type="text" @click="showCreateRuleDialog">
                <el-icon><Setting /></el-icon>
                配置规则
              </el-button>
            </div>
            
            <div class="rules-table">
              <el-table :data="escalationRules" style="width: 100%" stripe>
                <el-table-column prop="name" label="规则名称" width="200">
                  <template #default="scope">
                    <div class="rule-name">
                      <el-icon class="rule-icon"><Timer /></el-icon>
                      {{ scope.row.name }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="priority" label="优先级" width="120">
                  <template #default="scope">
                    <el-tag :type="getPriorityType(scope.row.priority)">
                      {{ scope.row.priority }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="condition" label="触发条件" width="250"></el-table-column>
                <el-table-column prop="escalateTime" label="升级时间" width="120"></el-table-column>
                <el-table-column prop="targetGroup" label="目标组" width="150"></el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.status"
                      :active-value="true"
                      :inactive-value="false"
                      @change="toggleRuleStatus(scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button type="text" size="small" @click="editRule(scope.row)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button type="text" size="small" @click="deleteRule(scope.row)" class="delete-btn">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 升级历史 -->
        <el-tab-pane label="升级历史" name="history">
          <div class="history-section">
            <div class="section-header">
              <h3>升级历史记录</h3>
              <div class="filter-controls">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                  @change="filterHistory"
                />
                <el-select v-model="statusFilter" placeholder="状态筛选" size="small" style="width: 120px;">
                  <el-option label="全部" value="" />
                  <el-option label="成功" value="success" />
                  <el-option label="失败" value="failed" />
                </el-select>
              </div>
            </div>
            
            <div class="history-timeline">
              <el-timeline>
                <el-timeline-item
                  v-for="item in escalationHistory"
                  :key="item.id"
                  :timestamp="item.timestamp"
                  :type="getTimelineType(item.status)"
                >
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <span class="incident-id">{{ item.incidentId }}</span>
                      <el-tag :type="getStatusType(item.status)" size="small">
                        {{ item.status }}
                      </el-tag>
                    </div>
                    <div class="timeline-body">
                      <p><strong>升级原因：</strong>{{ item.reason }}</p>
                      <p><strong>从：</strong>{{ item.fromGroup }} <strong>到：</strong>{{ item.toGroup }}</p>
                      <p><strong>处理人：</strong>{{ item.handler }}</p>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </el-tab-pane>

        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notifications">
          <div class="notifications-section">
            <div class="section-header">
              <h3>升级通知配置</h3>
            </div>
            
            <div class="notification-config">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="config-card">
                    <h4>
                      <el-icon><Message /></el-icon>
                      邮件通知
                    </h4>
                    <el-form :model="notificationSettings.email" label-width="120px">
                      <el-form-item label="启用邮件通知">
                        <el-switch v-model="notificationSettings.email.enabled" />
                      </el-form-item>
                      <el-form-item label="通知模板">
                        <el-select v-model="notificationSettings.email.template" style="width: 100%;">
                          <el-option label="标准模板" value="standard" />
                          <el-option label="详细模板" value="detailed" />
                          <el-option label="简洁模板" value="simple" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="发送延迟">
                        <el-input-number
                          v-model="notificationSettings.email.delay"
                          :min="0"
                          :max="60"
                          controls-position="right"
                        />
                        <span style="margin-left: 8px;">分钟</span>
                      </el-form-item>
                    </el-form>
                  </div>
                </el-col>
                
                <el-col :span="12">
                  <div class="config-card">
                    <h4>
                      <el-icon><ChatDotRound /></el-icon>
                      短信通知
                    </h4>
                    <el-form :model="notificationSettings.sms" label-width="120px">
                      <el-form-item label="启用短信通知">
                        <el-switch v-model="notificationSettings.sms.enabled" />
                      </el-form-item>
                      <el-form-item label="紧急事件">
                        <el-switch v-model="notificationSettings.sms.urgentOnly" />
                      </el-form-item>
                      <el-form-item label="发送延迟">
                        <el-input-number
                          v-model="notificationSettings.sms.delay"
                          :min="0"
                          :max="30"
                          controls-position="right"
                        />
                        <span style="margin-left: 8px;">分钟</span>
                      </el-form-item>
                    </el-form>
                  </div>
                </el-col>
              </el-row>
              
              <div class="notification-actions">
                <el-button type="primary" @click="saveNotificationSettings">
                  <el-icon><Check /></el-icon>
                  保存设置
                </el-button>
                <el-button @click="testNotification">
                  <el-icon><Bell /></el-icon>
                  测试通知
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 统计分析 -->
        <el-tab-pane label="统计分析" name="analytics">
          <div class="analytics-section">
            <div class="section-header">
              <h3>升级统计分析</h3>
            </div>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="chart-card">
                  <h4>升级趋势图</h4>
                  <div class="chart-placeholder">
                    <el-icon :size="48"><TrendCharts /></el-icon>
                    <p>升级趋势图表将在此显示</p>
                  </div>
                </div>
              </el-col>
              
              <el-col :span="12">
                <div class="chart-card">
                  <h4>升级原因分布</h4>
                  <div class="chart-placeholder">
                    <el-icon :size="48"><PieChart /></el-icon>
                    <p>升级原因分布图表将在此显示</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('rules')
const dateRange = ref([])
const statusFilter = ref('')

// 统计数据
const escalationStats = ref([
  {
    key: 'total',
    label: '总升级次数',
    value: '156',
    icon: 'Top',
    type: 'primary',
    trend: 'up',
    change: '+12%'
  },
  {
    key: 'success',
    label: '成功升级',
    value: '142',
    icon: 'Check',
    type: 'success',
    trend: 'up',
    change: '+8%'
  },
  {
    key: 'failed',
    label: '失败升级',
    value: '14',
    icon: 'Close',
    type: 'danger',
    trend: 'down',
    change: '-3%'
  },
  {
    key: 'avgTime',
    label: '平均升级时间',
    value: '25分钟',
    icon: 'Timer',
    type: 'warning',
    trend: 'down',
    change: '-5%'
  }
])

// 升级规则数据
const escalationRules = ref([
  {
    id: 1,
    name: '高优先级事件自动升级',
    priority: '高',
    condition: '优先级为高且超过30分钟未响应',
    escalateTime: '30分钟',
    targetGroup: '二级支持',
    status: true
  },
  {
    id: 2,
    name: '紧急事件立即升级',
    priority: '紧急',
    condition: '优先级为紧急且超过15分钟未响应',
    escalateTime: '15分钟',
    targetGroup: '高级工程师',
    status: true
  },
  {
    id: 3,
    name: '业务关键系统升级',
    priority: '高',
    condition: '影响业务关键系统且超过1小时未解决',
    escalateTime: '60分钟',
    targetGroup: '管理层',
    status: false
  }
])

// 升级历史数据
const escalationHistory = ref([
  {
    id: 1,
    incidentId: 'INC-2024-001',
    timestamp: '2024-01-15 14:30:00',
    reason: '超过响应时间阈值',
    fromGroup: '一级支持',
    toGroup: '二级支持',
    handler: '张三',
    status: '成功'
  },
  {
    id: 2,
    incidentId: 'INC-2024-002',
    timestamp: '2024-01-15 13:15:00',
    reason: '事件优先级提升',
    fromGroup: '二级支持',
    toGroup: '高级工程师',
    handler: '李四',
    status: '成功'
  },
  {
    id: 3,
    incidentId: 'INC-2024-003',
    timestamp: '2024-01-15 11:45:00',
    reason: '自动升级规则触发',
    fromGroup: '一级支持',
    toGroup: '二级支持',
    handler: '系统',
    status: '失败'
  }
])

// 通知设置
const notificationSettings = reactive({
  email: {
    enabled: true,
    template: 'standard',
    delay: 5
  },
  sms: {
    enabled: false,
    urgentOnly: true,
    delay: 2
  }
})

// 方法
const refreshData = () => {
  ElMessage.success('数据已刷新')
}

const showCreateRuleDialog = () => {
  ElMessage.info('创建规则对话框功能开发中...')
}

const getPriorityType = (priority) => {
  const types = {
    '低': '',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return types[priority] || ''
}

const getStatusType = (status) => {
  return status === '成功' ? 'success' : 'danger'
}

const getTimelineType = (status) => {
  return status === '成功' ? 'success' : 'danger'
}

const toggleRuleStatus = (rule) => {
  ElMessage.success(`规则 "${rule.name}" 已${rule.status ? '启用' : '禁用'}`)
}

const editRule = (rule) => {
  ElMessage.info(`编辑规则: ${rule.name}`)
}

const deleteRule = async (rule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则 "${rule.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = escalationRules.value.findIndex(r => r.id === rule.id)
    if (index > -1) {
      escalationRules.value.splice(index, 1)
      ElMessage.success('规则删除成功')
    }
  } catch {
    ElMessage.info('已取消删除')
  }
}

const filterHistory = () => {
  ElMessage.info('历史记录已筛选')
}

const saveNotificationSettings = () => {
  ElMessage.success('通知设置已保存')
}

const testNotification = () => {
  ElMessage.success('测试通知已发送')
}

onMounted(() => {
  console.log('升级管理页面已加载')
})
</script>

<style scoped>
.escalation-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
  border-left-color: #3b82f6;
}

.stat-card.success {
  border-left-color: #10b981;
}

.stat-card.danger {
  border-left-color: #ef4444;
}

.stat-card.warning {
  border-left-color: #f59e0b;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: #f3f4f6;
}

.stat-card.primary .stat-icon {
  background: #dbeafe;
  color: #3b82f6;
}

.stat-card.success .stat-icon {
  background: #d1fae5;
  color: #10b981;
}

.stat-card.danger .stat-icon {
  background: #fee2e2;
  color: #ef4444;
}

.stat-card.warning .stat-icon {
  background: #fef3c7;
  color: #f59e0b;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.up {
  color: #10b981;
}

.stat-trend.down {
  color: #ef4444;
}

/* 主要内容区域 */
.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.escalation-tabs {
  padding: 0;
}

.escalation-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.escalation-tabs :deep(.el-tabs__content) {
  padding: 24px;
}

/* 各个标签页样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

/* 规则表格 */
.rules-table {
  margin-top: 16px;
}

.rule-name {
  display: flex;
  align-items: center;
}

.rule-icon {
  margin-right: 8px;
  color: #6b7280;
}

.delete-btn {
  color: #ef4444 !important;
}

.delete-btn:hover {
  background: #fef2f2 !important;
}

/* 历史记录 */
.history-section {
  max-height: 600px;
  overflow-y: auto;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.history-timeline {
  margin-top: 20px;
}

.timeline-content {
  background: #f8fafc;
  border-radius: 6px;
  padding: 16px;
  border-left: 3px solid #e5e7eb;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.incident-id {
  font-weight: 600;
  color: #1f2937;
}

.timeline-body p {
  margin: 4px 0;
  font-size: 14px;
  color: #4b5563;
}

/* 通知设置 */
.notification-config {
  margin-top: 20px;
}

.config-card {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.config-card h4 {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.config-card h4 .el-icon {
  margin-right: 8px;
  color: #3b82f6;
}

.notification-actions {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 12px;
}

/* 统计分析 */
.chart-card {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.chart-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;
}

.chart-placeholder .el-icon {
  margin-bottom: 12px;
}

.chart-placeholder p {
  margin: 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .escalation-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .notification-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .escalation-tabs :deep(.el-tabs__content) {
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
