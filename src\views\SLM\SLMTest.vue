<template>
  <div class="slm-test">
    <el-card>
      <template #header>
        <h3>SLM模块功能测试</h3>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <!-- API测试 -->
        <div>
          <h4>API功能测试</h4>
          <el-space wrap>
            <el-button @click="testGetSLMOverview" :loading="loading.overview">
              测试SLM概览
            </el-button>
            <el-button @click="testGetServiceList" :loading="loading.services">
              测试服务列表
            </el-button>
            <el-button @click="testGetAgreementList" :loading="loading.agreements">
              测试协议列表
            </el-button>
            <el-button @click="testGetSLAReports" :loading="loading.reports">
              测试SLA报告
            </el-button>
            <el-button @click="testGetSLAMonitoring" :loading="loading.monitoring">
              测试SLA监控
            </el-button>
            <el-button @click="testCalculateHealthScore" :loading="loading.health">
              测试健康评分
            </el-button>
          </el-space>
        </div>

        <!-- 页面导航测试 -->
        <div>
          <h4>页面导航测试</h4>
          <el-space wrap>
            <el-button @click="$router.push('/slm')" type="primary">
              SLM概览
            </el-button>
            <el-button @click="$router.push('/slm/reports')" type="success">
              SLA报告
            </el-button>
            <el-button @click="$router.push('/slm/agreements')" type="warning">
              协议管理
            </el-button>
            <el-button @click="$router.push('/slm/monitoring')" type="info">
              SLA监控
            </el-button>
            <el-button @click="$router.push('/slm/health')" type="danger">
              服务健康
            </el-button>
          </el-space>
        </div>

        <!-- 测试结果 -->
        <div v-if="testResults.length > 0">
          <h4>测试结果</h4>
          <el-table :data="testResults" border stripe>
            <el-table-column prop="test" label="测试项" width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                  {{ row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="结果" />
            <el-table-column prop="time" label="测试时间" width="160" />
          </el-table>
        </div>

        <!-- 清空结果 -->
        <div v-if="testResults.length > 0">
          <el-button @click="clearResults">清空结果</el-button>
        </div>
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  getSLMOverview,
  getServiceList,
  getAgreementList,
  getSLAReports,
  getSLAMonitoring,
  calculateHealthScore
} from '@/api/slmApi.js'

// 路由
const router = useRouter()

// 加载状态
const loading = reactive({
  overview: false,
  services: false,
  agreements: false,
  reports: false,
  monitoring: false,
  health: false
})

// 测试结果
const testResults = ref([])

// 添加测试结果
const addTestResult = (test, status, message) => {
  testResults.value.unshift({
    test,
    status,
    message,
    time: new Date().toLocaleString()
  })
}

// 测试SLM概览
const testGetSLMOverview = async () => {
  loading.overview = true
  try {
    const response = await getSLMOverview()
    if (response.success) {
      addTestResult('SLM概览', 'success', `获取成功，共${response.data.totalServices}个服务`)
      ElMessage.success('SLM概览测试通过')
    } else {
      addTestResult('SLM概览', 'error', response.msg || '获取失败')
      ElMessage.error('SLM概览测试失败')
    }
  } catch (error) {
    addTestResult('SLM概览', 'error', error.message)
    ElMessage.error('SLM概览测试异常')
  } finally {
    loading.overview = false
  }
}

// 测试服务列表
const testGetServiceList = async () => {
  loading.services = true
  try {
    const response = await getServiceList({ page: 1, pageSize: 10 })
    if (response.success) {
      addTestResult('服务列表', 'success', `获取成功，共${response.data.total}个服务`)
      ElMessage.success('服务列表测试通过')
    } else {
      addTestResult('服务列表', 'error', response.msg || '获取失败')
      ElMessage.error('服务列表测试失败')
    }
  } catch (error) {
    addTestResult('服务列表', 'error', error.message)
    ElMessage.error('服务列表测试异常')
  } finally {
    loading.services = false
  }
}

// 测试协议列表
const testGetAgreementList = async () => {
  loading.agreements = true
  try {
    const response = await getAgreementList({ page: 1, pageSize: 10 })
    if (response.success) {
      addTestResult('协议列表', 'success', `获取成功，共${response.data.total}个协议`)
      ElMessage.success('协议列表测试通过')
    } else {
      addTestResult('协议列表', 'error', response.msg || '获取失败')
      ElMessage.error('协议列表测试失败')
    }
  } catch (error) {
    addTestResult('协议列表', 'error', error.message)
    ElMessage.error('协议列表测试异常')
  } finally {
    loading.agreements = false
  }
}

// 测试SLA报告
const testGetSLAReports = async () => {
  loading.reports = true
  try {
    const response = await getSLAReports({ period: 'monthly' })
    if (response.success) {
      addTestResult('SLA报告', 'success', `获取成功，共${response.data.length}个报告`)
      ElMessage.success('SLA报告测试通过')
    } else {
      addTestResult('SLA报告', 'error', response.msg || '获取失败')
      ElMessage.error('SLA报告测试失败')
    }
  } catch (error) {
    addTestResult('SLA报告', 'error', error.message)
    ElMessage.error('SLA报告测试异常')
  } finally {
    loading.reports = false
  }
}

// 测试SLA监控
const testGetSLAMonitoring = async () => {
  loading.monitoring = true
  try {
    const response = await getSLAMonitoring({ period: '7d' })
    if (response.success) {
      addTestResult('SLA监控', 'success', `获取成功，共${response.data.length}个监控项`)
      ElMessage.success('SLA监控测试通过')
    } else {
      addTestResult('SLA监控', 'error', response.msg || '获取失败')
      ElMessage.error('SLA监控测试失败')
    }
  } catch (error) {
    addTestResult('SLA监控', 'error', error.message)
    ElMessage.error('SLA监控测试异常')
  } finally {
    loading.monitoring = false
  }
}

// 测试健康评分
const testCalculateHealthScore = async () => {
  loading.health = true
  try {
    const response = await calculateHealthScore('SRV-001')
    if (response.success) {
      addTestResult('健康评分', 'success', `计算成功，评分：${response.data.healthScore}`)
      ElMessage.success('健康评分测试通过')
    } else {
      addTestResult('健康评分', 'error', response.msg || '计算失败')
      ElMessage.error('健康评分测试失败')
    }
  } catch (error) {
    addTestResult('健康评分', 'error', error.message)
    ElMessage.error('健康评分测试异常')
  } finally {
    loading.health = false
  }
}

// 清空结果
const clearResults = () => {
  testResults.value = []
}
</script>

<style scoped>
.slm-test {
  padding: 20px;
}

h4 {
  margin: 0 0 12px 0;
  color: #333;
}
</style>
