# CMDB项目修复指南

## 🚨 紧急修复步骤

### 第一步：安装缺失的依赖包

**Windows用户：**
```cmd
# 双击运行项目根目录下的 install-dependencies.bat 文件
# 或者在命令行中执行：
install-dependencies.bat
```

**Linux/Mac用户：**
```bash
# 在终端中执行：
chmod +x install-dependencies.sh
./install-dependencies.sh
```

**手动安装（如果脚本失败）：**
```bash
# 1. 清理现有安装
npm cache clean --force
rm -rf node_modules
rm -f package-lock.json

# 2. 安装所有依赖
npm install

# 3. 验证关键依赖
npm list pinia
npm list @element-plus/icons-vue
npm list axios
npm list mockjs
```

### 第二步：启动项目

```bash
npm run dev
```

如果仍然报错，尝试使用最小化配置：

1. 将 `src/main-minimal.js` 重命名为 `src/main.js`
2. 将 `src/App-minimal.vue` 重命名为 `src/App.vue`
3. 再次运行 `npm run dev`

## 🔍 错误诊断

### 错误1：pinia导入失败
```
Failed to resolve import "pinia" from "src/main.js"
```

**解决方案：**
```bash
npm install pinia
```

### 错误2：图标组件导入失败
```
Failed to resolve import "@element-plus/icons-vue"
```

**解决方案：**
```bash
npm install @element-plus/icons-vue
```

### 错误3：axios导入失败
```
Failed to resolve import "axios"
```

**解决方案：**
```bash
npm install axios
```

### 错误4：mockjs导入失败
```
Failed to resolve import "mockjs"
```

**解决方案：**
```bash
npm install mockjs
```

## 📋 完整的依赖列表

确保package.json中包含以下依赖：

```json
{
  "dependencies": {
    "@element-plus/icons-vue": "^2.3.1",
    "axios": "^1.6.0",
    "echarts": "^5.6.0",
    "echarts-wordcloud": "^2.1.0",
    "element-plus": "^2.9.10",
    "gsap": "^3.13.0",
    "mockjs": "^1.1.0",
    "pinia": "^2.1.7",
    "three": "^0.176.0",
    "vue": "^3.5.13",
    "vue-router": "^4.5.1"
  }
}
```

## 🛠️ 环境检查

### 检查Node.js版本
```bash
node --version
# 应该显示 >= 16.0.0
```

### 检查npm版本
```bash
npm --version
# 应该显示 >= 8.0.0
```

### 检查项目结构
确保以下文件存在：
- `package.json`
- `src/main.js`
- `src/App.vue`
- `src/router/index.js`
- `vite.config.js`

## 🔄 重置项目（最后手段）

如果所有方法都失败，执行完全重置：

```bash
# 1. 备份重要文件
cp -r src src_backup

# 2. 删除所有依赖和缓存
rm -rf node_modules
rm -f package-lock.json
npm cache clean --force

# 3. 重新安装
npm install

# 4. 如果还是失败，尝试使用yarn
npm install -g yarn
yarn install
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **操作系统**：Windows/Mac/Linux
2. **Node.js版本**：`node --version`
3. **npm版本**：`npm --version`
4. **完整错误信息**：复制控制台中的所有错误
5. **package.json内容**：确认依赖列表

## 🎯 快速测试

项目启动后，访问 `http://localhost:5173`，您应该看到：

1. ✅ 页面正常加载
2. ✅ Element Plus组件显示正常
3. ✅ 控制台没有错误信息
4. ✅ 可以点击按钮并看到消息提示

## 📝 常见问题FAQ

**Q: 为什么会出现依赖缺失？**
A: 项目开发过程中添加了新的依赖，但package.json没有及时更新。

**Q: 可以使用yarn代替npm吗？**
A: 可以，但建议统一使用一种包管理器。

**Q: 如何确认所有依赖都安装成功？**
A: 运行 `npm list --depth=0` 查看已安装的依赖。

**Q: 项目启动后页面空白怎么办？**
A: 检查浏览器控制台是否有JavaScript错误，并按照错误信息进行修复。

---

**重要提示：** 如果您是第一次运行这个项目，请务必先执行依赖安装步骤，然后再启动开发服务器。
