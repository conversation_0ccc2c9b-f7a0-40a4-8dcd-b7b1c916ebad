<template>
  <div class="stat-card" :class="cardClass" @click="handleClick">
    <div class="card-content">
      <!-- 图标区域 -->
      <div class="stat-icon" :style="iconStyle">
        <el-icon :size="iconSize">
          <component :is="icon" />
        </el-icon>
      </div>
      
      <!-- 内容区域 -->
      <div class="stat-info">
        <div class="stat-value" :style="valueStyle">
          <span v-if="prefix" class="value-prefix">{{ prefix }}</span>
          <span class="value-number">{{ formattedValue }}</span>
          <span v-if="suffix" class="value-suffix">{{ suffix }}</span>
        </div>
        
        <div class="stat-label">{{ label }}</div>
        
        <!-- 变化趋势 -->
        <div v-if="showTrend && trend" class="stat-trend" :class="trendClass">
          <el-icon :size="12">
            <component :is="trendIcon" />
          </el-icon>
          <span class="trend-text">{{ trendText }}</span>
        </div>
        
        <!-- 额外信息 -->
        <div v-if="extra" class="stat-extra">{{ extra }}</div>
      </div>
      
      <!-- 操作按钮 -->
      <div v-if="showAction" class="stat-action">
        <el-button 
          :type="actionType" 
          :size="actionSize"
          @click.stop="handleAction"
        >
          {{ actionText }}
        </el-button>
      </div>
    </div>
    
    <!-- 进度条 -->
    <div v-if="showProgress" class="stat-progress">
      <el-progress 
        :percentage="progressValue" 
        :stroke-width="progressHeight"
        :show-text="false"
        :color="progressColor"
      />
    </div>
    
    <!-- 图表区域 -->
    <div v-if="showChart" class="stat-chart">
      <div ref="chartRef" :style="{ height: chartHeight + 'px' }"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const props = defineProps({
  // 基本属性
  value: {
    type: [Number, String],
    required: true
  },
  label: {
    type: String,
    required: true
  },
  icon: {
    type: [String, Object],
    default: 'Document'
  },
  
  // 样式配置
  type: {
    type: String,
    default: 'default', // default, primary, success, warning, danger, info
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'default', // small, default, large
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  
  // 数值格式化
  formatter: {
    type: Function,
    default: null
  },
  precision: {
    type: Number,
    default: 0
  },
  prefix: {
    type: String,
    default: ''
  },
  suffix: {
    type: String,
    default: ''
  },
  
  // 趋势相关
  showTrend: {
    type: Boolean,
    default: false
  },
  trend: {
    type: Object,
    default: null // { type: 'up|down|flat', value: number, text: string }
  },
  
  // 进度条
  showProgress: {
    type: Boolean,
    default: false
  },
  progressValue: {
    type: Number,
    default: 0
  },
  progressHeight: {
    type: Number,
    default: 6
  },
  progressColor: {
    type: [String, Array],
    default: '#409EFF'
  },
  
  // 图表
  showChart: {
    type: Boolean,
    default: false
  },
  chartData: {
    type: Array,
    default: () => []
  },
  chartHeight: {
    type: Number,
    default: 60
  },
  chartType: {
    type: String,
    default: 'line' // line, bar, area
  },
  
  // 操作按钮
  showAction: {
    type: Boolean,
    default: false
  },
  actionText: {
    type: String,
    default: '查看详情'
  },
  actionType: {
    type: String,
    default: 'text'
  },
  actionSize: {
    type: String,
    default: 'small'
  },
  
  // 其他
  extra: {
    type: String,
    default: ''
  },
  clickable: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click', 'action'])

const chartRef = ref()
let chartInstance = null

// 计算属性
const formattedValue = computed(() => {
  if (props.formatter) {
    return props.formatter(props.value)
  }
  
  if (typeof props.value === 'number') {
    return props.value.toFixed(props.precision)
  }
  
  return props.value
})

const cardClass = computed(() => {
  return {
    [`stat-card--${props.type}`]: true,
    [`stat-card--${props.size}`]: true,
    'stat-card--clickable': props.clickable,
    'stat-card--loading': props.loading
  }
})

const iconSize = computed(() => {
  const sizeMap = {
    small: 20,
    default: 24,
    large: 32
  }
  return sizeMap[props.size] || 24
})

const iconStyle = computed(() => {
  const colorMap = {
    default: '#909399',
    primary: '#409EFF',
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C',
    info: '#909399'
  }
  
  return {
    color: colorMap[props.type] || colorMap.default
  }
})

const valueStyle = computed(() => {
  const colorMap = {
    default: '#303133',
    primary: '#409EFF',
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C',
    info: '#909399'
  }
  
  return {
    color: colorMap[props.type] || colorMap.default
  }
})

const trendClass = computed(() => {
  if (!props.trend) return ''
  
  return {
    'trend-up': props.trend.type === 'up',
    'trend-down': props.trend.type === 'down',
    'trend-flat': props.trend.type === 'flat'
  }
})

const trendIcon = computed(() => {
  if (!props.trend) return Minus
  
  const iconMap = {
    up: ArrowUp,
    down: ArrowDown,
    flat: Minus
  }
  
  return iconMap[props.trend.type] || Minus
})

const trendText = computed(() => {
  if (!props.trend) return ''
  
  if (props.trend.text) {
    return props.trend.text
  }
  
  if (props.trend.value !== undefined) {
    const prefix = props.trend.type === 'up' ? '+' : props.trend.type === 'down' ? '-' : ''
    return `${prefix}${Math.abs(props.trend.value)}%`
  }
  
  return ''
})

// 方法定义
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

const handleAction = () => {
  emit('action')
}

const initChart = () => {
  if (!props.showChart || !chartRef.value || props.chartData.length === 0) {
    return
  }
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 5,
      bottom: 5
    },
    xAxis: {
      type: 'category',
      show: false,
      data: props.chartData.map((_, index) => index)
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        type: props.chartType === 'bar' ? 'bar' : 'line',
        data: props.chartData,
        smooth: props.chartType === 'line',
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: iconStyle.value.color
        },
        itemStyle: {
          color: iconStyle.value.color
        },
        areaStyle: props.chartType === 'area' ? {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: iconStyle.value.color + '40' },
              { offset: 1, color: iconStyle.value.color + '10' }
            ]
          }
        } : undefined
      }
    ]
  }
  
  chartInstance.setOption(option)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 生命周期
onMounted(() => {
  if (props.showChart) {
    initChart()
    window.addEventListener('resize', resizeChart)
  }
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})

// 监听图表数据变化
watch(() => props.chartData, () => {
  if (props.showChart) {
    initChart()
  }
}, { deep: true })

watch(() => props.type, () => {
  if (props.showChart) {
    initChart()
  }
})
</script>

<style scoped>
.stat-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stat-card--clickable {
  cursor: pointer;
}

.stat-card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-card--loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.stat-card--small .card-content {
  padding: 12px 16px;
}

.stat-card--default .card-content {
  padding: 16px 20px;
}

.stat-card--large .card-content {
  padding: 20px 24px;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.stat-card--small .stat-icon {
  width: 40px;
  height: 40px;
}

.stat-card--large .stat-icon {
  width: 56px;
  height: 56px;
}

.stat-card--primary .stat-icon {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white !important;
}

.stat-card--success .stat-icon {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  color: white !important;
}

.stat-card--warning .stat-icon {
  background: linear-gradient(135deg, #E6A23C, #F0A020);
  color: white !important;
}

.stat-card--danger .stat-icon {
  background: linear-gradient(135deg, #F56C6C, #F78989);
  color: white !important;
}

.stat-card--info .stat-icon {
  background: linear-gradient(135deg, #909399, #A6A9AD);
  color: white !important;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.stat-card--small .stat-value {
  font-size: 20px;
}

.stat-card--large .stat-value {
  font-size: 28px;
}

.value-prefix,
.value-suffix {
  font-size: 0.7em;
  font-weight: 500;
  opacity: 0.8;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #67C23A;
}

.trend-down {
  color: #F56C6C;
}

.trend-flat {
  color: #909399;
}

.stat-extra {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.stat-action {
  flex-shrink: 0;
}

.stat-progress {
  margin-top: 12px;
}

.stat-chart {
  margin-top: 12px;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .stat-info {
    width: 100%;
  }
  
  .stat-action {
    width: 100%;
  }
  
  .stat-action .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .stat-card--default .card-content,
  .stat-card--large .card-content {
    padding: 12px 16px;
  }
  
  .stat-value {
    font-size: 20px !important;
  }
  
  .stat-icon {
    width: 40px !important;
    height: 40px !important;
  }
}
</style>
