<template>
  <div class="service-desk">
    <div class="page-header">
      <h2>服务台</h2>
      <p>多渠道接入，智能工单创建，精准路由分配</p>
    </div>

    <!-- 快速操作区 -->
    <div class="quick-actions">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="action-card" @click="showCreateTicket = true">
            <div class="action-content">
              <el-icon :size="32" color="#1976D2">
                <Plus />
              </el-icon>
              <h3>创建工单</h3>
              <p>快速创建新的服务请求或事件工单</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="action-card" @click="showSmartCreate = true">
            <div class="action-content">
              <el-icon :size="32" color="#FF6B35">
                <MagicStick />
              </el-icon>
              <h3>智能创建</h3>
              <p>AI辅助工单创建和智能路由</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="action-card" @click="showMultiChannelDialog = true">
            <div class="action-content">
              <el-icon :size="32" color="#4CAF50">
                <Connection />
              </el-icon>
              <h3>多渠道接入</h3>
              <p>邮件、移动端、企业微信等</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="action-card" @click="$router.push('/service-desk/status')">
            <div class="action-content">
              <el-icon :size="32" color="#FF9800">
                <DataBoard />
              </el-icon>
              <h3>状态看板</h3>
              <p>实时监控服务运行状态</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- SLA告警区域 -->
    <div class="sla-alerts" v-if="slaAlerts.length > 0">
      <el-alert
        title="SLA告警"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="alert-content">
            <span>发现 {{ slaAlerts.length }} 个工单即将或已经超时</span>
            <el-button type="text" size="small" @click="viewSLAAlerts">查看详情</el-button>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-overview">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>今日工单统计</span>
                <el-button type="text" size="small" @click="viewDetailedStats">查看详情</el-button>
              </div>
            </template>
            <div class="stats-grid">
              <div class="stat-item" @click="filterTicketsByStatus('all')">
                <div class="stat-number">{{ todayStats.total }}</div>
                <div class="stat-label">总工单数</div>
                <div class="stat-trend" :class="todayStats.totalTrend > 0 ? 'up' : 'down'">
                  <el-icon><component :is="todayStats.totalTrend > 0 ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ Math.abs(todayStats.totalTrend) }}%
                </div>
              </div>
              <div class="stat-item" @click="filterTicketsByStatus('pending')">
                <div class="stat-number pending">{{ todayStats.pending }}</div>
                <div class="stat-label">待处理</div>
                <div class="stat-progress">
                  <el-progress :percentage="(todayStats.pending / todayStats.total * 100)" :show-text="false" :stroke-width="4" />
                </div>
              </div>
              <div class="stat-item" @click="filterTicketsByStatus('processing')">
                <div class="stat-number processing">{{ todayStats.processing }}</div>
                <div class="stat-label">处理中</div>
                <div class="stat-progress">
                  <el-progress :percentage="(todayStats.processing / todayStats.total * 100)" :show-text="false" :stroke-width="4" color="#2196F3" />
                </div>
              </div>
              <div class="stat-item" @click="filterTicketsByStatus('resolved')">
                <div class="stat-number resolved">{{ todayStats.resolved }}</div>
                <div class="stat-label">已解决</div>
                <div class="stat-progress">
                  <el-progress :percentage="(todayStats.resolved / todayStats.total * 100)" :show-text="false" :stroke-width="4" color="#4CAF50" />
                </div>
              </div>
              <div class="stat-item urgent" @click="filterTicketsByStatus('overdue')">
                <div class="stat-number overdue">{{ todayStats.overdue }}</div>
                <div class="stat-label">超时工单</div>
                <div class="stat-alert" v-if="todayStats.overdue > 0">
                  <el-icon color="#F44336"><Warning /></el-icon>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="6">
          <el-card>
            <template #header>
              <span>渠道接入统计</span>
            </template>
            <div class="channel-stats">
              <div v-for="channel in channelStats" :key="channel.name" class="channel-item" @click="viewChannelDetails(channel)">
                <div class="channel-info">
                  <el-icon :size="20" :color="channel.color">
                    <component :is="channel.icon" />
                  </el-icon>
                  <span class="channel-name">{{ channel.name }}</span>
                </div>
                <div class="channel-count">{{ channel.count }}</div>
                <div class="channel-percentage">{{ ((channel.count / todayStats.total) * 100).toFixed(1) }}%</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="6">
          <el-card>
            <template #header>
              <span>智能路由统计</span>
            </template>
            <div class="routing-stats">
              <div class="routing-item">
                <div class="routing-label">自动分配率</div>
                <div class="routing-value">{{ routingStats.autoAssignRate }}%</div>
                <el-progress :percentage="routingStats.autoAssignRate" :show-text="false" :stroke-width="6" />
              </div>
              <div class="routing-item">
                <div class="routing-label">平均响应时间</div>
                <div class="routing-value">{{ routingStats.avgResponseTime }}分钟</div>
              </div>
              <div class="routing-item">
                <div class="routing-label">首次解决率</div>
                <div class="routing-value">{{ routingStats.firstCallResolution }}%</div>
                <el-progress :percentage="routingStats.firstCallResolution" :show-text="false" :stroke-width="6" color="#4CAF50" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近工单 -->
    <div class="recent-tickets">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近工单</span>
            <el-button type="primary" size="small" @click="$router.push('/service-desk/tickets')">
              查看全部
            </el-button>
          </div>
        </template>
        <el-table :data="recentTickets" style="width: 100%">
          <el-table-column prop="id" label="工单号" width="120" />
          <el-table-column prop="title" label="标题" />
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)" size="small">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="处理人" width="120" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewTicket(scope.row)">
                查看
              </el-button>
              <el-button type="success" size="small" text @click="processTicket(scope.row)">
                处理
              </el-button>
              <el-button type="warning" size="small" text @click="assignTicket(scope.row)">
                分配
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 创建工单对话框 -->
    <el-dialog v-model="showCreateTicket" title="创建工单" width="700px" :before-close="handleCreateDialogClose">
      <el-form :model="ticketForm" :rules="ticketRules" ref="ticketFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单类型" prop="type">
              <el-select v-model="ticketForm.type" placeholder="请选择工单类型" @change="onTypeChange">
                <el-option label="事件" value="incident" />
                <el-option label="服务请求" value="request" />
                <el-option label="咨询" value="inquiry" />
                <el-option label="变更" value="change" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="ticketForm.priority" placeholder="请选择优先级">
                <el-option label="低" value="低" />
                <el-option label="中" value="中" />
                <el-option label="高" value="高" />
                <el-option label="紧急" value="紧急" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="标题" prop="title">
          <el-input v-model="ticketForm.title" placeholder="请输入工单标题" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="ticketForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述问题或需求"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-cascader
                v-model="ticketForm.category"
                :options="categoryOptions"
                placeholder="请选择分类"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="影响用户">
              <el-input v-model="ticketForm.affectedUser" placeholder="受影响的用户" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="关联CI">
          <el-select v-model="ticketForm.relatedCI" placeholder="选择相关配置项" multiple style="width: 100%">
            <el-option v-for="ci in availableCIs" :key="ci.id" :label="ci.name" :value="ci.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="附件">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            multiple
            :auto-upload="false"
            :on-change="handleFileChange"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateTicket = false">取消</el-button>
        <el-button @click="saveDraft">保存草稿</el-button>
        <el-button type="primary" @click="createTicket" :loading="creating">创建工单</el-button>
      </template>
    </el-dialog>

    <!-- 智能创建工单对话框 -->
    <el-dialog v-model="showSmartCreate" title="智能工单创建" width="800px">
      <div class="smart-create-container">
        <div class="smart-input-section">
          <h3>描述您的问题或需求</h3>
          <el-input
            v-model="smartInput"
            type="textarea"
            :rows="6"
            placeholder="请用自然语言描述您遇到的问题或需求，AI将帮助您自动分类和填写工单信息..."
            @input="onSmartInputChange"
          />
          <div class="smart-actions">
            <el-button type="primary" @click="analyzeSmartInput" :loading="analyzing">
              <el-icon><MagicStick /></el-icon>
              AI分析
            </el-button>
            <el-button @click="clearSmartInput">清空</el-button>
          </div>
        </div>

        <div class="smart-result-section" v-if="smartAnalysis">
          <h3>AI分析结果</h3>
          <el-card>
            <div class="analysis-item">
              <label>建议类型：</label>
              <el-tag :type="getTypeColor(smartAnalysis.suggestedType)">
                {{ getTypeText(smartAnalysis.suggestedType) }}
              </el-tag>
            </div>
            <div class="analysis-item">
              <label>建议优先级：</label>
              <el-tag :type="getPriorityColor(smartAnalysis.suggestedPriority)">
                {{ smartAnalysis.suggestedPriority }}
              </el-tag>
            </div>
            <div class="analysis-item">
              <label>建议分类：</label>
              <span>{{ smartAnalysis.suggestedCategory.join(' > ') }}</span>
            </div>
            <div class="analysis-item">
              <label>关联CI：</label>
              <el-tag v-for="ci in smartAnalysis.relatedCIs" :key="ci" size="small" style="margin-right: 8px;">
                {{ ci }}
              </el-tag>
            </div>
            <div class="analysis-item">
              <label>建议处理人：</label>
              <span>{{ smartAnalysis.suggestedAssignee }}</span>
            </div>
          </el-card>

          <div class="smart-actions">
            <el-button type="success" @click="acceptSmartSuggestion">采用建议</el-button>
            <el-button @click="editSmartSuggestion">手动调整</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 多渠道接入对话框 -->
    <el-dialog v-model="showMultiChannelDialog" title="多渠道接入配置" width="700px">
      <div class="multi-channel-container">
        <el-tabs v-model="activeChannelTab">
          <el-tab-pane label="邮件转工单" name="email">
            <div class="channel-config">
              <h4>邮件转工单配置</h4>
              <el-form :model="emailConfig" label-width="120px">
                <el-form-item label="监控邮箱">
                  <el-input v-model="emailConfig.monitorEmail" placeholder="<EMAIL>" />
                </el-form-item>
                <el-form-item label="自动分类">
                  <el-switch v-model="emailConfig.autoClassify" />
                  <span class="form-help">根据邮件内容自动分类工单</span>
                </el-form-item>
                <el-form-item label="默认优先级">
                  <el-select v-model="emailConfig.defaultPriority">
                    <el-option label="低" value="低" />
                    <el-option label="中" value="中" />
                    <el-option label="高" value="高" />
                  </el-select>
                </el-form-item>
              </el-form>
              <div class="channel-stats">
                <p>今日通过邮件创建工单：<strong>{{ channelStats.find(c => c.name === '邮件')?.count || 0 }}</strong> 个</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="移动端接入" name="mobile">
            <div class="channel-config">
              <h4>移动端接入配置</h4>
              <el-form :model="mobileConfig" label-width="120px">
                <el-form-item label="APP推送">
                  <el-switch v-model="mobileConfig.pushEnabled" />
                </el-form-item>
                <el-form-item label="位置信息">
                  <el-switch v-model="mobileConfig.locationEnabled" />
                  <span class="form-help">自动获取用户位置信息</span>
                </el-form-item>
                <el-form-item label="拍照上传">
                  <el-switch v-model="mobileConfig.photoUpload" />
                </el-form-item>
              </el-form>
              <div class="channel-stats">
                <p>今日通过移动端创建工单：<strong>{{ channelStats.find(c => c.name === '移动端')?.count || 0 }}</strong> 个</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="企业微信" name="wechat">
            <div class="channel-config">
              <h4>企业微信接入配置</h4>
              <el-form :model="wechatConfig" label-width="120px">
                <el-form-item label="机器人回复">
                  <el-switch v-model="wechatConfig.botReply" />
                </el-form-item>
                <el-form-item label="群组通知">
                  <el-switch v-model="wechatConfig.groupNotify" />
                </el-form-item>
                <el-form-item label="状态同步">
                  <el-switch v-model="wechatConfig.statusSync" />
                  <span class="form-help">工单状态变更时同步到微信</span>
                </el-form-item>
              </el-form>
              <div class="channel-stats">
                <p>今日通过企业微信创建工单：<strong>{{ channelStats.find(c => c.name === '企业微信')?.count || 0 }}</strong> 个</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <el-button @click="showMultiChannelDialog = false">关闭</el-button>
        <el-button type="primary" @click="saveChannelConfig">保存配置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { getTicketStats, getServiceStatus } from '@/api/serviceDeskApi.js'

const router = useRouter()

// 对话框状态
const showCreateTicket = ref(false)
const showSmartCreate = ref(false)
const showMultiChannelDialog = ref(false)
const creating = ref(false)
const analyzing = ref(false)
const ticketFormRef = ref()

// 今日统计数据
const todayStats = ref({
  total: 45,
  pending: 12,
  processing: 18,
  resolved: 13,
  overdue: 2,
  totalTrend: 8.5 // 相比昨天的增长百分比
})

// 渠道统计
const channelStats = ref([
  { name: 'Web门户', icon: 'Monitor', count: 28, color: '#1976D2' },
  { name: '邮件', icon: 'Message', count: 12, color: '#4CAF50' },
  { name: '移动端', icon: 'Cellphone', count: 8, color: '#FF9800' },
  { name: '企业微信', icon: 'ChatDotRound', count: 5, color: '#9C27B0' },
  { name: '电话', icon: 'Phone', count: 3, color: '#607D8B' }
])

// 智能路由统计
const routingStats = ref({
  autoAssignRate: 85,
  avgResponseTime: 12,
  firstCallResolution: 78
})

// SLA告警
const slaAlerts = ref([
  { id: 'INC-2025-001', title: '服务器故障', remainingTime: '2小时' },
  { id: 'REQ-2025-005', title: '权限申请', remainingTime: '30分钟' }
])

// 最近工单
const recentTickets = ref([
  {
    id: 'INC-2025-001',
    title: '打印机无法打印',
    priority: '中',
    status: '处理中',
    assignee: '张工',
    createTime: '2025-01-30 14:30'
  },
  {
    id: 'REQ-2025-002',
    title: '申请新员工邮箱',
    priority: '低',
    status: '待处理',
    assignee: '李工',
    createTime: '2025-01-30 13:45'
  },
  {
    id: 'INC-2025-003',
    title: '服务器CPU使用率过高',
    priority: '高',
    status: '已解决',
    assignee: '王工',
    createTime: '2025-01-30 12:20'
  }
])

// 创建工单表单
const ticketForm = reactive({
  type: '',
  title: '',
  description: '',
  priority: '',
  category: [],
  affectedUser: '',
  relatedCI: [],
  attachments: []
})

// 表单验证规则
const ticketRules = {
  type: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入工单描述', trigger: 'blur' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }]
}

// 可用的CI列表
const availableCIs = ref([
  { id: 'CI-001', name: 'Web服务器-01' },
  { id: 'CI-002', name: '数据库服务器-01' },
  { id: 'CI-003', name: '网络交换机-01' },
  { id: 'CI-004', name: '打印机-办公室A' }
])

// 智能创建相关
const smartInput = ref('')
const smartAnalysis = ref(null)
const activeChannelTab = ref('email')

// 多渠道配置
const emailConfig = reactive({
  monitorEmail: '<EMAIL>',
  autoClassify: true,
  defaultPriority: '中'
})

const mobileConfig = reactive({
  pushEnabled: true,
  locationEnabled: true,
  photoUpload: true
})

const wechatConfig = reactive({
  botReply: true,
  groupNotify: true,
  statusSync: true
})

// 分类选项
const categoryOptions = ref([
  {
    value: 'hardware',
    label: '硬件',
    children: [
      { value: 'computer', label: '计算机' },
      { value: 'printer', label: '打印机' },
      { value: 'network', label: '网络设备' }
    ]
  },
  {
    value: 'software',
    label: '软件',
    children: [
      { value: 'os', label: '操作系统' },
      { value: 'application', label: '应用软件' },
      { value: 'security', label: '安全软件' }
    ]
  },
  {
    value: 'service',
    label: '服务',
    children: [
      { value: 'email', label: '邮件服务' },
      { value: 'network', label: '网络服务' },
      { value: 'database', label: '数据库服务' }
    ]
  }
])

// 获取优先级类型
const getPriorityType = (priority) => {
  const typeMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '待处理': 'info',
    '处理中': 'warning',
    '已解决': 'success',
    '已关闭': 'info'
  }
  return typeMap[status] || 'info'
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await getTicketStats({ timeRange: 'today' })
    if (response.code === 200) {
      const stats = response.data
      todayStats.value = {
        total: stats.total,
        pending: stats.pending,
        processing: stats.processing,
        resolved: stats.resolved,
        overdue: stats.overdue
      }

      // 更新渠道统计
      channelStats.value = [
        { name: 'Web门户', icon: 'Monitor', count: stats.byChannel.web },
        { name: '邮件', icon: 'Message', count: stats.byChannel.email },
        { name: '移动端', icon: 'Cellphone', count: stats.byChannel.mobile },
        { name: '企业微信', icon: 'ChatDotRound', count: stats.byChannel.wechat },
        { name: '电话', icon: 'Phone', count: stats.byChannel.phone }
      ]
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

// 查看详细统计
const viewDetailedStats = () => {
  router.push('/service-desk/analytics')
}

// 按状态筛选工单
const filterTicketsByStatus = (status) => {
  router.push({
    path: '/service-desk/tickets',
    query: { status }
  })
}

// 查看渠道详情
const viewChannelDetails = (channel) => {
  ElMessage.info(`查看${channel.name}渠道详情`)
  // 可以跳转到渠道详情页面
}

// 查看SLA告警
const viewSLAAlerts = () => {
  router.push('/service-desk/sla-alerts')
}

// 查看工单详情
const viewTicket = (ticket) => {
  router.push(`/service-desk/tickets/${ticket.id}`)
}

// 处理工单
const processTicket = (ticket) => {
  router.push(`/service-desk/tickets/${ticket.id}/process`)
}

// 分配工单
const assignTicket = (ticket) => {
  ElMessageBox.prompt('请选择处理人', '分配工单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '处理人不能为空'
  }).then(({ value }) => {
    ElMessage.success(`工单已分配给 ${value}`)
    ticket.assignee = value
  }).catch(() => {
    ElMessage.info('已取消分配')
  })
}

// 工单类型变更处理
const onTypeChange = (type) => {
  // 根据工单类型调整表单
  if (type === 'incident') {
    ticketForm.priority = '高'
  } else if (type === 'request') {
    ticketForm.priority = '中'
  }
}

// 文件上传处理
const handleFileChange = (file, fileList) => {
  ticketForm.attachments = fileList
}

// 保存草稿
const saveDraft = () => {
  ElMessage.success('草稿已保存')
  // 这里可以实现保存草稿的逻辑
}

// 创建工单
const createTicket = async () => {
  if (!ticketFormRef.value) return

  try {
    const valid = await ticketFormRef.value.validate()
    if (!valid) return

    creating.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    ElMessage.success('工单创建成功')
    showCreateTicket.value = false

    // 重置表单
    resetTicketForm()

    // 刷新统计数据
    loadStats()
  } catch (error) {
    console.error('创建工单失败:', error)
    ElMessage.error('创建工单失败')
  } finally {
    creating.value = false
  }
}

// 重置工单表单
const resetTicketForm = () => {
  Object.keys(ticketForm).forEach(key => {
    if (Array.isArray(ticketForm[key])) {
      ticketForm[key] = []
    } else {
      ticketForm[key] = ''
    }
  })
  if (ticketFormRef.value) {
    ticketFormRef.value.resetFields()
  }
}

// 对话框关闭处理
const handleCreateDialogClose = (done) => {
  const hasContent = ticketForm.title || ticketForm.description
  if (hasContent) {
    ElMessageBox.confirm('确认关闭？未保存的内容将丢失。')
      .then(() => {
        resetTicketForm()
        done()
      })
      .catch(() => {
        // 取消关闭
      })
  } else {
    done()
  }
}

// 智能输入变化处理
const onSmartInputChange = () => {
  // 可以实现实时分析或提示
}

// 分析智能输入
const analyzeSmartInput = async () => {
  if (!smartInput.value.trim()) {
    ElMessage.warning('请输入问题描述')
    return
  }

  analyzing.value = true

  try {
    // 模拟AI分析
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟分析结果
    smartAnalysis.value = {
      suggestedType: 'incident',
      suggestedPriority: '中',
      suggestedCategory: ['硬件', '计算机'],
      relatedCIs: ['Web服务器-01', '数据库服务器-01'],
      suggestedAssignee: '张工'
    }

    ElMessage.success('AI分析完成')
  } catch (error) {
    ElMessage.error('分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

// 清空智能输入
const clearSmartInput = () => {
  smartInput.value = ''
  smartAnalysis.value = null
}

// 采用智能建议
const acceptSmartSuggestion = () => {
  if (!smartAnalysis.value) return

  // 填充到普通创建表单
  ticketForm.type = smartAnalysis.value.suggestedType
  ticketForm.priority = smartAnalysis.value.suggestedPriority
  ticketForm.category = smartAnalysis.value.suggestedCategory
  ticketForm.title = smartInput.value.substring(0, 50) + '...'
  ticketForm.description = smartInput.value

  showSmartCreate.value = false
  showCreateTicket.value = true

  ElMessage.success('已采用AI建议，请确认工单信息')
}

// 编辑智能建议
const editSmartSuggestion = () => {
  acceptSmartSuggestion()
}

// 保存渠道配置
const saveChannelConfig = () => {
  ElMessage.success('渠道配置已保存')
  showMultiChannelDialog.value = false
  // 这里可以实现保存配置的API调用
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    'incident': 'danger',
    'request': 'primary',
    'inquiry': 'info',
    'change': 'warning'
  }
  return colorMap[type] || 'info'
}

// 获取类型文本
const getTypeText = (type) => {
  const textMap = {
    'incident': '事件',
    'request': '服务请求',
    'inquiry': '咨询',
    'change': '变更'
  }
  return textMap[type] || type
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colorMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return colorMap[priority] || 'info'
}

// 组件挂载时加载数据
onMounted(() => {
  loadStats()

  // 模拟实时数据更新
  setInterval(() => {
    // 随机更新一些统计数据
    if (Math.random() > 0.7) {
      todayStats.value.total += Math.floor(Math.random() * 3)
      todayStats.value.pending += Math.floor(Math.random() * 2)
    }
  }, 30000) // 每30秒更新一次
})
</script>

<style scoped>
.service-desk {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  color: #1976D2;
  margin-bottom: 8px;
}

.page-header p {
  color: #616161;
  margin: 0;
}

.quick-actions {
  margin-bottom: 30px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 140px;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-content {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.action-content h3 {
  margin: 12px 0 8px 0;
  color: #333;
}

.action-content p {
  color: #616161;
  font-size: 14px;
  margin: 0;
}

.statistics-overview {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  text-align: center;
}

.stat-item {
  padding: 20px 0;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  position: relative;
}

.stat-item:hover {
  background-color: #f5f7fa;
  transform: translateY(-2px);
}

.stat-item.urgent {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(244, 67, 54, 0); }
  100% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0); }
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #1976D2;
  margin-bottom: 8px;
}

.stat-number.pending {
  color: #FF9800;
}

.stat-number.processing {
  color: #2196F3;
}

.stat-number.resolved {
  color: #4CAF50;
}

.stat-number.overdue {
  color: #F44336;
}

.stat-label {
  color: #616161;
  font-size: 14px;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  margin-top: 4px;
}

.stat-trend.up {
  color: #4CAF50;
}

.stat-trend.down {
  color: #F44336;
}

.stat-progress {
  margin-top: 8px;
  padding: 0 10px;
}

.stat-alert {
  position: absolute;
  top: 5px;
  right: 5px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.channel-stats {
  padding: 10px 0;
}

.channel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.channel-item:hover {
  background-color: #f8f9fa;
}

.channel-item:last-child {
  border-bottom: none;
}

.channel-percentage {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.channel-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.channel-name {
  font-weight: 500;
}

.channel-count {
  font-size: 18px;
  font-weight: bold;
  color: #1976D2;
}

.recent-tickets {
  margin-bottom: 30px;
}

/* SLA告警样式 */
.sla-alerts {
  margin-bottom: 20px;
}

.alert-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 智能路由统计样式 */
.routing-stats {
  padding: 10px 0;
}

.routing-item {
  margin-bottom: 16px;
}

.routing-item:last-child {
  margin-bottom: 0;
}

.routing-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.routing-value {
  font-size: 18px;
  font-weight: bold;
  color: #1976D2;
  margin-bottom: 8px;
}

/* 智能创建样式 */
.smart-create-container {
  padding: 20px 0;
}

.smart-input-section {
  margin-bottom: 30px;
}

.smart-input-section h3 {
  color: #333;
  margin-bottom: 16px;
}

.smart-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.smart-result-section h3 {
  color: #333;
  margin-bottom: 16px;
}

.analysis-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.analysis-item label {
  min-width: 100px;
  font-weight: 500;
  color: #666;
}

/* 多渠道配置样式 */
.multi-channel-container {
  padding: 20px 0;
}

.channel-config {
  padding: 20px 0;
}

.channel-config h4 {
  color: #333;
  margin-bottom: 20px;
}

.channel-stats {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.channel-stats p {
  margin: 0;
  color: #666;
}

.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .alert-content {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .smart-actions {
    flex-direction: column;
  }

  .analysis-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .analysis-item label {
    margin-bottom: 4px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .service-desk {
    padding: 12px;
  }

  .quick-actions .el-col {
    margin-bottom: 12px;
  }

  .action-card {
    height: 120px;
  }

  .action-content h3 {
    font-size: 14px;
  }

  .action-content p {
    font-size: 12px;
  }
}
</style>
