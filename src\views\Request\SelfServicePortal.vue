<template>
  <div class="self-service-portal">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>自助服务门户</h2>
          <p>提交请求、跟踪进度、管理服务申请</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showApplyDialog = true">
            <el-icon><Plus /></el-icon>
            新建请求
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="tabs-section">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 快速申请 -->
        <el-tab-pane label="快速申请" name="apply">
          <div class="apply-section">
            <!-- 热门服务 -->
            <div class="popular-services">
              <h3>热门服务</h3>
              <el-row :gutter="16">
                <el-col
                  v-for="service in popularServices"
                  :key="service.id"
                  :xs="24" :sm="12" :md="8" :lg="6"
                >
                  <div class="quick-service-card" @click="quickApply(service)">
                    <div class="service-icon">
                      <el-icon :size="24">
                        <component :is="service.icon" />
                      </el-icon>
                    </div>
                    <div class="service-info">
                      <div class="service-name">{{ service.name }}</div>
                      <div class="service-time">{{ service.estimatedTime }}</div>
                    </div>
                    <div class="service-badge" v-if="service.automationSupported">
                      <el-tag type="success" size="small">自动化</el-tag>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 服务分类 -->
            <div class="service-categories">
              <h3>服务分类</h3>
              <el-row :gutter="16">
                <el-col
                  v-for="category in serviceCategories"
                  :key="category.id"
                  :xs="12" :sm="8" :md="6"
                >
                  <div class="category-card" @click="browseCategory(category)">
                    <div class="category-icon">
                      <el-icon :size="32">
                        <component :is="category.icon" />
                      </el-icon>
                    </div>
                    <div class="category-name">{{ category.name }}</div>
                    <div class="category-count">{{ category.services?.length || 0 }} 个服务</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 最近使用的服务 -->
            <div class="recent-services" v-if="recentServices.length > 0">
              <h3>最近使用</h3>
              <div class="recent-list">
                <div
                  v-for="service in recentServices"
                  :key="service.id"
                  class="recent-item"
                  @click="quickApply(service)"
                >
                  <el-icon>
                    <component :is="service.icon" />
                  </el-icon>
                  <span class="service-name">{{ service.name }}</span>
                  <span class="last-used">{{ service.lastUsed }}</span>
                  <el-icon class="arrow"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 我的请求 -->
        <el-tab-pane label="我的请求" name="my-requests">
          <div class="requests-section">
            <!-- 筛选器 -->
            <div class="filters">
              <el-row :gutter="16" align="middle">
                <el-col :span="6">
                  <el-select v-model="requestFilters.status" placeholder="状态" @change="loadUserRequests">
                    <el-option label="全部状态" value="all" />
                    <el-option label="待审批" value="pending_approval" />
                    <el-option label="处理中" value="in_progress" />
                    <el-option label="已完成" value="completed" />
                    <el-option label="已取消" value="cancelled" />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-select v-model="requestFilters.category" placeholder="分类" @change="loadUserRequests">
                    <el-option label="全部分类" value="all" />
                    <el-option
                      v-for="category in serviceCategories"
                      :key="category.id"
                      :label="category.name"
                      :value="category.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-date-picker
                    v-model="requestFilters.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="loadUserRequests"
                  />
                </el-col>
                <el-col :span="6">
                  <el-input
                    v-model="requestFilters.search"
                    placeholder="搜索请求..."
                    clearable
                    @input="handleRequestSearch"
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                </el-col>
              </el-row>
            </div>

            <!-- 请求列表 -->
            <div class="requests-list">
              <el-table
                :data="userRequests"
                v-loading="loading.requests"
                @row-click="viewRequestDetail"
                style="width: 100%"
              >
                <el-table-column label="请求编号" prop="requestNumber" width="140" />

                <el-table-column label="服务名称" min-width="200">
                  <template #default="{ row }">
                    <div class="service-cell">
                      <el-icon class="service-icon">
                        <component :is="getServiceIcon(row.serviceId)" />
                      </el-icon>
                      <div>
                        <div class="service-name">{{ row.serviceName }}</div>
                        <div class="service-category">{{ getCategoryName(row.category) }}</div>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)" size="small">
                      {{ row.statusLabel }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column label="优先级" prop="priority" width="80" />

                <el-table-column label="提交时间" prop="createdAt" width="160" />

                <el-table-column label="进度" width="120">
                  <template #default="{ row }">
                    <el-progress
                      :percentage="row.approvalProgress || 0"
                      :stroke-width="6"
                      :show-text="false"
                    />
                    <div class="progress-text">{{ row.approvalProgress || 0 }}%</div>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click.stop="viewRequestDetail(row)"
                    >
                      查看详情
                    </el-button>
                    <el-button
                      v-if="canCancelRequest(row.status)"
                      type="danger"
                      size="small"
                      @click.stop="cancelRequest(row)"
                    >
                      取消
                    </el-button>
                    <el-button
                      v-if="canRateRequest(row.status)"
                      type="warning"
                      size="small"
                      @click.stop="rateRequest(row)"
                    >
                      评价
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="pagination-section">
                <el-pagination
                  v-model:current-page="requestPagination.page"
                  v-model:page-size="requestPagination.pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="requestPagination.total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleRequestSizeChange"
                  @current-change="handleRequestCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 待审批 -->
        <el-tab-pane label="待审批" name="approvals">
          <div class="approvals-section">
            <div class="section-header">
              <h3>待我审批的请求</h3>
              <div class="header-actions">
                <el-button @click="loadPendingApprovals">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button
                  type="primary"
                  :disabled="selectedApprovals.length === 0"
                  @click="batchApprove"
                >
                  批量审批
                </el-button>
              </div>
            </div>

            <el-table
              :data="pendingApprovals"
              v-loading="loading.approvals"
              @selection-change="handleApprovalSelection"
              style="width: 100%"
            >
              <el-table-column type="selection" width="55" />

              <el-table-column label="请求编号" prop="requestNumber" width="140" />

              <el-table-column label="申请人" prop="requester" width="100" />

              <el-table-column label="服务名称" prop="serviceName" min-width="200" />

              <el-table-column label="部门" prop="department" width="120" />

              <el-table-column label="优先级" width="80">
                <template #default="{ row }">
                  <el-tag
                    :type="getPriorityType(row.priority)"
                    size="small"
                  >
                    {{ row.priority }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="提交时间" prop="createdAt" width="160" />

              <el-table-column label="等待时间" width="100">
                <template #default="{ row }">
                  {{ getWaitingTime(row.createdAt) }}
                </template>
              </el-table-column>

              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    @click="viewApprovalDetail(row)"
                  >
                    查看详情
                  </el-button>
                  <el-button
                    type="success"
                    size="small"
                    @click="approveRequest(row, 'approved')"
                  >
                    通过
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="approveRequest(row, 'rejected')"
                  >
                    拒绝
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 通知中心 -->
        <el-tab-pane label="通知中心" name="notifications">
          <div class="notifications-section">
            <div class="section-header">
              <h3>消息通知</h3>
              <div class="header-actions">
                <el-button @click="markAllAsRead">全部标记为已读</el-button>
                <el-button @click="loadNotifications">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>

            <div class="notifications-list">
              <div
                v-for="notification in notifications"
                :key="notification.id"
                class="notification-item"
                :class="{ 'unread': !notification.read }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-icon">
                  <el-icon :size="20">
                    <component :is="getNotificationIcon(notification.type)" />
                  </el-icon>
                </div>
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ notification.createdAt }}</div>
                </div>
                <div class="notification-actions">
                  <el-button
                    v-if="!notification.read"
                    type="primary"
                    size="small"
                    @click.stop="markAsRead(notification)"
                  >
                    标记已读
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 申请对话框 -->
    <ServiceApplyDialog
      v-model="showApplyDialog"
      :service="selectedApplyService"
      @submit="handleApplySubmit"
    />

    <!-- 请求详情对话框 -->
    <RequestDetailDialog
      v-model="showDetailDialog"
      :request="selectedRequest"
      @update="handleRequestUpdate"
    />

    <!-- 评价对话框 -->
    <RatingDialog
      v-model="showRatingDialog"
      :request="selectedRequest"
      @submit="handleRatingSubmit"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, Search, ArrowRight, Bell, CircleCheck,
  Warning, Document, User, Monitor, Connection, Key, Service
} from '@element-plus/icons-vue'
import { useRequestStore } from '@/stores/request'
import ServiceApplyDialog from '@/components/Request/ServiceApplyDialog.vue'
import RequestDetailDialog from '@/components/Request/RequestDetailDialog.vue'
import RatingDialog from '@/components/Request/RatingDialog.vue'

const router = useRouter()
const route = useRoute()
const requestStore = useRequestStore()

// 响应式数据
const activeTab = ref('apply')
const showApplyDialog = ref(false)
const showDetailDialog = ref(false)
const showRatingDialog = ref(false)
const selectedApplyService = ref(null)
const selectedRequest = ref(null)
const selectedApprovals = ref([])

// 筛选条件
const requestFilters = ref({
  status: 'all',
  category: 'all',
  dateRange: null,
  search: ''
})

// 分页信息
const requestPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

// 模拟数据
const recentServices = ref([])
const notifications = ref([])

// 搜索防抖定时器
let searchTimer = null

// 计算属性
const serviceCategories = computed(() => requestStore.serviceCategories)
const popularServices = computed(() => requestStore.popularServices)
const userRequests = computed(() => requestStore.userRequests)
const pendingApprovals = computed(() => requestStore.pendingApprovals)
const loading = computed(() => requestStore.loading)

// 方法定义
const refreshData = async () => {
  try {
    await Promise.all([
      requestStore.loadServiceCategories(),
      requestStore.loadServiceCatalog({ pageSize: 8 }),
      loadUserRequests(),
      loadPendingApprovals(),
      loadNotifications()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
    console.error('刷新数据失败:', error)
  }
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName

  // 根据标签页加载对应数据
  switch (tabName) {
    case 'my-requests':
      loadUserRequests()
      break
    case 'approvals':
      loadPendingApprovals()
      break
    case 'notifications':
      loadNotifications()
      break
  }
}

const quickApply = (service) => {
  selectedApplyService.value = service
  showApplyDialog.value = true
}

const browseCategory = (category) => {
  router.push({
    path: '/request/catalog',
    query: { category: category.id }
  })
}

const loadUserRequests = async () => {
  try {
    const params = {
      ...requestFilters.value,
      page: requestPagination.value.page,
      pageSize: requestPagination.value.pageSize
    }

    await requestStore.loadUserRequests(params)
    requestPagination.value.total = requestStore.pagination.requests.total
  } catch (error) {
    ElMessage.error('加载请求列表失败')
    console.error('加载请求失败:', error)
  }
}

const handleRequestSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(() => {
    loadUserRequests()
  }, 500)
}

const handleRequestSizeChange = (size) => {
  requestPagination.value.pageSize = size
  requestPagination.value.page = 1
  loadUserRequests()
}

const handleRequestCurrentChange = (page) => {
  requestPagination.value.page = page
  loadUserRequests()
}

const viewRequestDetail = (request) => {
  selectedRequest.value = request
  showDetailDialog.value = true
}

const cancelRequest = async (request) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消请求"${request.serviceName}"吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const { value: reason } = await ElMessageBox.prompt(
      '请输入取消原因：',
      '取消原因',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入取消原因'
      }
    )

    await requestStore.cancelUserRequest(request.id, reason)
    ElMessage.success('请求已取消')
    loadUserRequests()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消请求失败')
      console.error('取消请求失败:', error)
    }
  }
}

const rateRequest = (request) => {
  selectedRequest.value = request
  showRatingDialog.value = true
}

const canCancelRequest = (status) => {
  return ['submitted', 'pending_approval'].includes(status)
}

const canRateRequest = (status) => {
  return status === 'completed'
}

const loadPendingApprovals = async () => {
  try {
    await requestStore.loadPendingApprovals()
  } catch (error) {
    ElMessage.error('加载待审批列表失败')
    console.error('加载待审批失败:', error)
  }
}

const handleApprovalSelection = (selection) => {
  selectedApprovals.value = selection
}

const viewApprovalDetail = (request) => {
  selectedRequest.value = request
  showDetailDialog.value = true
}

const approveRequest = async (request, action) => {
  try {
    const actionText = action === 'approved' ? '通过' : '拒绝'

    await ElMessageBox.confirm(
      `确定要${actionText}请求"${request.serviceName}"吗？`,
      `确认${actionText}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: action === 'approved' ? 'success' : 'warning'
      }
    )

    let comment = ''
    if (action === 'rejected') {
      const { value } = await ElMessageBox.prompt(
        '请输入拒绝原因：',
        '拒绝原因',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /.+/,
          inputErrorMessage: '请输入拒绝原因'
        }
      )
      comment = value
    }

    await requestStore.processApproval(request.id, {
      action,
      comment
    })

    ElMessage.success(`请求已${actionText}`)
    loadPendingApprovals()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审批操作失败')
      console.error('审批失败:', error)
    }
  }
}

const batchApprove = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量通过选中的 ${selectedApprovals.value.length} 个请求吗？`,
      '批量审批',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    // 这里可以调用批量审批API
    ElMessage.success('批量审批成功')
    selectedApprovals.value = []
    loadPendingApprovals()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量审批失败')
      console.error('批量审批失败:', error)
    }
  }
}

const loadNotifications = async () => {
  try {
    // 模拟加载通知数据
    notifications.value = [
      {
        id: '1',
        type: 'approval',
        title: '审批通知',
        message: '您的笔记本电脑申请已通过审批',
        createdAt: '2024-01-15 10:30:00',
        read: false
      },
      {
        id: '2',
        type: 'completion',
        title: '完成通知',
        message: '您的VPN开通请求已完成',
        createdAt: '2024-01-14 16:45:00',
        read: true
      }
    ]
  } catch (error) {
    ElMessage.error('加载通知失败')
    console.error('加载通知失败:', error)
  }
}

const markAllAsRead = async () => {
  try {
    notifications.value.forEach(notification => {
      notification.read = true
    })
    ElMessage.success('已全部标记为已读')
  } catch (error) {
    ElMessage.error('标记失败')
    console.error('标记失败:', error)
  }
}

const markAsRead = async (notification) => {
  try {
    notification.read = true
    ElMessage.success('已标记为已读')
  } catch (error) {
    ElMessage.error('标记失败')
    console.error('标记失败:', error)
  }
}

const handleNotificationClick = (notification) => {
  if (!notification.read) {
    markAsRead(notification)
  }

  // 根据通知类型跳转到相应页面
  if (notification.requestId) {
    // 跳转到请求详情
    activeTab.value = 'my-requests'
  }
}

// 工具方法
const getServiceIcon = (serviceId) => {
  // 根据服务ID返回对应图标
  const iconMap = {
    'laptop': 'Monitor',
    'password-reset': 'Key',
    'vpn': 'Connection',
    'email': 'Message'
  }
  return iconMap[serviceId] || 'Service'
}

const getCategoryName = (categoryId) => {
  const category = serviceCategories.value.find(cat => cat.id === categoryId)
  return category ? category.name : '未知分类'
}

const getStatusType = (status) => {
  const statusMap = {
    submitted: 'info',
    pending_approval: 'warning',
    approved: 'success',
    rejected: 'danger',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getPriorityType = (priority) => {
  const priorityMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return priorityMap[priority] || 'info'
}

const getWaitingTime = (createdAt) => {
  const now = new Date()
  const created = new Date(createdAt)
  const diff = now - created
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (hours < 24) {
    return `${hours}小时`
  } else {
    const days = Math.floor(hours / 24)
    return `${days}天`
  }
}

const getNotificationIcon = (type) => {
  const iconMap = {
    approval: 'CircleCheck',
    completion: 'Document',
    reminder: 'Bell',
    warning: 'Warning'
  }
  return iconMap[type] || 'Bell'
}

// 事件处理
const handleApplySubmit = async (requestData) => {
  try {
    await requestStore.submitRequest(requestData)
    showApplyDialog.value = false
    selectedApplyService.value = null
    ElMessage.success('请求提交成功')

    // 切换到我的请求标签页
    activeTab.value = 'my-requests'
    loadUserRequests()
  } catch (error) {
    ElMessage.error('请求提交失败')
    console.error('提交请求失败:', error)
  }
}

const handleRequestUpdate = () => {
  showDetailDialog.value = false
  loadUserRequests()
}

const handleRatingSubmit = async (ratingData) => {
  try {
    await requestStore.submitRating(selectedRequest.value.id, ratingData)
    showRatingDialog.value = false
    selectedRequest.value = null
    ElMessage.success('评价提交成功')
    loadUserRequests()
  } catch (error) {
    ElMessage.error('评价提交失败')
    console.error('提交评价失败:', error)
  }
}

// 监听路由参数
watch(() => route.query, (newQuery) => {
  if (newQuery.tab) {
    activeTab.value = newQuery.tab
  }
  if (newQuery.serviceId) {
    // 如果指定了服务ID，直接打开申请对话框
    const service = popularServices.value.find(s => s.id === newQuery.serviceId)
    if (service) {
      quickApply(service)
    }
  }
}, { immediate: true })

// 生命周期
onMounted(async () => {
  try {
    await refreshData()
  } catch (error) {
    ElMessage.error('页面初始化失败')
    console.error('页面初始化失败:', error)
  }
})
</script>

<style scoped>
.self-service-portal {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 标签页样式 */
.tabs-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tabs-section :deep(.el-tabs__header) {
  margin: 0;
  background: #f8f9fa;
  padding: 0 24px;
}

.tabs-section :deep(.el-tabs__content) {
  padding: 24px;
}

/* 快速申请区域 */
.apply-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.apply-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

/* 热门服务卡片 */
.quick-service-card {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.quick-service-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.service-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 12px;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.service-time {
  font-size: 12px;
  color: #909399;
}

.service-badge {
  align-self: flex-end;
}

/* 服务分类卡片 */
.category-card {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.category-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
}

.category-icon {
  margin-bottom: 12px;
  color: #409EFF;
  transition: color 0.3s ease;
}

.category-card:hover .category-icon {
  color: white;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.category-count {
  font-size: 12px;
  color: #909399;
  transition: color 0.3s ease;
}

.category-card:hover .category-count {
  color: rgba(255, 255, 255, 0.8);
}

/* 最近使用服务 */
.recent-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-item:hover {
  border-color: #409EFF;
  background: #f0f8ff;
}

.recent-item .service-name {
  flex: 1;
  font-weight: 500;
  color: #303133;
}

.recent-item .last-used {
  font-size: 12px;
  color: #909399;
}

.recent-item .arrow {
  color: #C0C4CC;
  transition: all 0.3s ease;
}

.recent-item:hover .arrow {
  color: #409EFF;
  transform: translateX(4px);
}

/* 请求列表区域 */
.requests-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filters {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.service-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.service-cell .service-icon {
  color: #409EFF;
}

.service-cell .service-name {
  font-weight: 600;
  color: #303133;
}

.service-cell .service-category {
  font-size: 12px;
  color: #909399;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  text-align: center;
  margin-top: 4px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 审批区域 */
.approvals-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 通知中心 */
.notifications-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notification-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.notification-item.unread {
  border-left: 4px solid #409EFF;
  background: #f0f8ff;
}

.notification-icon {
  width: 40px;
  height: 40px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409EFF;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin-bottom: 8px;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-actions {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .self-service-portal {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .tabs-section :deep(.el-tabs__content) {
    padding: 16px;
  }

  .apply-section {
    gap: 24px;
  }

  .quick-service-card,
  .category-card {
    height: auto;
    min-height: 100px;
  }

  .filters .el-row {
    gap: 12px;
  }

  .filters .el-col {
    margin-bottom: 12px;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .notification-item {
    flex-direction: column;
    gap: 12px;
  }

  .notification-icon {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .quick-service-card {
    padding: 16px;
  }

  .category-card {
    padding: 20px;
  }

  .recent-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .recent-item .arrow {
    align-self: center;
  }
}
</style>
