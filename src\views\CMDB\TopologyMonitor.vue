<template>
  <div class="topology-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Monitor /></el-icon>
            拓扑监控中心
          </h1>
          <p class="page-description">实时监控IT基础设施拓扑状态，支持告警联动和影响分析</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshMonitoring">
            <el-icon><Refresh /></el-icon>
            刷新监控
          </el-button>
          <el-button @click="showAlertSettings = true">
            <el-icon><Bell /></el-icon>
            告警设置
          </el-button>
          <el-button @click="exportTopology">
            <el-icon><Download /></el-icon>
            导出拓扑
          </el-button>
          <el-button @click="showImpactAnalysis = true">
            <el-icon><WarningFilled /></el-icon>
            影响分析
          </el-button>
        </div>
      </div>
    </div>

    <!-- 监控状态概览 -->
    <div class="monitor-overview">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="stat in monitorStats" :key="stat.key">
          <div class="monitor-card" :class="stat.status">
            <div class="card-content">
              <div class="card-icon">
                <el-icon :size="32">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ stat.value }}</div>
                <div class="card-label">{{ stat.label }}</div>
                <div class="card-trend" v-if="stat.trend">
                  <el-icon :size="12">
                    <component :is="stat.trend > 0 ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                  <span>{{ Math.abs(stat.trend) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 拓扑监控视图 -->
        <el-col :span="16">
          <el-card class="topology-view">
            <template #header>
              <div class="card-header">
                <span>实时拓扑监控</span>
                <div class="view-controls">
                  <el-button-group>
                    <el-button
                      :type="viewMode === 'physical' ? 'primary' : ''"
                      size="small"
                      @click="setViewMode('physical')"
                    >
                      物理拓扑
                    </el-button>
                    <el-button
                      :type="viewMode === 'logical' ? 'primary' : ''"
                      size="small"
                      @click="setViewMode('logical')"
                    >
                      逻辑拓扑
                    </el-button>
                    <el-button
                      :type="viewMode === 'service' ? 'primary' : ''"
                      size="small"
                      @click="setViewMode('service')"
                    >
                      服务拓扑
                    </el-button>
                  </el-button-group>
                  
                  <el-button size="small" @click="toggleAutoRefresh">
                    <el-icon><Timer /></el-icon>
                    {{ autoRefresh ? '停止' : '开始' }}自动刷新
                  </el-button>
                </div>
              </div>
            </template>

            <div class="topology-container" ref="topologyContainer">
              <!-- 拓扑图渲染区域 -->
              <div class="topology-canvas" :style="canvasStyle">
                <!-- 设备节点 -->
                <div
                  v-for="device in visibleDevices"
                  :key="device.id"
                  class="device-node"
                  :class="[
                    `device-${device.type}`,
                    `status-${device.status}`,
                    {
                      'device-selected': selectedDevice?.id === device.id,
                      'device-highlighted': highlightedDevices.includes(device.id),
                      'device-alert': device.hasAlert
                    }
                  ]"
                  :style="getDeviceStyle(device)"
                  @click="selectDevice(device)"
                  @mouseenter="showDeviceTooltip(device, $event)"
                  @mouseleave="hideDeviceTooltip"
                >
                  <div class="device-icon">
                    <el-icon :size="getDeviceIconSize(device)">
                      <component :is="getDeviceIcon(device.type)" />
                    </el-icon>
                  </div>
                  
                  <!-- 状态指示器 -->
                  <div class="status-indicator" :class="`status-${device.status}`"></div>
                  
                  <!-- 告警指示器 -->
                  <div class="alert-indicator" v-if="device.hasAlert">
                    <el-icon :size="12" color="#F56C6C">
                      <WarningFilled />
                    </el-icon>
                  </div>
                  
                  <!-- 设备标签 -->
                  <div class="device-label" v-if="showLabels">{{ device.name }}</div>
                  
                  <!-- 性能指标 -->
                  <div class="device-metrics" v-if="device.metrics && showMetrics">
                    <div class="metric-item" v-for="metric in device.metrics" :key="metric.key">
                      <span class="metric-label">{{ metric.label }}:</span>
                      <span class="metric-value" :class="getMetricClass(metric)">{{ metric.value }}</span>
                    </div>
                  </div>
                </div>

                <!-- 连接线 -->
                <svg class="topology-connections" :width="canvasWidth" :height="canvasHeight">
                  <defs>
                    <!-- 连接状态标记 -->
                    <marker
                      v-for="status in connectionStatuses"
                      :key="`marker-${status}`"
                      :id="`marker-${status}`"
                      markerWidth="8"
                      markerHeight="8"
                      refX="6"
                      refY="3"
                      orient="auto"
                    >
                      <circle cx="3" cy="3" r="2" :fill="getConnectionColor(status)" />
                    </marker>
                  </defs>

                  <!-- 连接线 -->
                  <line
                    v-for="connection in visibleConnections"
                    :key="connection.id"
                    :x1="connection.x1 || 0"
                    :y1="connection.y1 || 0"
                    :x2="connection.x2 || 0"
                    :y2="connection.y2 || 0"
                    :class="[
                      `connection-${connection.type}`,
                      `status-${connection.status}`,
                      {
                        'connection-highlighted': highlightedConnections.includes(connection.id),
                        'connection-alert': connection.hasAlert
                      }
                    ]"
                    :stroke="getConnectionColor(connection.status)"
                    :stroke-width="connection.highlighted ? 4 : 2"
                    :marker-end="`url(#marker-${connection.status})`"
                    v-if="connection.x1 !== undefined && connection.x2 !== undefined"
                  />

                  <!-- 连接标签 -->
                  <text
                    v-for="connection in visibleConnections"
                    :key="`label-${connection.id}`"
                    :x="(connection.x1 + connection.x2) / 2"
                    :y="(connection.y1 + connection.y2) / 2"
                    class="connection-label"
                    v-if="connection.label && showConnectionLabels && connection.x1 !== undefined && connection.x2 !== undefined"
                  >
                    {{ connection.label }}
                  </text>
                </svg>

                <!-- 影响分析高亮区域 -->
                <div
                  v-if="impactAnalysis.active"
                  class="impact-overlay"
                  :style="impactOverlayStyle"
                >
                  <div class="impact-info">
                    <h4>影响分析</h4>
                    <p>受影响设备: {{ impactAnalysis.affectedDevices.length }}</p>
                    <p>受影响服务: {{ impactAnalysis.affectedServices.length }}</p>
                  </div>
                </div>

                <!-- 加载状态 -->
                <div class="topology-loading" v-if="loading">
                  <el-icon class="is-loading" :size="48"><Loading /></el-icon>
                  <p>正在加载拓扑数据...</p>
                </div>
              </div>

              <!-- 拓扑控制面板 -->
              <div class="topology-controls">
                <div class="control-group">
                  <el-button-group>
                    <el-button size="small" @click="zoomIn">
                      <el-icon><ZoomIn /></el-icon>
                    </el-button>
                    <el-button size="small" @click="zoomOut">
                      <el-icon><ZoomOut /></el-icon>
                    </el-button>
                    <el-button size="small" @click="resetView">
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </el-button-group>
                </div>
                
                <div class="control-group">
                  <el-switch
                    v-model="showLabels"
                    active-text="显示标签"
                    inactive-text="隐藏标签"
                    size="small"
                  />
                </div>
                
                <div class="control-group">
                  <el-switch
                    v-model="showMetrics"
                    active-text="显示指标"
                    inactive-text="隐藏指标"
                    size="small"
                  />
                </div>
                
                <div class="control-group">
                  <el-switch
                    v-model="showConnectionLabels"
                    active-text="连接标签"
                    inactive-text="隐藏连接"
                    size="small"
                  />
                </div>
              </div>

              <!-- 图例 -->
              <div class="topology-legend" v-if="showLegend">
                <div class="legend-title">状态图例</div>
                <div class="legend-items">
                  <div class="legend-item" v-for="status in deviceStatuses" :key="status.key">
                    <div class="legend-color" :class="`status-${status.key}`"></div>
                    <span>{{ status.label }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 监控信息面板 -->
        <el-col :span="8">
          <div class="monitor-panel">
            <!-- 实时告警 -->
            <el-card class="alert-panel">
              <template #header>
                <div class="card-header">
                  <span>实时告警</span>
                  <el-badge :value="activeAlerts.length" :max="99" type="danger">
                    <el-button size="small" @click="showAllAlerts">查看全部</el-button>
                  </el-badge>
                </div>
              </template>
              
              <div class="alert-list">
                <div
                  v-for="alert in recentAlerts"
                  :key="alert.id"
                  class="alert-item"
                  :class="`alert-${alert.level}`"
                  @click="selectAlert(alert)"
                >
                  <div class="alert-icon">
                    <el-icon :size="16">
                      <component :is="getAlertIcon(alert.level)" />
                    </el-icon>
                  </div>
                  <div class="alert-content">
                    <div class="alert-title">{{ alert.title }}</div>
                    <div class="alert-device">{{ alert.deviceName }}</div>
                    <div class="alert-time">{{ alert.time }}</div>
                  </div>
                </div>
              </div>
              
              <div class="alert-empty" v-if="recentAlerts.length === 0">
                <el-icon :size="32" color="#C0C4CC"><CircleCheck /></el-icon>
                <p>暂无活跃告警</p>
              </div>
            </el-card>

            <!-- 选中设备详情 -->
            <el-card class="device-details" v-if="selectedDevice">
              <template #header>
                <div class="card-header">
                  <span>{{ selectedDevice.name }}</span>
                  <el-button type="text" size="small" @click="clearSelection">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </template>
              
              <div class="device-info">
                <div class="info-item">
                  <label>设备类型:</label>
                  <span>{{ getDeviceTypeText(selectedDevice.type) }}</span>
                </div>
                <div class="info-item">
                  <label>运行状态:</label>
                  <el-tag :type="getStatusTagType(selectedDevice.status)">
                    {{ getStatusText(selectedDevice.status) }}
                  </el-tag>
                </div>
                <div class="info-item" v-if="selectedDevice.ip">
                  <label>IP地址:</label>
                  <span>{{ selectedDevice.ip }}</span>
                </div>
                <div class="info-item" v-if="selectedDevice.location">
                  <label>物理位置:</label>
                  <span>{{ selectedDevice.location }}</span>
                </div>
                <div class="info-item">
                  <label>最后更新:</label>
                  <span>{{ selectedDevice.lastUpdate }}</span>
                </div>
              </div>

              <!-- 性能指标 -->
              <div class="device-metrics-panel" v-if="selectedDevice.metrics">
                <h4>性能指标</h4>
                <div class="metrics-grid">
                  <div
                    v-for="metric in selectedDevice.metrics"
                    :key="metric.key"
                    class="metric-card"
                    :class="getMetricClass(metric)"
                  >
                    <div class="metric-label">{{ metric.label }}</div>
                    <div class="metric-value">{{ metric.value }}</div>
                    <div class="metric-unit" v-if="metric.unit">{{ metric.unit }}</div>
                  </div>
                </div>
              </div>

              <!-- 设备操作 -->
              <div class="device-actions">
                <el-button size="small" @click="viewDeviceHistory">历史数据</el-button>
                <el-button size="small" @click="analyzeDeviceImpact">影响分析</el-button>
                <el-button size="small" @click="manageDevice">设备管理</el-button>
              </div>
            </el-card>

            <!-- 监控统计 -->
            <el-card class="monitor-stats">
              <template #header>
                <span>监控统计</span>
              </template>
              
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="stat-label">监控设备:</span>
                  <span class="stat-value">{{ monitoringStats.totalDevices }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">在线设备:</span>
                  <span class="stat-value text-success">{{ monitoringStats.onlineDevices }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">告警设备:</span>
                  <span class="stat-value text-warning">{{ monitoringStats.alertDevices }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">离线设备:</span>
                  <span class="stat-value text-danger">{{ monitoringStats.offlineDevices }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">连接数:</span>
                  <span class="stat-value">{{ monitoringStats.totalConnections }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">活跃告警:</span>
                  <span class="stat-value text-danger">{{ monitoringStats.activeAlerts }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 告警设置对话框 -->
    <el-dialog
      v-model="showAlertSettings"
      title="告警设置"
      width="600px"
      @close="resetAlertSettings"
    >
      <el-form :model="alertSettings" label-width="120px">
        <el-form-item label="告警级别">
          <el-checkbox-group v-model="alertSettings.levels">
            <el-checkbox label="critical">严重</el-checkbox>
            <el-checkbox label="warning">警告</el-checkbox>
            <el-checkbox label="info">信息</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="告警类型">
          <el-checkbox-group v-model="alertSettings.types">
            <el-checkbox label="performance">性能告警</el-checkbox>
            <el-checkbox label="availability">可用性告警</el-checkbox>
            <el-checkbox label="security">安全告警</el-checkbox>
            <el-checkbox label="capacity">容量告警</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="通知方式">
          <el-checkbox-group v-model="alertSettings.notifications">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="webhook">Webhook</el-checkbox>
            <el-checkbox label="popup">弹窗提醒</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="刷新间隔">
          <el-select v-model="alertSettings.refreshInterval" style="width: 200px;">
            <el-option label="10秒" :value="10" />
            <el-option label="30秒" :value="30" />
            <el-option label="1分钟" :value="60" />
            <el-option label="5分钟" :value="300" />
          </el-select>
        </el-form-item>

        <el-form-item label="声音提醒">
          <el-switch v-model="alertSettings.soundAlert" />
        </el-form-item>

        <el-form-item label="自动确认">
          <el-switch v-model="alertSettings.autoAck" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAlertSettings = false">取消</el-button>
          <el-button @click="resetAlertSettings">重置</el-button>
          <el-button type="primary" @click="saveAlertSettings">
            保存设置
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 影响分析对话框 -->
    <el-dialog
      v-model="showImpactAnalysis"
      title="影响分析"
      width="800px"
      @close="resetImpactAnalysis"
    >
      <div class="impact-analysis-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>选择分析源</span>
              </template>

              <el-form :model="impactAnalysisForm" label-width="100px">
                <el-form-item label="设备类型">
                  <el-select v-model="impactAnalysisForm.deviceType" style="width: 100%;">
                    <el-option label="路由器" value="router" />
                    <el-option label="交换机" value="switch" />
                    <el-option label="服务器" value="server" />
                    <el-option label="数据库" value="database" />
                    <el-option label="应用服务" value="application" />
                  </el-select>
                </el-form-item>

                <el-form-item label="设备名称">
                  <el-select v-model="impactAnalysisForm.deviceId" style="width: 100%;">
                    <el-option
                      v-for="device in filteredDevices"
                      :key="device.id"
                      :label="device.name"
                      :value="device.id"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="故障类型">
                  <el-select v-model="impactAnalysisForm.faultType" style="width: 100%;">
                    <el-option label="设备离线" value="offline" />
                    <el-option label="性能异常" value="performance" />
                    <el-option label="连接中断" value="connection" />
                    <el-option label="服务停止" value="service" />
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="runImpactAnalysis">
                    开始分析
                  </el-button>
                  <el-button @click="clearImpactAnalysis">
                    清除结果
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card>
              <template #header>
                <span>影响分析结果</span>
              </template>

              <div class="impact-results" v-if="impactResults.analyzed">
                <div class="result-section">
                  <h4>直接影响设备 ({{ impactResults.directImpact.length }})</h4>
                  <el-tag
                    v-for="device in impactResults.directImpact"
                    :key="device.id"
                    type="danger"
                    style="margin: 2px;"
                  >
                    {{ device.name }}
                  </el-tag>
                </div>

                <div class="result-section">
                  <h4>间接影响设备 ({{ impactResults.indirectImpact.length }})</h4>
                  <el-tag
                    v-for="device in impactResults.indirectImpact"
                    :key="device.id"
                    type="warning"
                    style="margin: 2px;"
                  >
                    {{ device.name }}
                  </el-tag>
                </div>

                <div class="result-section">
                  <h4>受影响业务 ({{ impactResults.affectedServices.length }})</h4>
                  <el-tag
                    v-for="service in impactResults.affectedServices"
                    :key="service"
                    type="info"
                    style="margin: 2px;"
                  >
                    {{ service }}
                  </el-tag>
                </div>

                <div class="result-section">
                  <h4>影响评估</h4>
                  <el-progress
                    :percentage="impactResults.severity"
                    :color="getImpactColor(impactResults.severity)"
                    :status="impactResults.severity > 80 ? 'exception' : 'success'"
                  />
                  <p class="impact-description">{{ impactResults.description }}</p>
                </div>
              </div>

              <div class="no-results" v-else>
                <el-empty description="请选择设备并开始分析" />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showImpactAnalysis = false">关闭</el-button>
          <el-button type="primary" @click="exportImpactReport">
            导出报告
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)
const refreshInterval = ref(null)
const viewMode = ref('physical')
const selectedDevice = ref(null)
const highlightedDevices = ref([])
const highlightedConnections = ref([])
const showLabels = ref(true)
const showMetrics = ref(false)
const showConnectionLabels = ref(false)
const showLegend = ref(true)
const showAlertSettings = ref(false)
const showImpactAnalysis = ref(false)

// 告警设置
const alertSettings = reactive({
  levels: ['critical', 'warning'],
  types: ['performance', 'availability'],
  notifications: ['email', 'popup'],
  refreshInterval: 30,
  soundAlert: true,
  autoAck: false
})

// 影响分析表单
const impactAnalysisForm = reactive({
  deviceType: '',
  deviceId: '',
  faultType: ''
})

// 影响分析结果
const impactResults = reactive({
  analyzed: false,
  directImpact: [],
  indirectImpact: [],
  affectedServices: [],
  severity: 0,
  description: ''
})
const zoomLevel = ref(1)
const panX = ref(0)
const panY = ref(0)
const canvasWidth = ref(1000)
const canvasHeight = ref(600)

// 设备和连接数据
const devices = ref([])
const connections = ref([])
const alerts = ref([])

// 监控统计数据
const monitorStats = ref([
  {
    key: 'online',
    label: '在线设备',
    value: '156',
    icon: 'CircleCheck',
    status: 'success',
    trend: 2.1
  },
  {
    key: 'warning',
    label: '告警设备',
    value: '8',
    icon: 'WarningFilled',
    status: 'warning',
    trend: -1.5
  },
  {
    key: 'offline',
    label: '离线设备',
    value: '3',
    icon: 'CircleClose',
    status: 'danger',
    trend: 0.8
  },
  {
    key: 'performance',
    label: '性能异常',
    value: '12',
    icon: 'TrendCharts',
    status: 'info',
    trend: -3.2
  }
])

// 影响分析数据
const impactAnalysis = reactive({
  active: false,
  sourceDevice: null,
  affectedDevices: [],
  affectedServices: []
})

// 监控统计
const monitoringStats = reactive({
  totalDevices: 0,
  onlineDevices: 0,
  alertDevices: 0,
  offlineDevices: 0,
  totalConnections: 0,
  activeAlerts: 0
})

// 设备状态配置
const deviceStatuses = ref([
  { key: 'online', label: '在线', color: '#4CAF50' },
  { key: 'warning', label: '告警', color: '#FF9800' },
  { key: 'error', label: '故障', color: '#F44336' },
  { key: 'offline', label: '离线', color: '#9E9E9E' }
])

const connectionStatuses = ref(['normal', 'warning', 'error', 'offline'])

// 计算属性
const visibleDevices = computed(() => {
  return devices.value.filter(device => {
    // 根据视图模式过滤设备
    if (viewMode.value === 'physical') {
      return device.layer === 'physical'
    } else if (viewMode.value === 'logical') {
      return device.layer === 'logical'
    } else if (viewMode.value === 'service') {
      return device.layer === 'service'
    }
    return true
  })
})

const visibleConnections = computed(() => {
  const visibleDeviceIds = new Set(visibleDevices.value.map(d => d.id))
  return connections.value.filter(conn =>
    visibleDeviceIds.has(conn.source) && visibleDeviceIds.has(conn.target)
  )
})

const activeAlerts = computed(() => {
  return alerts.value.filter(alert => alert.status === 'active')
})

const recentAlerts = computed(() => {
  return activeAlerts.value.slice(0, 5)
})

const canvasStyle = computed(() => ({
  transform: `scale(${zoomLevel.value}) translate(${panX.value}px, ${panY.value}px)`,
  transformOrigin: 'center center'
}))

const impactOverlayStyle = computed(() => {
  if (!impactAnalysis.active || !impactAnalysis.sourceDevice) return {}

  const device = impactAnalysis.sourceDevice
  return {
    left: (device.x - 50) + 'px',
    top: (device.y - 50) + 'px',
    width: '100px',
    height: '100px'
  }
})

const filteredDevices = computed(() => {
  if (!impactAnalysisForm.deviceType) return devices.value
  return devices.value.filter(device => device.type === impactAnalysisForm.deviceType)
})

// 方法
const refreshMonitoring = async () => {
  loading.value = true
  try {
    await loadMonitoringData()
    ElMessage.success('监控数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadMonitoringData = async () => {
  // 模拟加载数据
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 生成示例数据
  devices.value = generateSampleDevices()
  connections.value = generateSampleConnections()
  alerts.value = generateSampleAlerts()

  updateMonitoringStats()
  calculateDevicePositions()
}

const generateSampleDevices = () => {
  const sampleDevices = [
    {
      id: 'router-01',
      name: '核心路由器',
      type: 'router',
      status: 'online',
      layer: 'physical',
      ip: '***********',
      location: '机房A-01',
      lastUpdate: '2024-01-15 14:30:00',
      hasAlert: false,
      metrics: [
        { key: 'cpu', label: 'CPU', value: '45%', unit: '%', status: 'normal' },
        { key: 'memory', label: '内存', value: '68%', unit: '%', status: 'warning' },
        { key: 'bandwidth', label: '带宽', value: '1.2Gbps', unit: 'Gbps', status: 'normal' }
      ]
    },
    {
      id: 'switch-01',
      name: '接入交换机',
      type: 'switch',
      status: 'warning',
      layer: 'physical',
      ip: '***********0',
      location: '机房A-02',
      lastUpdate: '2024-01-15 14:28:00',
      hasAlert: true,
      metrics: [
        { key: 'cpu', label: 'CPU', value: '78%', unit: '%', status: 'warning' },
        { key: 'memory', label: '内存', value: '82%', unit: '%', status: 'error' },
        { key: 'ports', label: '端口', value: '22/24', unit: '', status: 'normal' }
      ]
    },
    {
      id: 'server-01',
      name: 'Web服务器',
      type: 'server',
      status: 'online',
      layer: 'physical',
      ip: '*************',
      location: '机房B-01',
      lastUpdate: '2024-01-15 14:32:00',
      hasAlert: false,
      metrics: [
        { key: 'cpu', label: 'CPU', value: '35%', unit: '%', status: 'normal' },
        { key: 'memory', label: '内存', value: '56%', unit: '%', status: 'normal' },
        { key: 'disk', label: '磁盘', value: '73%', unit: '%', status: 'warning' }
      ]
    },
    {
      id: 'db-01',
      name: '数据库服务器',
      type: 'database',
      status: 'error',
      layer: 'logical',
      ip: '*************',
      location: '机房B-02',
      lastUpdate: '2024-01-15 14:25:00',
      hasAlert: true,
      metrics: [
        { key: 'cpu', label: 'CPU', value: '92%', unit: '%', status: 'error' },
        { key: 'memory', label: '内存', value: '95%', unit: '%', status: 'error' },
        { key: 'connections', label: '连接数', value: '450/500', unit: '', status: 'warning' }
      ]
    },
    {
      id: 'app-01',
      name: '应用服务',
      type: 'application',
      status: 'online',
      layer: 'service',
      lastUpdate: '2024-01-15 14:31:00',
      hasAlert: false,
      metrics: [
        { key: 'response', label: '响应时间', value: '120ms', unit: 'ms', status: 'normal' },
        { key: 'throughput', label: '吞吐量', value: '1200/s', unit: 'req/s', status: 'normal' },
        { key: 'errors', label: '错误率', value: '0.5%', unit: '%', status: 'normal' }
      ]
    }
  ]

  return sampleDevices.map((device, index) => ({
    ...device,
    x: 100 + (index % 3) * 250,
    y: 80 + Math.floor(index / 3) * 180
  }))
}

const generateSampleConnections = () => {
  return [
    {
      id: 'conn-1',
      source: 'router-01',
      target: 'switch-01',
      type: 'ethernet',
      status: 'normal',
      label: '1Gbps',
      hasAlert: false
    },
    {
      id: 'conn-2',
      source: 'switch-01',
      target: 'server-01',
      type: 'ethernet',
      status: 'warning',
      label: '100Mbps',
      hasAlert: true
    },
    {
      id: 'conn-3',
      source: 'server-01',
      target: 'db-01',
      type: 'tcp',
      status: 'error',
      label: 'DB连接',
      hasAlert: true
    },
    {
      id: 'conn-4',
      source: 'server-01',
      target: 'app-01',
      type: 'http',
      status: 'normal',
      label: 'HTTP',
      hasAlert: false
    }
  ]
}

const generateSampleAlerts = () => {
  return [
    {
      id: 'alert-1',
      title: 'CPU使用率过高',
      deviceName: '数据库服务器',
      deviceId: 'db-01',
      level: 'critical',
      status: 'active',
      time: '2024-01-15 14:25:00',
      description: 'CPU使用率达到92%，超过阈值90%'
    },
    {
      id: 'alert-2',
      title: '内存使用率告警',
      deviceName: '接入交换机',
      deviceId: 'switch-01',
      level: 'warning',
      status: 'active',
      time: '2024-01-15 14:28:00',
      description: '内存使用率达到82%，超过阈值80%'
    },
    {
      id: 'alert-3',
      title: '网络连接异常',
      deviceName: 'Web服务器',
      deviceId: 'server-01',
      level: 'warning',
      status: 'active',
      time: '2024-01-15 14:30:00',
      description: '到数据库服务器的连接出现间歇性中断'
    }
  ]
}

const updateMonitoringStats = () => {
  monitoringStats.totalDevices = devices.value.length
  monitoringStats.onlineDevices = devices.value.filter(d => d.status === 'online').length
  monitoringStats.alertDevices = devices.value.filter(d => d.hasAlert).length
  monitoringStats.offlineDevices = devices.value.filter(d => d.status === 'offline').length
  monitoringStats.totalConnections = connections.value.length
  monitoringStats.activeAlerts = activeAlerts.value.length

  // 更新监控统计卡片
  monitorStats.value[0].value = monitoringStats.onlineDevices.toString()
  monitorStats.value[1].value = monitoringStats.alertDevices.toString()
  monitorStats.value[2].value = monitoringStats.offlineDevices.toString()
  monitorStats.value[3].value = devices.value.filter(d =>
    d.metrics?.some(m => m.status === 'warning' || m.status === 'error')
  ).length.toString()
}

const calculateDevicePositions = () => {
  // 计算连接线坐标
  connections.value.forEach(connection => {
    const sourceDevice = devices.value.find(d => d.id === connection.source)
    const targetDevice = devices.value.find(d => d.id === connection.target)

    if (sourceDevice && targetDevice) {
      connection.x1 = sourceDevice.x + 30
      connection.y1 = sourceDevice.y + 30
      connection.x2 = targetDevice.x + 30
      connection.y2 = targetDevice.y + 30
    }
  })
}

const setViewMode = (mode) => {
  viewMode.value = mode
  ElMessage.info(`切换到${getViewModeText(mode)}`)
}

const getViewModeText = (mode) => {
  const modes = {
    physical: '物理拓扑',
    logical: '逻辑拓扑',
    service: '服务拓扑'
  }
  return modes[mode] || mode
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value

  if (autoRefresh.value) {
    startAutoRefresh()
    ElMessage.success('已开启自动刷新')
  } else {
    stopAutoRefresh()
    ElMessage.info('已停止自动刷新')
  }
}

const startAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }

  refreshInterval.value = setInterval(() => {
    loadMonitoringData()
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

const selectDevice = (device) => {
  selectedDevice.value = device
  highlightRelatedDevices(device)
}

const highlightRelatedDevices = (device) => {
  const relatedDeviceIds = new Set([device.id])
  const relatedConnectionIds = new Set()

  connections.value.forEach(connection => {
    if (connection.source === device.id || connection.target === device.id) {
      relatedConnectionIds.add(connection.id)
      relatedDeviceIds.add(connection.source)
      relatedDeviceIds.add(connection.target)
    }
  })

  highlightedDevices.value = Array.from(relatedDeviceIds)
  highlightedConnections.value = Array.from(relatedConnectionIds)
}

const clearSelection = () => {
  selectedDevice.value = null
  highlightedDevices.value = []
  highlightedConnections.value = []
}

const getDeviceStyle = (device) => ({
  left: device.x + 'px',
  top: device.y + 'px',
  zIndex: selectedDevice.value?.id === device.id ? 1000 : 100
})

const getDeviceIconSize = (device) => {
  const sizes = {
    router: 32,
    switch: 28,
    server: 30,
    database: 32,
    application: 26
  }
  return sizes[device.type] || 24
}

const getDeviceIcon = (type) => {
  const icons = {
    router: 'Connection',
    switch: 'Share',
    server: 'Monitor',
    database: 'Coin',
    application: 'Grid',
    firewall: 'Lock'
  }
  return icons[type] || 'Box'
}

const getConnectionColor = (status) => {
  const colors = {
    normal: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    offline: '#9E9E9E'
  }
  return colors[status] || '#666'
}

const getDeviceTypeText = (type) => {
  const types = {
    router: '路由器',
    switch: '交换机',
    server: '服务器',
    database: '数据库',
    application: '应用服务',
    firewall: '防火墙'
  }
  return types[type] || type
}

const getStatusText = (status) => {
  const texts = {
    online: '在线',
    warning: '告警',
    error: '故障',
    offline: '离线'
  }
  return texts[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    online: 'success',
    warning: 'warning',
    error: 'danger',
    offline: 'info'
  }
  return types[status] || ''
}

const getMetricClass = (metric) => {
  return `metric-${metric.status}`
}

const getAlertIcon = (level) => {
  const icons = {
    critical: 'CircleCloseFilled',
    warning: 'WarningFilled',
    info: 'InfoFilled'
  }
  return icons[level] || 'WarningFilled'
}

const selectAlert = (alert) => {
  // 选中告警对应的设备
  const device = devices.value.find(d => d.id === alert.deviceId)
  if (device) {
    selectDevice(device)
  }
  ElMessage.info(`查看告警: ${alert.title}`)
}

const showAllAlerts = () => {
  ElMessage.info('显示所有告警列表')
}

const showDeviceTooltip = (device, event) => {
  // 显示设备悬浮提示
}

const hideDeviceTooltip = () => {
  // 隐藏设备悬浮提示
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.1)
}

const resetView = () => {
  zoomLevel.value = 1
  panX.value = 0
  panY.value = 0
}

const exportTopology = () => {
  ElMessage.success('拓扑图导出功能开发中...')
}

const viewDeviceHistory = () => {
  if (selectedDevice.value) {
    ElMessage.info(`查看 ${selectedDevice.value.name} 历史数据`)
  }
}

const analyzeDeviceImpact = () => {
  if (selectedDevice.value) {
    impactAnalysis.active = true
    impactAnalysis.sourceDevice = selectedDevice.value
    impactAnalysis.affectedDevices = getAffectedDevices(selectedDevice.value)
    impactAnalysis.affectedServices = getAffectedServices(selectedDevice.value)
    ElMessage.info(`分析 ${selectedDevice.value.name} 的影响范围`)
  }
}

const getAffectedDevices = (device) => {
  // 模拟影响分析逻辑
  return devices.value.filter(d => d.id !== device.id).slice(0, 3)
}

const getAffectedServices = (device) => {
  // 模拟受影响服务（简单版本）
  return ['订单服务', '支付服务', '用户服务'].slice(0, 2)
}

const manageDevice = () => {
  if (selectedDevice.value) {
    ElMessage.info(`管理设备: ${selectedDevice.value.name}`)
  }
}

// 告警设置相关方法
const resetAlertSettings = () => {
  Object.assign(alertSettings, {
    levels: ['critical', 'warning'],
    types: ['performance', 'availability'],
    notifications: ['email', 'popup'],
    refreshInterval: 30,
    soundAlert: true,
    autoAck: false
  })
}

const saveAlertSettings = () => {
  ElMessage.success('告警设置已保存')

  // 应用新的告警设置
  if (alertSettings.refreshInterval !== 30) {
    stopAutoRefresh()
    if (autoRefresh.value) {
      refreshInterval.value = setInterval(() => {
        loadMonitoringData()
      }, alertSettings.refreshInterval * 1000)
    }
  }

  showAlertSettings.value = false
}

// 影响分析相关方法
const resetImpactAnalysis = () => {
  Object.assign(impactAnalysisForm, {
    deviceType: '',
    deviceId: '',
    faultType: ''
  })

  Object.assign(impactResults, {
    analyzed: false,
    directImpact: [],
    indirectImpact: [],
    affectedServices: [],
    severity: 0,
    description: ''
  })
}

const runImpactAnalysis = () => {
  if (!impactAnalysisForm.deviceId || !impactAnalysisForm.faultType) {
    ElMessage.warning('请选择设备和故障类型')
    return
  }

  const sourceDevice = devices.value.find(d => d.id === impactAnalysisForm.deviceId)
  if (!sourceDevice) {
    ElMessage.error('未找到指定设备')
    return
  }

  ElMessage.info('正在分析影响范围...')

  // 模拟影响分析
  setTimeout(() => {
    const directImpact = getDirectImpactDevices(sourceDevice)
    const indirectImpact = getIndirectImpactDevices(sourceDevice, directImpact)
    const affectedServices = getDetailedAffectedServices(sourceDevice, directImpact, indirectImpact)
    const severity = calculateImpactSeverity(directImpact, indirectImpact, affectedServices)

    Object.assign(impactResults, {
      analyzed: true,
      directImpact,
      indirectImpact,
      affectedServices,
      severity,
      description: generateImpactDescription(severity, directImpact.length, indirectImpact.length, affectedServices.length)
    })

    ElMessage.success('影响分析完成')
  }, 1500)
}

const getDirectImpactDevices = (sourceDevice) => {
  // 获取直接连接的设备
  const directConnections = connections.value.filter(conn =>
    conn.source === sourceDevice.id || conn.target === sourceDevice.id
  )

  const directDeviceIds = directConnections.map(conn =>
    conn.source === sourceDevice.id ? conn.target : conn.source
  )

  return devices.value.filter(device => directDeviceIds.includes(device.id))
}

const getIndirectImpactDevices = (sourceDevice, directImpact) => {
  // 获取间接影响的设备（通过直接影响设备连接的设备）
  const indirectDeviceIds = new Set()

  directImpact.forEach(device => {
    const indirectConnections = connections.value.filter(conn =>
      conn.source === device.id || conn.target === device.id
    )

    indirectConnections.forEach(conn => {
      const targetId = conn.source === device.id ? conn.target : conn.source
      if (targetId !== sourceDevice.id && !directImpact.find(d => d.id === targetId)) {
        indirectDeviceIds.add(targetId)
      }
    })
  })

  return devices.value.filter(device => indirectDeviceIds.has(device.id))
}

const getDetailedAffectedServices = (sourceDevice, directImpact, indirectImpact) => {
  // 模拟受影响的业务服务
  const services = ['订单服务', '支付服务', '用户服务', '库存服务', '物流服务']
  const affectedCount = Math.min(services.length, Math.ceil((directImpact.length + indirectImpact.length) / 2))
  return services.slice(0, affectedCount)
}

const calculateImpactSeverity = (directImpact, indirectImpact, affectedServices) => {
  // 计算影响严重程度（0-100）
  const directWeight = directImpact.length * 30
  const indirectWeight = indirectImpact.length * 15
  const serviceWeight = affectedServices.length * 20

  return Math.min(100, directWeight + indirectWeight + serviceWeight)
}

const generateImpactDescription = (severity, directCount, indirectCount, serviceCount) => {
  if (severity >= 80) {
    return `严重影响：${directCount}个设备直接受影响，${indirectCount}个设备间接受影响，${serviceCount}个业务服务可能中断。建议立即处理。`
  } else if (severity >= 50) {
    return `中等影响：${directCount}个设备直接受影响，${indirectCount}个设备间接受影响，${serviceCount}个业务服务可能受到影响。`
  } else {
    return `轻微影响：${directCount}个设备直接受影响，${indirectCount}个设备间接受影响，对业务影响较小。`
  }
}

const getImpactColor = (severity) => {
  if (severity >= 80) return '#F56C6C'
  if (severity >= 50) return '#E6A23C'
  return '#67C23A'
}

const clearImpactAnalysis = () => {
  Object.assign(impactResults, {
    analyzed: false,
    directImpact: [],
    indirectImpact: [],
    affectedServices: [],
    severity: 0,
    description: ''
  })
  ElMessage.info('分析结果已清除')
}

const exportImpactReport = () => {
  if (!impactResults.analyzed) {
    ElMessage.warning('请先进行影响分析')
    return
  }

  ElMessage.success('影响分析报告导出功能开发中...')
}

// 生命周期
onMounted(() => {
  loadMonitoringData()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.topology-monitor {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 监控状态概览 */
.monitor-overview {
  margin-bottom: 24px;
}

.monitor-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid #e5e7eb;
  transition: all 0.3s ease;
}

.monitor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.monitor-card.success {
  border-left-color: #10b981;
}

.monitor-card.warning {
  border-left-color: #f59e0b;
}

.monitor-card.danger {
  border-left-color: #ef4444;
}

.monitor-card.info {
  border-left-color: #3b82f6;
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: #f3f4f6;
}

.monitor-card.success .card-icon {
  background: #d1fae5;
  color: #10b981;
}

.monitor-card.warning .card-icon {
  background: #fef3c7;
  color: #f59e0b;
}

.monitor-card.danger .card-icon {
  background: #fee2e2;
  color: #ef4444;
}

.monitor-card.info .card-icon {
  background: #dbeafe;
  color: #3b82f6;
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.card-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  color: #10b981;
}

/* 拓扑视图 */
.topology-view {
  height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.topology-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: #fafbfc;
  border-radius: 4px;
}

.topology-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  transition: transform 0.3s ease;
}

/* 设备节点 */
.device-node {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: white;
  border: 2px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.device-node:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.device-node.device-selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.device-node.device-highlighted {
  border-color: #10b981;
  background: #ecfdf5;
}

.device-node.device-alert {
  animation: alertPulse 2s infinite;
}

@keyframes alertPulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.4); }
  50% { box-shadow: 0 0 0 10px rgba(245, 108, 108, 0); }
}

/* 设备类型样式 */
.device-router {
  border-color: #9C27B0;
}

.device-switch {
  border-color: #4CAF50;
}

.device-server {
  border-color: #2196F3;
}

.device-database {
  border-color: #FF9800;
}

.device-application {
  border-color: #607D8B;
}

.device-icon {
  margin-bottom: 4px;
}

.device-label {
  font-size: 10px;
  color: #374151;
  text-align: center;
  max-width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-online {
  background: #4CAF50;
}

.status-warning {
  background: #FF9800;
}

.status-error {
  background: #F44336;
}

.status-offline {
  background: #9E9E9E;
}

.alert-indicator {
  position: absolute;
  top: -6px;
  left: -6px;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: alertBlink 1s infinite;
}

@keyframes alertBlink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.device-metrics {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.device-node:hover .device-metrics {
  opacity: 1;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  margin-right: 8px;
}

.metric-value.metric-normal {
  color: #4CAF50;
}

.metric-value.metric-warning {
  color: #FF9800;
}

.metric-value.metric-error {
  color: #F44336;
}

/* 连接线 */
.topology-connections {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.connection-label {
  font-size: 10px;
  fill: #6b7280;
  text-anchor: middle;
  dominant-baseline: middle;
}

line.connection-highlighted {
  stroke-width: 4 !important;
  opacity: 1;
}

line.connection-alert {
  animation: connectionAlert 1s infinite;
}

@keyframes connectionAlert {
  0%, 100% { stroke-opacity: 1; }
  50% { stroke-opacity: 0.3; }
}

/* 拓扑控制面板 */
.topology-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 图例 */
.topology-legend {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.legend-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

/* 影响分析覆盖层 */
.impact-overlay {
  position: absolute;
  background: rgba(59, 130, 246, 0.1);
  border: 2px dashed #3b82f6;
  border-radius: 8px;
  pointer-events: none;
}

.impact-info {
  position: absolute;
  top: -60px;
  left: 0;
  background: #3b82f6;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.impact-info h4 {
  margin: 0 0 4px 0;
  font-size: 12px;
}

.impact-info p {
  margin: 2px 0;
  font-size: 10px;
}

/* 加载状态 */
.topology-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #6b7280;
}

.topology-loading .el-icon {
  margin-bottom: 16px;
}

/* 监控信息面板 */
.monitor-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 告警面板 */
.alert-panel {
  max-height: 300px;
}

.alert-list {
  max-height: 200px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.alert-item:hover {
  background: #f8fafc;
}

.alert-item.alert-critical {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.alert-item.alert-warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.alert-item.alert-info {
  border-left-color: #3b82f6;
  background: #eff6ff;
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.alert-device {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.alert-time {
  font-size: 11px;
  color: #9ca3af;
}

.alert-empty {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.alert-empty .el-icon {
  margin-bottom: 12px;
}

/* 设备详情面板 */
.device-details .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 500;
  color: #374151;
  min-width: 60px;
}

.device-metrics-panel {
  margin: 16px 0;
}

.device-metrics-panel h4 {
  font-size: 14px;
  color: #374151;
  margin: 0 0 12px 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.metric-card {
  background: #f8fafc;
  border-radius: 4px;
  padding: 8px;
  text-align: center;
  border-left: 3px solid #e5e7eb;
}

.metric-card.metric-normal {
  border-left-color: #10b981;
}

.metric-card.metric-warning {
  border-left-color: #f59e0b;
}

.metric-card.metric-error {
  border-left-color: #ef4444;
}

.metric-label {
  font-size: 11px;
  color: #6b7280;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.metric-unit {
  font-size: 10px;
  color: #9ca3af;
}

.device-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.device-actions .el-button {
  flex: 1;
  min-width: 0;
}

/* 监控统计面板 */
.monitor-stats .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.text-success {
  color: #10b981 !important;
}

.text-warning {
  color: #f59e0b !important;
}

.text-danger {
  color: #ef4444 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content .el-col:last-child {
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .topology-monitor {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .topology-view {
    height: 400px;
  }

  .device-node {
    width: 50px;
    height: 50px;
  }

  .device-label {
    font-size: 9px;
    max-width: 40px;
  }

  .topology-controls {
    position: relative;
    margin-top: 16px;
    flex-direction: row;
    justify-content: center;
  }

  .topology-legend {
    position: relative;
    margin-bottom: 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .device-actions {
    flex-direction: column;
  }

  .monitor-stats .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .view-controls {
    flex-direction: column;
    gap: 8px;
  }

  .topology-view {
    height: 300px;
  }

  .device-node {
    width: 40px;
    height: 40px;
  }

  .device-label {
    font-size: 8px;
    max-width: 30px;
  }

  .control-group {
    flex-direction: column;
    align-items: stretch;
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 动画效果 */
.device-node,
.monitor-card,
.alert-item {
  transition: all 0.3s ease;
}

.topology-canvas {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 自定义滚动条 */
.alert-list::-webkit-scrollbar,
.monitor-panel::-webkit-scrollbar {
  width: 6px;
}

.alert-list::-webkit-scrollbar-track,
.monitor-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.alert-list::-webkit-scrollbar-thumb,
.monitor-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.alert-list::-webkit-scrollbar-thumb:hover,
.monitor-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
