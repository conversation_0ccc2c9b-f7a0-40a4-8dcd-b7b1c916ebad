<template>
  <div class="bar-chart" ref="chartContainer">
    <div class="chart-header" v-if="config.data.title">
      <h3 class="chart-title">{{ config.data.title }}</h3>
    </div>
    <div class="chart-content" :style="chartStyle">
      <div class="chart-wrapper">
        <!-- Y轴 -->
        <div class="y-axis">
          <div 
            v-for="(tick, index) in yAxisTicks" 
            :key="index"
            class="y-axis-tick"
            :style="{ bottom: `${(tick / maxValue) * 100}%` }"
          >
            <span class="tick-label">{{ formatValue(tick) }}</span>
            <div class="tick-line"></div>
          </div>
        </div>
        
        <!-- 图表区域 -->
        <div class="chart-area">
          <div class="bars-container">
            <div 
              v-for="(item, index) in chartData" 
              :key="index"
              class="bar-item"
              :style="{ width: `${100 / chartData.length}%` }"
            >
              <div 
                class="bar"
                :style="getBarStyle(item, index)"
                @mouseenter="showTooltip($event, item, index)"
                @mouseleave="hideTooltip"
              >
                <div class="bar-value" v-if="config.style?.showValues">
                  {{ formatValue(item.value) }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- X轴 -->
          <div class="x-axis">
            <div 
              v-for="(item, index) in chartData" 
              :key="index"
              class="x-axis-label"
              :style="{ width: `${100 / chartData.length}%` }"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 工具提示 -->
    <div 
      v-if="tooltip.show"
      class="chart-tooltip"
      :style="tooltipStyle"
    >
      <div class="tooltip-content">
        <div class="tooltip-title">{{ tooltip.data.name }}</div>
        <div class="tooltip-value">{{ formatValue(tooltip.data.value) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const chartContainer = ref(null)

// 工具提示
const tooltip = reactive({
  show: false,
  x: 0,
  y: 0,
  data: {}
})

// 图表数据处理
const chartData = computed(() => {
  const xAxis = props.config.data.xAxis || []
  const series = props.config.data.series || []
  
  return xAxis.map((name, index) => ({
    name,
    value: series[index] || 0
  }))
})

// 最大值
const maxValue = computed(() => {
  const values = chartData.value.map(item => item.value)
  const max = Math.max(...values, 0)
  return max * 1.1 // 留出10%的空间
})

// Y轴刻度
const yAxisTicks = computed(() => {
  const max = maxValue.value
  const tickCount = 5
  const step = max / tickCount
  const ticks = []
  
  for (let i = 0; i <= tickCount; i++) {
    ticks.push(Math.round(step * i))
  }
  
  return ticks
})

// 图表样式
const chartStyle = computed(() => ({
  background: props.config.style?.background || '#ffffff',
  borderRadius: props.config.style?.borderRadius || '8px',
  padding: props.config.style?.padding || '20px'
}))

// 柱子样式
const getBarStyle = (item, index) => {
  const height = (item.value / maxValue.value) * 100
  const colors = props.config.style?.colors || ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
  const color = colors[index % colors.length]
  
  return {
    height: `${height}%`,
    background: props.config.style?.gradient 
      ? `linear-gradient(to top, ${color}, ${color}aa)` 
      : color,
    borderRadius: props.config.style?.barRadius || '4px 4px 0 0'
  }
}

// 工具提示样式
const tooltipStyle = computed(() => ({
  left: `${tooltip.x}px`,
  top: `${tooltip.y}px`
}))

// 格式化数值
const formatValue = (value) => {
  if (props.config.data.format === 'percentage') {
    return (value * 100).toFixed(1) + '%'
  } else if (props.config.data.format === 'currency') {
    return '¥' + value.toLocaleString()
  } else {
    return value.toLocaleString()
  }
}

// 显示工具提示
const showTooltip = (event, item, index) => {
  if (!props.config.style?.showTooltip) return
  
  const rect = chartContainer.value.getBoundingClientRect()
  tooltip.x = event.clientX - rect.left + 10
  tooltip.y = event.clientY - rect.top - 10
  tooltip.data = item
  tooltip.show = true
}

// 隐藏工具提示
const hideTooltip = () => {
  tooltip.show = false
}

// 生命周期
onMounted(() => {
  // 可以在这里添加动画效果
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped>
.bar-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-header {
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-content {
  height: calc(100% - 40px);
  position: relative;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
}

.y-axis {
  width: 60px;
  position: relative;
  border-right: 1px solid #e4e7ed;
}

.y-axis-tick {
  position: absolute;
  right: 0;
  display: flex;
  align-items: center;
  width: 100%;
}

.tick-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
  text-align: right;
  flex: 1;
}

.tick-line {
  width: 4px;
  height: 1px;
  background: #e4e7ed;
}

.chart-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 16px;
}

.bars-container {
  flex: 1;
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding-bottom: 16px;
}

.bar-item {
  display: flex;
  justify-content: center;
  height: 100%;
}

.bar {
  width: 60%;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 8px;
}

.bar:hover {
  opacity: 0.8;
  transform: translateY(-2px);
}

.bar-value {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.x-axis {
  display: flex;
  border-top: 1px solid #e4e7ed;
  padding-top: 8px;
}

.x-axis-label {
  text-align: center;
  font-size: 12px;
  color: #606266;
  padding: 0 4px;
  word-break: break-all;
}

.chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.tooltip-content {
  text-align: center;
}

.tooltip-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.tooltip-value {
  font-size: 14px;
  font-weight: 600;
}

/* 响应式 */
@media (max-width: 768px) {
  .y-axis {
    width: 40px;
  }
  
  .tick-label {
    font-size: 10px;
  }
  
  .x-axis-label {
    font-size: 10px;
  }
  
  .bar-value {
    font-size: 10px;
  }
}
</style>
