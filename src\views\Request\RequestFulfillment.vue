<template>
  <div class="request-fulfillment">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>请求履约概览</h2>
          <p>服务请求处理和履行管理中心</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="$router.push('/request/portal')">
            <el-icon><Plus /></el-icon>
            新建请求
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card total-requests">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="32"><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ overview.totalRequests }}</div>
                <div class="stat-label">总请求数</div>
                <div class="stat-change">
                  <span class="change-text">本月 +{{ overview.thisMonthRequests }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card pending-requests">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="32"><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ overview.pendingRequests }}</div>
                <div class="stat-label">待处理</div>
                <div class="stat-change">
                  <span class="change-text">占比 {{ pendingRate }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card completed-requests">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="32"><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ overview.completedRequests }}</div>
                <div class="stat-label">已完成</div>
                <div class="stat-change">
                  <span class="change-text">完成率 {{ completionRate }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card automation-rate">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="32"><MagicStick /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ overview.automationRate }}%</div>
                <div class="stat-label">自动化率</div>
                <div class="stat-change">
                  <span class="change-text">平均 {{ overview.avgProcessingTime }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表和数据展示区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 状态分布图 -->
        <el-col :xs="24" :md="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>请求状态分布</span>
                <el-button text @click="viewAllRequests">查看全部</el-button>
              </div>
            </template>
            <div class="chart-container">
              <div ref="statusChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 分类分布图 -->
        <el-col :xs="24" :md="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>服务分类分布</span>
                <el-button text @click="$router.push('/request/catalog')">查看目录</el-button>
              </div>
            </template>
            <div class="chart-container">
              <div ref="categoryChartRef" class="chart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 趋势图和热门服务 -->
    <div class="trend-section">
      <el-row :gutter="20">
        <!-- 月度趋势图 -->
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>月度请求趋势</span>
                <div class="header-controls">
                  <el-radio-group v-model="trendPeriod" size="small" @change="loadTrendData">
                    <el-radio-button value="6m">近6个月</el-radio-button>
                    <el-radio-button value="12m">近12个月</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            <div class="chart-container">
              <div ref="trendChartRef" class="chart" style="height: 350px;"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 热门服务排行 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>热门服务排行</span>
                <el-button text @click="$router.push('/request/catalog')">更多</el-button>
              </div>
            </template>
            <div class="popular-services">
              <div
                v-for="(service, index) in popularServices"
                :key="service.id"
                class="service-item"
                @click="viewServiceDetail(service.id)"
              >
                <div class="service-rank">{{ index + 1 }}</div>
                <div class="service-info">
                  <div class="service-name">{{ service.name }}</div>
                  <div class="service-stats">
                    <span class="requests-count">{{ service.requests || 0 }}次请求</span>
                    <span class="popularity">热度 {{ service.popularity }}%</span>
                  </div>
                </div>
                <div class="service-arrow">
                  <el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <span>快速操作</span>
        </template>
        <el-row :gutter="16">
          <el-col :xs="12" :sm="8" :md="6">
            <div class="action-item" @click="$router.push('/request/portal')">
              <el-icon :size="24"><Plus /></el-icon>
              <span>提交请求</span>
            </div>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6">
            <div class="action-item" @click="$router.push('/request/catalog')">
              <el-icon :size="24"><Menu /></el-icon>
              <span>服务目录</span>
            </div>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6">
            <div class="action-item" @click="viewMyRequests">
              <el-icon :size="24"><Document /></el-icon>
              <span>我的请求</span>
            </div>
          </el-col>
          <el-col :xs="12" :sm="8" :md="6">
            <div class="action-item" @click="viewPendingApprovals">
              <el-icon :size="24"><CircleCheck /></el-icon>
              <span>待审批</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus, Refresh, Document, Clock, CircleCheck, MagicStick,
  ArrowRight, Menu
} from '@element-plus/icons-vue'
import { useRequestStore } from '@/stores/request'
import * as echarts from 'echarts'

const router = useRouter()
const requestStore = useRequestStore()

// 响应式数据
const trendPeriod = ref('12m')
const statusChartRef = ref()
const categoryChartRef = ref()
const trendChartRef = ref()

// 计算属性
const overview = computed(() => requestStore.overview)
const completionRate = computed(() => requestStore.completionRate)
const pendingRate = computed(() => requestStore.pendingRate)
const popularServices = computed(() => requestStore.popularServices)

// 图表实例
let statusChart = null
let categoryChart = null
let trendChart = null

// 方法定义
const refreshData = async () => {
  try {
    await Promise.all([
      requestStore.loadOverview(),
      requestStore.loadStatistics(),
      requestStore.loadServiceCategories()
    ])

    // 重新渲染图表
    await nextTick()
    initCharts()

    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
    console.error('刷新数据失败:', error)
  }
}

const loadTrendData = () => {
  // 根据选择的时间段重新加载趋势数据
  initTrendChart()
}

const viewAllRequests = () => {
  router.push('/request/portal?tab=my-requests')
}

const viewServiceDetail = (serviceId) => {
  router.push(`/request/catalog?service=${serviceId}`)
}

const viewMyRequests = () => {
  router.push('/request/portal?tab=my-requests')
}

const viewPendingApprovals = () => {
  router.push('/request/portal?tab=approvals')
}

// 初始化状态分布图表
const initStatusChart = () => {
  if (!statusChartRef.value) return

  if (statusChart) {
    statusChart.dispose()
  }

  statusChart = echarts.init(statusChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '请求状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: overview.value.statusDistribution.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }

  statusChart.setOption(option)
}

// 初始化分类分布图表
const initCategoryChart = () => {
  if (!categoryChartRef.value) return

  if (categoryChart) {
    categoryChart.dispose()
  }

  categoryChart = echarts.init(categoryChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: overview.value.categoryDistribution.map(item => item.name)
    },
    series: [
      {
        name: '请求数量',
        type: 'bar',
        data: overview.value.categoryDistribution.map(item => item.value),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#409EFF' },
            { offset: 1, color: '#67C23A' }
          ])
        }
      }
    ]
  }

  categoryChart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  if (trendChart) {
    trendChart.dispose()
  }

  trendChart = echarts.init(trendChartRef.value)

  const trendData = overview.value.monthlyTrend || []
  const months = trendData.map(item => item.month)
  const requests = trendData.map(item => item.requests)
  const completed = trendData.map(item => item.completed)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['提交请求', '完成请求']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: months
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '提交请求',
        type: 'line',
        stack: 'Total',
        smooth: true,
        data: requests,
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      },
      {
        name: '完成请求',
        type: 'line',
        stack: 'Total',
        smooth: true,
        data: completed,
        itemStyle: {
          color: '#67C23A'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
          ])
        }
      }
    ]
  }

  trendChart.setOption(option)
}

// 初始化所有图表
const initCharts = () => {
  nextTick(() => {
    initStatusChart()
    initCategoryChart()
    initTrendChart()
  })
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (statusChart) statusChart.resize()
  if (categoryChart) categoryChart.resize()
  if (trendChart) trendChart.resize()
}

// 生命周期
onMounted(async () => {
  try {
    // 加载数据
    await Promise.all([
      requestStore.loadOverview(),
      requestStore.loadStatistics(),
      requestStore.loadServiceCategories(),
      requestStore.loadServiceCatalog({ pageSize: 6 }) // 加载热门服务
    ])

    // 初始化图表
    await nextTick()
    initCharts()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  } catch (error) {
    ElMessage.error('页面数据加载失败')
    console.error('页面初始化失败:', error)
  }
})

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (statusChart) statusChart.dispose()
  if (categoryChart) categoryChart.dispose()
  if (trendChart) trendChart.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.request-fulfillment {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  height: 120px;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 16px;
}

.stat-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.total-requests .stat-icon {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
}

.pending-requests .stat-icon {
  background: linear-gradient(135deg, #E6A23C, #F56C6C);
  color: white;
}

.completed-requests .stat-icon {
  background: linear-gradient(135deg, #67C23A, #409EFF);
  color: white;
}

.automation-rate .stat-icon {
  background: linear-gradient(135deg, #9C27B0, #673AB7);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
}

.change-text {
  color: #67C23A;
  font-weight: 500;
}

/* 图表区域样式 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  padding: 16px 0;
}

.chart {
  width: 100%;
}

/* 趋势区域样式 */
.trend-section {
  margin-bottom: 24px;
}

/* 热门服务样式 */
.popular-services {
  padding: 8px 0;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-item:hover {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding-left: 8px;
  padding-right: 8px;
}

.service-item:last-child {
  border-bottom: none;
}

.service-rank {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.service-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.requests-count {
  color: #409EFF;
}

.popularity {
  color: #67C23A;
}

.service-arrow {
  color: #C0C4CC;
  transition: all 0.3s ease;
}

.service-item:hover .service-arrow {
  color: #409EFF;
  transform: translateX(4px);
}

/* 快速操作样式 */
.quick-actions {
  margin-bottom: 24px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100px;
}

.action-item:hover {
  background: #409EFF;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.action-item .el-icon {
  margin-bottom: 8px;
}

.action-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .request-fulfillment {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .stat-content {
    padding: 12px;
  }

  .stat-value {
    font-size: 24px;
  }

  .chart {
    height: 250px !important;
  }

  .service-item {
    padding: 8px 0;
  }

  .action-item {
    height: 80px;
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .charts-section .el-col,
  .trend-section .el-col {
    margin-bottom: 16px;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .header-actions .el-button {
    width: 100%;
  }
}
</style>
