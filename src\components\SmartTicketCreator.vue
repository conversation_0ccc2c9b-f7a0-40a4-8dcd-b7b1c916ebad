<template>
  <div class="smart-ticket-creator">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>智能工单创建</span>
          <el-tag type="info" size="small">AI驱动</el-tag>
        </div>
      </template>
      
      <!-- 智能输入区域 -->
      <div class="smart-input-section">
        <div class="input-header">
          <h3>描述您的问题或需求</h3>
          <p>请用自然语言详细描述，AI将帮助您自动分析和分类</p>
        </div>
        
        <el-input
          v-model="userInput"
          type="textarea"
          :rows="6"
          placeholder="例如：我的电脑无法连接到公司网络，显示网络连接错误，影响了我的工作..."
          @input="onInputChange"
          class="smart-textarea"
        />
        
        <div class="input-actions">
          <el-button type="primary" @click="analyzeInput" :loading="analyzing">
            <el-icon><MagicStick /></el-icon>
            AI智能分析
          </el-button>
          <el-button @click="clearInput">清空</el-button>
          <el-button @click="useTemplate">使用模板</el-button>
        </div>
        
        <!-- 实时建议 -->
        <div v-if="suggestions.length > 0" class="suggestions">
          <h4>相关建议</h4>
          <div class="suggestion-list">
            <el-tag
              v-for="suggestion in suggestions"
              :key="suggestion"
              size="small"
              @click="applySuggestion(suggestion)"
              class="suggestion-tag"
            >
              {{ suggestion }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <!-- AI分析结果 -->
      <div v-if="analysisResult" class="analysis-result">
        <el-divider content-position="left">AI分析结果</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="analysis-item">
              <label>建议类型：</label>
              <el-tag :type="getTypeColor(analysisResult.suggestedType)" size="large">
                {{ getTypeText(analysisResult.suggestedType) }}
              </el-tag>
              <span class="confidence">置信度: {{ analysisResult.typeConfidence }}%</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="analysis-item">
              <label>建议优先级：</label>
              <el-tag :type="getPriorityColor(analysisResult.suggestedPriority)" size="large">
                {{ analysisResult.suggestedPriority }}
              </el-tag>
              <span class="confidence">置信度: {{ analysisResult.priorityConfidence }}%</span>
            </div>
          </el-col>
        </el-row>
        
        <div class="analysis-item">
          <label>建议分类：</label>
          <el-breadcrumb separator=">">
            <el-breadcrumb-item v-for="category in analysisResult.suggestedCategory" :key="category">
              {{ category }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="analysis-item">
          <label>关联CI：</label>
          <el-tag
            v-for="ci in analysisResult.relatedCIs"
            :key="ci.id"
            size="small"
            class="ci-tag"
          >
            {{ ci.name }}
          </el-tag>
        </div>
        
        <div class="analysis-item">
          <label>建议处理人：</label>
          <div class="assignee-suggestions">
            <div
              v-for="assignee in analysisResult.suggestedAssignees"
              :key="assignee.id"
              class="assignee-item"
            >
              <el-avatar :size="24" :src="assignee.avatar">{{ assignee.name[0] }}</el-avatar>
              <span class="assignee-name">{{ assignee.name }}</span>
              <span class="assignee-skill">{{ assignee.skill }}</span>
              <span class="assignee-load">负载: {{ assignee.workload }}%</span>
            </div>
          </div>
        </div>
        
        <div class="analysis-item">
          <label>预估解决时间：</label>
          <span class="estimated-time">{{ analysisResult.estimatedTime }}</span>
        </div>
        
        <div class="analysis-item">
          <label>相似历史工单：</label>
          <div class="similar-tickets">
            <el-link
              v-for="ticket in analysisResult.similarTickets"
              :key="ticket.id"
              type="primary"
              @click="viewSimilarTicket(ticket)"
              class="similar-ticket-link"
            >
              {{ ticket.id }} - {{ ticket.title }}
            </el-link>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="analysis-actions">
          <el-button type="success" @click="acceptAnalysis">
            <el-icon><Check /></el-icon>
            采用AI建议
          </el-button>
          <el-button @click="adjustAnalysis">
            <el-icon><Edit /></el-icon>
            手动调整
          </el-button>
          <el-button @click="reAnalyze">
            <el-icon><Refresh /></el-icon>
            重新分析
          </el-button>
        </div>
      </div>
      
      <!-- 知识库推荐 -->
      <div v-if="knowledgeRecommendations.length > 0" class="knowledge-recommendations">
        <el-divider content-position="left">相关知识库</el-divider>
        <div class="knowledge-list">
          <div
            v-for="kb in knowledgeRecommendations"
            :key="kb.id"
            class="knowledge-item"
            @click="viewKnowledge(kb)"
          >
            <div class="knowledge-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="knowledge-content">
              <div class="knowledge-title">{{ kb.title }}</div>
              <div class="knowledge-summary">{{ kb.summary }}</div>
              <div class="knowledge-meta">
                <span class="knowledge-category">{{ kb.category }}</span>
                <span class="knowledge-views">浏览: {{ kb.views }}</span>
                <span class="knowledge-rating">评分: {{ kb.rating }}/5</span>
              </div>
              <div class="knowledge-actions">
                <el-button size="small" type="text" @click.stop="applyKnowledgeSolution(kb)">
                  应用解决方案
                </el-button>
                <el-button size="small" type="text" @click.stop="createKnowledgeFromTicket">
                  创建相关知识
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['analysis-complete', 'ticket-created'])

// 响应式数据
const userInput = ref('')
const analyzing = ref(false)
const analysisResult = ref(null)
const suggestions = ref([])
const knowledgeRecommendations = ref([])

// 输入变化处理
const onInputChange = () => {
  // 实时生成建议
  if (userInput.value.length > 10) {
    generateSuggestions()
  } else {
    suggestions.value = []
  }
}

// 生成建议
const generateSuggestions = () => {
  const input = userInput.value.toLowerCase()
  const allSuggestions = [
    '网络连接问题', '打印机故障', '软件安装', '密码重置',
    '邮箱配置', '系统升级', '硬件故障', '权限申请'
  ]
  
  suggestions.value = allSuggestions.filter(s => 
    input.includes(s.toLowerCase()) || s.toLowerCase().includes(input.split(' ')[0])
  ).slice(0, 5)
}

// 应用建议
const applySuggestion = (suggestion) => {
  if (!userInput.value.includes(suggestion)) {
    userInput.value += (userInput.value ? '，' : '') + suggestion
  }
}

// AI分析输入
const analyzeInput = async () => {
  if (!userInput.value.trim()) {
    ElMessage.warning('请输入问题描述')
    return
  }
  
  analyzing.value = true
  
  try {
    // 模拟AI分析过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟分析结果
    analysisResult.value = {
      suggestedType: 'incident',
      typeConfidence: 92,
      suggestedPriority: '中',
      priorityConfidence: 85,
      suggestedCategory: ['硬件', '网络设备'],
      relatedCIs: [
        { id: 'CI-001', name: '网络交换机-A1' },
        { id: 'CI-002', name: '路由器-B2' }
      ],
      suggestedAssignees: [
        {
          id: 'user1',
          name: '张工',
          skill: '网络专家',
          workload: 65,
          avatar: ''
        },
        {
          id: 'user2',
          name: '李工',
          skill: '硬件工程师',
          workload: 45,
          avatar: ''
        }
      ],
      estimatedTime: '2-4小时',
      similarTickets: [
        { id: 'INC-2025-001', title: '网络连接异常' },
        { id: 'INC-2025-015', title: '无法访问内网资源' }
      ]
    }
    
    // 生成知识库推荐
    knowledgeRecommendations.value = [
      {
        id: 'KB-001',
        title: '网络连接故障排查指南',
        summary: '详细介绍网络连接问题的常见原因和解决方法',
        category: '网络',
        views: 1250,
        rating: 4.5
      },
      {
        id: 'KB-002',
        title: '企业网络配置最佳实践',
        summary: '企业网络环境配置和维护的最佳实践',
        category: '网络',
        views: 890,
        rating: 4.2
      }
    ]
    
    ElMessage.success('AI分析完成')
    emit('analysis-complete', analysisResult.value)
  } catch (error) {
    ElMessage.error('分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

// 清空输入
const clearInput = () => {
  userInput.value = ''
  analysisResult.value = null
  suggestions.value = []
  knowledgeRecommendations.value = []
}

// 使用模板
const useTemplate = () => {
  const templates = [
    '我的电脑无法连接到公司网络，显示"网络连接错误"，影响了我的工作。',
    '打印机无法正常工作，显示错误代码E001，需要技术支持。',
    '需要为新员工申请系统账号和邮箱，部门：技术部，姓名：张三。',
    '系统运行缓慢，CPU使用率持续超过90%，影响用户体验。'
  ]
  
  const randomTemplate = templates[Math.floor(Math.random() * templates.length)]
  userInput.value = randomTemplate
  onInputChange()
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    'incident': 'danger',
    'request': 'primary',
    'inquiry': 'info',
    'change': 'warning'
  }
  return colorMap[type] || 'info'
}

// 获取类型文本
const getTypeText = (type) => {
  const textMap = {
    'incident': '事件',
    'request': '服务请求',
    'inquiry': '咨询',
    'change': '变更'
  }
  return textMap[type] || type
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colorMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return colorMap[priority] || 'info'
}

// 查看相似工单
const viewSimilarTicket = (ticket) => {
  ElMessage.info(`查看相似工单: ${ticket.id}`)
}

// 查看知识库
const viewKnowledge = (kb) => {
  // 打开知识详情页面
  window.open(`/knowledge/articles/${kb.id}`, '_blank')
}

// 应用知识解决方案
const applyKnowledgeSolution = (kb) => {
  // 将知识库的解决方案应用到当前工单
  if (kb.solution) {
    formData.description += `\n\n参考解决方案（来自知识库：${kb.title}）：\n${kb.solution}`
    ElMessage.success('解决方案已应用到工单描述中')
  } else {
    ElMessage.info('该知识暂无具体解决方案')
  }
}

// 从工单创建知识
const createKnowledgeFromTicket = () => {
  // 跳转到知识创建页面，并预填充工单信息
  const knowledgeData = {
    title: `${formData.title} - 解决方案`,
    summary: formData.description,
    category: getCategoryByType(formData.type),
    tags: [formData.type, formData.priority]
  }

  // 将数据存储到sessionStorage，供知识创建页面使用
  sessionStorage.setItem('knowledgeFromTicket', JSON.stringify(knowledgeData))
  window.open('/knowledge/create', '_blank')
  ElMessage.success('已打开知识创建页面，相关信息已预填充')
}

// 根据工单类型获取知识分类
const getCategoryByType = (type) => {
  const categoryMap = {
    'incident': '故障处理',
    'request': '服务请求',
    'problem': '问题解决',
    'change': '变更管理'
  }
  return categoryMap[type] || '技术文档'
}

// 采用AI分析
const acceptAnalysis = () => {
  emit('ticket-created', {
    input: userInput.value,
    analysis: analysisResult.value
  })
  ElMessage.success('已采用AI建议，正在创建工单...')
}

// 手动调整
const adjustAnalysis = () => {
  ElMessage.info('切换到手动调整模式')
}

// 重新分析
const reAnalyze = () => {
  analyzeInput()
}
</script>

<style scoped>
.smart-ticket-creator {
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.smart-input-section {
  margin-bottom: 30px;
}

.input-header h3 {
  color: #333;
  margin-bottom: 8px;
}

.input-header p {
  color: #666;
  margin-bottom: 16px;
  font-size: 14px;
}

.smart-textarea {
  margin-bottom: 16px;
}

.input-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.suggestions h4 {
  color: #333;
  margin-bottom: 12px;
  font-size: 14px;
}

.suggestion-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-tag:hover {
  transform: scale(1.05);
}

.analysis-result {
  margin-bottom: 30px;
}

.analysis-item {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.analysis-item label {
  min-width: 100px;
  font-weight: 500;
  color: #333;
}

.confidence {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.ci-tag {
  margin-right: 8px;
}

.assignee-suggestions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.assignee-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.assignee-name {
  font-weight: 500;
}

.assignee-skill {
  color: #666;
  font-size: 12px;
}

.assignee-load {
  color: #999;
  font-size: 12px;
  margin-left: auto;
}

.estimated-time {
  color: #1976D2;
  font-weight: 500;
}

.similar-tickets {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.similar-ticket-link {
  font-size: 14px;
}

.analysis-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.knowledge-recommendations {
  margin-top: 30px;
}

.knowledge-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.knowledge-item {
  display: flex;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.knowledge-item:hover {
  border-color: #1976D2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
}

.knowledge-icon {
  margin-right: 12px;
  color: #1976D2;
}

.knowledge-content {
  flex: 1;
}

.knowledge-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.knowledge-summary {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.knowledge-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

@media (max-width: 768px) {
  .analysis-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .analysis-item label {
    min-width: auto;
    margin-bottom: 4px;
  }
  
  .assignee-item {
    flex-wrap: wrap;
  }
  
  .analysis-actions {
    flex-direction: column;
  }
}
</style>
