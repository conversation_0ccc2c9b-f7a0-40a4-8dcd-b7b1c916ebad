<template>
  <div class="cmdb-page">
    <div class="page-header">
      <h2>配置管理数据库 (CMDB)</h2>
      <p>统一管理IT基础设施配置项，作为ITSM系统的核心数据中枢</p>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="showDataQualityDialog = true">
          <el-icon><TrendCharts /></el-icon>
          数据质量报告
        </el-button>
        <el-button @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button type="warning" @click="runSystemTest" v-if="isDevelopment">
          <el-icon><Tools /></el-icon>
          系统测试
        </el-button>
      </div>
    </div>

    <!-- CMDB统计概览 -->
    <div class="cmdb-stats">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in cmdbStats" :key="stat.key">
          <el-card class="stat-card" :class="stat.trend">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24" :color="stat.color">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" v-if="stat.change">
                  <el-icon :size="12" :color="stat.change > 0 ? '#4CAF50' : '#F44336'">
                    <component :is="stat.change > 0 ? 'ArrowUpBold' : 'ArrowDownBold'" />
                  </el-icon>
                  <span :style="{ color: stat.change > 0 ? '#4CAF50' : '#F44336' }">
                    {{ Math.abs(stat.change) }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据质量监控 -->
    <div class="data-quality-section">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>数据质量监控</span>
                <el-button type="text" size="small" @click="viewQualityDetails">查看详情</el-button>
              </div>
            </template>
            <div class="quality-overview">
              <div class="quality-score">
                <div class="score-circle">
                  <div class="score-value">{{ dataQuality.overallScore }}</div>
                  <div class="score-label">总体评分</div>
                </div>
              </div>
              <div class="quality-metrics">
                <div class="metric-item" v-for="metric in dataQuality.metrics" :key="metric.key">
                  <div class="metric-label">{{ metric.label }}</div>
                  <el-progress
                    :percentage="metric.value"
                    :color="getQualityColor(metric.value)"
                    :show-text="false"
                  />
                  <div class="metric-value">{{ metric.value }}%</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <template #header>
              <span>问题CI统计</span>
            </template>
            <div class="problem-ci-stats">
              <div class="problem-item" v-for="problem in problemCIs" :key="problem.type">
                <div class="problem-icon">
                  <el-icon :size="20" :color="problem.color">
                    <component :is="problem.icon" />
                  </el-icon>
                </div>
                <div class="problem-info">
                  <div class="problem-count">{{ problem.count }}</div>
                  <div class="problem-label">{{ problem.label }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动和版本控制 -->
    <div class="recent-activities">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>最近变更</span>
                <el-button type="text" size="small" @click="viewAllChanges">查看全部</el-button>
              </div>
            </template>
            <div class="activity-list">
              <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
                <div class="activity-icon">
                  <el-icon :size="16" :color="activity.color">
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-meta">
                    <span class="activity-user">{{ activity.user }}</span>
                    <span class="activity-time">{{ activity.time }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>版本统计</span>
                <el-button type="text" size="small" @click="viewVersionHistory">版本历史</el-button>
              </div>
            </template>
            <div class="version-stats">
              <div class="version-item" v-for="version in versionStats" :key="version.type">
                <div class="version-label">{{ version.label }}</div>
                <div class="version-count">{{ version.count }}</div>
                <div class="version-bar">
                  <div class="version-progress" :style="{ width: version.percentage + '%' }"></div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 配置项列表 -->
    <div class="ci-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>配置项列表</span>
            <div class="header-controls">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索配置项..."
                style="width: 200px; margin-right: 12px"
                prefix-icon="Search"
                @input="handleSearch"
              />
              <el-select
                v-model="selectedType"
                placeholder="类型筛选"
                style="width: 120px; margin-right: 12px"
                @change="handleTypeFilter"
              >
                <el-option label="全部类型" value="" />
                <el-option label="服务器" value="server" />
                <el-option label="网络设备" value="network" />
                <el-option label="应用系统" value="application" />
                <el-option label="数据库" value="database" />
              </el-select>
              <el-button type="primary" size="small" @click="addConfigItem">
                <el-icon><Plus /></el-icon>
                添加配置项
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="filteredConfigItems" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="名称" min-width="150">
            <template #default="scope">
              <el-button type="text" @click="viewCIDetails(scope.row)">
                {{ scope.row.name }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="120">
            <template #default="scope">
              <el-tag :type="getTypeColor(scope.row.type)" size="small">
                {{ getTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="owner" label="负责人" width="120" />
          <el-table-column prop="version" label="版本" width="80" />
          <el-table-column prop="updateTime" label="更新时间" width="160" />
          <el-table-column prop="healthScore" label="健康分" width="100">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.healthScore"
                :color="getHealthColor(scope.row.healthScore)"
                :show-text="false"
                style="width: 60px"
              />
              <span style="margin-left: 8px; font-size: 12px">{{ scope.row.healthScore }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewCIDetails(scope.row)">
                详情
              </el-button>
              <el-button type="primary" size="small" text @click="editCI(scope.row)">
                编辑
              </el-button>
              <el-button type="primary" size="small" text @click="viewRelations(scope.row)">
                关系
              </el-button>
              <el-button type="primary" size="small" text @click="viewHistory(scope.row)">
                历史
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalItems"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 数据质量详情对话框 -->
    <el-dialog v-model="showDataQualityDialog" title="数据质量详情报告" width="70%">
      <div class="quality-details">
        <el-tabs v-model="activeQualityTab">
          <el-tab-pane label="完整性检查" name="completeness">
            <el-table :data="qualityIssues.completeness" style="width: 100%">
              <el-table-column prop="ciName" label="配置项名称" />
              <el-table-column prop="missingFields" label="缺失字段" />
              <el-table-column prop="severity" label="严重程度">
                <template #default="scope">
                  <el-tag :type="getSeverityType(scope.row.severity)" size="small">
                    {{ scope.row.severity }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button type="primary" size="small" text @click="fixIssue(scope.row)">
                    修复
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="一致性检查" name="consistency">
            <el-table :data="qualityIssues.consistency" style="width: 100%">
              <el-table-column prop="ciName" label="配置项名称" />
              <el-table-column prop="conflictField" label="冲突字段" />
              <el-table-column prop="conflictValue" label="冲突值" />
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button type="primary" size="small" text @click="resolveConflict(scope.row)">
                    解决
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="僵尸CI" name="zombie">
            <el-table :data="qualityIssues.zombie" style="width: 100%">
              <el-table-column prop="ciName" label="配置项名称" />
              <el-table-column prop="lastUpdate" label="最后更新" />
              <el-table-column prop="daysSinceUpdate" label="天数" />
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="warning" size="small" text @click="archiveCI(scope.row)">
                    归档
                  </el-button>
                  <el-button type="danger" size="small" text @click="deleteCI(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDataQualityDialog = false">关闭</el-button>
          <el-button type="primary" @click="generateQualityReport">生成报告</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑配置项对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑配置项" width="60%">
      <el-form :model="editingCI" :rules="editRules" ref="editFormRef" label-width="120px">
        <el-form-item label="配置项名称" prop="name">
          <el-input v-model="editingCI.name" placeholder="请输入配置项名称" />
        </el-form-item>
        <el-form-item label="配置项类型" prop="type">
          <el-select v-model="editingCI.type" placeholder="请选择配置项类型" style="width: 100%">
            <el-option label="服务器" value="server" />
            <el-option label="网络设备" value="network" />
            <el-option label="应用系统" value="application" />
            <el-option label="数据库" value="database" />
          </el-select>
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="editingCI.ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="负责人" prop="owner">
          <el-input v-model="editingCI.owner" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="editingCI.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="运行中" value="运行中" />
            <el-option label="维护中" value="维护中" />
            <el-option label="故障" value="故障" />
            <el-option label="离线" value="离线" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="editingCI.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="saveCI">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { runQuickTest, runFullTest } from '@/utils/testUtils'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const selectedType = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const showDataQualityDialog = ref(false)
const isDevelopment = ref(import.meta.env.DEV)
const activeQualityTab = ref('completeness')

// CMDB统计数据
const cmdbStats = ref([
  {
    key: 'total',
    label: '配置项总数',
    value: '1,245',
    icon: 'Box',
    color: '#1976D2',
    change: 5.2
  },
  {
    key: 'servers',
    label: '服务器',
    value: '156',
    icon: 'Monitor',
    color: '#4CAF50',
    change: 2.1
  },
  {
    key: 'network',
    label: '网络设备',
    value: '89',
    icon: 'Link',
    color: '#FF9800',
    change: -1.3
  },
  {
    key: 'applications',
    label: '应用系统',
    value: '234',
    icon: 'Grid',
    color: '#9C27B0',
    change: 8.7
  }
])

// 数据质量监控
const dataQuality = reactive({
  overallScore: 85,
  metrics: [
    { key: 'completeness', label: '完整性', value: 92 },
    { key: 'consistency', label: '一致性', value: 88 },
    { key: 'freshness', label: '新鲜度', value: 76 },
    { key: 'accuracy', label: '准确性', value: 94 }
  ]
})

// 问题CI统计
const problemCIs = ref([
  { type: 'incomplete', label: '不完整CI', count: 23, icon: 'Warning', color: '#FF9800' },
  { type: 'zombie', label: '僵尸CI', count: 15, icon: 'Delete', color: '#F44336' },
  { type: 'duplicate', label: '重复CI', count: 8, icon: 'CopyDocument', color: '#9C27B0' },
  { type: 'orphan', label: '孤立CI', count: 12, icon: 'Link', color: '#607D8B' }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '新增服务器 WEB-SRV-05',
    user: '张工',
    time: '2分钟前',
    icon: 'Plus',
    color: '#4CAF50'
  },
  {
    id: 2,
    title: '更新数据库配置 DB-PROD-01',
    user: '李工',
    time: '15分钟前',
    icon: 'Edit',
    color: '#1976D2'
  },
  {
    id: 3,
    title: '删除过期CI NET-SW-OLD',
    user: '王工',
    time: '1小时前',
    icon: 'Delete',
    color: '#F44336'
  },
  {
    id: 4,
    title: '建立关系 APP-ERP → DB-PROD-01',
    user: '赵工',
    time: '2小时前',
    icon: 'Link',
    color: '#9C27B0'
  }
])

// 版本统计
const versionStats = ref([
  { type: 'today', label: '今日变更', count: 12, percentage: 60 },
  { type: 'week', label: '本周变更', count: 45, percentage: 75 },
  { type: 'month', label: '本月变更', count: 156, percentage: 85 },
  { type: 'total', label: '总版本数', count: 2847, percentage: 100 }
])

// 配置项数据
const configItems = ref([
  {
    id: 1,
    name: 'WEB-SRV-01',
    type: 'server',
    status: '运行中',
    owner: '张工',
    version: 'v2.1',
    updateTime: '2025-01-30 14:30',
    healthScore: 95
  },
  {
    id: 2,
    name: 'CORE-SW-01',
    type: 'network',
    status: '正常',
    owner: '李工',
    version: 'v1.5',
    updateTime: '2025-01-30 13:45',
    healthScore: 88
  },
  {
    id: 3,
    name: 'ERP-SYSTEM',
    type: 'application',
    status: '运行中',
    owner: '王工',
    version: 'v3.2',
    updateTime: '2025-01-30 12:20',
    healthScore: 92
  },
  {
    id: 4,
    name: 'DB-PROD-01',
    type: 'database',
    status: '维护中',
    owner: '赵工',
    version: 'v5.7',
    updateTime: '2025-01-30 10:15',
    healthScore: 76
  }
])

// 数据质量问题
const qualityIssues = reactive({
  completeness: [
    { ciName: 'WEB-SRV-03', missingFields: 'IP地址, 负责人', severity: '高' },
    { ciName: 'APP-CRM', missingFields: '版本号', severity: '中' },
    { ciName: 'NET-FW-02', missingFields: '描述', severity: '低' }
  ],
  consistency: [
    { ciName: 'DB-TEST-01', conflictField: 'IP地址', conflictValue: '************* vs *************' },
    { ciName: 'WEB-SRV-02', conflictField: '状态', conflictValue: '运行中 vs 维护中' }
  ],
  zombie: [
    { ciName: 'OLD-SRV-01', lastUpdate: '2024-08-15', daysSinceUpdate: 168 },
    { ciName: 'LEGACY-APP', lastUpdate: '2024-09-20', daysSinceUpdate: 132 },
    { ciName: 'TEST-DB-OLD', lastUpdate: '2024-10-05', daysSinceUpdate: 117 }
  ]
})

// 计算属性
const filteredConfigItems = computed(() => {
  let items = configItems.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    items = items.filter(item =>
      item.name.toLowerCase().includes(keyword) ||
      item.owner.toLowerCase().includes(keyword)
    )
  }

  // 按类型筛选
  if (selectedType.value) {
    items = items.filter(item => item.type === selectedType.value)
  }

  totalItems.value = items.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return items.slice(start, end)
})

// 方法函数
const refreshData = () => {
  loading.value = true
  ElMessage.success('数据刷新中...')

  // 模拟数据刷新
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
    // 更新统计数据
    cmdbStats.value[0].value = '1,267'
    cmdbStats.value[0].change = 6.8
  }, 1500)
}

const exportData = () => {
  ElMessage.success('数据导出中...')
  // 这里可以实现数据导出逻辑
}

const runSystemTest = async () => {
  ElMessageBox.confirm(
    '系统测试将验证所有CMDB功能模块，可能需要几分钟时间，是否继续？',
    '系统测试',
    {
      confirmButtonText: '开始测试',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      loading.value = true
      ElMessage.info('正在执行系统测试，请稍候...')

      const results = await runFullTest()

      // 显示测试结果
      const totalTests = results.functional.reduce((sum, r) => sum + r.total, 0)
      const passedTests = results.functional.reduce((sum, r) => sum + r.passed, 0)
      const failedTests = results.functional.reduce((sum, r) => sum + r.failed, 0)

      if (failedTests === 0) {
        ElMessage.success(`系统测试完成！所有 ${totalTests} 项测试均通过`)
      } else {
        ElMessage.warning(`系统测试完成！${totalTests} 项测试中有 ${failedTests} 项失败`)
      }

      console.log('完整测试结果:', results)

    } catch (error) {
      ElMessage.error('系统测试执行失败')
      console.error('测试错误:', error)
    } finally {
      loading.value = false
    }
  }).catch(() => {
    ElMessage.info('已取消系统测试')
  })
}

const viewQualityDetails = () => {
  showDataQualityDialog.value = true
}

const getQualityColor = (value) => {
  if (value >= 90) return '#4CAF50'
  if (value >= 70) return '#FF9800'
  return '#F44336'
}

const viewAllChanges = () => {
  ElMessage.info('跳转到变更历史页面')
  // 这里可以实现路由跳转
}

const viewVersionHistory = () => {
  ElMessage.info('跳转到版本历史页面')
  // 这里可以实现路由跳转
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleTypeFilter = () => {
  currentPage.value = 1
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const addConfigItem = () => {
  ElMessage.info('打开添加配置项对话框')
  // 这里可以实现添加配置项的逻辑
}

const viewCIDetails = (ci) => {
  // 跳转到CI详情页面
  window.open(`/cmdb/ci/${ci.id}`, '_blank')
}

const editCI = (ci) => {
  // 打开编辑对话框
  showEditDialog.value = true
  editingCI.value = { ...ci }
}

// 添加编辑相关的响应式数据
const showEditDialog = ref(false)
const editingCI = ref({})
const editFormRef = ref(null)

// 编辑表单验证规则
const editRules = {
  name: [
    { required: true, message: '请输入配置项名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择配置项类型', trigger: 'change' }
  ],
  ip: [
    { pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: '请输入正确的IP地址格式', trigger: 'blur' }
  ],
  owner: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

const saveCI = () => {
  editFormRef.value?.validate((valid) => {
    if (valid) {
      // 更新配置项数据
      const index = configItems.value.findIndex(item => item.id === editingCI.value.id)
      if (index > -1) {
        configItems.value[index] = { ...editingCI.value }
        ElMessage.success('配置项更新成功')
        showEditDialog.value = false
      }
    }
  })
}

const viewRelations = (ci) => {
  // 跳转到关系映射页面，并筛选该CI的关系
  window.open(`/cmdb/relations?ci=${ci.id}`, '_blank')
}

const viewHistory = (ci) => {
  // 跳转到版本控制页面，并筛选该CI的版本历史
  window.open(`/cmdb/versions?ci=${ci.id}`, '_blank')
}

const fixIssue = (issue) => {
  ElMessage.success(`修复问题: ${issue.ciName}`)
  // 这里可以实现修复问题的逻辑
}

const resolveConflict = (conflict) => {
  ElMessage.success(`解决冲突: ${conflict.ciName}`)
  // 这里可以实现解决冲突的逻辑
}

const archiveCI = (ci) => {
  ElMessageBox.confirm(
    `确定要归档配置项 "${ci.ciName}" 吗？`,
    '确认归档',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success(`配置项 "${ci.ciName}" 已归档`)
  }).catch(() => {
    ElMessage.info('已取消归档')
  })
}

const deleteCI = (ci) => {
  ElMessageBox.confirm(
    `确定要删除配置项 "${ci.ciName}" 吗？此操作不可恢复！`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success(`配置项 "${ci.ciName}" 已删除`)
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const generateQualityReport = () => {
  ElMessage.success('数据质量报告生成中...')
  showDataQualityDialog.value = false
  // 这里可以实现生成报告的逻辑
}

// 辅助函数
const getStatusType = (status) => {
  const typeMap = {
    '运行中': 'success',
    '正常': 'success',
    '维护中': 'warning',
    '故障': 'danger',
    '离线': 'info'
  }
  return typeMap[status] || 'info'
}

const getTypeColor = (type) => {
  const colorMap = {
    'server': 'success',
    'network': 'primary',
    'application': 'warning',
    'database': 'danger'
  }
  return colorMap[type] || 'info'
}

const getTypeText = (type) => {
  const textMap = {
    'server': '服务器',
    'network': '网络设备',
    'application': '应用系统',
    'database': '数据库'
  }
  return textMap[type] || type
}

const getHealthColor = (score) => {
  if (score >= 90) return '#4CAF50'
  if (score >= 70) return '#FF9800'
  return '#F44336'
}

const getSeverityType = (severity) => {
  const typeMap = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return typeMap[severity] || 'info'
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化数据
  totalItems.value = configItems.value.length
})
</script>

<style scoped>
.cmdb-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片样式 */
.cmdb-stats {
  margin-bottom: 20px;
}

.stat-card {
  height: 90px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 12px;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
}

/* 数据质量监控样式 */
.data-quality-section {
  margin-bottom: 20px;
}

.quality-overview {
  display: flex;
  align-items: center;
  gap: 30px;
}

.quality-score {
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.score-value {
  font-size: 24px;
  font-weight: bold;
}

.score-label {
  font-size: 12px;
  margin-top: 4px;
}

.quality-metrics {
  flex: 1;
}

.metric-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.metric-label {
  width: 60px;
  font-size: 12px;
  color: #666;
}

.metric-value {
  width: 40px;
  font-size: 12px;
  font-weight: bold;
  text-align: right;
}

/* 问题CI统计样式 */
.problem-ci-stats {
  padding: 16px 0;
}

.problem-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 6px;
  background: #f8f9fa;
}

.problem-icon {
  margin-right: 12px;
}

.problem-info {
  flex: 1;
}

.problem-count {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.problem-label {
  font-size: 12px;
  color: #666;
}

/* 最近活动样式 */
.recent-activities {
  margin-bottom: 20px;
}

.activity-list {
  padding: 16px 0;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.activity-item:hover {
  background: #f8f9fa;
}

.activity-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.activity-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

/* 版本统计样式 */
.version-stats {
  padding: 16px 0;
}

.version-item {
  margin-bottom: 16px;
}

.version-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.version-count {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 6px;
}

.version-bar {
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
}

.version-progress {
  height: 100%;
  background: linear-gradient(90deg, #1976D2, #42A5F5);
  transition: width 0.3s ease;
}

/* 配置项列表样式 */
.ci-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 数据质量详情对话框样式 */
.quality-details {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-button--text {
  padding: 4px 8px;
}

/* 进度条样式 */
.el-progress {
  margin: 0;
}

.el-progress__text {
  font-size: 12px !important;
}

/* 标签样式 */
.el-tag {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .quality-overview {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .score-circle {
    width: 80px;
    height: 80px;
  }

  .score-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .cmdb-page {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stat-card {
    margin-bottom: 12px;
    height: 80px;
  }

  .stat-value {
    font-size: 16px;
  }

  .header-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .header-controls .el-input,
  .header-controls .el-select {
    width: 100% !important;
  }

  /* 表格在移动端的优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* 对话框在移动端的优化 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .quality-overview {
    text-align: center;
  }

  .metric-item {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }

  .metric-label,
  .metric-value {
    width: auto;
  }

  .activity-item {
    flex-direction: column;
    gap: 8px;
  }

  .activity-meta {
    justify-content: center;
  }
}

/* 动画效果 */
.stat-card,
.activity-item,
.problem-item {
  transition: all 0.3s ease;
}

.version-progress {
  animation: progressLoad 1s ease-out;
}

@keyframes progressLoad {
  from {
    width: 0;
  }
  to {
    width: var(--progress-width);
  }
}

/* 加载状态 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa;
}

/* 自定义滚动条 */
.quality-details::-webkit-scrollbar {
  width: 6px;
}

.quality-details::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.quality-details::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.quality-details::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
