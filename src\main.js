import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'

console.log('Starting CMDB application...')

const app = createApp(App)
const pinia = createPinia()

// 使用路由器和状态管理
app.use(router)
app.use(pinia)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
}

console.log('Vue app created successfully')

// 尝试导入Element Plus
try {
  console.log('Attempting to import Element Plus...')
  import('element-plus').then((ElementPlus) => {
    app.use(ElementPlus.default)
    console.log('✓ Element Plus loaded successfully')

    // 尝试导入Element Plus CSS
    import('element-plus/dist/index.css').then(() => {
      console.log('✓ Element Plus CSS loaded successfully')
    }).catch((error) => {
      console.warn('⚠ Element Plus CSS not available:', error)
    })

    // 尝试导入图标
    import('@element-plus/icons-vue').then((ElementPlusIconsVue) => {
      for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component)
      }
      console.log('✓ Icons loaded successfully')
    }).catch((error) => {
      console.warn('⚠ Icons not available:', error)
    })

  }).catch((error) => {
    console.error('❌ Element Plus import failed:', error)
  })
} catch (error) {
  console.error('❌ Element Plus loading failed:', error)
}

// 挂载应用
try {
  app.mount('#app')
  console.log('🚀 CMDB Application mounted successfully')
} catch (error) {
  console.error('❌ App mounting failed:', error)
}
