<template>
  <div class="visualization-preview">
    <!-- 全屏控制栏 -->
    <div class="preview-controls" :class="{ 'hidden': isFullscreen }">
      <div class="controls-left">
        <el-button :icon="ArrowLeft" @click="goBack" type="primary">
          返回编辑
        </el-button>
        <span class="dashboard-title">{{ dashboardTitle }}</span>
      </div>
      
      <div class="controls-right">
        <el-button :icon="Refresh" @click="refreshData" :loading="refreshing">
          刷新数据
        </el-button>
        <el-button :icon="Share" @click="shareDashboard">
          分享
        </el-button>
        <el-button :icon="Download" @click="exportDashboard">
          导出
        </el-button>
        <el-button :icon="FullScreen" @click="toggleFullscreen">
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
      </div>
    </div>

    <!-- 大屏内容 -->
    <div class="preview-content" :class="{ 'fullscreen': isFullscreen }">
      <div 
        class="dashboard-canvas"
        :style="canvasStyle"
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <!-- 背景 -->
        <div class="canvas-background" :style="backgroundStyle"></div>
        
        <!-- 组件渲染 -->
        <div 
          v-for="component in components" 
          :key="component.id"
          class="preview-component"
          :style="getComponentStyle(component)"
        >
          <!-- 组件占位符 -->
          <div class="component-placeholder" @click="handleComponentClick(component)">
            <div class="placeholder-icon">
              <el-icon><component :is="getComponentIcon(component.type)" /></el-icon>
            </div>
            <div class="placeholder-text">{{ component.type }}</div>
            <div class="placeholder-data" v-if="component.data.text">
              {{ component.data.text }}
            </div>
            <div class="placeholder-data" v-else-if="component.data.value">
              {{ component.data.value }} {{ component.data.unit }}
            </div>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <p>正在加载大屏数据...</p>
        </div>
      </div>
    </div>

    <!-- 分享对话框 -->
    <el-dialog
      v-model="showShareDialog"
      title="分享大屏"
      width="500px"
    >
      <div class="share-content">
        <div class="share-item">
          <label>预览链接：</label>
          <el-input
            v-model="shareUrl"
            readonly
            class="share-input"
          >
            <template #append>
              <el-button @click="copyToClipboard(shareUrl)">复制</el-button>
            </template>
          </el-input>
        </div>
        
        <div class="share-item">
          <label>嵌入代码：</label>
          <el-input
            v-model="embedCode"
            type="textarea"
            :rows="4"
            readonly
            class="share-input"
          />
          <el-button @click="copyToClipboard(embedCode)" style="margin-top: 8px;">
            复制嵌入代码
          </el-button>
        </div>
        
        <div class="share-item">
          <label>二维码：</label>
          <div class="qr-code">
            <div class="qr-placeholder">
              <el-icon :size="80"><Grid /></el-icon>
              <p>二维码</p>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft, Refresh, Share, Download, FullScreen,
  Loading, Grid, Histogram, Document, EditPen
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const refreshing = ref(false)
const isFullscreen = ref(false)
const showShareDialog = ref(false)

// 大屏数据
const dashboardTitle = ref('智慧城市监控大屏')
const components = ref([])
const backgroundConfig = ref({
  type: 'color', // color, gradient, image, video
  value: '#0a0a0a'
})

// 分享相关
const shareUrl = computed(() => {
  return `${window.location.origin}/visualization/preview/${route.params.id}`
})

const embedCode = computed(() => {
  return `<iframe src="${shareUrl.value}" width="1920" height="1080" frameborder="0"></iframe>`
})

// 画布样式
const canvasStyle = computed(() => {
  const scale = calculateScale()
  return {
    width: '1920px',
    height: '1080px',
    transform: `scale(${scale})`,
    transformOrigin: 'center center'
  }
})

// 背景样式
const backgroundStyle = computed(() => {
  const config = backgroundConfig.value
  switch (config.type) {
    case 'color':
      return { backgroundColor: config.value }
    case 'gradient':
      return { background: config.value }
    case 'image':
      return { 
        backgroundImage: `url(${config.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }
    case 'video':
      return {}
    default:
      return { backgroundColor: '#0a0a0a' }
  }
})

// 生命周期
onMounted(() => {
  loadDashboard()
  addKeyboardListeners()
  startAutoRefresh()
})

onUnmounted(() => {
  removeKeyboardListeners()
  stopAutoRefresh()
})

// 自动刷新
let autoRefreshTimer = null

const startAutoRefresh = () => {
  // 每30秒自动刷新一次数据
  autoRefreshTimer = setInterval(() => {
    refreshData(true) // 静默刷新
  }, 30000)
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer)
    autoRefreshTimer = null
  }
}

// 加载大屏数据
const loadDashboard = async () => {
  loading.value = true
  try {
    const id = route.params.id
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    dashboardTitle.value = '智慧城市监控大屏'
    components.value = [
      {
        id: 'comp1',
        type: 'title',
        x: 50,
        y: 30,
        width: 400,
        height: 60,
        data: { text: '智慧城市实时监控' }
      },
      {
        id: 'comp2',
        type: 'number-card',
        x: 100,
        y: 150,
        width: 200,
        height: 120,
        data: { value: 12345, title: '在线设备', unit: '台' }
      },
      {
        id: 'comp3',
        type: 'bar-chart',
        x: 400,
        y: 150,
        width: 500,
        height: 300,
        data: {
          xAxis: ['设备A', '设备B', '设备C', '设备D', '设备E'],
          series: [120, 200, 150, 80, 70]
        }
      }
    ]
    
    ElMessage.success('大屏加载成功')
  } catch (error) {
    console.error('加载大屏失败:', error)
    ElMessage.error('加载大屏失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = async (silent = false) => {
  if (!silent) {
    refreshing.value = true
  }
  
  try {
    // 模拟刷新各组件数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新组件数据
    components.value.forEach(component => {
      if (component.type === 'number-card') {
        component.data.value = Math.floor(Math.random() * 20000) + 10000
      } else if (component.type === 'bar-chart') {
        component.data.series = component.data.series.map(() => 
          Math.floor(Math.random() * 200) + 50
        )
      }
    })
    
    if (!silent) {
      ElMessage.success('数据刷新成功')
    }
  } catch (error) {
    if (!silent) {
      ElMessage.error('数据刷新失败')
    }
  } finally {
    if (!silent) {
      refreshing.value = false
    }
  }
}

// 计算缩放比例
const calculateScale = () => {
  if (isFullscreen.value) {
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const scaleX = windowWidth / 1920
    const scaleY = windowHeight / 1080
    return Math.min(scaleX, scaleY)
  } else {
    const containerWidth = window.innerWidth - 40
    const containerHeight = window.innerHeight - 120
    const scaleX = containerWidth / 1920
    const scaleY = containerHeight / 1080
    return Math.min(scaleX, scaleY, 1)
  }
}

// 获取组件样式
const getComponentStyle = (component) => ({
  position: 'absolute',
  left: `${component.x}px`,
  top: `${component.y}px`,
  width: `${component.width}px`,
  height: `${component.height}px`
})

// 获取组件渲染器
const getComponentRenderer = (type) => {
  // 暂时返回简单的div占位符，后续可以改为动态组件
  return 'div'
}

// 获取组件图标
const getComponentIcon = (type) => {
  const iconMap = {
    'bar-chart': 'Histogram',
    'line-chart': 'Histogram',
    'pie-chart': 'Histogram',
    'number-card': 'Document',
    'title': 'Document',
    'text': 'EditPen'
  }
  return iconMap[type] || 'Document'
}

// 组件点击处理
const handleComponentClick = (component) => {
  if (component.clickAction) {
    switch (component.clickAction) {
      case 'link':
        if (component.linkUrl) {
          window.open(component.linkUrl, '_blank')
        }
        break
      case 'refresh':
        refreshData()
        break
      case 'linkage':
        // 处理组件联动
        break
    }
  }
}

// 返回编辑
const goBack = () => {
  router.push(`/visualization/editor/${route.params.id}`)
}

// 全屏切换
const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    enterFullscreen()
  } else {
    exitFullscreen()
  }
}

const enterFullscreen = () => {
  const element = document.documentElement
  if (element.requestFullscreen) {
    element.requestFullscreen()
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullscreen()
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen()
  }
  isFullscreen.value = true
}

const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen()
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen()
  }
  isFullscreen.value = false
}

// 分享大屏
const shareDashboard = () => {
  showShareDialog.value = true
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 导出大屏
const exportDashboard = () => {
  ElMessage.info('导出功能开发中')
}

// 键盘事件
const addKeyboardListeners = () => {
  document.addEventListener('keydown', handleKeyDown)
  document.addEventListener('fullscreenchange', handleFullscreenChange)
}

const removeKeyboardListeners = () => {
  document.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
}

const handleKeyDown = (event) => {
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  } else if (event.key === 'Escape' && isFullscreen.value) {
    exitFullscreen()
  }
}

const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}
</script>

<style scoped>
.visualization-preview {
  height: 100vh;
  background: #000000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 控制栏 */
.preview-controls {
  height: 60px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: relative;
  z-index: 1000;
  transition: all 0.3s ease;
}

.preview-controls.hidden {
  transform: translateY(-100%);
  opacity: 0;
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.controls-right .el-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.controls-right .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.controls-right .el-button--primary {
  background: #409eff;
  border-color: #409eff;
}

/* 预览内容 */
.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;
}

.preview-content.fullscreen {
  padding: 0;
}

.dashboard-canvas {
  position: relative;
  background: #0a0a0a;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.preview-content.fullscreen .dashboard-canvas {
  border-radius: 0;
  box-shadow: none;
}

.canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.preview-component {
  position: absolute;
  z-index: 1;
  transition: all 0.3s ease;
}

.preview-component:hover {
  z-index: 2;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  z-index: 1000;
}

.loading-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 分享对话框 */
.share-content {
  padding: 20px 0;
}

.share-item {
  margin-bottom: 24px;
}

.share-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.share-input {
  width: 100%;
}

.qr-code {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.qr-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.qr-placeholder p {
  margin: 8px 0 0 0;
  font-size: 12px;
}

/* 全屏模式下隐藏滚动条 */
.preview-content.fullscreen::-webkit-scrollbar {
  display: none;
}

/* 响应式 */
@media (max-width: 768px) {
  .preview-controls {
    height: 50px;
    padding: 0 12px;
  }

  .controls-left,
  .controls-right {
    gap: 8px;
  }

  .dashboard-title {
    font-size: 14px;
  }

  .controls-right .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }

  .preview-content {
    padding: 10px;
  }
}

/* 组件占位符 */
.component-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: #ffffff;
  text-align: center;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.component-placeholder:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #409eff;
}

.placeholder-text {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #ffffff;
}

.placeholder-data {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}
</style>
