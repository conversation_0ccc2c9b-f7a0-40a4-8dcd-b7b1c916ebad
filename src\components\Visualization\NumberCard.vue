<template>
  <div class="number-card" :style="cardStyle">
    <div class="card-header" v-if="config.data.title">
      <h3 class="card-title">{{ config.data.title }}</h3>
      <div class="card-icon" v-if="config.data.icon">
        <el-icon><component :is="config.data.icon" /></el-icon>
      </div>
    </div>
    
    <div class="card-content">
      <div class="number-display">
        <span class="number-value" :style="numberStyle">
          {{ formattedValue }}
        </span>
        <span class="number-unit" v-if="config.data.unit">
          {{ config.data.unit }}
        </span>
      </div>
      
      <div class="trend-info" v-if="config.data.trend">
        <div class="trend-indicator" :class="trendClass">
          <el-icon>
            <ArrowUp v-if="config.data.trend > 0" />
            <ArrowDown v-else-if="config.data.trend < 0" />
            <Minus v-else />
          </el-icon>
          <span>{{ Math.abs(config.data.trend) }}%</span>
        </div>
        <span class="trend-text">{{ config.data.trendText || '较昨日' }}</span>
      </div>
    </div>
    
    <div class="card-footer" v-if="config.data.description">
      <p class="card-description">{{ config.data.description }}</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// 格式化数值
const formattedValue = computed(() => {
  const value = props.config.data.value || 0
  
  // 如果启用了数字动画，这里可以添加动画逻辑
  if (props.config.data.animation) {
    // 数字滚动动画逻辑
  }
  
  // 数字格式化
  if (props.config.data.format === 'percentage') {
    return (value * 100).toFixed(1) + '%'
  } else if (props.config.data.format === 'currency') {
    return '¥' + value.toLocaleString()
  } else if (props.config.data.format === 'decimal') {
    return value.toFixed(2)
  } else {
    return value.toLocaleString()
  }
})

// 卡片样式
const cardStyle = computed(() => ({
  background: props.config.style?.background || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  borderRadius: props.config.style?.borderRadius || '8px',
  padding: props.config.style?.padding || '20px',
  color: props.config.style?.color || '#ffffff',
  boxShadow: props.config.style?.boxShadow || '0 4px 12px rgba(0, 0, 0, 0.15)'
}))

// 数字样式
const numberStyle = computed(() => ({
  fontSize: props.config.style?.fontSize || '32px',
  fontWeight: props.config.style?.fontWeight || '600',
  color: props.config.style?.numberColor || '#ffffff'
}))

// 趋势样式类
const trendClass = computed(() => {
  const trend = props.config.data.trend
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-neutral'
})
</script>

<style scoped>
.number-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.card-icon {
  font-size: 20px;
  opacity: 0.8;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.number-display {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 8px;
}

.number-value {
  font-family: 'Arial', 'Helvetica', sans-serif;
  line-height: 1;
}

.number-unit {
  font-size: 16px;
  opacity: 0.8;
}

.trend-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.trend-up {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
}

.trend-down {
  background: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
}

.trend-neutral {
  background: rgba(144, 147, 153, 0.2);
  color: #909399;
}

.trend-text {
  opacity: 0.7;
}

.card-footer {
  margin-top: 12px;
}

.card-description {
  margin: 0;
  font-size: 12px;
  opacity: 0.7;
  line-height: 1.4;
}

/* 响应式 */
@media (max-width: 768px) {
  .card-title {
    font-size: 12px;
  }
  
  .number-value {
    font-size: 24px !important;
  }
  
  .number-unit {
    font-size: 14px;
  }
}
</style>
