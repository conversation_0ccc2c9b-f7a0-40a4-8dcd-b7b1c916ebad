<template>
  <div class="login-container">
    <div class="login-background">
      <div class="bg-overlay"></div>
    </div>
    
    <div class="login-content">
      <div class="login-card">
        <div class="login-header">
          <div class="logo">
            <el-icon :size="48" color="#1976D2">
              <Monitor />
            </el-icon>
            <h1>ITSM服务管理平台</h1>
            <p>智能化IT服务全生命周期管理</p>
          </div>
        </div>

        <div class="login-form">
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            size="large"
            @keyup.enter="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
                clearable
              />
            </el-form-item>

            <el-form-item>
              <div class="login-options">
                <el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
                <el-button type="text" @click="showForgotPassword">忘记密码？</el-button>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                style="width: 100%"
                :loading="loginLoading"
                @click="handleLogin"
              >
                {{ loginLoading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer">
            <div class="quick-login">
              <p>快速登录</p>
              <div class="quick-login-buttons">
                <el-button @click="quickLogin('admin')">管理员</el-button>
                <el-button @click="quickLogin('support')">技术支持</el-button>
                <el-button @click="quickLogin('user')">普通用户</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="login-info">
        <div class="feature-list">
          <div class="feature-item">
            <el-icon :size="24" color="#1976D2">
              <Headset />
            </el-icon>
            <div class="feature-text">
              <h3>多渠道服务台</h3>
              <p>统一接入，智能路由</p>
            </div>
          </div>
          
          <div class="feature-item">
            <el-icon :size="24" color="#1976D2">
              <TrendCharts />
            </el-icon>
            <div class="feature-text">
              <h3>智能分析</h3>
              <p>数据驱动，持续改进</p>
            </div>
          </div>
          
          <div class="feature-item">
            <el-icon :size="24" color="#1976D2">
              <Connection />
            </el-icon>
            <div class="feature-text">
              <h3>配置管理</h3>
              <p>全面掌控IT资产</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog v-model="forgotPasswordDialog" title="忘记密码" width="400px">
      <el-form :model="forgotForm" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="forgotForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="forgotForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="forgotPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="handleForgotPassword">发送重置邮件</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 表单引用
const loginFormRef = ref(null)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 登录状态
const loginLoading = ref(false)

// 忘记密码
const forgotPasswordDialog = ref(false)
const forgotForm = reactive({
  username: '',
  email: ''
})

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    
    loginLoading.value = true
    
    // 模拟登录请求
    setTimeout(() => {
      loginLoading.value = false
      ElMessage.success('登录成功')
      
      // 保存登录状态
      localStorage.setItem('isLoggedIn', 'true')
      localStorage.setItem('userInfo', JSON.stringify({
        username: loginForm.username,
        role: getUserRole(loginForm.username)
      }))
      
      // 跳转到仪表盘
      router.push('/dashboard')
    }, 1500)
    
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 快速登录
const quickLogin = (role) => {
  const accounts = {
    admin: { username: 'admin', password: 'admin123' },
    support: { username: 'support', password: 'support123' },
    user: { username: 'user', password: 'user123' }
  }
  
  const account = accounts[role]
  if (account) {
    loginForm.username = account.username
    loginForm.password = account.password
    ElMessage.info(`已填入${role}账号信息，点击登录即可`)
  }
}

// 获取用户角色
const getUserRole = (username) => {
  const roleMap = {
    admin: 'IT主管',
    support: '技术支持',
    user: '普通用户'
  }
  return roleMap[username] || '普通用户'
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  forgotPasswordDialog.value = true
}

// 处理忘记密码
const handleForgotPassword = () => {
  if (!forgotForm.username || !forgotForm.email) {
    ElMessage.warning('请填写完整信息')
    return
  }
  
  ElMessage.success('重置密码邮件已发送')
  forgotPasswordDialog.value = false
  
  // 重置表单
  forgotForm.username = ''
  forgotForm.email = ''
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.login-content {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 60px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo h1 {
  color: #1976D2;
  margin: 16px 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.logo p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-footer {
  margin-top: 30px;
  text-align: center;
}

.quick-login p {
  color: #666;
  margin-bottom: 16px;
  font-size: 14px;
}

.quick-login-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.quick-login-buttons .el-button {
  flex: 1;
  font-size: 12px;
}

.login-info {
  color: white;
  max-width: 400px;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.feature-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
}

.feature-text p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    gap: 30px;
  }

  .login-card {
    width: 100%;
    max-width: 400px;
    padding: 30px 20px;
  }

  .login-info {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 20px;
  }

  .login-header {
    margin-bottom: 30px;
  }

  .logo h1 {
    font-size: 20px;
  }

  .quick-login-buttons {
    flex-direction: column;
  }

  .quick-login-buttons .el-button {
    width: 100%;
  }
}

/* 动画效果 */
.login-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item {
  animation: fadeInLeft 0.8s ease-out;
}

.feature-item:nth-child(2) {
  animation-delay: 0.2s;
}

.feature-item:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
