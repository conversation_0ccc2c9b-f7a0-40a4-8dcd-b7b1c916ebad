<template>
  <div class="title-component" :style="titleStyle">
    <h1 
      v-if="config.data.level === 1"
      class="title-text"
      :style="textStyle"
    >
      {{ config.data.text }}
    </h1>
    <h2 
      v-else-if="config.data.level === 2"
      class="title-text"
      :style="textStyle"
    >
      {{ config.data.text }}
    </h2>
    <h3 
      v-else-if="config.data.level === 3"
      class="title-text"
      :style="textStyle"
    >
      {{ config.data.text }}
    </h3>
    <h4 
      v-else
      class="title-text"
      :style="textStyle"
    >
      {{ config.data.text }}
    </h4>
    
    <!-- 装饰线 -->
    <div 
      v-if="config.style?.showDecoration"
      class="title-decoration"
      :style="decorationStyle"
    ></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// 容器样式
const titleStyle = computed(() => ({
  textAlign: props.config.style?.textAlign || 'left',
  padding: props.config.style?.padding || '0',
  background: props.config.style?.background || 'transparent',
  borderRadius: props.config.style?.borderRadius || '0'
}))

// 文字样式
const textStyle = computed(() => {
  const level = props.config.data.level || 1
  const defaultSizes = {
    1: '32px',
    2: '28px', 
    3: '24px',
    4: '20px'
  }
  
  return {
    fontSize: props.config.style?.fontSize || defaultSizes[level],
    fontWeight: props.config.style?.fontWeight || '600',
    color: props.config.style?.color || '#303133',
    fontFamily: props.config.style?.fontFamily || 'inherit',
    lineHeight: props.config.style?.lineHeight || '1.2',
    letterSpacing: props.config.style?.letterSpacing || 'normal',
    textShadow: props.config.style?.textShadow || 'none',
    margin: '0'
  }
})

// 装饰线样式
const decorationStyle = computed(() => ({
  width: props.config.style?.decorationWidth || '60px',
  height: props.config.style?.decorationHeight || '3px',
  background: props.config.style?.decorationColor || '#409eff',
  marginTop: props.config.style?.decorationMargin || '8px',
  borderRadius: props.config.style?.decorationRadius || '2px'
}))
</script>

<style scoped>
.title-component {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title-text {
  word-break: break-word;
  white-space: pre-wrap;
}

.title-decoration {
  flex-shrink: 0;
}

/* 响应式 */
@media (max-width: 768px) {
  .title-text {
    font-size: 18px !important;
  }
}
</style>
