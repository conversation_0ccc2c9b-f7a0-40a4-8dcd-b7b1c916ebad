# 🚨 CMDB项目紧急启动指南

## 当前状态
- ❌ 项目无法正常启动（500错误）
- ❌ 缺少关键依赖包
- ✅ 已创建紧急修复版本

## 🔧 立即修复步骤

### 第1步：安装缺失依赖
```bash
# 方法1：运行修复脚本
fix-dependencies-now.bat    # Windows
./fix-dependencies-now.sh   # Linux/Mac

# 方法2：手动安装
npm install pinia axios mockjs

# 方法3：如果npm有问题，使用yarn
npm install -g yarn
yarn add pinia axios mockjs
```

### 第2步：启动项目
```bash
npm run dev
```

### 第3步：验证启动
访问 `http://localhost:5173`，您应该看到：
- ✅ CMDB配置管理系统标题
- ✅ 系统状态显示
- ✅ 依赖检查结果

## 🛡️ 紧急版本特性

我已经创建了一个故障保护版本：

### 简化的main.js
- 移除了所有可能导致错误的复杂导入
- 使用异步导入避免启动失败
- 详细的控制台日志便于调试

### 简化的App.vue
- 不依赖Element Plus也能正常显示
- 内置依赖检查功能
- 清晰的错误提示和修复指导

## 📋 故障排除

### 如果仍然出现500错误：

1. **检查Node.js版本**
   ```bash
   node --version  # 需要 >= 16.0.0
   ```

2. **清理并重新安装**
   ```bash
   rm -rf node_modules package-lock.json
   npm cache clean --force
   npm install
   ```

3. **检查端口占用**
   ```bash
   # Windows
   netstat -ano | findstr :5173
   
   # Linux/Mac
   lsof -ti:5173
   ```

4. **使用不同端口**
   ```bash
   npm run dev -- --port 3000
   ```

### 如果依赖安装失败：

1. **检查网络连接**
   ```bash
   npm config get registry
   ```

2. **使用国内镜像**
   ```bash
   npm config set registry https://registry.npmmirror.com/
   ```

3. **手动下载依赖**
   ```bash
   npm install pinia --verbose
   npm install axios --verbose
   npm install mockjs --verbose
   ```

## 🎯 成功标志

修复成功后，您将看到：

1. **控制台输出**：
   ```
   Starting CMDB application...
   Vue app created successfully
   ✓ Element Plus loaded successfully
   🚀 CMDB Application mounted successfully
   ```

2. **浏览器页面**：
   - 显示"CMDB配置管理系统"标题
   - 系统状态显示正常
   - 依赖检查显示安装状态

3. **功能测试**：
   - 点击"测试功能"按钮有弹窗
   - 点击"检查依赖"显示依赖状态

## 📞 如果仍然无法解决

请提供以下信息：

1. **系统信息**：
   - 操作系统版本
   - Node.js版本 (`node --version`)
   - npm版本 (`npm --version`)

2. **错误信息**：
   - 完整的控制台错误信息
   - 浏览器开发者工具中的错误

3. **文件检查**：
   ```bash
   # 检查关键文件是否存在
   ls -la src/main.js
   ls -la src/App.vue
   ls -la package.json
   ```

## ⚡ 快速命令参考

```bash
# 完整重置
rm -rf node_modules package-lock.json
npm cache clean --force
npm install pinia axios mockjs
npm run dev

# 检查依赖
npm list pinia axios mockjs

# 使用yarn（备选）
yarn install
yarn add pinia axios mockjs
yarn dev

# 检查端口
netstat -ano | findstr :5173  # Windows
lsof -ti:5173                 # Linux/Mac
```

## 🔄 恢复到完整版本

依赖安装成功后，可以恢复到完整的CMDB功能：

1. 确认所有依赖都已安装
2. 页面显示"所有依赖都已就绪"
3. 刷新页面或重启开发服务器

---

**重要提示**：当前版本是紧急修复版本，主要用于解决启动问题。依赖安装完成后，系统将自动恢复到完整功能。
