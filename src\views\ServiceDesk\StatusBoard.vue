<template>
  <div class="status-board">
    <div class="page-header">
      <h2>服务状态看板</h2>
      <div class="header-actions">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="toggleAutoRefresh">
          <el-icon><Timer /></el-icon>
          {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
        </el-button>
      </div>
    </div>

    <!-- 实时监控指标 -->
    <div class="monitoring-metrics">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="metric in metrics" :key="metric.key">
          <el-card class="metric-card" :class="metric.status">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon :size="24">
                  <component :is="metric.icon" />
                </el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-trend" :class="metric.trend">
                  <el-icon :size="12">
                    <component :is="metric.trend === 'up' ? 'ArrowUp' : metric.trend === 'down' ? 'ArrowDown' : 'Minus'" />
                  </el-icon>
                  {{ metric.change }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 工单状态分布 -->
    <div class="ticket-distribution">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>工单状态分布</span>
                <el-button type="text" size="small" @click="viewStatusDetails">查看详情</el-button>
              </div>
            </template>
            <div class="chart-container" ref="statusChart"></div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>优先级分布</span>
                <el-button type="text" size="small" @click="viewPriorityDetails">查看详情</el-button>
              </div>
            </template>
            <div class="chart-container" ref="priorityChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 支持团队负载 -->
    <div class="team-workload">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>支持团队负载分布</span>
            <div class="team-controls">
              <el-select v-model="selectedTeam" placeholder="选择团队" size="small" style="width: 150px;" @change="onTeamChange">
                <el-option label="全部团队" value="all" />
                <el-option label="桌面支持组" value="desktop" />
                <el-option label="网络运维组" value="network" />
                <el-option label="应用支持组" value="application" />
                <el-option label="数据库组" value="database" />
              </el-select>
              <el-button type="text" size="small" @click="showTeamDetails">团队详情</el-button>
            </div>
          </div>
        </template>
        <div class="team-grid">
          <div v-for="team in filteredTeams" :key="team.name" class="team-card">
            <div class="team-header">
              <div class="team-name">{{ team.name }}</div>
              <div class="team-status" :class="team.status">
                {{ team.statusText }}
              </div>
            </div>
            <div class="team-metrics">
              <div class="team-metric">
                <span class="metric-label">待处理</span>
                <span class="metric-value pending">{{ team.pending }}</span>
              </div>
              <div class="team-metric">
                <span class="metric-label">处理中</span>
                <span class="metric-value processing">{{ team.processing }}</span>
              </div>
              <div class="team-metric">
                <span class="metric-label">今日完成</span>
                <span class="metric-value completed">{{ team.completed }}</span>
              </div>
            </div>
            <div class="team-progress">
              <div class="progress-label">负载率</div>
              <el-progress 
                :percentage="team.workload" 
                :status="getWorkloadStatus(team.workload)"
                :show-text="false"
              />
              <span class="progress-text">{{ team.workload }}%</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 即将超时工单 -->
    <div class="overdue-tickets">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>即将超时工单 (SLA剩余 < 30分钟)</span>
            <el-badge :value="overdueTickets.length" class="badge">
              <el-button type="danger" size="small" @click="handleUrgentTickets">
                <el-icon><Warning /></el-icon>
                紧急处理
              </el-button>
            </el-badge>
          </div>
        </template>
        <div v-if="overdueTickets.length === 0" class="empty-state">
          <el-icon :size="48" color="#C0C4CC">
            <SuccessFilled />
          </el-icon>
          <p>暂无即将超时的工单</p>
        </div>
        <div v-else class="overdue-list">
          <div v-for="ticket in overdueTickets" :key="ticket.id" class="overdue-item">
            <div class="ticket-info">
              <div class="ticket-id">{{ ticket.id }}</div>
              <div class="ticket-title">{{ ticket.title }}</div>
              <div class="ticket-meta">
                <el-tag :type="getPriorityType(ticket.priority)" size="small">
                  {{ ticket.priority }}
                </el-tag>
                <span class="assignee">{{ ticket.assignee }}</span>
              </div>
            </div>
            <div class="time-remaining">
              <div class="time-value" :class="getTimeRemainingClass(ticket.timeRemaining)">
                {{ ticket.timeRemaining }}
              </div>
              <div class="time-label">剩余时间</div>
            </div>
            <div class="actions">
              <el-button type="primary" size="small" @click="processUrgentTicket(ticket)">处理</el-button>
              <el-button type="warning" size="small" @click="escalateTicket(ticket)">升级</el-button>
              <el-button type="info" size="small" @click="viewTicketDetail(ticket)">详情</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 服务可用性状态 -->
    <div class="service-availability">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>服务可用性状态</span>
            <div class="status-legend">
              <span class="legend-item">
                <span class="legend-dot healthy"></span>
                正常
              </span>
              <span class="legend-item">
                <span class="legend-dot warning"></span>
                警告
              </span>
              <span class="legend-item">
                <span class="legend-dot error"></span>
                故障
              </span>
            </div>
          </div>
        </template>
        <div class="service-grid">
          <div v-for="service in services" :key="service.name" class="service-item">
            <div class="service-status" :class="service.status">
              <div class="status-dot"></div>
            </div>
            <div class="service-info">
              <div class="service-name">{{ service.name }}</div>
              <div class="service-uptime">可用性: {{ service.uptime }}</div>
              <div class="service-response">响应时间: {{ service.responseTime }}</div>
            </div>
            <div class="service-actions">
              <el-button type="text" size="small" @click="viewServiceDetail(service)">详情</el-button>
              <el-button type="text" size="small" @click="checkServiceHealth(service)" v-if="service.status !== 'healthy'">
                检查
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 实时趋势图 -->
    <div class="trend-charts">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>工单趋势 (最近7天)</span>
                <el-radio-group v-model="trendPeriod" size="small">
                  <el-radio-button label="7d">7天</el-radio-button>
                  <el-radio-button label="30d">30天</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container" ref="trendChart"></div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>SLA达成率趋势</span>
                <el-button type="text" size="small" @click="viewSLAReport">SLA报告</el-button>
              </div>
            </template>
            <div class="chart-container" ref="slaChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时告警 -->
    <div class="real-time-alerts" v-if="realTimeAlerts.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>实时告警</span>
            <el-button type="text" size="small" @click="clearAllAlerts">清除全部</el-button>
          </div>
        </template>
        <div class="alerts-list">
          <el-alert
            v-for="alert in realTimeAlerts"
            :key="alert.id"
            :title="alert.title"
            :type="alert.type"
            :description="alert.description"
            show-icon
            closable
            @close="removeAlert(alert.id)"
            class="alert-item"
          >
            <template #default>
              <div class="alert-content">
                <div class="alert-info">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-description">{{ alert.description }}</div>
                  <div class="alert-time">{{ alert.time }}</div>
                </div>
                <div class="alert-actions">
                  <el-button size="small" type="primary" @click="handleAlert(alert)">处理</el-button>
                </div>
              </div>
            </template>
          </el-alert>
        </div>
      </el-card>
    </div>

    <!-- 团队详情对话框 -->
    <el-dialog v-model="showTeamDialog" title="团队详情" width="70%">
      <div v-if="selectedTeamDetail" class="team-detail">
        <el-tabs v-model="activeTeamTab">
          <el-tab-pane label="团队概览" name="overview">
            <div class="team-overview">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-statistic title="团队成员" :value="selectedTeamDetail.members" />
                </el-col>
                <el-col :span="8">
                  <el-statistic title="在线成员" :value="selectedTeamDetail.onlineMembers" />
                </el-col>
                <el-col :span="8">
                  <el-statistic title="平均负载" :value="selectedTeamDetail.avgWorkload" suffix="%" />
                </el-col>
              </el-row>
              <div class="team-members">
                <h4>团队成员</h4>
                <el-table :data="selectedTeamDetail.memberList" style="width: 100%">
                  <el-table-column prop="name" label="姓名" />
                  <el-table-column prop="role" label="角色" />
                  <el-table-column prop="status" label="状态">
                    <template #default="scope">
                      <el-tag :type="scope.row.status === '在线' ? 'success' : 'info'" size="small">
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="currentTickets" label="当前工单" />
                  <el-table-column prop="todayResolved" label="今日解决" />
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="工作负载" name="workload">
            <div class="workload-analysis">
              <div class="chart-container" ref="teamWorkloadChart"></div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="绩效统计" name="performance">
            <div class="performance-stats">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="本月解决工单" :value="selectedTeamDetail.monthlyResolved" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="平均解决时间" :value="selectedTeamDetail.avgResolutionTime" suffix="小时" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="客户满意度" :value="selectedTeamDetail.satisfaction" suffix="%" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="首次解决率" :value="selectedTeamDetail.firstCallResolution" suffix="%" />
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <el-button @click="showTeamDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 服务详情对话框 -->
    <el-dialog v-model="showServiceDialog" :title="`服务详情 - ${currentService?.name}`" width="60%">
      <div v-if="currentService" class="service-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="服务名称">{{ currentService.name }}</el-descriptions-item>
          <el-descriptions-item label="服务状态">
            <el-tag :type="getServiceStatusType(currentService.status)">
              {{ getServiceStatusText(currentService.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="可用性">{{ currentService.uptime }}</el-descriptions-item>
          <el-descriptions-item label="响应时间">{{ currentService.responseTime }}</el-descriptions-item>
          <el-descriptions-item label="最后检查时间">{{ currentService.lastCheck || '2025-01-30 17:30' }}</el-descriptions-item>
          <el-descriptions-item label="负责团队">{{ currentService.team || '运维团队' }}</el-descriptions-item>
        </el-descriptions>

        <div class="service-metrics">
          <h4>服务指标</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="metric-item">
                <div class="metric-label">CPU使用率</div>
                <el-progress :percentage="currentService.cpu || 45" />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="metric-item">
                <div class="metric-label">内存使用率</div>
                <el-progress :percentage="currentService.memory || 62" />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="metric-item">
                <div class="metric-label">磁盘使用率</div>
                <el-progress :percentage="currentService.disk || 38" />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <template #footer>
        <el-button @click="showServiceDialog = false">关闭</el-button>
        <el-button type="primary" @click="restartService(currentService)">重启服务</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'

const router = useRouter()

// 自动刷新
const autoRefresh = ref(false)
let refreshTimer = null

// 选中的团队
const selectedTeam = ref('all')

// 对话框状态
const showTeamDialog = ref(false)
const showServiceDialog = ref(false)

// 当前查看的详情
const selectedTeamDetail = ref(null)
const currentService = ref(null)
const activeTeamTab = ref('overview')

// 趋势图时间段
const trendPeriod = ref('7d')

// 实时监控指标
const metrics = ref([
  {
    key: 'pending',
    label: '待处理工单',
    value: '23',
    change: '+5',
    trend: 'up',
    status: 'warning',
    icon: 'Clock'
  },
  {
    key: 'processing',
    label: '处理中工单',
    value: '45',
    change: '+12',
    trend: 'up',
    status: 'info',
    icon: 'Loading'
  },
  {
    key: 'overdue',
    label: '超时工单',
    value: '3',
    change: '-2',
    trend: 'down',
    status: 'danger',
    icon: 'Warning'
  },
  {
    key: 'avgResponse',
    label: '平均响应时间',
    value: '15min',
    change: '0',
    trend: 'stable',
    status: 'success',
    icon: 'Timer'
  }
])

// 支持团队数据
const teams = ref([
  {
    name: '桌面支持组',
    type: 'desktop',
    status: 'normal',
    statusText: '正常',
    pending: 8,
    processing: 15,
    completed: 12,
    workload: 65
  },
  {
    name: '网络运维组',
    type: 'network',
    status: 'busy',
    statusText: '繁忙',
    pending: 12,
    processing: 20,
    completed: 8,
    workload: 85
  },
  {
    name: '应用支持组',
    type: 'application',
    status: 'normal',
    statusText: '正常',
    pending: 5,
    processing: 10,
    completed: 15,
    workload: 55
  },
  {
    name: '数据库组',
    type: 'database',
    status: 'idle',
    statusText: '空闲',
    pending: 2,
    processing: 3,
    completed: 6,
    workload: 25
  }
])

// 即将超时工单
const overdueTickets = ref([
  {
    id: 'INC-2025-001',
    title: '服务器CPU使用率过高',
    priority: '高',
    assignee: '张工',
    timeRemaining: '15分钟'
  },
  {
    id: 'INC-2025-002',
    title: '网络连接异常',
    priority: '紧急',
    assignee: '李工',
    timeRemaining: '5分钟'
  }
])

// 服务可用性
const services = ref([
  {
    name: 'ERP系统',
    status: 'healthy',
    uptime: '99.9%',
    responseTime: '120ms'
  },
  {
    name: '邮件服务',
    status: 'healthy',
    uptime: '99.8%',
    responseTime: '85ms'
  },
  {
    name: 'OA系统',
    status: 'warning',
    uptime: '98.5%',
    responseTime: '350ms'
  },
  {
    name: '数据库服务',
    status: 'healthy',
    uptime: '99.9%',
    responseTime: '45ms'
  },
  {
    name: '文件服务器',
    status: 'error',
    uptime: '95.2%',
    responseTime: '超时'
  },
  {
    name: '监控系统',
    status: 'healthy',
    uptime: '99.7%',
    responseTime: '200ms'
  }
])

// 实时告警数据
const realTimeAlerts = ref([
  {
    id: 1,
    title: 'SLA即将超时',
    description: '工单 INC-2025-001 将在5分钟后超时',
    type: 'warning',
    time: '2025-01-30 17:25'
  },
  {
    id: 2,
    title: '服务异常',
    description: '文件服务器响应超时，可能影响用户访问',
    type: 'error',
    time: '2025-01-30 17:20'
  }
])

// 图表引用
const statusChart = ref(null)
const priorityChart = ref(null)
const trendChart = ref(null)
const slaChart = ref(null)
const teamWorkloadChart = ref(null)

// 过滤后的团队
const filteredTeams = computed(() => {
  if (selectedTeam.value === 'all') {
    return teams.value
  }
  return teams.value.filter(team => team.type === selectedTeam.value)
})

// 团队详情数据
const teamDetails = {
  desktop: {
    name: '桌面支持组',
    members: 8,
    onlineMembers: 6,
    avgWorkload: 65,
    monthlyResolved: 245,
    avgResolutionTime: 4.2,
    satisfaction: 92,
    firstCallResolution: 78,
    memberList: [
      { name: '张工', role: '高级工程师', status: '在线', currentTickets: 5, todayResolved: 3 },
      { name: '李工', role: '工程师', status: '在线', currentTickets: 3, todayResolved: 2 },
      { name: '王工', role: '工程师', status: '离线', currentTickets: 0, todayResolved: 4 }
    ]
  },
  network: {
    name: '网络运维组',
    members: 6,
    onlineMembers: 5,
    avgWorkload: 85,
    monthlyResolved: 180,
    avgResolutionTime: 6.8,
    satisfaction: 88,
    firstCallResolution: 65,
    memberList: [
      { name: '赵工', role: '网络专家', status: '在线', currentTickets: 8, todayResolved: 2 },
      { name: '钱工', role: '高级工程师', status: '在线', currentTickets: 6, todayResolved: 1 }
    ]
  }
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const typeMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取负载状态
const getWorkloadStatus = (workload) => {
  if (workload >= 80) return 'exception'
  if (workload >= 60) return 'warning'
  return 'success'
}

// 获取剩余时间样式
const getTimeRemainingClass = (timeRemaining) => {
  const minutes = parseInt(timeRemaining)
  if (minutes <= 5) return 'critical'
  if (minutes <= 15) return 'warning'
  return 'normal'
}

// 查看状态详情
const viewStatusDetails = () => {
  router.push('/service-desk/tickets?view=status')
}

// 查看优先级详情
const viewPriorityDetails = () => {
  router.push('/service-desk/tickets?view=priority')
}

// 团队变更处理
const onTeamChange = () => {
  ElMessage.info(`切换到${selectedTeam.value === 'all' ? '全部团队' : '指定团队'}视图`)
}

// 显示团队详情
const showTeamDetails = () => {
  if (selectedTeam.value === 'all') {
    ElMessage.warning('请先选择具体团队')
    return
  }

  selectedTeamDetail.value = teamDetails[selectedTeam.value]
  if (selectedTeamDetail.value) {
    showTeamDialog.value = true
    activeTeamTab.value = 'overview'
  }
}

// 处理紧急工单
const handleUrgentTickets = () => {
  if (overdueTickets.value.length === 0) {
    ElMessage.info('当前没有紧急工单')
    return
  }

  ElMessageBox.confirm(
    `发现 ${overdueTickets.value.length} 个即将超时的工单，是否立即处理？`,
    '紧急工单处理',
    {
      confirmButtonText: '立即处理',
      cancelButtonText: '稍后处理',
      type: 'warning',
    }
  ).then(() => {
    router.push('/service-desk/tickets?filter=urgent')
  })
}

// 处理紧急工单
const processUrgentTicket = (ticket) => {
  router.push(`/service-desk/tickets/${ticket.id}/process`)
}

// 升级工单
const escalateTicket = (ticket) => {
  ElMessageBox.confirm(
    `确定要升级工单 "${ticket.title}" 吗？`,
    '确认升级',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('工单已升级')
    // 这里可以更新工单状态
  })
}

// 查看工单详情
const viewTicketDetail = (ticket) => {
  router.push(`/service-desk/tickets/${ticket.id}`)
}

// 查看服务详情
const viewServiceDetail = (service) => {
  currentService.value = service
  showServiceDialog.value = true
}

// 检查服务健康状态
const checkServiceHealth = (service) => {
  ElMessage.info(`正在检查 ${service.name} 的健康状态...`)

  // 模拟健康检查
  setTimeout(() => {
    if (Math.random() > 0.5) {
      service.status = 'healthy'
      ElMessage.success(`${service.name} 健康检查通过`)
    } else {
      ElMessage.warning(`${service.name} 仍存在问题，建议重启服务`)
    }
  }, 2000)
}

// 重启服务
const restartService = (service) => {
  ElMessageBox.confirm(
    `确定要重启 ${service.name} 吗？这可能会影响用户访问。`,
    '确认重启服务',
    {
      confirmButtonText: '确定重启',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success(`${service.name} 重启命令已发送`)
    showServiceDialog.value = false

    // 模拟重启过程
    setTimeout(() => {
      service.status = 'healthy'
      service.uptime = '99.9%'
      service.responseTime = '120ms'
      ElMessage.success(`${service.name} 重启完成`)
    }, 5000)
  })
}

// 查看SLA报告
const viewSLAReport = () => {
  router.push('/service-desk/sla-report')
}

// 处理告警
const handleAlert = (alert) => {
  ElMessage.info(`处理告警: ${alert.title}`)
  removeAlert(alert.id)
}

// 移除告警
const removeAlert = (alertId) => {
  const index = realTimeAlerts.value.findIndex(alert => alert.id === alertId)
  if (index > -1) {
    realTimeAlerts.value.splice(index, 1)
  }
}

// 清除所有告警
const clearAllAlerts = () => {
  realTimeAlerts.value = []
  ElMessage.success('所有告警已清除')
}

// 获取服务状态类型
const getServiceStatusType = (status) => {
  const typeMap = {
    'healthy': 'success',
    'warning': 'warning',
    'error': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取服务状态文本
const getServiceStatusText = (status) => {
  const textMap = {
    'healthy': '正常',
    'warning': '警告',
    'error': '故障'
  }
  return textMap[status] || status
}

// 初始化图表
const initCharts = () => {
  // 工单状态分布图
  const statusChartInstance = echarts.init(statusChart.value)
  statusChartInstance.setOption({
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '工单状态',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 23, name: '待处理' },
          { value: 45, name: '处理中' },
          { value: 32, name: '已解决' },
          { value: 8, name: '已关闭' }
        ]
      }
    ]
  })

  // 优先级分布图
  const priorityChartInstance = echarts.init(priorityChart.value)
  priorityChartInstance.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['低', '中', '高', '紧急']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '工单数量',
        type: 'bar',
        data: [15, 35, 28, 8],
        itemStyle: {
          color: function(params) {
            const colors = ['#4CAF50', '#FF9800', '#F44336', '#9C27B0']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  })

  // 工单趋势图
  if (trendChart.value) {
    const trendChartInstance = echarts.init(trendChart.value)
    trendChartInstance.setOption({
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新建', '解决', '关闭']
      },
      xAxis: {
        type: 'category',
        data: ['1/24', '1/25', '1/26', '1/27', '1/28', '1/29', '1/30']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新建',
          type: 'line',
          data: [12, 15, 18, 22, 16, 19, 23]
        },
        {
          name: '解决',
          type: 'line',
          data: [8, 12, 16, 18, 14, 17, 20]
        },
        {
          name: '关闭',
          type: 'line',
          data: [6, 10, 14, 16, 12, 15, 18]
        }
      ]
    })
  }

  // SLA达成率趋势图
  if (slaChart.value) {
    const slaChartInstance = echarts.init(slaChart.value)
    slaChartInstance.setOption({
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['1/24', '1/25', '1/26', '1/27', '1/28', '1/29', '1/30']
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100
      },
      series: [
        {
          name: 'SLA达成率',
          type: 'line',
          data: [85, 88, 92, 89, 91, 94, 87],
          itemStyle: {
            color: '#4CAF50'
          },
          areaStyle: {
            color: 'rgba(76, 175, 80, 0.1)'
          }
        }
      ]
    })
  }
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')

  // 模拟数据更新
  metrics.value.forEach(metric => {
    const change = Math.floor(Math.random() * 10) - 5
    const currentValue = parseInt(metric.value) || 0
    const newValue = Math.max(0, currentValue + change)

    if (metric.key !== 'avgResponse') {
      metric.value = newValue.toString()
      metric.change = change > 0 ? `+${change}` : change.toString()
      metric.trend = change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
    }
  })

  // 重新初始化图表
  nextTick(() => {
    initCharts()
  })
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value) {
    refreshTimer = setInterval(refreshData, 30000) // 30秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })

  // 模拟实时数据更新
  setInterval(() => {
    // 随机添加新告警
    if (Math.random() > 0.95 && realTimeAlerts.value.length < 5) {
      const alertTypes = ['warning', 'error', 'info']
      const alertTitles = ['SLA即将超时', '服务异常', '系统告警', '性能警告']

      const newAlert = {
        id: Date.now(),
        title: alertTitles[Math.floor(Math.random() * alertTitles.length)],
        description: '系统检测到异常情况，请及时处理',
        type: alertTypes[Math.floor(Math.random() * alertTypes.length)],
        time: new Date().toLocaleString()
      }

      realTimeAlerts.value.unshift(newAlert)
    }
  }, 10000) // 每10秒检查一次
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.status-board {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.monitoring-metrics {
  margin-bottom: 20px;
}

.metric-card {
  height: 100px;
  transition: all 0.3s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-card.warning {
  border-left: 4px solid #FF9800;
}

.metric-card.danger {
  border-left: 4px solid #F44336;
}

.metric-card.success {
  border-left: 4px solid #4CAF50;
}

.metric-card.info {
  border-left: 4px solid #2196F3;
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.metric-icon {
  margin-right: 12px;
  color: #1976D2;
}

.metric-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.metric-label {
  color: #616161;
  font-size: 12px;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.metric-trend.up {
  color: #4CAF50;
}

.metric-trend.down {
  color: #F44336;
}

.metric-trend.stable {
  color: #616161;
}

.ticket-distribution,
.team-workload,
.overdue-tickets,
.service-availability,
.trend-charts,
.real-time-alerts {
  margin-bottom: 20px;
}

.team-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.team-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.team-name {
  font-weight: 500;
  color: #333;
}

.team-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.team-status.normal {
  background: #e8f5e8;
  color: #4CAF50;
}

.team-status.busy {
  background: #fff3e0;
  color: #FF9800;
}

.team-status.idle {
  background: #e3f2fd;
  color: #2196F3;
}

.team-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.team-metric {
  text-align: center;
}

.team-metric .metric-label {
  font-size: 12px;
  color: #616161;
  margin-bottom: 4px;
}

.team-metric .metric-value {
  font-size: 18px;
  font-weight: bold;
}

.team-metric .metric-value.pending {
  color: #FF9800;
}

.team-metric .metric-value.processing {
  color: #2196F3;
}

.team-metric .metric-value.completed {
  color: #4CAF50;
}

.team-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-label {
  font-size: 12px;
  color: #616161;
  min-width: 40px;
}

.progress-text {
  font-size: 12px;
  color: #616161;
  min-width: 35px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #616161;
}

.overdue-list {
  max-height: 400px;
  overflow-y: auto;
}

.overdue-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  gap: 16px;
}

.overdue-item:last-child {
  border-bottom: none;
}

.ticket-info {
  flex: 1;
}

.ticket-id {
  font-weight: bold;
  color: #1976D2;
  margin-bottom: 4px;
}

.ticket-title {
  color: #333;
  margin-bottom: 8px;
}

.ticket-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee {
  font-size: 12px;
  color: #616161;
}

.time-remaining {
  text-align: center;
  min-width: 80px;
}

.time-value {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.time-value.critical {
  color: #F44336;
}

.time-value.warning {
  color: #FF9800;
}

.time-value.normal {
  color: #4CAF50;
}

.time-label {
  font-size: 12px;
  color: #616161;
}

.actions {
  display: flex;
  gap: 8px;
}

.status-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.healthy {
  background: #4CAF50;
}

.legend-dot.warning {
  background: #FF9800;
}

.legend-dot.error {
  background: #F44336;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  gap: 12px;
}

.service-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.service-status.healthy {
  background: #e8f5e8;
}

.service-status.warning {
  background: #fff3e0;
}

.service-status.error {
  background: #ffebee;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.service-status.healthy .status-dot {
  background: #4CAF50;
}

.service-status.warning .status-dot {
  background: #FF9800;
}

.service-status.error .status-dot {
  background: #F44336;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.service-uptime,
.service-response {
  font-size: 12px;
  color: #616161;
  margin-bottom: 2px;
}

/* 实时告警样式 */
.alerts-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  margin-bottom: 12px;
}

.alert-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.alert-info {
  flex: 1;
}

.alert-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.alert-description {
  color: #666;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #999;
}

.alert-actions {
  margin-left: 16px;
}

/* 团队详情样式 */
.team-detail {
  padding: 20px 0;
}

.team-overview {
  padding: 20px 0;
}

.team-members {
  margin-top: 30px;
}

.team-members h4 {
  color: #333;
  margin-bottom: 16px;
}

.workload-analysis,
.performance-stats {
  padding: 20px 0;
}

/* 服务详情样式 */
.service-detail {
  padding: 20px 0;
}

.service-metrics {
  margin-top: 30px;
}

.service-metrics h4 {
  color: #333;
  margin-bottom: 20px;
}

.metric-item {
  text-align: center;
}

.metric-item .metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }

  .service-grid {
    grid-template-columns: 1fr;
  }

  .overdue-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .time-remaining {
    align-self: flex-end;
  }
}
</style>
