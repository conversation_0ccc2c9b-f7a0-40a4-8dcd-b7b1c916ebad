import request from '@/utils/request'

// 请求履约模块 API

/**
 * 获取请求概览数据
 * @returns {Promise}
 */
export function getRequestOverview() {
  return request({
    url: '/api/request/overview',
    method: 'get'
  })
}

/**
 * 获取请求统计数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getRequestStatistics(params) {
  return request({
    url: '/api/request/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取服务目录列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getServiceCatalog(params) {
  return request({
    url: '/api/request/catalog',
    method: 'get',
    params
  })
}

/**
 * 获取服务分类
 * @returns {Promise}
 */
export function getServiceCategories() {
  return request({
    url: '/api/request/categories',
    method: 'get'
  })
}

/**
 * 获取服务详情
 * @param {string} serviceId - 服务ID
 * @returns {Promise}
 */
export function getServiceDetail(serviceId) {
  return request({
    url: `/api/request/catalog/${serviceId}`,
    method: 'get'
  })
}

/**
 * 创建服务请求
 * @param {Object} data - 请求数据
 * @returns {Promise}
 */
export function createServiceRequest(data) {
  return request({
    url: '/api/request/requests',
    method: 'post',
    data
  })
}

/**
 * 获取用户请求列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getUserRequests(params) {
  return request({
    url: '/api/request/requests',
    method: 'get',
    params
  })
}

/**
 * 获取请求详情
 * @param {string} requestId - 请求ID
 * @returns {Promise}
 */
export function getRequestDetail(requestId) {
  return request({
    url: `/api/request/requests/${requestId}`,
    method: 'get'
  })
}

/**
 * 更新请求状态
 * @param {string} requestId - 请求ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateRequestStatus(requestId, data) {
  return request({
    url: `/api/request/requests/${requestId}/status`,
    method: 'put',
    data
  })
}

/**
 * 取消请求
 * @param {string} requestId - 请求ID
 * @param {Object} data - 取消原因
 * @returns {Promise}
 */
export function cancelRequest(requestId, data) {
  return request({
    url: `/api/request/requests/${requestId}/cancel`,
    method: 'put',
    data
  })
}

/**
 * 获取审批流程配置
 * @param {string} serviceId - 服务ID
 * @returns {Promise}
 */
export function getApprovalWorkflow(serviceId) {
  return request({
    url: `/api/request/workflow/${serviceId}`,
    method: 'get'
  })
}

/**
 * 提交审批
 * @param {string} requestId - 请求ID
 * @param {Object} data - 审批数据
 * @returns {Promise}
 */
export function submitApproval(requestId, data) {
  return request({
    url: `/api/request/requests/${requestId}/approve`,
    method: 'post',
    data
  })
}

/**
 * 获取待审批请求列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getPendingApprovals(params) {
  return request({
    url: '/api/request/approvals/pending',
    method: 'get',
    params
  })
}

/**
 * 批量审批
 * @param {Object} data - 批量审批数据
 * @returns {Promise}
 */
export function batchApproval(data) {
  return request({
    url: '/api/request/approvals/batch',
    method: 'post',
    data
  })
}

/**
 * 获取请求历史记录
 * @param {string} requestId - 请求ID
 * @returns {Promise}
 */
export function getRequestHistory(requestId) {
  return request({
    url: `/api/request/requests/${requestId}/history`,
    method: 'get'
  })
}

/**
 * 提交满意度评价
 * @param {string} requestId - 请求ID
 * @param {Object} data - 评价数据
 * @returns {Promise}
 */
export function submitSatisfactionRating(requestId, data) {
  return request({
    url: `/api/request/requests/${requestId}/rating`,
    method: 'post',
    data
  })
}

/**
 * 获取自动化处理状态
 * @param {string} requestId - 请求ID
 * @returns {Promise}
 */
export function getAutomationStatus(requestId) {
  return request({
    url: `/api/request/requests/${requestId}/automation`,
    method: 'get'
  })
}

/**
 * 触发自动化处理
 * @param {string} requestId - 请求ID
 * @param {Object} data - 自动化参数
 * @returns {Promise}
 */
export function triggerAutomation(requestId, data) {
  return request({
    url: `/api/request/requests/${requestId}/automation`,
    method: 'post',
    data
  })
}

/**
 * 获取通知设置
 * @returns {Promise}
 */
export function getNotificationSettings() {
  return request({
    url: '/api/request/notifications/settings',
    method: 'get'
  })
}

/**
 * 更新通知设置
 * @param {Object} data - 通知设置
 * @returns {Promise}
 */
export function updateNotificationSettings(data) {
  return request({
    url: '/api/request/notifications/settings',
    method: 'put',
    data
  })
}

/**
 * 获取用户通知列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getUserNotifications(params) {
  return request({
    url: '/api/request/notifications',
    method: 'get',
    params
  })
}

/**
 * 标记通知为已读
 * @param {string} notificationId - 通知ID
 * @returns {Promise}
 */
export function markNotificationAsRead(notificationId) {
  return request({
    url: `/api/request/notifications/${notificationId}/read`,
    method: 'put'
  })
}

/**
 * 搜索服务
 * @param {Object} params - 搜索参数
 * @returns {Promise}
 */
export function searchServices(params) {
  return request({
    url: '/api/request/catalog/search',
    method: 'get',
    params
  })
}

/**
 * 获取热门服务
 * @param {number} limit - 限制数量
 * @returns {Promise}
 */
export function getPopularServices(limit = 10) {
  return request({
    url: '/api/request/catalog/popular',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取推荐服务
 * @param {string} userId - 用户ID
 * @returns {Promise}
 */
export function getRecommendedServices(userId) {
  return request({
    url: `/api/request/catalog/recommended/${userId}`,
    method: 'get'
  })
}

/**
 * 导出请求数据
 * @param {Object} params - 导出参数
 * @returns {Promise}
 */
export function exportRequestData(params) {
  return request({
    url: '/api/request/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取请求模板
 * @param {string} serviceId - 服务ID
 * @returns {Promise}
 */
export function getRequestTemplate(serviceId) {
  return request({
    url: `/api/request/templates/${serviceId}`,
    method: 'get'
  })
}

/**
 * 保存请求草稿
 * @param {Object} data - 草稿数据
 * @returns {Promise}
 */
export function saveRequestDraft(data) {
  return request({
    url: '/api/request/drafts',
    method: 'post',
    data
  })
}

/**
 * 获取用户草稿列表
 * @returns {Promise}
 */
export function getUserDrafts() {
  return request({
    url: '/api/request/drafts',
    method: 'get'
  })
}

/**
 * 删除草稿
 * @param {string} draftId - 草稿ID
 * @returns {Promise}
 */
export function deleteDraft(draftId) {
  return request({
    url: `/api/request/drafts/${draftId}`,
    method: 'delete'
  })
}
