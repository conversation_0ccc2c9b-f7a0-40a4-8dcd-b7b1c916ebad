<template>
  <div class="change-management">
    <div class="page-header">
      <div class="header-info">
        <h2>变更管理</h2>
        <p>控制变更风险，确保服务稳定</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createChange">
          <el-icon><Plus /></el-icon>
          创建变更
        </el-button>
        <el-button @click="$router.push('/change/calendar')">
          <el-icon><Calendar /></el-icon>
          变更日历
        </el-button>
        <el-button @click="$router.push('/change/cab')">
          <el-icon><Select /></el-icon>
          CAB审批
        </el-button>
        <el-button @click="exportChanges">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 变更统计 -->
    <div class="change-stats">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in changeStats" :key="stat.key">
          <el-card class="stat-card" :class="stat.status" @click="filterByStatus(stat.key)">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24" :color="stat.color">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trend">
                  <el-icon><component :is="stat.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ stat.change }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 变更趋势和风险分析 -->
    <div class="change-analytics">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="16">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>变更趋势分析</span>
                <el-radio-group v-model="trendPeriod" size="small">
                  <el-radio-button label="7d">7天</el-radio-button>
                  <el-radio-button label="30d">30天</el-radio-button>
                  <el-radio-button label="90d">90天</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="trend-chart" ref="trendChart"></div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="8">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>风险分布</span>
                <el-button type="text" size="small" @click="viewRiskDetails">详情</el-button>
              </div>
            </template>
            <div class="risk-chart" ref="riskChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 变更窗口分析 -->
    <div class="change-window-analysis">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>变更窗口分析</span>
            <el-button type="primary" size="small" @click="optimizeWindows">
              <el-icon><MagicStick /></el-icon>
              智能优化
            </el-button>
          </div>
        </template>
        <div class="window-content">
          <el-table :data="changeWindows" style="width: 100%">
            <el-table-column prop="window" label="变更窗口" />
            <el-table-column prop="scheduled" label="已安排" width="80" />
            <el-table-column prop="capacity" label="容量" width="80" />
            <el-table-column prop="utilization" label="利用率" width="120">
              <template #default="scope">
                <el-progress :percentage="scope.row.utilization" :show-text="false" />
                <span style="margin-left: 8px;">{{ scope.row.utilization }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="risk" label="风险评估" width="120">
              <template #default="scope">
                <el-tag :type="getRiskType(scope.row.risk)" size="small">
                  {{ scope.row.risk }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" size="small" text @click="viewWindowDetails(scope.row)">
                  查看详情
                </el-button>
                <el-button type="warning" size="small" text @click="adjustWindow(scope.row)">
                  调整
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 变更列表 -->
    <div class="change-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <span>变更列表 ({{ filteredChanges.length }})</span>
              <el-tag v-if="currentFilter !== 'all'" type="primary" size="small" closable @close="clearFilter">
                {{ getFilterText(currentFilter) }}
              </el-tag>
            </div>
            <div class="header-right">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索变更..."
                prefix-icon="Search"
                size="small"
                style="width: 200px; margin-right: 12px;"
                @input="searchChanges"
              />
              <el-select v-model="statusFilter" placeholder="状态筛选" size="small" style="width: 120px; margin-right: 12px;">
                <el-option label="全部状态" value="all" />
                <el-option label="草稿" value="草稿" />
                <el-option label="待审批" value="待审批" />
                <el-option label="已批准" value="已批准" />
                <el-option label="已实施" value="已实施" />
              </el-select>
              <el-button type="text" size="small" @click="viewAllChanges">查看全部</el-button>
            </div>
          </div>
        </template>
        <el-table :data="paginatedChanges" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="变更ID" width="120" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="type" label="类型" width="100">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="risk" label="风险等级" width="100">
            <template #default="scope">
              <el-tag :type="getRiskType(scope.row.risk)" size="small">
                {{ scope.row.risk }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)" size="small">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="负责人" width="100" />
          <el-table-column prop="implementDate" label="实施日期" width="160" />
          <el-table-column label="操作" width="250">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewChange(scope.row)">
                查看
              </el-button>
              <el-button type="success" size="small" text @click="approveChange(scope.row)" v-if="scope.row.status === '待审批'">
                审批
              </el-button>
              <el-button type="warning" size="small" text @click="implementChange(scope.row)" v-if="scope.row.status === '已批准'">
                实施
              </el-button>
              <el-dropdown @command="handleChangeCommand">
                <el-button type="primary" size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'edit', row: scope.row}">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'duplicate', row: scope.row}">复制</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'rollback', row: scope.row}" v-if="scope.row.status === '已实施'">回滚</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'close', row: scope.row}">关闭</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredChanges.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 创建变更对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建变更" width="60%" :before-close="handleClose">
      <el-form :model="changeForm" :rules="changeRules" ref="changeFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="变更标题" prop="title">
              <el-input v-model="changeForm.title" placeholder="请输入变更标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="变更类型" prop="type">
              <el-select v-model="changeForm.type" placeholder="请选择变更类型" style="width: 100%">
                <el-option label="标准变更" value="标准变更" />
                <el-option label="紧急变更" value="紧急变更" />
                <el-option label="正常变更" value="正常变更" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="风险等级" prop="risk">
              <el-select v-model="changeForm.risk" placeholder="请选择风险等级" style="width: 100%">
                <el-option label="低" value="低" />
                <el-option label="中" value="中" />
                <el-option label="高" value="高" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="changeForm.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="低" value="低" />
                <el-option label="中" value="中" />
                <el-option label="高" value="高" />
                <el-option label="紧急" value="紧急" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划实施时间" prop="implementDate">
              <el-date-picker
                v-model="changeForm.implementDate"
                type="datetime"
                placeholder="选择实施时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="assignee">
              <el-select v-model="changeForm.assignee" placeholder="请选择负责人" style="width: 100%">
                <el-option label="张工" value="张工" />
                <el-option label="李工" value="李工" />
                <el-option label="王工" value="王工" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="变更描述" prop="description">
          <el-input
            v-model="changeForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述变更内容、目的和预期效果"
          />
        </el-form-item>

        <el-form-item label="影响分析" prop="impact">
          <el-input
            v-model="changeForm.impact"
            type="textarea"
            :rows="3"
            placeholder="请分析变更可能产生的影响"
          />
        </el-form-item>

        <el-form-item label="回滚计划" prop="rollbackPlan">
          <el-input
            v-model="changeForm.rollbackPlan"
            type="textarea"
            :rows="3"
            placeholder="请制定回滚计划"
          />
        </el-form-item>

        <el-form-item label="测试计划" prop="testPlan">
          <el-input
            v-model="changeForm.testPlan"
            type="textarea"
            :rows="3"
            placeholder="请制定测试计划"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveChange">保存草稿</el-button>
          <el-button type="success" @click="submitChange">提交审批</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('all')
const currentFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const trendPeriod = ref('7d')

// 变更统计数据
const changeStats = ref([
  {
    key: 'total',
    label: '总变更数',
    value: '32',
    icon: 'Edit',
    color: '#1976D2',
    status: 'info',
    trend: 'up',
    change: '+3'
  },
  {
    key: 'pending',
    label: '待审批',
    value: '8',
    icon: 'Clock',
    color: '#FF9800',
    status: 'warning',
    trend: 'down',
    change: '-2'
  },
  {
    key: 'approved',
    label: '已批准',
    value: '15',
    icon: 'Select',
    color: '#4CAF50',
    status: 'success',
    trend: 'up',
    change: '+5'
  },
  {
    key: 'implemented',
    label: '已实施',
    value: '9',
    icon: 'CircleCheck',
    color: '#2196F3',
    status: 'info',
    trend: 'up',
    change: '+2'
  }
])

// 变更列表数据
const changes = ref([
  {
    id: 'CHG-2025-001',
    title: '服务器系统升级',
    type: '标准变更',
    status: '已批准',
    risk: '中',
    priority: '高',
    assignee: '张工',
    implementDate: '2025-02-01 02:00',
    description: '升级生产服务器操作系统到最新版本'
  },
  {
    id: 'CHG-2025-002',
    title: '网络设备配置更新',
    type: '紧急变更',
    status: '待审批',
    risk: '高',
    priority: '紧急',
    assignee: '李工',
    implementDate: '2025-01-31 20:00',
    description: '更新核心网络设备配置以修复安全漏洞'
  },
  {
    id: 'CHG-2025-003',
    title: '数据库性能优化',
    type: '正常变更',
    status: '已实施',
    risk: '低',
    priority: '中',
    assignee: '王工',
    implementDate: '2025-01-30 14:00',
    description: '优化数据库查询性能，提升系统响应速度'
  },
  {
    id: 'CHG-2025-004',
    title: '应用程序版本更新',
    type: '标准变更',
    status: '草稿',
    risk: '中',
    priority: '中',
    assignee: '赵工',
    implementDate: '2025-02-02 10:00',
    description: '更新应用程序到最新版本，修复已知问题'
  }
])

// 变更窗口数据
const changeWindows = ref([
  {
    window: '周一 02:00-06:00',
    scheduled: 3,
    capacity: 5,
    utilization: 60,
    risk: '低'
  },
  {
    window: '周三 02:00-06:00',
    scheduled: 4,
    capacity: 5,
    utilization: 80,
    risk: '中'
  },
  {
    window: '周六 20:00-24:00',
    scheduled: 5,
    capacity: 5,
    utilization: 100,
    risk: '高'
  },
  {
    window: '周日 02:00-06:00',
    scheduled: 2,
    capacity: 5,
    utilization: 40,
    risk: '低'
  }
])

// 图表引用
const trendChart = ref()
const riskChart = ref()

// 计算属性
const filteredChanges = computed(() => {
  let filtered = changes.value

  // 按状态过滤
  if (currentFilter.value !== 'all') {
    const statusMap = {
      'total': null,
      'pending': '待审批',
      'approved': '已批准',
      'implemented': '已实施'
    }
    const targetStatus = statusMap[currentFilter.value]
    if (targetStatus) {
      filtered = filtered.filter(c => c.status === targetStatus)
    }
  }

  // 按状态筛选
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(c => c.status === statusFilter.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(c =>
      c.id.toLowerCase().includes(keyword) ||
      c.title.toLowerCase().includes(keyword) ||
      c.description.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

const paginatedChanges = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredChanges.value.slice(start, end)
})

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    '草稿': 'info',
    '待审批': 'warning',
    '已批准': 'success',
    '已拒绝': 'danger',
    '已实施': 'success',
    '已回滚': 'warning',
    '已关闭': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取风险类型
const getRiskType = (risk) => {
  const typeMap = {
    '低': 'success',
    '中': 'warning',
    '高': 'danger'
  }
  return typeMap[risk] || 'info'
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const typeMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取过滤器文本
const getFilterText = (filter) => {
  const textMap = {
    'total': '全部',
    'pending': '待审批',
    'approved': '已批准',
    'implemented': '已实施'
  }
  return textMap[filter] || filter
}

// 对话框状态
const showCreateDialog = ref(false)
const changeFormRef = ref()

// 变更表单
const changeForm = reactive({
  title: '',
  type: '',
  risk: '',
  priority: '',
  implementDate: '',
  assignee: '',
  description: '',
  impact: '',
  rollbackPlan: '',
  testPlan: ''
})

// 表单验证规则
const changeRules = {
  title: [
    { required: true, message: '请输入变更标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择变更类型', trigger: 'change' }
  ],
  risk: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  implementDate: [
    { required: true, message: '请选择实施时间', trigger: 'change' }
  ],
  assignee: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入变更描述', trigger: 'blur' }
  ]
}

// 按状态过滤
const filterByStatus = (status) => {
  currentFilter.value = status
  currentPage.value = 1
}

// 清除过滤器
const clearFilter = () => {
  currentFilter.value = 'all'
  currentPage.value = 1
}

// 搜索变更
const searchChanges = () => {
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 查看风险详情
const viewRiskDetails = () => {
  router.push('/change/risk-analysis')
}

// 智能优化变更窗口
const optimizeWindows = () => {
  ElMessage.success('正在进行智能优化分析...')
  // 模拟AI优化过程
  setTimeout(() => {
    ElMessage.success('变更窗口优化建议已生成')
  }, 2000)
}

// 查看窗口详情
const viewWindowDetails = (window) => {
  ElMessage.info(`查看窗口详情: ${window.window}`)
}

// 调整变更窗口
const adjustWindow = (window) => {
  ElMessage.info(`调整变更窗口: ${window.window}`)
}

// 创建变更
const createChange = () => {
  showCreateDialog.value = true
  // 重置表单
  Object.keys(changeForm).forEach(key => {
    changeForm[key] = ''
  })
}

// 导出变更
const exportChanges = () => {
  ElMessage.success('变更数据导出中...')
  // 这里可以实现导出逻辑
}

// 查看全部变更
const viewAllChanges = () => {
  router.push('/change/list')
}

// 查看变更详情
const viewChange = (change) => {
  ElMessage.info(`查看变更详情: ${change.id}`)
  // 这里可以实现查看详情的逻辑
}

// 审批变更
const approveChange = (change) => {
  router.push(`/change/cab?changeId=${change.id}`)
}

// 实施变更
const implementChange = (change) => {
  ElMessageBox.confirm(
    `确定要实施变更 "${change.title}" 吗？`,
    '确认实施',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 实施变更
    change.status = '已实施'
    ElMessage.success('变更已实施')

    // 更新统计数据
    const implementedStat = changeStats.value.find(s => s.key === 'implemented')
    const approvedStat = changeStats.value.find(s => s.key === 'approved')
    if (implementedStat) implementedStat.value = String(parseInt(implementedStat.value) + 1)
    if (approvedStat) approvedStat.value = String(parseInt(approvedStat.value) - 1)
  }).catch(() => {
    ElMessage.info('已取消实施')
  })
}

// 处理变更命令
const handleChangeCommand = (command) => {
  const { action, row } = command
  switch (action) {
    case 'edit':
      editChange(row)
      break
    case 'duplicate':
      duplicateChange(row)
      break
    case 'rollback':
      rollbackChange(row)
      break
    case 'close':
      closeChange(row)
      break
  }
}

const editChange = (change) => {
  router.push(`/change/edit/${change.id}`)
}

const duplicateChange = (change) => {
  // 复制变更信息到表单
  changeForm.title = `${change.title} (副本)`
  changeForm.type = change.type
  changeForm.risk = change.risk
  changeForm.priority = change.priority
  changeForm.assignee = change.assignee
  changeForm.description = change.description
  showCreateDialog.value = true
}

const rollbackChange = (change) => {
  ElMessageBox.confirm(
    `确定要回滚变更 "${change.title}" 吗？`,
    '确认回滚',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    change.status = '已回滚'
    ElMessage.success('变更已回滚')
  }).catch(() => {
    ElMessage.info('已取消回滚')
  })
}

const closeChange = (change) => {
  ElMessageBox.confirm(
    `确定要关闭变更 "${change.title}" 吗？`,
    '确认关闭',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    change.status = '已关闭'
    ElMessage.success('变更已关闭')
  }).catch(() => {
    ElMessage.info('已取消关闭')
  })
}

// 保存变更草稿
const saveChange = () => {
  changeFormRef.value.validate((valid) => {
    if (valid) {
      ElMessage.success('变更草稿已保存')
      showCreateDialog.value = false

      // 添加到变更列表
      const newChange = {
        id: `CHG-${Date.now()}`,
        title: changeForm.title,
        type: changeForm.type,
        status: '草稿',
        risk: changeForm.risk,
        implementDate: new Date(changeForm.implementDate).toLocaleString()
      }
      changes.value.unshift(newChange)
    }
  })
}

// 提交变更审批
const submitChange = () => {
  changeFormRef.value.validate((valid) => {
    if (valid) {
      ElMessage.success('变更已提交审批')
      showCreateDialog.value = false

      // 添加到变更列表
      const newChange = {
        id: `CHG-${Date.now()}`,
        title: changeForm.title,
        type: changeForm.type,
        status: '待审批',
        risk: changeForm.risk,
        implementDate: new Date(changeForm.implementDate).toLocaleString()
      }
      changes.value.unshift(newChange)
    }
  })
}

// 对话框关闭处理
const handleClose = (done) => {
  if (changeForm.title || changeForm.description) {
    ElMessageBox.confirm('表单内容将会丢失，确定要关闭吗？')
      .then(() => {
        resetChangeForm()
        done()
      })
      .catch(() => {})
  } else {
    done()
  }
}

const resetChangeForm = () => {
  Object.keys(changeForm).forEach(key => {
    changeForm[key] = ''
  })
  changeForm.implementDate = null
}

// 初始化图表
const initCharts = () => {
  // 变更趋势图
  if (trendChart.value) {
    const trendChartInstance = echarts.init(trendChart.value)
    trendChartInstance.setOption({
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新建变更', '已实施变更', '累计变更']
      },
      xAxis: {
        type: 'category',
        data: ['1/24', '1/25', '1/26', '1/27', '1/28', '1/29', '1/30']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新建变更',
          type: 'line',
          data: [3, 5, 2, 6, 4, 3, 4],
          itemStyle: { color: '#FF9800' }
        },
        {
          name: '已实施变更',
          type: 'line',
          data: [2, 3, 4, 2, 3, 5, 3],
          itemStyle: { color: '#4CAF50' }
        },
        {
          name: '累计变更',
          type: 'bar',
          data: [32, 34, 32, 36, 37, 35, 36],
          itemStyle: { color: '#1976D2' }
        }
      ]
    })
  }

  // 风险分布图
  if (riskChart.value) {
    const riskChartInstance = echarts.init(riskChart.value)
    riskChartInstance.setOption({
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '风险分布',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 12, name: '低风险', itemStyle: { color: '#4CAF50' } },
            { value: 15, name: '中风险', itemStyle: { color: '#FF9800' } },
            { value: 5, name: '高风险', itemStyle: { color: '#F44336' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })

  // 模拟实时数据更新
  setInterval(() => {
    // 随机更新统计数据
    changeStats.value.forEach(stat => {
      if (stat.key !== 'total') {
        const change = Math.floor(Math.random() * 3) - 1
        const currentValue = parseInt(stat.value) || 0
        const newValue = Math.max(0, currentValue + change)

        stat.value = newValue.toString()
        stat.change = change > 0 ? `+${change}` : change.toString()
        stat.trend = change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
      }
    })

    // 更新总数
    const totalChanges = changeStats.value
      .filter(s => s.key !== 'total')
      .reduce((sum, s) => sum + parseInt(s.value), 0)

    const totalStat = changeStats.value.find(s => s.key === 'total')
    if (totalStat) {
      totalStat.value = totalChanges.toString()
    }
  }, 30000) // 每30秒更新一次
})
</script>

<style scoped>
.change-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-info h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-info p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.change-stats,
.change-analytics,
.change-window-analysis,
.change-list {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.info {
  border-left: 4px solid #1976D2;
}

.stat-card.warning {
  border-left: 4px solid #FF9800;
}

.stat-card.success {
  border-left: 4px solid #4CAF50;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-trend.up {
  color: #4CAF50;
}

.stat-trend.down {
  color: #F44336;
}

.stat-trend.stable {
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 图表样式 */
.trend-chart,
.risk-chart {
  height: 300px;
  margin-top: 20px;
}

/* 窗口分析样式 */
.window-content {
  margin-top: 20px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .stat-card {
    height: 120px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}
</style>
