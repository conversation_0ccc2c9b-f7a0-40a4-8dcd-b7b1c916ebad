<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">
          <el-icon :size="120" color="#E0E0E0">
            <QuestionFilled />
          </el-icon>
        </div>
      </div>
      
      <div class="error-info">
        <h1>页面未找到</h1>
        <p>抱歉，您访问的页面不存在或已被移除</p>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>

    <div class="helpful-links">
      <h3>您可能需要：</h3>
      <div class="link-grid">
        <div class="link-item" @click="$router.push('/service-desk')">
          <el-icon :size="24" color="#1976D2">
            <Headset />
          </el-icon>
          <div class="link-text">
            <div class="link-title">服务台</div>
            <div class="link-desc">创建和管理工单</div>
          </div>
        </div>
        
        <div class="link-item" @click="$router.push('/incident')">
          <el-icon :size="24" color="#1976D2">
            <Warning />
          </el-icon>
          <div class="link-text">
            <div class="link-title">事件管理</div>
            <div class="link-desc">处理系统事件</div>
          </div>
        </div>
        
        <div class="link-item" @click="$router.push('/knowledge')">
          <el-icon :size="24" color="#1976D2">
            <Reading />
          </el-icon>
          <div class="link-text">
            <div class="link-title">知识库</div>
            <div class="link-desc">查找解决方案</div>
          </div>
        </div>
        
        <div class="link-item" @click="$router.push('/reports')">
          <el-icon :size="24" color="#1976D2">
            <DataAnalysis />
          </el-icon>
          <div class="link-text">
            <div class="link-title">报表中心</div>
            <div class="link-desc">查看统计报表</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/dashboard')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/dashboard')
  }
}
</script>

<style scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.not-found-content {
  text-align: center;
  margin-bottom: 60px;
}

.error-illustration {
  position: relative;
  margin-bottom: 40px;
}

.error-code {
  font-size: 120px;
  font-weight: bold;
  color: #E0E0E0;
  line-height: 1;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-icon {
  margin-bottom: 20px;
}

.error-info h1 {
  font-size: 32px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 600;
}

.error-info p {
  font-size: 16px;
  color: #666;
  margin-bottom: 40px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.helpful-links {
  max-width: 800px;
  width: 100%;
}

.helpful-links h3 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 20px;
  font-weight: 500;
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
}

.link-item {
  background: white;
  padding: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.link-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.link-text {
  flex: 1;
  text-align: left;
}

.link-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.link-desc {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-container {
    padding: 20px;
  }

  .error-code {
    font-size: 80px;
  }

  .error-info h1 {
    font-size: 24px;
  }

  .error-info p {
    font-size: 14px;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .error-actions .el-button {
    width: 200px;
  }

  .link-grid {
    grid-template-columns: 1fr;
  }

  .link-item {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 60px;
  }

  .error-info h1 {
    font-size: 20px;
  }

  .helpful-links h3 {
    font-size: 18px;
  }

  .link-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .link-text {
    text-align: center;
  }
}

/* 动画效果 */
.not-found-content {
  animation: fadeInUp 0.6s ease-out;
}

.helpful-links {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.link-item {
  animation: fadeInScale 0.6s ease-out;
}

.link-item:nth-child(2) {
  animation-delay: 0.1s;
}

.link-item:nth-child(3) {
  animation-delay: 0.2s;
}

.link-item:nth-child(4) {
  animation-delay: 0.3s;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.error-icon {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
</style>
