<template>
  <div class="system-settings">
    <div class="page-header">
      <h2>系统设置</h2>
      <p>配置系统参数和管理选项</p>
    </div>

    <div class="settings-content">
      <el-row :gutter="20">
        <!-- 左侧设置菜单 -->
        <el-col :xs="24" :lg="6">
          <el-card>
            <el-menu
              :default-active="activeMenu"
              @select="handleMenuSelect"
              class="settings-menu"
            >
              <el-menu-item index="general">
                <el-icon><Setting /></el-icon>
                <span>基本设置</span>
              </el-menu-item>
              <el-menu-item index="users">
                <el-icon><User /></el-icon>
                <span>用户管理</span>
              </el-menu-item>
              <el-menu-item index="roles">
                <el-icon><UserFilled /></el-icon>
                <span>角色权限</span>
              </el-menu-item>
              <el-menu-item index="notifications">
                <el-icon><Bell /></el-icon>
                <span>通知设置</span>
              </el-menu-item>
              <el-menu-item index="backup">
                <el-icon><FolderOpened /></el-icon>
                <span>备份恢复</span>
              </el-menu-item>
              <el-menu-item index="logs">
                <el-icon><Document /></el-icon>
                <span>系统日志</span>
              </el-menu-item>
            </el-menu>
          </el-card>
        </el-col>

        <!-- 右侧设置内容 -->
        <el-col :xs="24" :lg="18">
          <el-card>
            <!-- 基本设置 -->
            <div v-if="activeMenu === 'general'" class="setting-panel">
              <h3>基本设置</h3>
              <el-form :model="generalSettings" label-width="120px">
                <el-form-item label="系统名称">
                  <el-input v-model="generalSettings.systemName" />
                </el-form-item>
                <el-form-item label="系统描述">
                  <el-input v-model="generalSettings.systemDesc" type="textarea" :rows="3" />
                </el-form-item>
                <el-form-item label="时区设置">
                  <el-select v-model="generalSettings.timezone">
                    <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                    <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                    <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                  </el-select>
                </el-form-item>
                <el-form-item label="语言设置">
                  <el-select v-model="generalSettings.language">
                    <el-option label="简体中文" value="zh-CN" />
                    <el-option label="English" value="en-US" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="saveGeneralSettings">保存设置</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 用户管理 -->
            <div v-if="activeMenu === 'users'" class="setting-panel">
              <div class="panel-header">
                <h3>用户管理</h3>
                <el-button type="primary" size="small">
                  <el-icon><Plus /></el-icon>
                  添加用户
                </el-button>
              </div>
              <el-table :data="users" style="width: 100%">
                <el-table-column prop="username" label="用户名" />
                <el-table-column prop="name" label="姓名" />
                <el-table-column prop="email" label="邮箱" />
                <el-table-column prop="role" label="角色" />
                <el-table-column prop="status" label="状态">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === '启用' ? 'success' : 'danger'">
                      {{ scope.row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button type="primary" size="small" text>编辑</el-button>
                    <el-button type="danger" size="small" text>删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 角色权限 -->
            <div v-if="activeMenu === 'roles'" class="setting-panel">
              <div class="panel-header">
                <h3>角色权限</h3>
                <el-button type="primary" size="small">
                  <el-icon><Plus /></el-icon>
                  添加角色
                </el-button>
              </div>
              <div class="roles-grid">
                <div v-for="role in roles" :key="role.id" class="role-card">
                  <div class="role-header">
                    <h4>{{ role.name }}</h4>
                    <el-button type="text" size="small">编辑</el-button>
                  </div>
                  <p class="role-desc">{{ role.description }}</p>
                  <div class="role-permissions">
                    <el-tag v-for="permission in role.permissions" :key="permission" size="small">
                      {{ permission }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 通知设置 -->
            <div v-if="activeMenu === 'notifications'" class="setting-panel">
              <h3>通知设置</h3>
              <el-form :model="notificationSettings" label-width="120px">
                <el-form-item label="邮件通知">
                  <el-switch v-model="notificationSettings.emailEnabled" />
                </el-form-item>
                <el-form-item label="短信通知">
                  <el-switch v-model="notificationSettings.smsEnabled" />
                </el-form-item>
                <el-form-item label="微信通知">
                  <el-switch v-model="notificationSettings.wechatEnabled" />
                </el-form-item>
                <el-form-item label="SMTP服务器">
                  <el-input v-model="notificationSettings.smtpServer" />
                </el-form-item>
                <el-form-item label="SMTP端口">
                  <el-input v-model="notificationSettings.smtpPort" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 其他设置面板 -->
            <div v-if="activeMenu === 'backup'" class="setting-panel">
              <h3>备份恢复</h3>
              <p>备份恢复功能开发中...</p>
            </div>

            <div v-if="activeMenu === 'logs'" class="setting-panel">
              <h3>系统日志</h3>
              <p>系统日志功能开发中...</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 当前激活的菜单
const activeMenu = ref('general')

// 基本设置
const generalSettings = reactive({
  systemName: 'ITSM服务管理平台',
  systemDesc: '智能化IT服务全生命周期管理平台',
  timezone: 'Asia/Shanghai',
  language: 'zh-CN'
})

// 通知设置
const notificationSettings = reactive({
  emailEnabled: true,
  smsEnabled: false,
  wechatEnabled: true,
  smtpServer: 'smtp.company.com',
  smtpPort: '587'
})

// 用户数据
const users = ref([
  {
    id: 1,
    username: 'admin',
    name: '系统管理员',
    email: '<EMAIL>',
    role: '管理员',
    status: '启用'
  },
  {
    id: 2,
    username: 'support',
    name: '技术支持',
    email: '<EMAIL>',
    role: '技术支持',
    status: '启用'
  }
])

// 角色数据
const roles = ref([
  {
    id: 1,
    name: '系统管理员',
    description: '拥有系统所有权限',
    permissions: ['用户管理', '系统设置', '数据备份', '日志查看']
  },
  {
    id: 2,
    name: '技术支持',
    description: '处理工单和事件',
    permissions: ['工单管理', '事件处理', '知识库']
  },
  {
    id: 3,
    name: '普通用户',
    description: '基本的服务请求权限',
    permissions: ['提交工单', '查看状态']
  }
])

// 菜单选择处理
const handleMenuSelect = (key) => {
  activeMenu.value = key
}

// 保存基本设置
const saveGeneralSettings = () => {
  ElMessage.success('基本设置保存成功')
}

// 保存通知设置
const saveNotificationSettings = () => {
  ElMessage.success('通知设置保存成功')
}
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #616161;
  margin: 0;
}

.settings-content {
  margin-bottom: 20px;
}

.settings-menu {
  border: none;
}

.setting-panel {
  padding: 20px;
}

.setting-panel h3 {
  color: #333;
  margin: 0 0 20px 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.role-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.role-header h4 {
  margin: 0;
  color: #333;
}

.role-desc {
  color: #666;
  font-size: 14px;
  margin: 0 0 12px 0;
}

.role-permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .roles-grid {
    grid-template-columns: 1fr;
  }
}
</style>
