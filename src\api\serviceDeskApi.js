import { 
  createApiResponse, 
  createApiError, 
  paginate, 
  generateId, 
  formatDateTime,
  getRandomStatus,
  getRandomPriority,
  getRandomUser,
  getRandomCategory,
  getRandomDate
} from './index.js'

// 模拟工单数据
let mockTickets = []

// 初始化模拟数据
function initMockData() {
  const ticketTitles = [
    '打印机无法打印',
    '电脑蓝屏重启',
    '网络连接异常',
    '邮箱无法收发邮件',
    '软件安装请求',
    '密码重置申请',
    'VPN连接失败',
    '服务器响应缓慢',
    '数据库连接超时',
    '应用程序崩溃',
    '文件服务器无法访问',
    '新员工账号开通',
    '权限申请',
    '设备故障报修',
    '系统升级通知'
  ]

  const ticketTypes = ['incident', 'request', 'inquiry']
  const channels = ['web', 'email', 'mobile', 'wechat', 'phone']

  for (let i = 1; i <= 50; i++) {
    const type = ticketTypes[Math.floor(Math.random() * ticketTypes.length)]
    const title = ticketTitles[Math.floor(Math.random() * ticketTitles.length)]
    const user = getRandomUser()
    const assignee = Math.random() > 0.3 ? getRandomUser() : null
    const category = getRandomCategory()
    
    mockTickets.push({
      id: generateId(type === 'incident' ? 'INC' : type === 'request' ? 'REQ' : 'INQ'),
      title: `${title} - ${i}`,
      description: `这是一个${type === 'incident' ? '事件' : type === 'request' ? '服务请求' : '咨询'}工单的详细描述...`,
      type,
      priority: getRandomPriority(),
      status: getRandomStatus('ticket'),
      reporter: user.name,
      reporterDept: user.department,
      assignee: assignee ? assignee.name : '',
      assigneeDept: assignee ? assignee.department : '',
      category: category.fullPath,
      channel: channels[Math.floor(Math.random() * channels.length)],
      createTime: getRandomDate(30),
      updateTime: getRandomDate(7),
      sla: Math.floor(Math.random() * 40) + 60, // 60-100%
      tags: ['标签1', '标签2'].slice(0, Math.floor(Math.random() * 3))
    })
  }
}

// 初始化数据
initMockData()

/**
 * 获取工单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 工单列表
 */
export function getTicketList(params = {}) {
  const { 
    page = 1, 
    pageSize = 20, 
    ticketId = '', 
    title = '', 
    status = '', 
    priority = '', 
    assignee = '',
    type = '',
    channel = ''
  } = params

  let filteredTickets = mockTickets.filter(ticket => {
    return (!ticketId || ticket.id.includes(ticketId)) &&
           (!title || ticket.title.includes(title)) &&
           (!status || ticket.status === status) &&
           (!priority || ticket.priority === priority) &&
           (!assignee || ticket.assignee.includes(assignee)) &&
           (!type || ticket.type === type) &&
           (!channel || ticket.channel === channel)
  })

  // 按创建时间倒序排列
  filteredTickets.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))

  const paginatedData = paginate(filteredTickets, page, pageSize)
  
  return createApiResponse(paginatedData, '获取工单列表成功')
}

/**
 * 获取工单详情
 * @param {string} ticketId 工单ID
 * @returns {Promise} 工单详情
 */
export function getTicketDetail(ticketId) {
  const ticket = mockTickets.find(t => t.id === ticketId)
  
  if (!ticket) {
    return createApiError('工单不存在', 404)
  }

  // 添加详细信息
  const detailTicket = {
    ...ticket,
    history: [
      {
        id: 1,
        action: '创建工单',
        operator: ticket.reporter,
        time: ticket.createTime,
        description: '用户提交了新的工单'
      },
      {
        id: 2,
        action: '分配工单',
        operator: '系统管理员',
        time: ticket.updateTime,
        description: `工单已分配给 ${ticket.assignee}`
      }
    ],
    attachments: [
      {
        id: 1,
        name: '错误截图.png',
        size: '2.5MB',
        uploadTime: ticket.createTime,
        url: '/api/files/screenshot.png'
      }
    ],
    comments: [
      {
        id: 1,
        author: ticket.assignee,
        content: '正在处理中，预计30分钟内解决',
        time: ticket.updateTime,
        type: 'internal'
      }
    ]
  }

  return createApiResponse(detailTicket, '获取工单详情成功')
}

/**
 * 创建工单
 * @param {Object} ticketData 工单数据
 * @returns {Promise} 创建结果
 */
export function createTicket(ticketData) {
  const newTicket = {
    id: generateId(ticketData.type === 'incident' ? 'INC' : ticketData.type === 'request' ? 'REQ' : 'INQ'),
    ...ticketData,
    status: 'pending',
    createTime: formatDateTime(),
    updateTime: formatDateTime(),
    sla: 100
  }

  mockTickets.unshift(newTicket)
  
  return createApiResponse(newTicket, '工单创建成功')
}

/**
 * 更新工单
 * @param {string} ticketId 工单ID
 * @param {Object} updateData 更新数据
 * @returns {Promise} 更新结果
 */
export function updateTicket(ticketId, updateData) {
  const ticketIndex = mockTickets.findIndex(t => t.id === ticketId)
  
  if (ticketIndex === -1) {
    return createApiError('工单不存在', 404)
  }

  mockTickets[ticketIndex] = {
    ...mockTickets[ticketIndex],
    ...updateData,
    updateTime: formatDateTime()
  }

  return createApiResponse(mockTickets[ticketIndex], '工单更新成功')
}

/**
 * 删除工单
 * @param {string} ticketId 工单ID
 * @returns {Promise} 删除结果
 */
export function deleteTicket(ticketId) {
  const ticketIndex = mockTickets.findIndex(t => t.id === ticketId)
  
  if (ticketIndex === -1) {
    return createApiError('工单不存在', 404)
  }

  mockTickets.splice(ticketIndex, 1)
  
  return createApiResponse(null, '工单删除成功')
}

/**
 * 批量操作工单
 * @param {Array} ticketIds 工单ID数组
 * @param {string} action 操作类型
 * @param {Object} data 操作数据
 * @returns {Promise} 操作结果
 */
export function batchUpdateTickets(ticketIds, action, data = {}) {
  const updatedTickets = []
  
  ticketIds.forEach(ticketId => {
    const ticketIndex = mockTickets.findIndex(t => t.id === ticketId)
    if (ticketIndex !== -1) {
      switch (action) {
        case 'assign':
          mockTickets[ticketIndex].assignee = data.assignee
          mockTickets[ticketIndex].assigneeDept = data.assigneeDept
          break
        case 'close':
          mockTickets[ticketIndex].status = 'closed'
          break
        case 'escalate':
          mockTickets[ticketIndex].priority = '高'
          break
      }
      mockTickets[ticketIndex].updateTime = formatDateTime()
      updatedTickets.push(mockTickets[ticketIndex])
    }
  })

  return createApiResponse(updatedTickets, `批量${action}操作成功`)
}

/**
 * 获取工单统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 统计数据
 */
export function getTicketStats(params = {}) {
  const { timeRange = 'today' } = params
  
  // 根据时间范围过滤数据
  let filteredTickets = mockTickets
  if (timeRange === 'today') {
    const today = new Date().toDateString()
    filteredTickets = mockTickets.filter(ticket => 
      new Date(ticket.createTime).toDateString() === today
    )
  }

  const stats = {
    total: filteredTickets.length,
    pending: filteredTickets.filter(t => t.status === 'pending').length,
    processing: filteredTickets.filter(t => t.status === 'processing').length,
    resolved: filteredTickets.filter(t => t.status === 'resolved').length,
    overdue: filteredTickets.filter(t => t.sla < 50).length,
    byPriority: {
      '低': filteredTickets.filter(t => t.priority === '低').length,
      '中': filteredTickets.filter(t => t.priority === '中').length,
      '高': filteredTickets.filter(t => t.priority === '高').length,
      '紧急': filteredTickets.filter(t => t.priority === '紧急').length
    },
    byChannel: {
      web: filteredTickets.filter(t => t.channel === 'web').length,
      email: filteredTickets.filter(t => t.channel === 'email').length,
      mobile: filteredTickets.filter(t => t.channel === 'mobile').length,
      wechat: filteredTickets.filter(t => t.channel === 'wechat').length,
      phone: filteredTickets.filter(t => t.channel === 'phone').length
    },
    byType: {
      incident: filteredTickets.filter(t => t.type === 'incident').length,
      request: filteredTickets.filter(t => t.type === 'request').length,
      inquiry: filteredTickets.filter(t => t.type === 'inquiry').length
    }
  }

  return createApiResponse(stats, '获取统计数据成功')
}

/**
 * 获取服务状态数据
 * @returns {Promise} 服务状态数据
 */
export function getServiceStatus() {
  const services = [
    { name: 'ERP系统', status: 'healthy', uptime: '99.9%', responseTime: '120ms' },
    { name: '邮件服务', status: 'healthy', uptime: '99.8%', responseTime: '85ms' },
    { name: 'OA系统', status: 'warning', uptime: '98.5%', responseTime: '350ms' },
    { name: '数据库服务', status: 'healthy', uptime: '99.9%', responseTime: '45ms' },
    { name: '文件服务器', status: 'error', uptime: '95.2%', responseTime: '超时' },
    { name: '监控系统', status: 'healthy', uptime: '99.7%', responseTime: '200ms' }
  ]

  const metrics = [
    { key: 'pending', label: '待处理工单', value: '23', change: '+5', trend: 'up', status: 'warning', icon: 'Clock' },
    { key: 'processing', label: '处理中工单', value: '45', change: '+12', trend: 'up', status: 'info', icon: 'Loading' },
    { key: 'overdue', label: '超时工单', value: '3', change: '-2', trend: 'down', status: 'danger', icon: 'Warning' },
    { key: 'avgResponse', label: '平均响应时间', value: '15min', change: '0', trend: 'stable', status: 'success', icon: 'Timer' }
  ]

  return createApiResponse({ services, metrics }, '获取服务状态成功')
}

export default {
  getTicketList,
  getTicketDetail,
  createTicket,
  updateTicket,
  deleteTicket,
  batchUpdateTickets,
  getTicketStats,
  getServiceStatus
}
