<template>
  <div class="knowledge-management">
    <div class="page-header">
      <h2>知识管理</h2>
      <p>构建和维护企业知识库</p>
      <div class="header-actions">
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          创建知识
        </el-button>
      </div>
    </div>

    <!-- 知识统计 -->
    <div class="knowledge-stats">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in knowledgeStats" :key="stat.key">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 知识分类和搜索 -->
    <div class="knowledge-content">
      <el-row :gutter="20">
        <!-- 左侧分类树 -->
        <el-col :xs="24" :lg="6">
          <el-card>
            <template #header>
              <span>知识分类</span>
            </template>
            <el-tree
              :data="categoryTree"
              :props="{ children: 'children', label: 'label' }"
              @node-click="handleCategoryClick"
            />
          </el-card>
        </el-col>

        <!-- 右侧知识列表 -->
        <el-col :xs="24" :lg="18">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>知识列表</span>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索知识..."
                  prefix-icon="Search"
                  style="width: 300px;"
                  @keyup.enter="searchKnowledge"
                />
              </div>
            </template>

            <div class="knowledge-list">
              <div v-for="item in knowledgeList" :key="item.id" class="knowledge-item">
                <div class="knowledge-header">
                  <h3 class="knowledge-title" @click="viewKnowledge(item)">
                    {{ item.title }}
                  </h3>
                  <div class="knowledge-meta">
                    <el-tag size="small">{{ item.category }}</el-tag>
                    <span class="author">{{ item.author }}</span>
                    <span class="date">{{ item.updateTime }}</span>
                  </div>
                </div>
                <div class="knowledge-summary">
                  {{ item.summary }}
                </div>
                <div class="knowledge-actions">
                  <el-button type="text" size="small" @click="viewKnowledge(item)">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button type="text" size="small" @click="editKnowledge(item)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button type="text" size="small">
                    <el-icon><Star /></el-icon>
                    收藏
                  </el-button>
                  <el-button type="text" size="small">
                    <el-icon><Share /></el-icon>
                    分享
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 知识统计数据
const knowledgeStats = ref([
  { key: 'total', label: '知识总数', value: '1,234', icon: 'Reading' },
  { key: 'categories', label: '分类数量', value: '45', icon: 'Collection' },
  { key: 'views', label: '今日浏览', value: '567', icon: 'View' },
  { key: 'favorites', label: '收藏数量', value: '89', icon: 'Star' }
])

// 分类树数据
const categoryTree = ref([
  {
    id: 1,
    label: '技术文档',
    children: [
      { id: 11, label: '系统配置' },
      { id: 12, label: '故障排除' },
      { id: 13, label: '操作指南' }
    ]
  },
  {
    id: 2,
    label: '流程规范',
    children: [
      { id: 21, label: '事件处理' },
      { id: 22, label: '变更管理' },
      { id: 23, label: '服务请求' }
    ]
  },
  {
    id: 3,
    label: 'FAQ',
    children: [
      { id: 31, label: '常见问题' },
      { id: 32, label: '用户指南' }
    ]
  }
])

// 搜索和分页
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 知识列表数据
const knowledgeList = ref([
  {
    id: 1,
    title: '服务器性能监控配置指南',
    category: '技术文档',
    author: '张工',
    updateTime: '2025-01-30',
    summary: '详细介绍如何配置服务器性能监控系统，包括CPU、内存、磁盘等关键指标的监控设置...'
  },
  {
    id: 2,
    title: '网络故障排除流程',
    category: '流程规范',
    author: '李工',
    updateTime: '2025-01-29',
    summary: '标准化的网络故障排除流程，包括问题诊断、解决方案和预防措施...'
  },
  {
    id: 3,
    title: '邮箱配置常见问题',
    category: 'FAQ',
    author: '王工',
    updateTime: '2025-01-28',
    summary: '用户在配置邮箱时经常遇到的问题及解决方案，包括SMTP、POP3、IMAP等配置...'
  }
])

// 处理分类点击
const handleCategoryClick = (data) => {
  ElMessage.info(`选择分类: ${data.label}`)
}

// 搜索知识
const searchKnowledge = () => {
  ElMessage.info(`搜索: ${searchKeyword.value}`)
}

// 查看知识
const viewKnowledge = (item) => {
  ElMessage.info(`查看知识: ${item.title}`)
}

// 编辑知识
const editKnowledge = (item) => {
  ElMessage.info(`编辑知识: ${item.title}`)
}
</script>

<style scoped>
.knowledge-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.knowledge-stats {
  margin-bottom: 20px;
}

.stat-card {
  height: 80px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 12px;
  color: #1976D2;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 12px;
}

.knowledge-content {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.knowledge-list {
  min-height: 400px;
}

.knowledge-item {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.knowledge-item:hover {
  background-color: #fafafa;
}

.knowledge-item:last-child {
  border-bottom: none;
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.knowledge-title {
  color: #1976D2;
  cursor: pointer;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.knowledge-title:hover {
  text-decoration: underline;
}

.knowledge-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.knowledge-summary {
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
}

.knowledge-actions {
  display: flex;
  gap: 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .knowledge-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .knowledge-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>
