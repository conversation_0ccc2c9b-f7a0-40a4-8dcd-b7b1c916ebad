<template>
  <div class="problem-incident-linkage">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>问题与事件联动</span>
          <div class="header-actions">
            <el-button type="primary" @click="showLinkageDialog = true">
              <el-icon><Connection /></el-icon>
              建立关联
            </el-button>
            <el-button @click="refreshLinkages">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 联动概览 -->
      <div class="linkage-overview">
        <el-row :gutter="20">
          <el-col :xs="12" :sm="6" v-for="stat in linkageStats" :key="stat.key">
            <el-card class="stat-card" :class="stat.status">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon :size="24" :color="stat.color">
                    <component :is="stat.icon" />
                  </el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-label">{{ stat.label }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <!-- 智能推荐 -->
      <div class="smart-recommendations" v-if="recommendations.length > 0">
        <el-card>
          <template #header>
            <div class="recommendations-header">
              <span>智能推荐</span>
              <el-tag type="info" size="small">AI驱动</el-tag>
            </div>
          </template>
          
          <div class="recommendations-list">
            <div v-for="rec in recommendations" :key="rec.id" class="recommendation-item">
              <div class="rec-header">
                <div class="rec-title">
                  <el-icon :color="getRecommendationColor(rec.type)">
                    <component :is="getRecommendationIcon(rec.type)" />
                  </el-icon>
                  <span>{{ rec.title }}</span>
                </div>
                <div class="rec-confidence">
                  <el-tag :type="getConfidenceType(rec.confidence)" size="small">
                    置信度 {{ rec.confidence }}%
                  </el-tag>
                </div>
              </div>
              <div class="rec-description">{{ rec.description }}</div>
              <div class="rec-actions">
                <el-button type="primary" size="small" @click="applyRecommendation(rec)">
                  应用推荐
                </el-button>
                <el-button size="small" @click="viewRecommendationDetail(rec)">
                  查看详情
                </el-button>
                <el-button type="info" size="small" text @click="dismissRecommendation(rec)">
                  忽略
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 联动关系图 -->
      <div class="linkage-graph">
        <el-card>
          <template #header>
            <div class="graph-header">
              <span>问题-事件关联图</span>
              <div class="graph-controls">
                <el-button-group size="small">
                  <el-button @click="graphView = 'network'" :type="graphView === 'network' ? 'primary' : ''">
                    网络图
                  </el-button>
                  <el-button @click="graphView = 'timeline'" :type="graphView === 'timeline' ? 'primary' : ''">
                    时间线
                  </el-button>
                  <el-button @click="graphView = 'hierarchy'" :type="graphView === 'hierarchy' ? 'primary' : ''">
                    层次图
                  </el-button>
                </el-button-group>
                <el-button size="small" @click="exportGraph">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </div>
          </template>
          <div class="graph-container" ref="linkageGraphRef"></div>
        </el-card>
      </div>
      
      <!-- 联动列表 -->
      <div class="linkage-list">
        <el-card>
          <template #header>
            <div class="list-header">
              <span>关联关系列表</span>
              <div class="list-controls">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索关联..."
                  prefix-icon="Search"
                  size="small"
                  style="width: 200px; margin-right: 12px;"
                />
                <el-select v-model="filterType" placeholder="关联类型" size="small" style="width: 120px;">
                  <el-option label="全部" value="all" />
                  <el-option label="直接关联" value="direct" />
                  <el-option label="间接关联" value="indirect" />
                  <el-option label="潜在关联" value="potential" />
                </el-select>
              </div>
            </div>
          </template>
          
          <el-table :data="filteredLinkages" style="width: 100%" v-loading="loading">
            <el-table-column prop="problemId" label="问题ID" width="120" />
            <el-table-column prop="problemTitle" label="问题标题" min-width="200" />
            <el-table-column prop="incidentId" label="事件ID" width="120" />
            <el-table-column prop="incidentTitle" label="事件标题" min-width="200" />
            <el-table-column prop="linkageType" label="关联类型" width="120">
              <template #default="scope">
                <el-tag :type="getLinkageTypeColor(scope.row.linkageType)" size="small">
                  {{ getLinkageTypeText(scope.row.linkageType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="strength" label="关联强度" width="120">
              <template #default="scope">
                <el-progress :percentage="scope.row.strength" :show-text="false" />
                <span style="margin-left: 8px;">{{ scope.row.strength }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="建立时间" width="160" />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" text @click="viewLinkageDetail(scope.row)">
                  详情
                </el-button>
                <el-button type="warning" size="small" text @click="editLinkage(scope.row)">
                  编辑
                </el-button>
                <el-button type="danger" size="small" text @click="removeLinkage(scope.row)">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-card>
    
    <!-- 建立关联对话框 -->
    <el-dialog v-model="showLinkageDialog" title="建立问题-事件关联" width="70%">
      <div class="linkage-form">
        <el-form :model="linkageForm" :rules="linkageRules" ref="linkageFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="选择问题" prop="problemId">
                <el-select v-model="linkageForm.problemId" placeholder="请选择问题" style="width: 100%" filterable>
                  <el-option
                    v-for="problem in availableProblems"
                    :key="problem.id"
                    :label="`${problem.id} - ${problem.title}`"
                    :value="problem.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择事件" prop="incidentId">
                <el-select v-model="linkageForm.incidentId" placeholder="请选择事件" style="width: 100%" filterable>
                  <el-option
                    v-for="incident in availableIncidents"
                    :key="incident.id"
                    :label="`${incident.id} - ${incident.title}`"
                    :value="incident.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关联类型" prop="linkageType">
                <el-select v-model="linkageForm.linkageType" placeholder="请选择关联类型">
                  <el-option label="直接关联" value="direct" />
                  <el-option label="间接关联" value="indirect" />
                  <el-option label="潜在关联" value="potential" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联强度" prop="strength">
                <el-slider v-model="linkageForm.strength" :min="1" :max="100" show-input />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="关联说明" prop="description">
            <el-input
              v-model="linkageForm.description"
              type="textarea"
              :rows="3"
              placeholder="请描述问题与事件的关联关系"
            />
          </el-form-item>
          
          <el-form-item label="证据材料">
            <el-upload
              class="upload-demo"
              drag
              :auto-upload="false"
              multiple
              :on-change="handleFileChange"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传日志文件、截图等证据材料
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showLinkageDialog = false">取消</el-button>
        <el-button type="primary" @click="createLinkage" :loading="creating">建立关联</el-button>
      </template>
    </el-dialog>
    
    <!-- 推荐详情对话框 -->
    <el-dialog v-model="showRecommendationDetail" :title="`推荐详情 - ${currentRecommendation?.title}`" width="60%">
      <div v-if="currentRecommendation" class="recommendation-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="推荐类型">{{ currentRecommendation.type }}</el-descriptions-item>
          <el-descriptions-item label="置信度">{{ currentRecommendation.confidence }}%</el-descriptions-item>
          <el-descriptions-item label="相关问题">{{ currentRecommendation.problemId }}</el-descriptions-item>
          <el-descriptions-item label="相关事件">{{ currentRecommendation.incidentId }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="recommendation-analysis">
          <h4>分析依据：</h4>
          <ul>
            <li v-for="reason in currentRecommendation.reasons" :key="reason">{{ reason }}</li>
          </ul>
        </div>
        
        <div class="recommendation-impact">
          <h4>预期影响：</h4>
          <p>{{ currentRecommendation.expectedImpact }}</p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showRecommendationDetail = false">关闭</el-button>
        <el-button type="primary" @click="applyRecommendation(currentRecommendation)">应用推荐</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const showLinkageDialog = ref(false)
const showRecommendationDetail = ref(false)
const searchKeyword = ref('')
const filterType = ref('all')
const graphView = ref('network')
const currentRecommendation = ref(null)

// 表单数据
const linkageForm = reactive({
  problemId: '',
  incidentId: '',
  linkageType: '',
  strength: 80,
  description: '',
  evidenceFiles: []
})

// 表单验证规则
const linkageRules = {
  problemId: [{ required: true, message: '请选择问题', trigger: 'change' }],
  incidentId: [{ required: true, message: '请选择事件', trigger: 'change' }],
  linkageType: [{ required: true, message: '请选择关联类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入关联说明', trigger: 'blur' }]
}

// 图表引用
const linkageGraphRef = ref()
const linkageFormRef = ref()

// 联动统计数据
const linkageStats = ref([
  {
    key: 'total',
    label: '总关联数',
    value: '45',
    icon: 'Connection',
    color: '#1976D2',
    status: 'info'
  },
  {
    key: 'direct',
    label: '直接关联',
    value: '28',
    icon: 'Link',
    color: '#4CAF50',
    status: 'success'
  },
  {
    key: 'indirect',
    label: '间接关联',
    value: '12',
    icon: 'Share',
    color: '#FF9800',
    status: 'warning'
  },
  {
    key: 'potential',
    label: '潜在关联',
    value: '5',
    icon: 'QuestionFilled',
    color: '#9C27B0',
    status: 'info'
  }
])

// 智能推荐数据
const recommendations = ref([
  {
    id: 1,
    type: 'problem_creation',
    title: '建议为"打印机故障"事件聚类创建问题记录',
    description: '检测到15个相似的打印机故障事件，建议创建统一的问题记录进行根因分析',
    confidence: 92,
    problemId: '',
    incidentId: 'INC-2025-001',
    reasons: [
      '事件发生频率高，过去7天内发生15次',
      '事件症状相似度达到92%',
      '影响范围集中在办公区域',
      '尚未建立对应的问题记录'
    ],
    expectedImpact: '通过根因分析可以减少类似事件重复发生，预计可降低60%的相关事件'
  },
  {
    id: 2,
    type: 'incident_linkage',
    title: '建议将VPN连接事件关联到网络问题PRB-2025-002',
    description: 'VPN连接超时事件与现有网络问题存在关联性，建议建立关联关系',
    confidence: 85,
    problemId: 'PRB-2025-002',
    incidentId: 'INC-2025-008',
    reasons: [
      '事件发生时间与问题调查期间重叠',
      '故障症状与问题描述匹配度高',
      '影响的网络设备存在关联性'
    ],
    expectedImpact: '建立关联后可以统一分析网络问题，提高解决效率'
  },
  {
    id: 3,
    type: 'solution_recommendation',
    title: '推荐应用已知错误KE-2025-001的解决方案',
    description: '当前事件与已知错误库中的解决方案匹配，建议直接应用',
    confidence: 78,
    problemId: 'PRB-2025-003',
    incidentId: 'INC-2025-012',
    reasons: [
      '错误症状完全匹配',
      '解决方案已验证有效',
      '适用于当前环境配置'
    ],
    expectedImpact: '可以快速解决问题，避免重复分析，节省2-4小时处理时间'
  }
])

// 关联关系数据
const linkages = ref([
  {
    id: 1,
    problemId: 'PRB-2025-001',
    problemTitle: '服务器频繁重启问题',
    incidentId: 'INC-2025-001',
    incidentTitle: '服务器宕机',
    linkageType: 'direct',
    strength: 95,
    createTime: '2025-01-30 14:30',
    description: '服务器重启问题的直接表现'
  },
  {
    id: 2,
    problemId: 'PRB-2025-001',
    problemTitle: '服务器频繁重启问题',
    incidentId: 'INC-2025-003',
    incidentTitle: '应用服务异常',
    linkageType: 'indirect',
    strength: 75,
    createTime: '2025-01-30 15:20',
    description: '服务器重启导致的间接影响'
  },
  {
    id: 3,
    problemId: 'PRB-2025-002',
    problemTitle: '网络连接不稳定',
    incidentId: 'INC-2025-005',
    incidentTitle: 'VPN连接失败',
    linkageType: 'potential',
    strength: 60,
    createTime: '2025-01-29 16:45',
    description: '可能与网络问题相关'
  }
])

// 可用的问题和事件
const availableProblems = ref([
  { id: 'PRB-2025-001', title: '服务器频繁重启问题' },
  { id: 'PRB-2025-002', title: '网络连接不稳定' },
  { id: 'PRB-2025-003', title: '邮件系统性能缓慢' }
])

const availableIncidents = ref([
  { id: 'INC-2025-001', title: '服务器宕机' },
  { id: 'INC-2025-002', title: '网络中断' },
  { id: 'INC-2025-003', title: '应用服务异常' },
  { id: 'INC-2025-004', title: 'VPN连接失败' },
  { id: 'INC-2025-005', title: '邮件发送缓慢' }
])

// 计算属性
const filteredLinkages = computed(() => {
  let filtered = linkages.value

  // 按类型过滤
  if (filterType.value !== 'all') {
    filtered = filtered.filter(l => l.linkageType === filterType.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(l =>
      l.problemId.toLowerCase().includes(keyword) ||
      l.problemTitle.toLowerCase().includes(keyword) ||
      l.incidentId.toLowerCase().includes(keyword) ||
      l.incidentTitle.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 获取推荐颜色
const getRecommendationColor = (type) => {
  const colorMap = {
    'problem_creation': '#4CAF50',
    'incident_linkage': '#1976D2',
    'solution_recommendation': '#FF9800'
  }
  return colorMap[type] || '#666'
}

// 获取推荐图标
const getRecommendationIcon = (type) => {
  const iconMap = {
    'problem_creation': 'Plus',
    'incident_linkage': 'Connection',
    'solution_recommendation': 'MagicStick'
  }
  return iconMap[type] || 'InfoFilled'
}

// 获取置信度类型
const getConfidenceType = (confidence) => {
  if (confidence >= 90) return 'success'
  if (confidence >= 70) return 'warning'
  return 'info'
}

// 获取关联类型颜色
const getLinkageTypeColor = (type) => {
  const colorMap = {
    'direct': 'success',
    'indirect': 'warning',
    'potential': 'info'
  }
  return colorMap[type] || 'info'
}

// 获取关联类型文本
const getLinkageTypeText = (type) => {
  const textMap = {
    'direct': '直接关联',
    'indirect': '间接关联',
    'potential': '潜在关联'
  }
  return textMap[type] || type
}

// 应用推荐
const applyRecommendation = (recommendation) => {
  ElMessageBox.confirm(
    `确定要应用推荐 "${recommendation.title}" 吗？`,
    '应用推荐',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    // 根据推荐类型执行不同操作
    switch (recommendation.type) {
      case 'problem_creation':
        ElMessage.success('已创建问题记录，正在进行关联')
        break
      case 'incident_linkage':
        ElMessage.success('已建立事件关联关系')
        // 添加到关联列表
        const newLinkage = {
          id: linkages.value.length + 1,
          problemId: recommendation.problemId,
          problemTitle: availableProblems.value.find(p => p.id === recommendation.problemId)?.title || '',
          incidentId: recommendation.incidentId,
          incidentTitle: availableIncidents.value.find(i => i.id === recommendation.incidentId)?.title || '',
          linkageType: 'direct',
          strength: recommendation.confidence,
          createTime: new Date().toLocaleString(),
          description: '基于AI推荐建立的关联'
        }
        linkages.value.unshift(newLinkage)
        break
      case 'solution_recommendation':
        ElMessage.success('已应用解决方案')
        break
    }

    // 移除已应用的推荐
    const index = recommendations.value.findIndex(r => r.id === recommendation.id)
    if (index > -1) {
      recommendations.value.splice(index, 1)
    }
  })
}

// 查看推荐详情
const viewRecommendationDetail = (recommendation) => {
  currentRecommendation.value = recommendation
  showRecommendationDetail.value = true
}

// 忽略推荐
const dismissRecommendation = (recommendation) => {
  const index = recommendations.value.findIndex(r => r.id === recommendation.id)
  if (index > -1) {
    recommendations.value.splice(index, 1)
    ElMessage.info('已忽略推荐')
  }
}

// 刷新联动数据
const refreshLinkages = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

// 查看关联详情
const viewLinkageDetail = (linkage) => {
  ElMessage.info(`查看关联详情: ${linkage.problemId} <-> ${linkage.incidentId}`)
}

// 编辑关联
const editLinkage = (linkage) => {
  // 填充表单数据
  linkageForm.problemId = linkage.problemId
  linkageForm.incidentId = linkage.incidentId
  linkageForm.linkageType = linkage.linkageType
  linkageForm.strength = linkage.strength
  linkageForm.description = linkage.description

  showLinkageDialog.value = true
}

// 移除关联
const removeLinkage = (linkage) => {
  ElMessageBox.confirm(
    `确定要移除关联 "${linkage.problemId} <-> ${linkage.incidentId}" 吗？`,
    '确认移除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = linkages.value.findIndex(l => l.id === linkage.id)
    if (index > -1) {
      linkages.value.splice(index, 1)
      ElMessage.success('关联已移除')
    }
  })
}

// 创建关联
const createLinkage = () => {
  linkageFormRef.value.validate((valid) => {
    if (valid) {
      creating.value = true

      setTimeout(() => {
        const newLinkage = {
          id: linkages.value.length + 1,
          problemId: linkageForm.problemId,
          problemTitle: availableProblems.value.find(p => p.id === linkageForm.problemId)?.title || '',
          incidentId: linkageForm.incidentId,
          incidentTitle: availableIncidents.value.find(i => i.id === linkageForm.incidentId)?.title || '',
          linkageType: linkageForm.linkageType,
          strength: linkageForm.strength,
          createTime: new Date().toLocaleString(),
          description: linkageForm.description
        }

        linkages.value.unshift(newLinkage)

        creating.value = false
        showLinkageDialog.value = false

        // 重置表单
        Object.keys(linkageForm).forEach(key => {
          if (Array.isArray(linkageForm[key])) {
            linkageForm[key] = []
          } else if (typeof linkageForm[key] === 'number') {
            linkageForm[key] = key === 'strength' ? 80 : 0
          } else {
            linkageForm[key] = ''
          }
        })

        ElMessage.success('关联已建立')
      }, 2000)
    }
  })
}

// 处理文件上传
const handleFileChange = (file) => {
  linkageForm.evidenceFiles.push(file)
}

// 导出图表
const exportGraph = () => {
  ElMessage.success('正在导出关联图...')
}

// 初始化关联图
const initLinkageGraph = () => {
  if (!linkageGraphRef.value) return

  const chartInstance = echarts.init(linkageGraphRef.value)

  // 构建图数据
  const problems = [...new Set(linkages.value.map(l => l.problemId))].map(id => ({
    id,
    name: availableProblems.value.find(p => p.id === id)?.title || id,
    category: 'problem',
    symbolSize: 60
  }))

  const incidents = [...new Set(linkages.value.map(l => l.incidentId))].map(id => ({
    id,
    name: availableIncidents.value.find(i => i.id === id)?.title || id,
    category: 'incident',
    symbolSize: 40
  }))

  const nodes = [...problems, ...incidents]

  const links = linkages.value.map(l => ({
    source: l.problemId,
    target: l.incidentId,
    value: l.strength,
    lineStyle: {
      width: l.strength / 20,
      color: getLinkageTypeColor(l.linkageType) === 'success' ? '#4CAF50' :
             getLinkageTypeColor(l.linkageType) === 'warning' ? '#FF9800' : '#1976D2'
    }
  }))

  const categories = [
    { name: 'problem' },
    { name: 'incident' }
  ]

  chartInstance.setOption({
    title: {
      text: '问题-事件关联网络',
      left: 'center'
    },
    tooltip: {
      formatter: (params) => {
        if (params.dataType === 'node') {
          return `${params.data.category === 'problem' ? '问题' : '事件'}: ${params.data.name}`
        } else {
          return `关联强度: ${params.data.value}%`
        }
      }
    },
    legend: {
      data: ['problem', 'incident'],
      bottom: 10
    },
    series: [{
      type: 'graph',
      layout: 'force',
      data: nodes,
      links: links,
      categories: categories,
      roam: true,
      focusNodeAdjacency: true,
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{b}'
      },
      lineStyle: {
        curveness: 0.3
      },
      emphasis: {
        lineStyle: {
          width: 10
        }
      },
      force: {
        repulsion: 1000,
        edgeLength: 200
      }
    }]
  })
}

onMounted(() => {
  nextTick(() => {
    initLinkageGraph()
  })
})
</script>

<style scoped>
.problem-incident-linkage {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.linkage-overview {
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 80px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
}

.stat-icon {
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 12px;
}

.smart-recommendations {
  margin-bottom: 30px;
}

.recommendations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rec-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
}

.rec-description {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.4;
}

.rec-actions {
  display: flex;
  gap: 8px;
}

.linkage-graph,
.linkage-list {
  margin-bottom: 30px;
}

.graph-header,
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.graph-controls,
.list-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.graph-container {
  height: 400px;
  margin-top: 20px;
}

.linkage-form {
  padding: 20px 0;
}

.recommendation-detail {
  padding: 20px 0;
}

.recommendation-analysis,
.recommendation-impact {
  margin-top: 20px;
}

.recommendation-analysis h4,
.recommendation-impact h4 {
  color: #333;
  margin-bottom: 12px;
}

.recommendation-analysis ul {
  margin: 0;
  padding-left: 20px;
}

.recommendation-analysis li {
  margin-bottom: 8px;
  color: #666;
}

@media (max-width: 768px) {
  .graph-header,
  .list-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .graph-controls,
  .list-controls {
    flex-direction: column;
    width: 100%;
  }
  
  .rec-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .rec-actions {
    flex-wrap: wrap;
  }
}
</style>
