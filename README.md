# ITSM服务管理平台

智能化IT服务全生命周期管理平台，基于Vue 3 + Element Plus构建的现代化前端应用。

## 项目特性

- 🎯 **完整的ITSM功能模块** - 涵盖服务台、事件管理、问题管理、变更管理、CMDB等核心功能
- 🎨 **现代化UI设计** - 采用蓝灰科技色调，扁平化设计风格
- 📱 **响应式布局** - 支持桌面、平板、移动端多终端访问
- 🔧 **模块化架构** - 清晰的代码结构，易于维护和扩展
- 📊 **数据可视化** - 集成ECharts图表库，提供丰富的数据展示
- 🚀 **高性能** - 基于Vue 3 Composition API，优化的性能表现

## 技术栈

- **前端框架**: Vue 3.4+
- **UI组件库**: Element Plus 2.4+
- **路由管理**: Vue Router 4.2+
- **图表库**: ECharts 5.4+
- **构建工具**: Vite 5.0+
- **包管理器**: npm

## 快速开始

### 环境要求

- Node.js 18.0+
- npm 9.0+

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:5173 查看应用

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 登录信息

系统提供了快速登录功能，支持以下角色：

- **管理员**: admin / admin123
- **技术支持**: support / support123
- **普通用户**: user / user123

## 功能模块

### 1. 仪表盘
- 关键指标展示
- 实时数据监控
- 图表数据可视化
- 待处理任务列表

### 2. 服务台
- 多渠道工单接入
- 工单生命周期管理
- 智能路由分配
- 服务状态看板

### 3. 事件管理
- 事件分类和优先级管理
- SLA监控和报警
- 升级流程管理
- 根本原因分析

### 4. 问题管理
- 问题识别和记录
- 根本原因分析工具
- 已知错误库管理
- 解决方案知识库

### 5. 变更管理
- 变更请求流程
- CAB审批机制
- 变更日历管理
- 风险评估和控制

### 6. CMDB配置管理
- 配置项管理
- 关系映射
- 自动发现
- 拓扑图展示

### 7. 其他模块
- 发布管理、服务级别管理、知识管理、资产管理、请求履行、持续改进、报表中心、系统设置

## 许可证

MIT License
