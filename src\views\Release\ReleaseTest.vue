<template>
  <div class="release-test">
    <el-card>
      <template #header>
        <h3>发布管理功能测试</h3>
      </template>
      
      <div class="test-section">
        <h4>API 测试</h4>
        <el-space wrap>
          <el-button @click="testGetReleaseList" :loading="testing.list">
            测试获取发布列表
          </el-button>
          <el-button @click="testCreateRelease" :loading="testing.create">
            测试创建发布
          </el-button>
          <el-button @click="testGetStatistics" :loading="testing.stats">
            测试获取统计数据
          </el-button>
          <el-button @click="testHealthCheck" :loading="testing.health">
            测试健康检查
          </el-button>
          <el-button @click="testRollbackPlan" :loading="testing.rollback">
            测试回滚计划
          </el-button>
          <el-button @click="testReports" :loading="testing.reports">
            测试报表数据
          </el-button>
        </el-space>
      </div>
      
      <div class="test-section">
        <h4>组件测试</h4>
        <el-space wrap>
          <el-button @click="testReleaseCalendar">
            测试发布日历
          </el-button>
          <el-button @click="testChangeAssociation">
            测试变更关联
          </el-button>
          <el-button @click="testRollbackManagement">
            测试回滚管理
          </el-button>
          <el-button @click="testReleaseReports">
            测试发布报表
          </el-button>
        </el-space>
      </div>
      
      <div class="test-section">
        <h4>导航测试</h4>
        <el-space wrap>
          <el-button @click="$router.push('/release')">
            发布管理
          </el-button>
          <el-button @click="$router.push('/release/dashboard')">
            发布看板
          </el-button>
          <el-button @click="$router.push('/release/calendar')">
            发布日历
          </el-button>
          <el-button @click="$router.push('/release/reports')">
            发布报表
          </el-button>
        </el-space>
      </div>
      
      <div class="test-results" v-if="testResults.length > 0">
        <h4>测试结果</h4>
        <el-timeline>
          <el-timeline-item
            v-for="result in testResults"
            :key="result.id"
            :timestamp="result.timestamp"
            :type="result.success ? 'success' : 'danger'"
          >
            <div class="result-content">
              <div class="result-title">{{ result.title }}</div>
              <div class="result-message">{{ result.message }}</div>
              <div v-if="result.data" class="result-data">
                <el-collapse>
                  <el-collapse-item title="查看数据">
                    <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  getReleaseList,
  createRelease,
  getReleaseStatistics,
  performHealthCheck,
  getRollbackPlan,
  getReleaseReports
} from '@/api/releaseApi.js'

const router = useRouter()

// 测试状态
const testing = reactive({
  list: false,
  create: false,
  stats: false,
  health: false,
  rollback: false,
  reports: false
})

// 测试结果
const testResults = ref([])

// 添加测试结果
const addTestResult = (title, success, message, data = null) => {
  testResults.value.unshift({
    id: Date.now(),
    title,
    success,
    message,
    data,
    timestamp: new Date().toLocaleString()
  })
}

// API 测试方法
const testGetReleaseList = async () => {
  testing.list = true
  try {
    const response = await getReleaseList({ page: 1, pageSize: 10 })
    addTestResult('获取发布列表', true, `成功获取 ${response.data.list.length} 条记录`, response.data)
    ElMessage.success('发布列表测试通过')
  } catch (error) {
    addTestResult('获取发布列表', false, error.message || '测试失败', error)
    ElMessage.error('发布列表测试失败')
  } finally {
    testing.list = false
  }
}

const testCreateRelease = async () => {
  testing.create = true
  try {
    const testData = {
      name: '测试发布-' + Date.now(),
      description: '这是一个测试发布',
      type: 'patch',
      relatedChange: 'CHG-TEST-001',
      owner: '测试用户',
      plannedStartDate: new Date().toISOString(),
      plannedEndDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    }
    
    const response = await createRelease(testData)
    addTestResult('创建发布', true, '发布创建成功', response.data)
    ElMessage.success('创建发布测试通过')
  } catch (error) {
    addTestResult('创建发布', false, error.message || '测试失败', error)
    ElMessage.error('创建发布测试失败')
  } finally {
    testing.create = false
  }
}

const testGetStatistics = async () => {
  testing.stats = true
  try {
    const response = await getReleaseStatistics()
    addTestResult('获取统计数据', true, '统计数据获取成功', response.data)
    ElMessage.success('统计数据测试通过')
  } catch (error) {
    addTestResult('获取统计数据', false, error.message || '测试失败', error)
    ElMessage.error('统计数据测试失败')
  } finally {
    testing.stats = false
  }
}

const testHealthCheck = async () => {
  testing.health = true
  try {
    // 使用第一个发布进行健康检查测试
    const listResponse = await getReleaseList({ page: 1, pageSize: 1 })
    if (listResponse.data.list.length > 0) {
      const releaseId = listResponse.data.list[0].id
      const response = await performHealthCheck(releaseId)
      addTestResult('健康检查', true, '健康检查执行成功', response.data)
      ElMessage.success('健康检查测试通过')
    } else {
      addTestResult('健康检查', false, '没有可用的发布进行测试')
      ElMessage.warning('没有可用的发布进行健康检查测试')
    }
  } catch (error) {
    addTestResult('健康检查', false, error.message || '测试失败', error)
    ElMessage.error('健康检查测试失败')
  } finally {
    testing.health = false
  }
}

const testRollbackPlan = async () => {
  testing.rollback = true
  try {
    // 使用第一个发布进行回滚计划测试
    const listResponse = await getReleaseList({ page: 1, pageSize: 1 })
    if (listResponse.data.list.length > 0) {
      const releaseId = listResponse.data.list[0].id
      const response = await getRollbackPlan(releaseId)
      addTestResult('回滚计划', true, '回滚计划获取成功', response.data)
      ElMessage.success('回滚计划测试通过')
    } else {
      addTestResult('回滚计划', false, '没有可用的发布进行测试')
      ElMessage.warning('没有可用的发布进行回滚计划测试')
    }
  } catch (error) {
    addTestResult('回滚计划', false, error.message || '测试失败', error)
    ElMessage.error('回滚计划测试失败')
  } finally {
    testing.rollback = false
  }
}

const testReports = async () => {
  testing.reports = true
  try {
    const response = await getReleaseReports('30d')
    addTestResult('报表数据', true, '报表数据获取成功', response.data)
    ElMessage.success('报表数据测试通过')
  } catch (error) {
    addTestResult('报表数据', false, error.message || '测试失败', error)
    ElMessage.error('报表数据测试失败')
  } finally {
    testing.reports = false
  }
}

// 组件测试方法
const testReleaseCalendar = () => {
  router.push('/release/calendar')
  ElMessage.info('跳转到发布日历页面')
}

const testChangeAssociation = () => {
  // 跳转到第一个发布的详情页面
  getReleaseList({ page: 1, pageSize: 1 }).then(response => {
    if (response.data.list.length > 0) {
      router.push(`/release/detail/${response.data.list[0].id}`)
      ElMessage.info('跳转到发布详情页面测试变更关联')
    } else {
      ElMessage.warning('没有可用的发布进行测试')
    }
  })
}

const testRollbackManagement = () => {
  // 跳转到第一个发布的详情页面
  getReleaseList({ page: 1, pageSize: 1 }).then(response => {
    if (response.data.list.length > 0) {
      router.push(`/release/detail/${response.data.list[0].id}`)
      ElMessage.info('跳转到发布详情页面测试回滚管理')
    } else {
      ElMessage.warning('没有可用的发布进行测试')
    }
  })
}

const testReleaseReports = () => {
  router.push('/release/reports')
  ElMessage.info('跳转到发布报表页面')
}
</script>

<style scoped>
.release-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.test-section h4 {
  margin: 0 0 16px 0;
  color: #333;
}

.test-results {
  margin-top: 24px;
}

.test-results h4 {
  margin-bottom: 16px;
  color: #333;
}

.result-content {
  padding: 8px 0;
}

.result-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.result-message {
  color: #666;
  margin-bottom: 8px;
}

.result-data {
  margin-top: 8px;
}

.result-data pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
