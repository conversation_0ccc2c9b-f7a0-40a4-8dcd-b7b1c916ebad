<template>
  <div class="knowledge-audit">
    <div class="page-header">
      <h2>知识审核</h2>
      <p>管理知识文章的审核流程</p>
      <div class="header-actions">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="batchAuditDialog = true" :disabled="selectedItems.length === 0">
          <el-icon><Select /></el-icon>
          批量审核 ({{ selectedItems.length }})
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in auditStats" :key="stat.key">
          <el-card class="stat-card" :class="stat.type" @click="filterByStatus(stat.status)">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" :md="6">
          <el-input
            v-model="filterForm.keyword"
            placeholder="搜索知识标题..."
            prefix-icon="Search"
            clearable
            @keyup.enter="loadAuditList"
            @clear="loadAuditList"
          />
        </el-col>
        <el-col :xs="24" :sm="8" :md="4">
          <el-select
            v-model="filterForm.status"
            placeholder="审核状态"
            clearable
            @change="loadAuditList"
          >
            <el-option label="待审核" value="pending" />
            <el-option label="审核中" value="reviewing" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="需修改" value="revision" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="8" :md="4">
          <el-select
            v-model="filterForm.category"
            placeholder="知识分类"
            clearable
            @change="loadAuditList"
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="8" :md="4">
          <el-select
            v-model="filterForm.priority"
            placeholder="优先级"
            clearable
            @change="loadAuditList"
          >
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="8" :md="6">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadAuditList"
          />
        </el-col>
      </el-row>
    </el-card>

    <!-- 审核列表 -->
    <el-card class="audit-list-card">
      <template #header>
        <div class="card-header">
          <span>审核列表 ({{ pagination.total }} 条)</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''"
                @click="viewMode = 'table'"
              >
                <el-icon><List /></el-icon>
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''"
                @click="viewMode = 'card'"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table
          :data="auditList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="title" label="知识标题" min-width="200" sortable="custom">
            <template #default="{ row }">
              <div class="title-cell">
                <el-link type="primary" @click="viewKnowledge(row)" class="knowledge-title">
                  {{ row.title }}
                </el-link>
                <div class="knowledge-summary">{{ row.summary }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="author" label="作者" width="100" />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="{ row }">
              <el-tag size="small" :type="getCategoryType(row.category)">
                {{ row.category }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag size="small" :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="80">
            <template #default="{ row }">
              <el-tag size="small" :type="getPriorityType(row.priority)">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submitTime" label="提交时间" width="150" sortable="custom">
            <template #default="{ row }">
              {{ formatTime(row.submitTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="reviewer" label="审核人" width="100" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewKnowledge(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="auditKnowledge(row)"
                v-if="canAudit(row)"
              >
                <el-icon><Select /></el-icon>
                审核
              </el-button>
              <el-dropdown @command="(command) => handleItemAction(command, row)">
                <el-button type="text" size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="history">审核历史</el-dropdown-item>
                    <el-dropdown-item command="assign">分配审核人</el-dropdown-item>
                    <el-dropdown-item command="priority">设置优先级</el-dropdown-item>
                    <el-dropdown-item command="withdraw" divided>撤回</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col 
            :xs="24" :sm="12" :md="8" :lg="6"
            v-for="item in auditList" 
            :key="item.id"
          >
            <el-card class="audit-card" @click="viewKnowledge(item)">
              <div class="card-header">
                <el-checkbox 
                  v-model="item.selected"
                  @change="handleCardSelection(item)"
                  @click.stop
                />
                <div class="card-status">
                  <el-tag size="small" :type="getStatusType(item.status)">
                    {{ getStatusText(item.status) }}
                  </el-tag>
                  <el-tag size="small" :type="getPriorityType(item.priority)">
                    {{ getPriorityText(item.priority) }}
                  </el-tag>
                </div>
              </div>
              <div class="card-content">
                <h3 class="card-title">{{ item.title }}</h3>
                <p class="card-summary">{{ item.summary }}</p>
                <div class="card-meta">
                  <div class="meta-item">
                    <el-icon><User /></el-icon>
                    <span>{{ item.author }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Calendar /></el-icon>
                    <span>{{ formatTime(item.submitTime) }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Collection /></el-icon>
                    <span>{{ item.category }}</span>
                  </div>
                </div>
                <div class="card-actions">
                  <el-button size="small" @click.stop="viewKnowledge(item)">
                    查看
                  </el-button>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click.stop="auditKnowledge(item)"
                    v-if="canAudit(item)"
                  >
                    审核
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialog"
      :title="`审核知识 - ${currentAuditItem?.title}`"
      width="800px"
      :before-close="handleAuditClose"
    >
      <div class="audit-content">
        <!-- 知识预览 -->
        <div class="knowledge-preview">
          <h3>{{ currentAuditItem?.title }}</h3>
          <div class="preview-meta">
            <el-tag>{{ currentAuditItem?.category }}</el-tag>
            <span>作者：{{ currentAuditItem?.author }}</span>
            <span>提交时间：{{ formatTime(currentAuditItem?.submitTime) }}</span>
          </div>
          <div class="preview-summary">{{ currentAuditItem?.summary }}</div>
          <div class="preview-actions">
            <el-button size="small" @click="viewFullContent">查看完整内容</el-button>
            <el-button size="small" @click="viewHistory">查看历史版本</el-button>
          </div>
        </div>

        <!-- 审核表单 -->
        <el-form ref="auditFormRef" :model="auditForm" :rules="auditRules" label-width="100px">
          <el-form-item label="审核结果" prop="result">
            <el-radio-group v-model="auditForm.result">
              <el-radio label="approved">通过</el-radio>
              <el-radio label="rejected">拒绝</el-radio>
              <el-radio label="revision">需要修改</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="审核意见" prop="comment">
            <el-input
              v-model="auditForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="优先级" v-if="auditForm.result === 'approved'">
            <el-select v-model="auditForm.priority" placeholder="设置优先级">
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>

          <el-form-item label="发布时间" v-if="auditForm.result === 'approved'">
            <el-radio-group v-model="auditForm.publishType">
              <el-radio label="immediate">立即发布</el-radio>
              <el-radio label="scheduled">定时发布</el-radio>
            </el-radio-group>
            <el-date-picker
              v-if="auditForm.publishType === 'scheduled'"
              v-model="auditForm.publishTime"
              type="datetime"
              placeholder="选择发布时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="margin-left: 12px;"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="auditDialog = false">取消</el-button>
        <el-button type="primary" @click="submitAudit" :loading="auditLoading">
          提交审核
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量审核对话框 -->
    <el-dialog
      v-model="batchAuditDialog"
      title="批量审核"
      width="600px"
    >
      <div class="batch-audit-content">
        <p>已选择 {{ selectedItems.length }} 篇知识进行批量审核</p>
        <el-form :model="batchAuditForm" label-width="100px">
          <el-form-item label="审核结果">
            <el-radio-group v-model="batchAuditForm.result">
              <el-radio label="approved">批量通过</el-radio>
              <el-radio label="rejected">批量拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见">
            <el-input
              v-model="batchAuditForm.comment"
              type="textarea"
              :rows="3"
              placeholder="请输入批量审核意见..."
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="batchAuditDialog = false">取消</el-button>
        <el-button type="primary" @click="submitBatchAudit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Select, Search, List, Grid, View, ArrowDown,
  User, Calendar, Collection
} from '@element-plus/icons-vue'
import { 
  getKnowledgeList,
  reviewKnowledge,
  batchOperateKnowledge
} from '@/api/knowledgeApi'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const auditDialog = ref(false)
const batchAuditDialog = ref(false)
const auditLoading = ref(false)
const viewMode = ref('table')
const selectedItems = ref([])
const currentAuditItem = ref(null)

// 审核统计
const auditStats = ref([
  {
    key: 'pending',
    label: '待审核',
    value: '23',
    icon: 'Clock',
    type: 'warning',
    status: 'pending'
  },
  {
    key: 'reviewing',
    label: '审核中',
    value: '8',
    icon: 'Loading',
    type: 'primary',
    status: 'reviewing'
  },
  {
    key: 'approved',
    label: '已通过',
    value: '156',
    icon: 'CircleCheck',
    type: 'success',
    status: 'approved'
  },
  {
    key: 'rejected',
    label: '已拒绝',
    value: '12',
    icon: 'CircleClose',
    type: 'danger',
    status: 'rejected'
  }
])

// 筛选表单
const filterForm = reactive({
  keyword: '',
  status: '',
  category: '',
  priority: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 审核列表
const auditList = ref([
  {
    id: 1,
    title: '服务器性能监控配置完整指南',
    summary: '详细介绍如何配置服务器性能监控系统，包括CPU、内存、磁盘等关键指标的监控设置。',
    author: '张工',
    category: '技术文档',
    status: 'pending',
    priority: 'high',
    submitTime: '2025-01-30 14:30',
    reviewer: '',
    selected: false
  },
  {
    id: 2,
    title: '网络故障快速排除流程',
    summary: '标准化的网络故障排除流程，包括问题诊断步骤、常用工具使用方法。',
    author: '李工',
    category: '流程规范',
    status: 'reviewing',
    priority: 'medium',
    submitTime: '2025-01-29 16:45',
    reviewer: '王审核员',
    selected: false
  }
])

// 分类数据
const categories = ref([
  { id: 1, name: '技术文档' },
  { id: 2, name: '流程规范' },
  { id: 3, name: 'FAQ' },
  { id: 4, name: '解决方案' },
  { id: 5, name: '操作指南' }
])

// 审核表单
const auditFormRef = ref()
const auditForm = reactive({
  result: '',
  comment: '',
  priority: 'medium',
  publishType: 'immediate',
  publishTime: ''
})

const auditRules = {
  result: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' },
    { min: 10, message: '审核意见至少10个字符', trigger: 'blur' }
  ]
}

// 批量审核表单
const batchAuditForm = reactive({
  result: '',
  comment: ''
})

// 生命周期
onMounted(() => {
  loadAuditList()
})

// 加载审核列表
const loadAuditList = async () => {
  loading.value = true
  try {
    // const response = await getKnowledgeList({
    //   ...filterForm,
    //   page: pagination.currentPage,
    //   pageSize: pagination.pageSize,
    //   auditMode: true
    // })
    // auditList.value = response.data.list
    // pagination.total = response.data.total

    // 模拟数据
    pagination.total = 45
    ElMessage.success('审核列表加载完成')
  } catch (error) {
    ElMessage.error('加载审核列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const refreshData = () => {
  loadAuditList()
}

const filterByStatus = (status) => {
  filterForm.status = status
  loadAuditList()
}

const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

const handleCardSelection = (item) => {
  if (item.selected) {
    selectedItems.value.push(item)
  } else {
    const index = selectedItems.value.findIndex(i => i.id === item.id)
    if (index > -1) {
      selectedItems.value.splice(index, 1)
    }
  }
}

const handleSortChange = ({ prop, order }) => {
  // 处理排序
  loadAuditList()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadAuditList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadAuditList()
}

// 审核相关
const viewKnowledge = (item) => {
  router.push(`/knowledge/articles/${item.id}`)
}

const auditKnowledge = (item) => {
  currentAuditItem.value = item
  auditForm.result = ''
  auditForm.comment = ''
  auditForm.priority = 'medium'
  auditForm.publishType = 'immediate'
  auditForm.publishTime = ''
  auditDialog.value = true
}

const canAudit = (item) => {
  return ['pending', 'reviewing'].includes(item.status)
}

const handleAuditClose = () => {
  auditDialog.value = false
  currentAuditItem.value = null
}

const submitAudit = async () => {
  try {
    await auditFormRef.value.validate()
    auditLoading.value = true

    // await reviewKnowledge(currentAuditItem.value.id, auditForm)

    ElMessage.success('审核提交成功')
    auditDialog.value = false
    loadAuditList()
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('审核提交失败')
    }
  } finally {
    auditLoading.value = false
  }
}

const submitBatchAudit = async () => {
  if (!batchAuditForm.result) {
    ElMessage.warning('请选择审核结果')
    return
  }

  try {
    const ids = selectedItems.value.map(item => item.id)
    // await batchOperateKnowledge({
    //   action: 'audit',
    //   ids,
    //   auditData: batchAuditForm
    // })

    ElMessage.success('批量审核成功')
    batchAuditDialog.value = false
    selectedItems.value = []
    loadAuditList()
  } catch (error) {
    ElMessage.error('批量审核失败')
  }
}

// 其他操作
const handleItemAction = (command, item) => {
  switch (command) {
    case 'history':
      ElMessage.info('查看审核历史功能开发中...')
      break
    case 'assign':
      ElMessage.info('分配审核人功能开发中...')
      break
    case 'priority':
      ElMessage.info('设置优先级功能开发中...')
      break
    case 'withdraw':
      handleWithdraw(item)
      break
  }
}

const handleWithdraw = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤回知识"${item.title}"的审核吗？`,
      '确认撤回',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('撤回成功')
    loadAuditList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤回失败')
    }
  }
}

const viewFullContent = () => {
  router.push(`/knowledge/articles/${currentAuditItem.value.id}`)
}

const viewHistory = () => {
  ElMessage.info('查看历史版本功能开发中...')
}

// 工具函数
const getCategoryType = (category) => {
  const types = {
    '技术文档': 'primary',
    '流程规范': 'success',
    'FAQ': 'warning',
    '解决方案': 'danger',
    '操作指南': 'info'
  }
  return types[category] || ''
}

const getStatusType = (status) => {
  const types = {
    'pending': 'warning',
    'reviewing': 'primary',
    'approved': 'success',
    'rejected': 'danger',
    'revision': 'info'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    'pending': '待审核',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已拒绝',
    'revision': '需修改'
  }
  return texts[status] || status
}

const getPriorityType = (priority) => {
  const types = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return texts[priority] || priority
}

const formatTime = (time) => {
  return time
}
</script>

<style scoped>
.knowledge-audit {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.warning {
  border-left: 4px solid #E6A23C;
}

.stat-card.primary {
  border-left: 4px solid #409EFF;
}

.stat-card.success {
  border-left: 4px solid #67C23A;
}

.stat-card.danger {
  border-left: 4px solid #F56C6C;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;
}

.stat-icon {
  margin-right: 16px;
  color: #409EFF;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 审核列表样式 */
.audit-list-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

/* 表格样式 */
.title-cell {
  max-width: 300px;
}

.knowledge-title {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
  display: block;
}

.knowledge-summary {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 卡片视图样式 */
.card-view {
  margin-bottom: 20px;
}

.audit-card {
  height: 240px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.audit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.audit-card .card-header {
  position: absolute;
  top: 12px;
  right: 12px;
  left: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1;
}

.card-status {
  display: flex;
  gap: 4px;
}

.audit-card .card-content {
  padding: 40px 16px 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-summary {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin: 0 0 12px 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.card-actions {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 审核对话框样式 */
.audit-content {
  max-height: 600px;
  overflow-y: auto;
}

.knowledge-preview {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 20px;
}

.knowledge-preview h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.preview-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.preview-summary {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 12px;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

/* 批量审核对话框样式 */
.batch-audit-content {
  padding: 20px 0;
}

.batch-audit-content p {
  margin-bottom: 20px;
  color: #606266;
  text-align: center;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-audit {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-section .el-row {
    flex-direction: column;
  }

  .filter-section .el-col {
    margin-bottom: 12px;
  }

  .audit-card {
    height: 200px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-summary {
    -webkit-line-clamp: 2;
  }
}
</style>
