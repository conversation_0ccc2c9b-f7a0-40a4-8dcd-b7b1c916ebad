# CMDB项目故障排除指南

## 常见问题及解决方案

### 1. 依赖包缺失错误

**错误信息：**
```
[plugin:vite:import-analysis] Failed to resolve import "pinia" from "src/main.js"
```

**解决方案：**

#### 方法一：使用安装脚本（推荐）
1. **Windows用户**：双击运行 `install-dependencies.bat`
2. **Linux/Mac用户**：在终端运行 `chmod +x install-dependencies.sh && ./install-dependencies.sh`

#### 方法二：手动安装
```bash
# 1. 清理缓存
npm cache clean --force

# 2. 删除现有依赖
rm -rf node_modules
rm -f package-lock.json

# 3. 安装依赖
npm install

# 4. 如果上述命令失败，尝试逐个安装关键依赖
npm install pinia @element-plus/icons-vue axios mockjs
```

### 2. 端口占用问题

**错误信息：**
```
Port 5173 is already in use
```

**解决方案：**
```bash
# 方法一：使用不同端口
npm run dev -- --port 3000

# 方法二：杀死占用端口的进程（Windows）
netstat -ano | findstr :5173
taskkill /PID <PID号> /F

# 方法二：杀死占用端口的进程（Linux/Mac）
lsof -ti:5173 | xargs kill -9
```

### 3. 模块解析错误

**错误信息：**
```
Cannot resolve module './components/xxx'
```

**解决方案：**
1. 检查文件路径是否正确
2. 确保文件扩展名正确（.vue, .js）
3. 检查大小写是否匹配

### 4. CSS样式不生效

**可能原因：**
- Element Plus CSS未正确导入
- 自定义CSS文件路径错误

**解决方案：**
```javascript
// 确保在main.js中正确导入
import 'element-plus/dist/index.css'
import './assets/main.css'
```

### 5. 图标不显示

**可能原因：**
- @element-plus/icons-vue 未安装
- 图标组件未正确注册

**解决方案：**
```bash
npm install @element-plus/icons-vue
```

### 6. 路由跳转失败

**可能原因：**
- 路由配置错误
- 组件导入路径错误

**解决方案：**
1. 检查 `src/router/index.js` 中的路由配置
2. 确保组件文件存在且路径正确

## 开发环境要求

### 必需软件版本
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0

### 检查版本命令
```bash
node --version
npm --version
```

## 项目启动步骤

### 1. 克隆/下载项目后首次运行
```bash
# 1. 进入项目目录
cd your-project-directory

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev
```

### 2. 日常开发
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 浏览器兼容性

### 支持的浏览器
- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

### 不支持的浏览器
- Internet Explorer（所有版本）

## 性能优化建议

### 1. 开发环境优化
```javascript
// vite.config.js
export default {
  server: {
    hmr: {
      overlay: false // 关闭错误覆盖层
    }
  }
}
```

### 2. 内存使用优化
```bash
# 增加Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
npm run dev
```

## 调试技巧

### 1. 启用详细日志
```bash
# 启用Vite详细日志
npm run dev -- --debug
```

### 2. 检查网络请求
- 打开浏览器开发者工具
- 切换到Network标签
- 查看失败的请求

### 3. 检查控制台错误
- 打开浏览器开发者工具
- 切换到Console标签
- 查看错误信息和警告

## 常用命令

```bash
# 安装新依赖
npm install package-name

# 安装开发依赖
npm install package-name --save-dev

# 更新依赖
npm update

# 检查过时的依赖
npm outdated

# 审计安全漏洞
npm audit

# 修复安全漏洞
npm audit fix
```

## 联系支持

如果以上解决方案都无法解决您的问题，请：

1. 检查控制台的完整错误信息
2. 确认Node.js和npm版本
3. 提供详细的错误截图
4. 说明操作系统版本

## 快速修复脚本

创建一个 `quick-fix.bat`（Windows）或 `quick-fix.sh`（Linux/Mac）：

```bash
#!/bin/bash
echo "执行快速修复..."

# 清理并重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 检查关键依赖
echo "检查依赖安装情况..."
npm list --depth=0

echo "修复完成！尝试运行: npm run dev"
```

---

**注意：** 如果问题仍然存在，请确保您的网络连接正常，并且npm源可以正常访问。如果在中国大陆，建议使用淘宝npm镜像：

```bash
npm config set registry https://registry.npmmirror.com/
```
