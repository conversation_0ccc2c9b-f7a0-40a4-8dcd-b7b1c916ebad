<template>
  <div class="license-management">
    <h1>许可证管理页面</h1>
    <p>这是一个测试页面，用于验证路由是否正常工作。</p>
    
    <el-card>
      <template #header>
        <div class="card-header">
          <span>许可证列表</span>
          <el-button type="primary" @click="testClick">测试按钮</el-button>
        </div>
      </template>
      
      <el-table :data="testData" stripe>
        <el-table-column prop="name" label="软件名称" />
        <el-table-column prop="version" label="版本" />
        <el-table-column prop="status" label="状态" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDetail(row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click="editItem(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

const testData = ref([
  { id: 1, name: 'Microsoft Office', version: '365', status: '有效' },
  { id: 2, name: 'Adobe Creative Suite', version: '2024', status: '即将到期' },
  { id: 3, name: 'AutoCAD', version: '2024', status: '有效' }
])

const testClick = () => {
  ElMessage.success('测试按钮点击成功！')
}

const viewDetail = (row) => {
  ElMessage.info(`查看 ${row.name} 的详情`)
  router.push(`/asset/license/detail/${row.id}`)
}

const editItem = (row) => {
  ElMessage.info(`编辑 ${row.name}`)
  router.push(`/asset/license/edit/${row.id}`)
}
</script>

<style scoped>
.license-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
