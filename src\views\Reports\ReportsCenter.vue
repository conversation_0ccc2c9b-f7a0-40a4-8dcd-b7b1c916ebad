<template>
  <div class="reports-center">
    <div class="page-header">
      <h2>报表中心</h2>
      <p>数据分析与报表生成</p>
    </div>

    <!-- 报表分类 -->
    <div class="report-categories">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :lg="6" v-for="category in reportCategories" :key="category.key">
          <el-card class="category-card" @click="selectCategory(category)">
            <div class="category-content">
              <el-icon :size="32" :color="category.color">
                <component :is="category.icon" />
              </el-icon>
              <h3>{{ category.name }}</h3>
              <p>{{ category.description }}</p>
              <div class="report-count">{{ category.count }} 个报表</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 常用报表 -->
    <div class="popular-reports">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>常用报表</span>
            <el-button type="primary" size="small">
              <el-icon><Plus /></el-icon>
              自定义报表
            </el-button>
          </div>
        </template>

        <div class="reports-grid">
          <div v-for="report in popularReports" :key="report.id" class="report-item">
            <div class="report-header">
              <el-icon :size="20" :color="report.color">
                <component :is="report.icon" />
              </el-icon>
              <h4>{{ report.name }}</h4>
            </div>
            <p class="report-desc">{{ report.description }}</p>
            <div class="report-actions">
              <el-button type="primary" size="small" text @click="generateReport(report)">
                生成报表
              </el-button>
              <el-button type="primary" size="small" text @click="scheduleReport(report)">
                定时生成
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 报表分类
const reportCategories = ref([
  {
    key: 'service',
    name: '服务报表',
    description: '服务台、事件、问题相关报表',
    icon: 'Headset',
    color: '#1976D2',
    count: 12
  },
  {
    key: 'performance',
    name: '性能报表',
    description: 'SLA、KPI、性能指标报表',
    icon: 'TrendCharts',
    color: '#4CAF50',
    count: 8
  },
  {
    key: 'asset',
    name: '资产报表',
    description: 'CMDB、资产管理相关报表',
    icon: 'Box',
    color: '#FF9800',
    count: 6
  },
  {
    key: 'financial',
    name: '财务报表',
    description: '成本分析、预算相关报表',
    icon: 'Money',
    color: '#9C27B0',
    count: 4
  }
])

// 常用报表
const popularReports = ref([
  {
    id: 1,
    name: '工单统计报表',
    description: '按时间、类型、状态统计工单数量',
    icon: 'Document',
    color: '#1976D2'
  },
  {
    id: 2,
    name: 'SLA达成率报表',
    description: '各服务级别协议的达成情况',
    icon: 'SuccessFilled',
    color: '#4CAF50'
  },
  {
    id: 3,
    name: '事件趋势分析',
    description: '事件发生趋势和分类分析',
    icon: 'TrendCharts',
    color: '#FF9800'
  },
  {
    id: 4,
    name: '用户满意度报表',
    description: '用户满意度调查结果统计',
    icon: 'Star',
    color: '#9C27B0'
  },
  {
    id: 5,
    name: '资产清单报表',
    description: 'IT资产的详细清单和状态',
    icon: 'Box',
    color: '#607D8B'
  },
  {
    id: 6,
    name: '成本分析报表',
    description: 'IT服务成本分析和预算对比',
    icon: 'Money',
    color: '#795548'
  }
])

// 选择分类
const selectCategory = (category) => {
  ElMessage.info(`选择分类: ${category.name}`)
}

// 生成报表
const generateReport = (report) => {
  ElMessage.success(`正在生成报表: ${report.name}`)
}

// 定时生成报表
const scheduleReport = (report) => {
  ElMessage.info(`设置定时生成: ${report.name}`)
}
</script>

<style scoped>
.reports-center {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #616161;
  margin: 0;
}

.report-categories {
  margin-bottom: 30px;
}

.category-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 160px;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-content {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.category-content h3 {
  margin: 12px 0 8px 0;
  color: #333;
}

.category-content p {
  color: #666;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.report-count {
  color: #1976D2;
  font-size: 12px;
  font-weight: 500;
}

.popular-reports {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.report-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s;
}

.report-item:hover {
  border-color: #1976D2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
}

.report-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.report-header h4 {
  margin: 0;
  color: #333;
}

.report-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.report-actions {
  display: flex;
  gap: 12px;
}

@media (max-width: 768px) {
  .reports-grid {
    grid-template-columns: 1fr;
  }

  .report-actions {
    flex-direction: column;
    gap: 8px;
  }

  .report-actions .el-button {
    width: 100%;
  }
}
</style>
