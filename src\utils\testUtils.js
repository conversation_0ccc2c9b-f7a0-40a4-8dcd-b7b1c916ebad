/**
 * CMDB 测试工具集
 * 用于功能验证和系统测试
 */

import { ElMessage, ElNotification } from 'element-plus'
import cmdbService from '@/services/cmdbService'
import eventBus, { CMDB_EVENTS } from '@/utils/eventBus'

/**
 * 测试结果类
 */
class TestResult {
  constructor(name) {
    this.name = name
    this.passed = 0
    this.failed = 0
    this.errors = []
    this.startTime = Date.now()
    this.endTime = null
  }

  pass(message) {
    this.passed++
    console.log(`✅ ${this.name}: ${message}`)
  }

  fail(message, error = null) {
    this.failed++
    this.errors.push({ message, error })
    console.error(`❌ ${this.name}: ${message}`, error)
  }

  finish() {
    this.endTime = Date.now()
    const duration = this.endTime - this.startTime
    const total = this.passed + this.failed
    const successRate = total > 0 ? ((this.passed / total) * 100).toFixed(1) : 0

    console.log(`\n📊 ${this.name} 测试结果:`)
    console.log(`   总计: ${total} 项`)
    console.log(`   通过: ${this.passed} 项`)
    console.log(`   失败: ${this.failed} 项`)
    console.log(`   成功率: ${successRate}%`)
    console.log(`   耗时: ${duration}ms`)

    if (this.failed > 0) {
      console.log(`\n❌ 失败详情:`)
      this.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.message}`)
        if (error.error) {
          console.log(`      ${error.error.message || error.error}`)
        }
      })
    }

    return {
      name: this.name,
      total,
      passed: this.passed,
      failed: this.failed,
      successRate: parseFloat(successRate),
      duration,
      errors: this.errors
    }
  }
}

/**
 * 测试套件类
 */
class TestSuite {
  constructor(name) {
    this.name = name
    this.tests = []
    this.results = []
  }

  addTest(testFn, name) {
    this.tests.push({ fn: testFn, name })
  }

  async run() {
    console.log(`\n🚀 开始运行测试套件: ${this.name}`)
    
    for (const test of this.tests) {
      const result = new TestResult(test.name)
      try {
        await test.fn(result)
      } catch (error) {
        result.fail('测试执行异常', error)
      }
      this.results.push(result.finish())
    }

    this.printSummary()
    return this.results
  }

  printSummary() {
    const totalTests = this.results.reduce((sum, r) => sum + r.total, 0)
    const totalPassed = this.results.reduce((sum, r) => sum + r.passed, 0)
    const totalFailed = this.results.reduce((sum, r) => sum + r.failed, 0)
    const overallSuccessRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0

    console.log(`\n📈 ${this.name} 总体结果:`)
    console.log(`   测试套件: ${this.results.length} 个`)
    console.log(`   测试项目: ${totalTests} 项`)
    console.log(`   通过: ${totalPassed} 项`)
    console.log(`   失败: ${totalFailed} 项`)
    console.log(`   总体成功率: ${overallSuccessRate}%`)

    // 显示通知
    if (totalFailed === 0) {
      ElNotification.success({
        title: '测试完成',
        message: `所有 ${totalTests} 项测试均通过！`,
        duration: 5000
      })
    } else {
      ElNotification.warning({
        title: '测试完成',
        message: `${totalTests} 项测试中有 ${totalFailed} 项失败`,
        duration: 5000
      })
    }
  }
}

/**
 * CMDB 功能测试
 */
export class CMDBTester {
  constructor() {
    this.suite = new TestSuite('CMDB 功能测试')
    this.setupTests()
  }

  setupTests() {
    // 配置项管理测试
    this.suite.addTest(this.testCIManagement.bind(this), '配置项管理')
    
    // 拓扑图测试
    this.suite.addTest(this.testTopology.bind(this), '拓扑图功能')
    
    // 自动发现测试
    this.suite.addTest(this.testAutoDiscovery.bind(this), '自动发现')
    
    // 关系映射测试
    this.suite.addTest(this.testRelationMapping.bind(this), '关系映射')
    
    // 版本控制测试
    this.suite.addTest(this.testVersionControl.bind(this), '版本控制')
    
    // 数据质量测试
    this.suite.addTest(this.testDataQuality.bind(this), '数据质量')
    
    // 事件总线测试
    this.suite.addTest(this.testEventBus.bind(this), '事件总线')
    
    // 数据服务测试
    this.suite.addTest(this.testDataService.bind(this), '数据服务')
  }

  async run() {
    return await this.suite.run()
  }

  // 配置项管理测试
  async testCIManagement(result) {
    try {
      // 测试获取配置项列表
      const ciList = await cmdbService.getCIList({ page: 1, pageSize: 10 })
      if (ciList && ciList.items && Array.isArray(ciList.items)) {
        result.pass('获取配置项列表成功')
      } else {
        result.fail('配置项列表格式不正确')
      }

      // 测试数据处理
      if (ciList.items.length > 0) {
        const firstCI = ciList.items[0]
        if (firstCI.statusColor && firstCI.typeIcon) {
          result.pass('配置项数据处理正确')
        } else {
          result.fail('配置项数据处理缺少必要字段')
        }
      }

      // 测试缓存功能
      const cachedList = await cmdbService.getCIList({ page: 1, pageSize: 10 }, true)
      if (cachedList) {
        result.pass('配置项缓存功能正常')
      } else {
        result.fail('配置项缓存功能异常')
      }

    } catch (error) {
      result.fail('配置项管理测试异常', error)
    }
  }

  // 拓扑图测试
  async testTopology(result) {
    try {
      // 测试获取拓扑图数据
      const topologyData = await cmdbService.getTopologyData()
      if (topologyData && topologyData.nodes && topologyData.connections) {
        result.pass('获取拓扑图数据成功')
      } else {
        result.fail('拓扑图数据格式不正确')
      }

      // 测试节点数据处理
      if (topologyData.nodes.length > 0) {
        const firstNode = topologyData.nodes[0]
        if (firstNode.icon && firstNode.color && firstNode.size) {
          result.pass('拓扑图节点数据处理正确')
        } else {
          result.fail('拓扑图节点数据处理缺少必要字段')
        }
      }

      // 测试连接数据处理
      if (topologyData.connections.length > 0) {
        const firstConnection = topologyData.connections[0]
        if (firstConnection.color && firstConnection.style) {
          result.pass('拓扑图连接数据处理正确')
        } else {
          result.fail('拓扑图连接数据处理缺少必要字段')
        }
      }

    } catch (error) {
      result.fail('拓扑图测试异常', error)
    }
  }

  // 自动发现测试
  async testAutoDiscovery(result) {
    try {
      // 模拟发现任务创建
      const mockTask = {
        name: '测试发现任务',
        type: 'network',
        range: '***********/24'
      }

      // 测试任务数据验证
      if (mockTask.name && mockTask.type && mockTask.range) {
        result.pass('发现任务数据验证通过')
      } else {
        result.fail('发现任务数据验证失败')
      }

      // 测试IP地址验证
      const validIP = cmdbService.isValidIP('***********')
      const invalidIP = cmdbService.isValidIP('invalid.ip')
      
      if (validIP && !invalidIP) {
        result.pass('IP地址验证功能正常')
      } else {
        result.fail('IP地址验证功能异常')
      }

    } catch (error) {
      result.fail('自动发现测试异常', error)
    }
  }

  // 关系映射测试
  async testRelationMapping(result) {
    try {
      // 测试关系数据结构
      const mockRelation = {
        sourceId: 'CI-001',
        targetId: 'CI-002',
        type: 'depends',
        confidence: 95
      }

      if (mockRelation.sourceId && mockRelation.targetId && mockRelation.type) {
        result.pass('关系数据结构验证通过')
      } else {
        result.fail('关系数据结构验证失败')
      }

      // 测试关系类型颜色映射
      const connectionColor = cmdbService.getConnectionColor('depends')
      if (connectionColor && connectionColor.startsWith('#')) {
        result.pass('关系类型颜色映射正常')
      } else {
        result.fail('关系类型颜色映射异常')
      }

    } catch (error) {
      result.fail('关系映射测试异常', error)
    }
  }

  // 版本控制测试
  async testVersionControl(result) {
    try {
      // 测试版本数据结构
      const mockVersion = {
        id: 'VER-001',
        ciId: 'CI-001',
        version: 'v1.0.0',
        type: 'manual',
        createTime: new Date().toISOString()
      }

      if (mockVersion.id && mockVersion.ciId && mockVersion.version) {
        result.pass('版本数据结构验证通过')
      } else {
        result.fail('版本数据结构验证失败')
      }

      // 测试时间格式化
      const formattedTime = cmdbService.formatTime(mockVersion.createTime)
      if (formattedTime) {
        result.pass('时间格式化功能正常')
      } else {
        result.fail('时间格式化功能异常')
      }

    } catch (error) {
      result.fail('版本控制测试异常', error)
    }
  }

  // 数据质量测试
  async testDataQuality(result) {
    try {
      // 测试健康分数计算
      const mockCI = {
        status: '运行中',
        updateTime: new Date().toISOString()
      }

      const healthScore = cmdbService.calculateHealthScore(mockCI)
      if (typeof healthScore === 'number' && healthScore >= 0 && healthScore <= 100) {
        result.pass('健康分数计算正常')
      } else {
        result.fail('健康分数计算异常')
      }

      // 测试严重程度颜色映射
      const severityColor = cmdbService.getSeverityColor('critical')
      if (severityColor && severityColor.startsWith('#')) {
        result.pass('严重程度颜色映射正常')
      } else {
        result.fail('严重程度颜色映射异常')
      }

    } catch (error) {
      result.fail('数据质量测试异常', error)
    }
  }

  // 事件总线测试
  async testEventBus(result) {
    try {
      let eventReceived = false
      const testData = { test: 'data' }

      // 监听测试事件
      const unsubscribe = eventBus.on('test:event', (data) => {
        if (data && data.test === 'data') {
          eventReceived = true
        }
      })

      // 触发测试事件
      eventBus.emit('test:event', testData)

      // 验证事件是否被接收
      if (eventReceived) {
        result.pass('事件总线功能正常')
      } else {
        result.fail('事件总线功能异常')
      }

      // 清理
      unsubscribe()

      // 测试事件监听器数量
      const listenerCount = eventBus.listenerCount('test:event')
      if (listenerCount === 0) {
        result.pass('事件监听器清理正常')
      } else {
        result.fail('事件监听器清理异常')
      }

    } catch (error) {
      result.fail('事件总线测试异常', error)
    }
  }

  // 数据服务测试
  async testDataService(result) {
    try {
      // 测试缓存功能
      const cacheKey = 'test:cache'
      const testData = { cached: true }

      cmdbService.setCache(cacheKey, testData)
      const cachedData = cmdbService.getCache(cacheKey)

      if (cachedData && cachedData.cached === true) {
        result.pass('数据服务缓存功能正常')
      } else {
        result.fail('数据服务缓存功能异常')
      }

      // 测试缓存清理
      cmdbService.clearCache(cacheKey)
      const clearedData = cmdbService.getCache(cacheKey)

      if (clearedData === null) {
        result.pass('数据服务缓存清理正常')
      } else {
        result.fail('数据服务缓存清理异常')
      }

      // 测试工具方法
      const statusColor = cmdbService.getStatusColor('运行中')
      const typeIcon = cmdbService.getTypeIcon('server')

      if (statusColor && typeIcon) {
        result.pass('数据服务工具方法正常')
      } else {
        result.fail('数据服务工具方法异常')
      }

    } catch (error) {
      result.fail('数据服务测试异常', error)
    }
  }
}

/**
 * 性能测试工具
 */
export class PerformanceTester {
  constructor() {
    this.results = []
  }

  async testPageLoadTime(pageName, loadFn) {
    const startTime = performance.now()
    
    try {
      await loadFn()
      const endTime = performance.now()
      const loadTime = endTime - startTime

      this.results.push({
        page: pageName,
        loadTime: Math.round(loadTime),
        status: 'success'
      })

      console.log(`📊 ${pageName} 页面加载时间: ${Math.round(loadTime)}ms`)
      
      if (loadTime > 3000) {
        ElMessage.warning(`${pageName} 页面加载较慢 (${Math.round(loadTime)}ms)`)
      }

    } catch (error) {
      this.results.push({
        page: pageName,
        loadTime: 0,
        status: 'error',
        error: error.message
      })

      console.error(`❌ ${pageName} 页面加载失败:`, error)
    }
  }

  getResults() {
    return this.results
  }

  printSummary() {
    const avgLoadTime = this.results
      .filter(r => r.status === 'success')
      .reduce((sum, r) => sum + r.loadTime, 0) / this.results.length

    console.log(`\n📈 性能测试结果:`)
    console.log(`   平均加载时间: ${Math.round(avgLoadTime)}ms`)
    console.log(`   成功页面: ${this.results.filter(r => r.status === 'success').length}`)
    console.log(`   失败页面: ${this.results.filter(r => r.status === 'error').length}`)
  }
}

/**
 * 快速测试函数
 */
export async function runQuickTest() {
  ElMessage.info('开始执行快速测试...')
  
  const tester = new CMDBTester()
  const results = await tester.run()
  
  return results
}

/**
 * 完整测试函数
 */
export async function runFullTest() {
  ElMessage.info('开始执行完整测试...')
  
  const functionalTester = new CMDBTester()
  const performanceTester = new PerformanceTester()
  
  // 功能测试
  const functionalResults = await functionalTester.run()
  
  // 性能测试
  await performanceTester.testPageLoadTime('CMDB概览', async () => {
    await cmdbService.getCIList()
  })
  
  await performanceTester.testPageLoadTime('拓扑图', async () => {
    await cmdbService.getTopologyData()
  })
  
  performanceTester.printSummary()
  
  return {
    functional: functionalResults,
    performance: performanceTester.getResults()
  }
}

export default {
  CMDBTester,
  PerformanceTester,
  runQuickTest,
  runFullTest
}
