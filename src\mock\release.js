// 发布管理 Mock 数据
import { formatDateTime } from '@/api/index.js'

/**
 * 发布模板数据
 */
export const releaseTemplates = [
  {
    id: 'template-001',
    name: 'Web应用发布模板',
    description: '适用于前端Web应用的标准发布流程',
    stages: [
      {
        name: '开发环境部署',
        duration: 1,
        checklist: ['代码合并', '单元测试', '构建验证']
      },
      {
        name: '测试环境部署',
        duration: 2,
        checklist: ['功能测试', '集成测试', '性能测试']
      },
      {
        name: '预生产验证',
        duration: 1,
        checklist: ['用户验收测试', '业务确认', '安全扫描']
      },
      {
        name: '生产环境发布',
        duration: 1,
        checklist: ['生产部署', '健康检查', '监控确认']
      }
    ],
    rollbackPlan: {
      estimatedTime: '30分钟',
      steps: [
        '停止新版本服务',
        '切换负载均衡到旧版本',
        '验证服务可用性',
        '通知相关团队'
      ]
    }
  },
  {
    id: 'template-002',
    name: '数据库升级模板',
    description: '适用于数据库结构和数据升级的发布流程',
    stages: [
      {
        name: '开发环境验证',
        duration: 1,
        checklist: ['脚本测试', '数据备份', '回滚验证']
      },
      {
        name: '测试环境升级',
        duration: 2,
        checklist: ['数据迁移', '功能验证', '性能测试']
      },
      {
        name: '预生产升级',
        duration: 1,
        checklist: ['生产数据备份', '升级执行', '数据一致性检查']
      },
      {
        name: '生产环境升级',
        duration: 2,
        checklist: ['维护窗口确认', '数据库升级', '应用重启', '服务验证']
      }
    ],
    rollbackPlan: {
      estimatedTime: '60分钟',
      steps: [
        '停止应用服务',
        '恢复数据库备份',
        '重启数据库服务',
        '启动应用服务',
        '验证数据完整性'
      ]
    }
  },
  {
    id: 'template-003',
    name: '微服务发布模板',
    description: '适用于微服务架构的渐进式发布流程',
    stages: [
      {
        name: '开发环境部署',
        duration: 1,
        checklist: ['容器构建', '服务注册', '接口测试']
      },
      {
        name: '测试环境部署',
        duration: 2,
        checklist: ['集成测试', '链路测试', '压力测试']
      },
      {
        name: '灰度发布',
        duration: 1,
        checklist: ['5%流量切换', '监控指标', '错误率检查']
      },
      {
        name: '全量发布',
        duration: 1,
        checklist: ['100%流量切换', '服务监控', '告警确认']
      }
    ],
    rollbackPlan: {
      estimatedTime: '15分钟',
      steps: [
        '切换流量到旧版本',
        '停止新版本容器',
        '清理服务注册',
        '监控服务恢复'
      ]
    }
  }
]

/**
 * 发布检查清单模板
 */
export const checklistTemplates = {
  preRelease: [
    { item: '确认变更请求已批准', required: true, category: '合规性' },
    { item: '代码已合并到发布分支', required: true, category: '代码管理' },
    { item: '所有测试用例通过', required: true, category: '质量保证' },
    { item: '安全扫描无高危漏洞', required: true, category: '安全性' },
    { item: '性能测试达到预期', required: false, category: '性能' },
    { item: '文档已更新', required: false, category: '文档' },
    { item: '回滚方案已准备', required: true, category: '风险控制' },
    { item: '监控告警已配置', required: true, category: '监控' }
  ],
  postRelease: [
    { item: '服务健康检查通过', required: true, category: '服务可用性' },
    { item: 'API接口响应正常', required: true, category: '功能验证' },
    { item: '关键业务流程验证', required: true, category: '业务验证' },
    { item: '性能指标正常', required: true, category: '性能监控' },
    { item: '错误日志无异常', required: true, category: '日志监控' },
    { item: '数据库连接正常', required: true, category: '数据库' },
    { item: '第三方集成正常', required: false, category: '集成' },
    { item: '用户反馈收集', required: false, category: '用户体验' }
  ],
  rollback: [
    { item: '确认回滚决策', required: true, category: '决策' },
    { item: '通知相关团队', required: true, category: '沟通' },
    { item: '停止新版本服务', required: true, category: '服务控制' },
    { item: '恢复旧版本', required: true, category: '版本恢复' },
    { item: '数据库回滚（如需要）', required: true, category: '数据恢复' },
    { item: '配置文件恢复', required: true, category: '配置管理' },
    { item: '验证服务恢复', required: true, category: '验证' },
    { item: '记录回滚原因', required: true, category: '文档记录' }
  ]
}

/**
 * 发布窗口建议
 */
export const releaseWindows = [
  {
    name: '标准维护窗口',
    description: '每周二凌晨2:00-4:00',
    schedule: 'weekly',
    dayOfWeek: 2, // 周二
    startTime: '02:00',
    endTime: '04:00',
    riskLevel: 'low',
    suitable: ['patch', 'minor']
  },
  {
    name: '月度发布窗口',
    description: '每月第一个周六上午10:00-12:00',
    schedule: 'monthly',
    dayOfMonth: 'first-saturday',
    startTime: '10:00',
    endTime: '12:00',
    riskLevel: 'medium',
    suitable: ['minor', 'major']
  },
  {
    name: '紧急发布窗口',
    description: '7x24小时可用，需要审批',
    schedule: 'on-demand',
    riskLevel: 'high',
    suitable: ['hotfix'],
    approvalRequired: true
  }
]

/**
 * 发布影响分析模板
 */
export const impactAnalysisTemplate = {
  technical: [
    { aspect: '系统可用性', impact: 'medium', description: '发布期间可能出现短暂服务中断' },
    { aspect: '性能影响', impact: 'low', description: '新版本可能影响系统响应时间' },
    { aspect: '数据完整性', impact: 'low', description: '数据迁移过程需要验证' },
    { aspect: '集成接口', impact: 'medium', description: '可能影响第三方系统集成' }
  ],
  business: [
    { aspect: '用户体验', impact: 'medium', description: '界面变更可能影响用户操作习惯' },
    { aspect: '业务流程', impact: 'low', description: '新功能可能改变现有业务流程' },
    { aspect: '数据报表', impact: 'low', description: '可能影响现有报表的数据结构' },
    { aspect: '合规要求', impact: 'low', description: '需要确保符合相关法规要求' }
  ],
  operational: [
    { aspect: '运维复杂度', impact: 'medium', description: '新版本可能增加运维工作量' },
    { aspect: '监控告警', impact: 'low', description: '需要更新监控规则和告警阈值' },
    { aspect: '备份策略', impact: 'low', description: '可能需要调整备份和恢复策略' },
    { aspect: '容量规划', impact: 'medium', description: '新功能可能影响系统资源使用' }
  ]
}

/**
 * 发布成功标准
 */
export const successCriteria = {
  technical: [
    { metric: '服务可用性', threshold: '99.9%', measurement: '发布后24小时内' },
    { metric: 'API响应时间', threshold: '<500ms', measurement: '95%请求' },
    { metric: '错误率', threshold: '<0.1%', measurement: '发布后1小时内' },
    { metric: '资源使用率', threshold: '<80%', measurement: 'CPU和内存' }
  ],
  business: [
    { metric: '用户满意度', threshold: '>4.0', measurement: '5分制评分' },
    { metric: '业务功能', threshold: '100%', measurement: '关键业务流程可用' },
    { metric: '数据准确性', threshold: '100%', measurement: '数据验证通过' },
    { metric: '合规检查', threshold: '100%', measurement: '所有合规项通过' }
  ],
  operational: [
    { metric: '部署时间', threshold: '<2小时', measurement: '从开始到完成' },
    { metric: '回滚时间', threshold: '<30分钟', measurement: '如需回滚' },
    { metric: '监控覆盖', threshold: '100%', measurement: '所有关键指标' },
    { metric: '文档完整性', threshold: '100%', measurement: '发布文档和记录' }
  ]
}

/**
 * 发布风险评估矩阵
 */
export const riskMatrix = {
  factors: [
    { name: '技术复杂度', weight: 0.3, levels: ['简单', '中等', '复杂', '极复杂'] },
    { name: '业务影响范围', weight: 0.25, levels: ['单模块', '多模块', '跨系统', '全局'] },
    { name: '数据变更程度', weight: 0.2, levels: ['无变更', '少量变更', '大量变更', '结构变更'] },
    { name: '回滚难度', weight: 0.15, levels: ['容易', '中等', '困难', '极困难'] },
    { name: '时间窗口', weight: 0.1, levels: ['充足', '适中', '紧张', '极紧张'] }
  ],
  scoring: {
    low: { min: 0, max: 2.0, color: '#4CAF50', actions: ['标准流程发布'] },
    medium: { min: 2.1, max: 3.0, color: '#FF9800', actions: ['增加测试', '准备回滚方案'] },
    high: { min: 3.1, max: 3.5, color: '#F44336', actions: ['CAB审批', '专家评审', '分阶段发布'] },
    critical: { min: 3.6, max: 4.0, color: '#9C27B0', actions: ['高级管理层审批', '应急预案', '24小时监控'] }
  }
}

export default {
  releaseTemplates,
  checklistTemplates,
  releaseWindows,
  impactAnalysisTemplate,
  successCriteria,
  riskMatrix
}
