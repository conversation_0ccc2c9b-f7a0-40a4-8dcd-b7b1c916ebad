// API 基础配置和通用方法

/**
 * 统一的API响应格式
 * @param {*} data 响应数据
 * @param {string} msg 响应消息
 * @param {number} code 状态码
 * @returns {Promise} Promise对象
 */
export function createApiResponse(data, msg = '操作成功', code = 200) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code,
        msg,
        data
      })
    }, Math.random() * 500 + 100) // 模拟网络延迟 100-600ms
  })
}

/**
 * 模拟API错误响应
 * @param {string} msg 错误消息
 * @param {number} code 错误码
 * @returns {Promise} Promise对象
 */
export function createApiError(msg = '操作失败', code = 500) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      reject({
        code,
        msg,
        data: null
      })
    }, Math.random() * 300 + 100)
  })
}

/**
 * 分页数据处理
 * @param {Array} data 原始数据
 * @param {number} page 页码
 * @param {number} pageSize 每页大小
 * @returns {Object} 分页结果
 */
export function paginate(data, page = 1, pageSize = 20) {
  const total = data.length
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = data.slice(start, end)
  
  return {
    list,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  }
}

/**
 * 生成随机ID
 * @param {string} prefix 前缀
 * @returns {string} ID
 */
export function generateId(prefix = 'ID') {
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.random().toString(36).substr(2, 4).toUpperCase()
  return `${prefix}-${timestamp}-${random}`
}

/**
 * 格式化日期时间
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(date = new Date()) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

/**
 * 模拟用户数据
 */
export const mockUsers = [
  { id: 'user001', name: '张工', role: '技术支持', department: '运维部' },
  { id: 'user002', name: '李工', role: '网络工程师', department: '网络部' },
  { id: 'user003', name: '王工', role: '系统管理员', department: '运维部' },
  { id: 'user004', name: '赵工', role: '应用支持', department: '应用部' },
  { id: 'user005', name: '刘工', role: '数据库管理员', department: '数据部' }
]

/**
 * 模拟部门数据
 */
export const mockDepartments = [
  { id: 'dept001', name: '运维部', manager: '张主管' },
  { id: 'dept002', name: '网络部', manager: '李主管' },
  { id: 'dept003', name: '应用部', manager: '王主管' },
  { id: 'dept004', name: '数据部', manager: '赵主管' },
  { id: 'dept005', name: '安全部', manager: '刘主管' }
]

/**
 * 模拟服务分类
 */
export const mockCategories = [
  {
    id: 'cat001',
    name: '硬件',
    children: [
      { id: 'cat001001', name: '计算机', parent: 'cat001' },
      { id: 'cat001002', name: '打印机', parent: 'cat001' },
      { id: 'cat001003', name: '网络设备', parent: 'cat001' }
    ]
  },
  {
    id: 'cat002',
    name: '软件',
    children: [
      { id: 'cat002001', name: '操作系统', parent: 'cat002' },
      { id: 'cat002002', name: '应用软件', parent: 'cat002' },
      { id: 'cat002003', name: '安全软件', parent: 'cat002' }
    ]
  },
  {
    id: 'cat003',
    name: '服务',
    children: [
      { id: 'cat003001', name: '邮件服务', parent: 'cat003' },
      { id: 'cat003002', name: '网络服务', parent: 'cat003' },
      { id: 'cat003003', name: '数据库服务', parent: 'cat003' }
    ]
  }
]

/**
 * 优先级配置
 */
export const priorityConfig = [
  { value: '低', label: '低', color: 'info', level: 1 },
  { value: '中', label: '中', color: 'warning', level: 2 },
  { value: '高', label: '高', color: 'danger', level: 3 },
  { value: '紧急', label: '紧急', color: 'danger', level: 4 }
]

/**
 * 状态配置
 */
export const statusConfig = {
  ticket: [
    { value: 'pending', label: '待处理', color: 'info' },
    { value: 'processing', label: '处理中', color: 'warning' },
    { value: 'resolved', label: '已解决', color: 'success' },
    { value: 'closed', label: '已关闭', color: 'info' }
  ],
  incident: [
    { value: 'new', label: '新建', color: 'info' },
    { value: 'assigned', label: '已分配', color: 'warning' },
    { value: 'in-progress', label: '处理中', color: 'primary' },
    { value: 'resolved', label: '已解决', color: 'success' },
    { value: 'closed', label: '已关闭', color: 'info' }
  ],
  change: [
    { value: 'draft', label: '草稿', color: 'info' },
    { value: 'submitted', label: '已提交', color: 'warning' },
    { value: 'approved', label: '已批准', color: 'success' },
    { value: 'rejected', label: '已拒绝', color: 'danger' },
    { value: 'implemented', label: '已实施', color: 'success' },
    { value: 'closed', label: '已关闭', color: 'info' }
  ],
  release: [
    { value: 'planning', label: '计划中', color: 'info' },
    { value: 'dev', label: '开发阶段', color: 'primary' },
    { value: 'test', label: '测试阶段', color: 'warning' },
    { value: 'uat', label: '用户验收', color: 'warning' },
    { value: 'prod', label: '生产部署', color: 'success' },
    { value: 'verified', label: '已验证', color: 'success' },
    { value: 'failed', label: '失败', color: 'danger' },
    { value: 'rollback', label: '已回滚', color: 'danger' },
    { value: 'closed', label: '已关闭', color: 'info' }
  ]
}

/**
 * 获取随机状态
 * @param {string} type 状态类型
 * @returns {string} 随机状态值
 */
export function getRandomStatus(type = 'ticket') {
  const statuses = statusConfig[type] || statusConfig.ticket
  return statuses[Math.floor(Math.random() * statuses.length)].value
}

/**
 * 获取随机优先级
 * @returns {string} 随机优先级
 */
export function getRandomPriority() {
  return priorityConfig[Math.floor(Math.random() * priorityConfig.length)].value
}

/**
 * 获取随机用户
 * @returns {Object} 随机用户对象
 */
export function getRandomUser() {
  return mockUsers[Math.floor(Math.random() * mockUsers.length)]
}

/**
 * 获取随机分类
 * @returns {Object} 随机分类对象
 */
export function getRandomCategory() {
  const category = mockCategories[Math.floor(Math.random() * mockCategories.length)]
  const subCategory = category.children[Math.floor(Math.random() * category.children.length)]
  return {
    category: category.name,
    subCategory: subCategory.name,
    fullPath: `${category.name}/${subCategory.name}`
  }
}

/**
 * 生成随机日期
 * @param {number} daysAgo 几天前
 * @returns {string} 格式化的日期时间
 */
export function getRandomDate(daysAgo = 30) {
  const now = new Date()
  const randomDays = Math.floor(Math.random() * daysAgo)
  const randomHours = Math.floor(Math.random() * 24)
  const randomMinutes = Math.floor(Math.random() * 60)
  
  const date = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000)
  date.setHours(randomHours, randomMinutes, 0, 0)
  
  return formatDateTime(date)
}

export default {
  createApiResponse,
  createApiError,
  paginate,
  generateId,
  formatDateTime,
  mockUsers,
  mockDepartments,
  mockCategories,
  priorityConfig,
  statusConfig,
  getRandomStatus,
  getRandomPriority,
  getRandomUser,
  getRandomCategory,
  getRandomDate
}
