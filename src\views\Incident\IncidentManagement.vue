<template>
  <div class="incident-management">
    <div class="page-header">
      <h2>事件管理</h2>
      <p>快速恢复服务，最小化业务影响</p>
      <div class="header-actions">
        <el-button type="primary" @click="createIncident">
          <el-icon><Plus /></el-icon>
          创建事件
        </el-button>
      </div>
    </div>

    <!-- 事件概览 -->
    <div class="incident-overview">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in incidentStats" :key="stat.key">
          <el-card class="stat-card" :class="stat.type">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 优先级分布和SLA监控 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <span>事件优先级分布</span>
            </template>
            <div class="chart-container" ref="priorityChart"></div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <span>SLA达成率监控</span>
            </template>
            <div class="sla-monitoring">
              <div v-for="sla in slaData" :key="sla.priority" class="sla-item">
                <div class="sla-header">
                  <span class="sla-priority">{{ sla.priority }}</span>
                  <span class="sla-percentage" :class="getSLAClass(sla.percentage)">
                    {{ sla.percentage }}%
                  </span>
                </div>
                <el-progress 
                  :percentage="sla.percentage" 
                  :status="getSLAStatus(sla.percentage)"
                  :show-text="false"
                />
                <div class="sla-details">
                  <span>响应时间: {{ sla.responseTime }}</span>
                  <span>解决时间: {{ sla.resolutionTime }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 活跃事件列表 -->
    <div class="active-incidents">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>活跃事件 ({{ activeIncidents.length }})</span>
            <div class="header-filters">
              <el-select v-model="filterPriority" placeholder="优先级" size="small" clearable>
                <el-option label="P1-紧急" value="P1" />
                <el-option label="P2-高" value="P2" />
                <el-option label="P3-中" value="P3" />
                <el-option label="P4-低" value="P4" />
              </el-select>
              <el-select v-model="filterStatus" placeholder="状态" size="small" clearable>
                <el-option label="新建" value="new" />
                <el-option label="分配" value="assigned" />
                <el-option label="处理中" value="in-progress" />
                <el-option label="等待" value="waiting" />
              </el-select>
            </div>
          </div>
        </template>

        <el-table :data="filteredIncidents" style="width: 100%">
          <el-table-column prop="id" label="事件ID" width="120" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)" size="small">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="处理人" width="120" />
          <el-table-column prop="category" label="分类" width="150" />
          <el-table-column label="SLA状态" width="120">
            <template #default="scope">
              <div class="sla-progress">
                <el-progress 
                  :percentage="scope.row.slaProgress" 
                  :status="getSLAProgressStatus(scope.row.slaProgress)"
                  :show-text="false"
                  :stroke-width="6"
                />
                <span class="sla-text">{{ scope.row.slaProgress }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewIncident(scope.row)">
                查看
              </el-button>
              <el-button type="primary" size="small" text @click="assignIncident(scope.row)">
                分配
              </el-button>
              <el-button type="warning" size="small" text @click="escalateIncident(scope.row)">
                升级
              </el-button>
              <el-button type="success" size="small" text @click="resolveIncident(scope.row)">
                解决
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 事件分类树 -->
    <div class="incident-categories">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>事件分类统计</span>
            <el-button type="text" size="small" @click="manageCategoriesDialog = true">
              管理分类
            </el-button>
          </div>
        </template>
        <div class="category-stats">
          <div v-for="category in categoryStats" :key="category.name" class="category-item">
            <div class="category-info">
              <el-icon :size="20">
                <component :is="category.icon" />
              </el-icon>
              <span class="category-name">{{ category.name }}</span>
            </div>
            <div class="category-count">{{ category.count }}</div>
            <div class="category-trend" :class="category.trend">
              <el-icon :size="12">
                <component :is="category.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
              {{ category.change }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 管理分类对话框 -->
    <el-dialog v-model="manageCategoriesDialog" title="管理事件分类" width="600px">
      <el-tree
        :data="categoryTree"
        show-checkbox
        node-key="id"
        :default-expanded-keys="[1, 2, 3]"
        :props="{ children: 'children', label: 'label' }"
      />
      <template #footer>
        <el-button @click="manageCategoriesDialog = false">取消</el-button>
        <el-button type="primary">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 筛选条件
const filterPriority = ref('')
const filterStatus = ref('')

// 对话框状态
const manageCategoriesDialog = ref(false)

// 事件统计数据
const incidentStats = ref([
  {
    key: 'total',
    label: '总事件数',
    value: '156',
    type: 'primary',
    icon: 'Document'
  },
  {
    key: 'p1',
    label: 'P1紧急事件',
    value: '3',
    type: 'danger',
    icon: 'Warning'
  },
  {
    key: 'overdue',
    label: '超时事件',
    value: '8',
    type: 'warning',
    icon: 'Clock'
  },
  {
    key: 'resolved',
    label: '今日已解决',
    value: '24',
    type: 'success',
    icon: 'CircleCheck'
  }
])

// SLA数据
const slaData = ref([
  {
    priority: 'P1-紧急',
    percentage: 95,
    responseTime: '15分钟',
    resolutionTime: '2小时'
  },
  {
    priority: 'P2-高',
    percentage: 88,
    responseTime: '30分钟',
    resolutionTime: '8小时'
  },
  {
    priority: 'P3-中',
    percentage: 92,
    responseTime: '2小时',
    resolutionTime: '24小时'
  },
  {
    priority: 'P4-低',
    percentage: 98,
    responseTime: '8小时',
    resolutionTime: '72小时'
  }
])

// 活跃事件数据
const activeIncidents = ref([
  {
    id: 'INC-2025-001',
    title: '服务器CPU使用率过高',
    priority: 'P1',
    status: 'in-progress',
    assignee: '张工',
    category: '服务器/性能',
    slaProgress: 75,
    createTime: '2025-01-30 14:30'
  },
  {
    id: 'INC-2025-002',
    title: '网络连接异常',
    priority: 'P2',
    status: 'assigned',
    assignee: '李工',
    category: '网络/连接',
    slaProgress: 60,
    createTime: '2025-01-30 13:45'
  },
  {
    id: 'INC-2025-003',
    title: '打印机无法打印',
    priority: 'P3',
    status: 'new',
    assignee: '',
    category: '桌面支持/打印机',
    slaProgress: 90,
    createTime: '2025-01-30 15:20'
  }
])

// 分类统计
const categoryStats = ref([
  {
    name: '网络问题',
    icon: 'Connection',
    count: 45,
    change: '+12%',
    trend: 'up'
  },
  {
    name: '应用故障',
    icon: 'Monitor',
    count: 32,
    change: '-5%',
    trend: 'down'
  },
  {
    name: '硬件故障',
    icon: 'Box',
    count: 28,
    change: '+8%',
    trend: 'up'
  },
  {
    name: '安全事件',
    icon: 'Lock',
    count: 15,
    change: '+3%',
    trend: 'up'
  }
])

// 分类树数据
const categoryTree = ref([
  {
    id: 1,
    label: '网络',
    children: [
      { id: 11, label: '接入问题' },
      { id: 12, label: '路由器故障' },
      { id: 13, label: '带宽拥塞' }
    ]
  },
  {
    id: 2,
    label: '服务器',
    children: [
      { id: 21, label: '物理服务器' },
      { id: 22, label: '虚拟机' },
      { id: 23, label: '集群故障' }
    ]
  },
  {
    id: 3,
    label: '应用系统',
    children: [
      { id: 31, label: 'ERP' },
      { id: 32, label: 'OA' },
      { id: 33, label: '邮件系统' }
    ]
  }
])

// 图表引用
const priorityChart = ref(null)

// 过滤后的事件
const filteredIncidents = computed(() => {
  return activeIncidents.value.filter(incident => {
    return (!filterPriority.value || incident.priority === filterPriority.value) &&
           (!filterStatus.value || incident.status === filterStatus.value)
  })
})

// 获取优先级类型
const getPriorityType = (priority) => {
  const typeMap = {
    'P1': 'danger',
    'P2': 'warning',
    'P3': 'info',
    'P4': 'success'
  }
  return typeMap[priority] || 'info'
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    'new': 'info',
    'assigned': 'warning',
    'in-progress': 'primary',
    'waiting': 'warning',
    'resolved': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'new': '新建',
    'assigned': '已分配',
    'in-progress': '处理中',
    'waiting': '等待',
    'resolved': '已解决'
  }
  return textMap[status] || status
}

// 获取SLA类别
const getSLAClass = (percentage) => {
  if (percentage >= 95) return 'excellent'
  if (percentage >= 85) return 'good'
  if (percentage >= 70) return 'warning'
  return 'danger'
}

// 获取SLA状态
const getSLAStatus = (percentage) => {
  if (percentage >= 95) return 'success'
  if (percentage >= 85) return ''
  if (percentage >= 70) return 'warning'
  return 'exception'
}

// 获取SLA进度状态
const getSLAProgressStatus = (progress) => {
  if (progress >= 80) return 'success'
  if (progress >= 60) return 'warning'
  return 'exception'
}

// 初始化图表
const initCharts = () => {
  const chart = echarts.init(priorityChart.value)
  chart.setOption({
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '事件优先级',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 3, name: 'P1-紧急' },
          { value: 15, name: 'P2-高' },
          { value: 45, name: 'P3-中' },
          { value: 93, name: 'P4-低' }
        ]
      }
    ]
  })
}

// 创建事件
const createIncident = () => {
  ElMessage.info('创建事件功能开发中...')
}

// 查看事件
const viewIncident = (incident) => {
  ElMessage.info(`查看事件: ${incident.id}`)
}

// 分配事件
const assignIncident = (incident) => {
  ElMessage.info(`分配事件: ${incident.id}`)
}

// 升级事件
const escalateIncident = (incident) => {
  ElMessage.info(`升级事件: ${incident.id}`)
}

// 解决事件
const resolveIncident = (incident) => {
  ElMessage.info(`解决事件: ${incident.id}`)
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<style scoped>
.incident-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.incident-overview,
.charts-section,
.active-incidents,
.incident-categories {
  margin-bottom: 20px;
}

.stat-card {
  height: 80px;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
  border-left: 4px solid #1976D2;
}

.stat-card.danger {
  border-left: 4px solid #F44336;
}

.stat-card.warning {
  border-left: 4px solid #FF9800;
}

.stat-card.success {
  border-left: 4px solid #4CAF50;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 12px;
  color: #1976D2;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 12px;
}

.chart-container {
  height: 300px;
}

.sla-monitoring {
  padding: 20px 0;
}

.sla-item {
  margin-bottom: 20px;
}

.sla-item:last-child {
  margin-bottom: 0;
}

.sla-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sla-priority {
  font-weight: 500;
}

.sla-percentage {
  font-weight: bold;
}

.sla-percentage.excellent {
  color: #4CAF50;
}

.sla-percentage.good {
  color: #2196F3;
}

.sla-percentage.warning {
  color: #FF9800;
}

.sla-percentage.danger {
  color: #F44336;
}

.sla-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #616161;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-filters {
  display: flex;
  gap: 12px;
}

.sla-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sla-text {
  font-size: 12px;
  color: #616161;
  min-width: 35px;
}

.category-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  gap: 12px;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.category-name {
  font-weight: 500;
}

.category-count {
  font-size: 18px;
  font-weight: bold;
  color: #1976D2;
}

.category-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.category-trend.up {
  color: #4CAF50;
}

.category-trend.down {
  color: #F44336;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .header-filters {
    flex-direction: column;
    gap: 8px;
  }

  .category-stats {
    grid-template-columns: 1fr;
  }

  .sla-details {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
