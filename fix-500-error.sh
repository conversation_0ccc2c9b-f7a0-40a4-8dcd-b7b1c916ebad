#!/bin/bash

echo "========================================"
echo "修复CMDB项目500错误"
echo "========================================"
echo

echo "[步骤1] 停止可能运行的开发服务器..."
pkill -f "vite\|npm run dev" 2>/dev/null || true
echo "✓ 已尝试停止开发服务器进程"

echo
echo "[步骤2] 清理项目缓存和依赖..."
if [ -d "node_modules" ]; then
    echo "删除 node_modules 文件夹..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    echo "删除 package-lock.json 文件..."
    rm -f package-lock.json
fi

echo "清理npm缓存..."
npm cache clean --force

echo "✓ 清理完成"

echo
echo "[步骤3] 重新安装基础依赖..."
echo "安装Vue和Vite..."
npm install vue@latest vite@latest

echo "安装Element Plus..."
npm install element-plus @element-plus/icons-vue

echo "✓ 基础依赖安装完成"

echo
echo "[步骤4] 安装CMDB特定依赖..."
echo "安装状态管理..."
npm install pinia

echo "安装HTTP客户端..."
npm install axios

echo "安装模拟数据..."
npm install mockjs

echo "✓ CMDB依赖安装完成"

echo
echo "[步骤5] 验证安装..."
npm list vue vite element-plus pinia axios mockjs

echo
echo "[步骤6] 启动开发服务器..."
echo "正在启动项目..."
echo "项目将在 http://localhost:5173 启动"

# 在后台启动开发服务器
npm run dev &

echo
echo "========================================"
echo "修复完成！"
echo "========================================"
echo
echo "如果仍有问题，请："
echo "1. 检查 http://localhost:5173 是否正常显示"
echo "2. 查看控制台是否有错误信息"
echo "3. 运行 ./test-fix.sh 进行诊断"
echo

# 等待几秒钟让服务器启动
sleep 3

# 尝试打开浏览器
if command -v xdg-open > /dev/null; then
    xdg-open http://localhost:5173
elif command -v open > /dev/null; then
    open http://localhost:5173
else
    echo "请手动打开浏览器访问: http://localhost:5173"
fi
