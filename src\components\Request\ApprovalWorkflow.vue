<template>
  <div class="approval-workflow">
    <!-- 工作流配置 -->
    <div class="workflow-config" v-if="showConfig">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>审批流程配置</span>
            <el-button type="primary" @click="saveWorkflow">保存配置</el-button>
          </div>
        </template>
        
        <el-form :model="workflowConfig" label-width="120px">
          <el-form-item label="服务类型">
            <el-select v-model="workflowConfig.serviceType" placeholder="选择服务类型">
              <el-option 
                v-for="service in serviceTypes" 
                :key="service.id"
                :label="service.name" 
                :value="service.id" 
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="触发条件">
            <div class="conditions-config">
              <div 
                v-for="(condition, index) in workflowConfig.conditions" 
                :key="index"
                class="condition-item"
              >
                <el-select v-model="condition.field" placeholder="字段">
                  <el-option label="申请人角色" value="requester_role" />
                  <el-option label="费用金额" value="cost" />
                  <el-option label="部门" value="department" />
                  <el-option label="优先级" value="priority" />
                </el-select>
                
                <el-select v-model="condition.operator" placeholder="操作符">
                  <el-option label="等于" value="=" />
                  <el-option label="大于" value=">" />
                  <el-option label="小于" value="<" />
                  <el-option label="包含" value="contains" />
                </el-select>
                
                <el-input v-model="condition.value" placeholder="值" />
                
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="removeCondition(index)"
                  :disabled="workflowConfig.conditions.length <= 1"
                >
                  删除
                </el-button>
              </div>
              
              <el-button type="primary" size="small" @click="addCondition">
                添加条件
              </el-button>
            </div>
          </el-form-item>
          
          <el-form-item label="审批步骤">
            <div class="steps-config">
              <div 
                v-for="(step, index) in workflowConfig.steps" 
                :key="index"
                class="step-item"
              >
                <div class="step-number">{{ index + 1 }}</div>
                
                <el-select v-model="step.approver" placeholder="审批人">
                  <el-option label="直属主管" value="direct_manager" />
                  <el-option label="部门经理" value="department_manager" />
                  <el-option label="IT主管" value="it_head" />
                  <el-option label="财务主管" value="finance_head" />
                  <el-option label="总经理" value="general_manager" />
                </el-select>
                
                <el-input v-model="step.timeout" placeholder="超时时间(小时)">
                  <template #append>小时</template>
                </el-input>
                
                <el-select v-model="step.timeoutAction" placeholder="超时处理">
                  <el-option label="自动通过" value="auto_approve" />
                  <el-option label="自动拒绝" value="auto_reject" />
                  <el-option label="升级处理" value="escalate" />
                  <el-option label="跳过步骤" value="skip" />
                </el-select>
                
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="removeStep(index)"
                  :disabled="workflowConfig.steps.length <= 1"
                >
                  删除
                </el-button>
              </div>
              
              <el-button type="primary" size="small" @click="addStep">
                添加步骤
              </el-button>
            </div>
          </el-form-item>
          
          <el-form-item label="其他设置">
            <el-checkbox v-model="workflowConfig.allowParallel">允许并行审批</el-checkbox>
            <el-checkbox v-model="workflowConfig.allowDelegate">允许委托审批</el-checkbox>
            <el-checkbox v-model="workflowConfig.autoApprove">符合条件自动通过</el-checkbox>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 审批流程展示 -->
    <div class="workflow-display" v-else>
      <div class="workflow-header">
        <h3>{{ workflowTitle }}</h3>
        <div class="workflow-actions">
          <el-button @click="showConfig = true" v-if="canEdit">编辑流程</el-button>
          <el-button type="primary" @click="startApproval" v-if="canStart">开始审批</el-button>
        </div>
      </div>
      
      <!-- 流程图 -->
      <div class="workflow-diagram">
        <div class="workflow-steps">
          <div 
            v-for="(step, index) in displaySteps" 
            :key="index"
            class="workflow-step"
            :class="getStepClass(step)"
          >
            <div class="step-icon">
              <el-icon v-if="step.status === 'completed'"><CircleCheck /></el-icon>
              <el-icon v-else-if="step.status === 'current'"><Clock /></el-icon>
              <el-icon v-else-if="step.status === 'rejected'"><CircleClose /></el-icon>
              <el-icon v-else><User /></el-icon>
            </div>
            
            <div class="step-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-approver">{{ step.approver }}</div>
              <div class="step-time" v-if="step.processedAt">
                {{ step.processedAt }}
              </div>
              <div class="step-comment" v-if="step.comment">
                {{ step.comment }}
              </div>
            </div>
            
            <div class="step-status">
              <el-tag :type="getStatusType(step.status)">
                {{ getStatusText(step.status) }}
              </el-tag>
            </div>
            
            <!-- 连接线 -->
            <div 
              v-if="index < displaySteps.length - 1" 
              class="step-connector"
              :class="{ 'active': step.status === 'completed' }"
            ></div>
          </div>
        </div>
      </div>
      
      <!-- 审批操作 -->
      <div class="approval-actions" v-if="showActions">
        <el-card>
          <template #header>
            <span>审批操作</span>
          </template>
          
          <el-form :model="approvalForm" label-width="80px">
            <el-form-item label="审批意见">
              <el-input 
                v-model="approvalForm.comment"
                type="textarea"
                :rows="3"
                placeholder="请输入审批意见..."
              />
            </el-form-item>
            
            <el-form-item label="附件">
              <el-upload
                v-model:file-list="approvalForm.attachments"
                action="#"
                :auto-upload="false"
                multiple
              >
                <el-button size="small">选择文件</el-button>
              </el-upload>
            </el-form-item>
            
            <el-form-item>
              <el-button type="success" @click="approve('approved')">
                <el-icon><CircleCheck /></el-icon>
                通过
              </el-button>
              <el-button type="danger" @click="approve('rejected')">
                <el-icon><CircleClose /></el-icon>
                拒绝
              </el-button>
              <el-button @click="approve('returned')">
                <el-icon><Back /></el-icon>
                退回
              </el-button>
              <el-button type="warning" @click="showDelegateDialog = true">
                <el-icon><User /></el-icon>
                委托
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>

    <!-- 委托审批对话框 -->
    <el-dialog v-model="showDelegateDialog" title="委托审批" width="400px">
      <el-form :model="delegateForm" label-width="80px">
        <el-form-item label="委托给">
          <el-select v-model="delegateForm.delegateTo" placeholder="选择委托人">
            <el-option 
              v-for="user in availableUsers" 
              :key="user.id"
              :label="user.name" 
              :value="user.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="委托原因">
          <el-input 
            v-model="delegateForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入委托原因..."
          />
        </el-form-item>
        
        <el-form-item label="有效期">
          <el-date-picker
            v-model="delegateForm.validUntil"
            type="datetime"
            placeholder="选择有效期"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showDelegateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitDelegate">确定委托</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  CircleCheck, Clock, CircleClose, User, Back
} from '@element-plus/icons-vue'

const props = defineProps({
  requestId: {
    type: String,
    default: ''
  },
  workflowData: {
    type: Object,
    default: () => ({})
  },
  canEdit: {
    type: Boolean,
    default: false
  },
  canStart: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['save', 'start', 'approve', 'delegate'])

const showConfig = ref(false)
const showDelegateDialog = ref(false)

// 工作流配置
const workflowConfig = reactive({
  serviceType: '',
  conditions: [
    { field: '', operator: '', value: '' }
  ],
  steps: [
    { approver: '', timeout: '24', timeoutAction: 'escalate' }
  ],
  allowParallel: false,
  allowDelegate: true,
  autoApprove: false
})

// 审批表单
const approvalForm = reactive({
  comment: '',
  attachments: []
})

// 委托表单
const delegateForm = reactive({
  delegateTo: '',
  reason: '',
  validUntil: null
})

// 模拟数据
const serviceTypes = ref([
  { id: 'laptop', name: '笔记本电脑申请' },
  { id: 'software', name: '软件权限申请' },
  { id: 'access', name: '系统访问申请' }
])

const availableUsers = ref([
  { id: '1', name: '张三' },
  { id: '2', name: '李四' },
  { id: '3', name: '王五' }
])

const displaySteps = ref([
  {
    title: '部门经理审批',
    approver: '张经理',
    status: 'completed',
    processedAt: '2024-01-15 10:30:00',
    comment: '同意申请'
  },
  {
    title: 'IT主管审批',
    approver: '李主管',
    status: 'current',
    processedAt: null,
    comment: null
  },
  {
    title: '财务审批',
    approver: '王总监',
    status: 'pending',
    processedAt: null,
    comment: null
  }
])

const workflowTitle = computed(() => {
  return props.workflowData.title || '审批流程'
})

const getStepClass = (step) => {
  return {
    'completed': step.status === 'completed',
    'current': step.status === 'current',
    'rejected': step.status === 'rejected',
    'pending': step.status === 'pending'
  }
}

const getStatusType = (status) => {
  const statusMap = {
    completed: 'success',
    current: 'warning',
    rejected: 'danger',
    pending: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    completed: '已完成',
    current: '审批中',
    rejected: '已拒绝',
    pending: '待审批'
  }
  return statusMap[status] || '未知'
}

// 配置相关方法
const addCondition = () => {
  workflowConfig.conditions.push({ field: '', operator: '', value: '' })
}

const removeCondition = (index) => {
  workflowConfig.conditions.splice(index, 1)
}

const addStep = () => {
  workflowConfig.steps.push({ approver: '', timeout: '24', timeoutAction: 'escalate' })
}

const removeStep = (index) => {
  workflowConfig.steps.splice(index, 1)
}

const saveWorkflow = () => {
  emit('save', workflowConfig)
  showConfig.value = false
  ElMessage.success('工作流配置保存成功')
}

// 审批相关方法
const startApproval = () => {
  emit('start', props.requestId)
  ElMessage.success('审批流程已启动')
}

const approve = async (action) => {
  try {
    if (action === 'rejected' && !approvalForm.comment.trim()) {
      ElMessage.error('拒绝时必须填写审批意见')
      return
    }
    
    await ElMessageBox.confirm(
      `确定要${action === 'approved' ? '通过' : action === 'rejected' ? '拒绝' : '退回'}此申请吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: action === 'approved' ? 'success' : 'warning'
      }
    )
    
    const approvalData = {
      action,
      comment: approvalForm.comment,
      attachments: approvalForm.attachments
    }
    
    emit('approve', approvalData)
    ElMessage.success('审批操作成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审批操作失败')
    }
  }
}

const submitDelegate = () => {
  if (!delegateForm.delegateTo || !delegateForm.reason) {
    ElMessage.error('请填写完整的委托信息')
    return
  }
  
  emit('delegate', delegateForm)
  showDelegateDialog.value = false
  ElMessage.success('委托成功')
}
</script>

<style scoped>
.approval-workflow {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conditions-config,
.steps-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.condition-item,
.step-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.workflow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.workflow-header h3 {
  margin: 0;
  color: #303133;
}

.workflow-diagram {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.workflow-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.workflow-step {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.workflow-step.completed {
  background: #f0f9ff;
  border-left: 4px solid #67C23A;
}

.workflow-step.current {
  background: #fff7e6;
  border-left: 4px solid #E6A23C;
}

.workflow-step.rejected {
  background: #fef0f0;
  border-left: 4px solid #F56C6C;
}

.workflow-step.pending {
  background: #f8f9fa;
  border-left: 4px solid #C0C4CC;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.completed .step-icon {
  background: #67C23A;
}

.current .step-icon {
  background: #E6A23C;
}

.rejected .step-icon {
  background: #F56C6C;
}

.pending .step-icon {
  background: #C0C4CC;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.step-approver {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.step-time {
  font-size: 12px;
  color: #909399;
}

.step-comment {
  font-size: 12px;
  color: #606266;
  font-style: italic;
  margin-top: 4px;
}

.step-connector {
  position: absolute;
  left: 36px;
  bottom: -20px;
  width: 2px;
  height: 20px;
  background: #E4E7ED;
  transition: background 0.3s ease;
}

.step-connector.active {
  background: #67C23A;
}

.approval-actions {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .condition-item,
  .step-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .workflow-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .workflow-step {
    flex-direction: column;
    text-align: center;
  }
  
  .step-connector {
    display: none;
  }
}
</style>
