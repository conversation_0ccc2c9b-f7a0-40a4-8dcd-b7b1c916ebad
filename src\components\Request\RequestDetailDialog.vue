<template>
  <el-dialog
    v-model="visible"
    :title="`请求详情 - ${request?.requestNumber || ''}`"
    width="800px"
    :before-close="handleClose"
    class="request-detail-dialog"
  >
    <div v-if="request" class="request-detail-content">
      <!-- 请求基本信息 -->
      <div class="request-header">
        <div class="request-info">
          <div class="request-title">
            <h3>{{ request.serviceName }}</h3>
            <el-tag :type="getStatusType(request.status)">
              {{ request.statusLabel }}
            </el-tag>
          </div>
          <div class="request-meta">
            <div class="meta-item">
              <span class="label">请求编号：</span>
              <span class="value">{{ request.requestNumber }}</span>
            </div>
            <div class="meta-item">
              <span class="label">申请人：</span>
              <span class="value">{{ request.requester }}</span>
            </div>
            <div class="meta-item">
              <span class="label">部门：</span>
              <span class="value">{{ request.department }}</span>
            </div>
            <div class="meta-item">
              <span class="label">优先级：</span>
              <el-tag :type="getPriorityType(request.priority)" size="small">
                {{ request.priority }}
              </el-tag>
            </div>
          </div>
        </div>
        <div class="request-progress">
          <div class="progress-info">
            <span>处理进度</span>
            <span class="progress-percent">{{ request.approvalProgress || 0 }}%</span>
          </div>
          <el-progress 
            :percentage="request.approvalProgress || 0" 
            :stroke-width="8"
            :show-text="false"
          />
        </div>
      </div>

      <!-- 标签页内容 -->
      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <div class="basic-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="服务名称">
                {{ request.serviceName }}
              </el-descriptions-item>
              <el-descriptions-item label="服务分类">
                {{ getCategoryName(request.category) }}
              </el-descriptions-item>
              <el-descriptions-item label="提交时间">
                {{ request.createdAt }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ request.updatedAt }}
              </el-descriptions-item>
              <el-descriptions-item label="预计完成时间">
                {{ request.estimatedCompletion || '待确定' }}
              </el-descriptions-item>
              <el-descriptions-item label="实际费用">
                {{ request.actualCost || request.cost || '免费' }}
              </el-descriptions-item>
              <el-descriptions-item label="自动化处理" :span="2">
                <el-tag :type="request.automationEnabled ? 'success' : 'info'">
                  {{ request.automationEnabled ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <!-- 自定义字段 -->
            <div v-if="request.customFields" class="custom-fields">
              <h4>申请详情</h4>
              <el-descriptions :column="1" border>
                <el-descriptions-item 
                  v-for="(value, key) in request.customFields" 
                  :key="key"
                  :label="getFieldLabel(key)"
                >
                  <span v-if="Array.isArray(value)">{{ value.join('、') }}</span>
                  <span v-else>{{ value }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 备注说明 -->
            <div v-if="request.remarks" class="remarks">
              <h4>备注说明</h4>
              <div class="remarks-content">{{ request.remarks }}</div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 处理流程 -->
        <el-tab-pane label="处理流程" name="workflow">
          <div class="workflow-content">
            <el-timeline>
              <el-timeline-item
                v-for="(step, index) in workflowSteps"
                :key="index"
                :timestamp="step.timestamp"
                :type="step.type"
                :icon="step.icon"
              >
                <div class="timeline-content">
                  <div class="step-title">{{ step.title }}</div>
                  <div class="step-description">{{ step.description }}</div>
                  <div v-if="step.operator" class="step-operator">
                    操作人：{{ step.operator }}
                  </div>
                  <div v-if="step.comment" class="step-comment">
                    备注：{{ step.comment }}
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>

        <!-- 附件文件 -->
        <el-tab-pane label="附件文件" name="attachments">
          <div class="attachments-content">
            <div v-if="request.attachments && request.attachments.length > 0" class="attachments-list">
              <div 
                v-for="(attachment, index) in request.attachments" 
                :key="index"
                class="attachment-item"
              >
                <div class="attachment-icon">
                  <el-icon :size="24">
                    <Document />
                  </el-icon>
                </div>
                <div class="attachment-info">
                  <div class="attachment-name">{{ attachment.name || attachment }}</div>
                  <div class="attachment-meta">
                    <span v-if="attachment.size">{{ formatFileSize(attachment.size) }}</span>
                    <span v-if="attachment.uploadTime">{{ attachment.uploadTime }}</span>
                  </div>
                </div>
                <div class="attachment-actions">
                  <el-button type="primary" size="small" @click="downloadAttachment(attachment)">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                </div>
              </div>
            </div>
            <div v-else class="no-attachments">
              <el-empty description="暂无附件" />
            </div>
          </div>
        </el-tab-pane>

        <!-- 评论记录 -->
        <el-tab-pane label="评论记录" name="comments">
          <div class="comments-content">
            <!-- 评论列表 -->
            <div v-if="comments.length > 0" class="comments-list">
              <div 
                v-for="comment in comments" 
                :key="comment.id"
                class="comment-item"
              >
                <div class="comment-avatar">
                  <el-avatar :size="32">{{ comment.author.charAt(0) }}</el-avatar>
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-author">{{ comment.author }}</span>
                    <span class="comment-time">{{ comment.createdAt }}</span>
                  </div>
                  <div class="comment-text">{{ comment.content }}</div>
                </div>
              </div>
            </div>
            <div v-else class="no-comments">
              <el-empty description="暂无评论" />
            </div>

            <!-- 添加评论 -->
            <div class="add-comment">
              <el-input
                v-model="newComment"
                type="textarea"
                :rows="3"
                placeholder="添加评论..."
                maxlength="500"
                show-word-limit
              />
              <div class="comment-actions">
                <el-button type="primary" @click="addComment" :disabled="!newComment.trim()">
                  添加评论
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="canCancelRequest"
          type="danger" 
          @click="cancelRequest"
        >
          取消请求
        </el-button>
        <el-button 
          v-if="canRateRequest"
          type="warning" 
          @click="rateRequest"
        >
          评价服务
        </el-button>
        <el-button 
          v-if="canApproveRequest"
          type="success" 
          @click="approveRequest"
        >
          审批通过
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Download, CircleCheck, Clock, Warning } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  request: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'update', 'cancel', 'rate', 'approve'])

const activeTab = ref('basic')
const newComment = ref('')
const comments = ref([])
const workflowSteps = ref([])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canCancelRequest = computed(() => {
  return props.request && ['submitted', 'pending_approval'].includes(props.request.status)
})

const canRateRequest = computed(() => {
  return props.request && props.request.status === 'completed'
})

const canApproveRequest = computed(() => {
  return props.request && props.request.status === 'pending_approval'
})

const getStatusType = (status) => {
  const statusMap = {
    submitted: 'info',
    pending_approval: 'warning',
    approved: 'success',
    rejected: 'danger',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getPriorityType = (priority) => {
  const priorityMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return priorityMap[priority] || 'info'
}

const getCategoryName = (categoryId) => {
  // 这里可以从store获取分类名称
  const categoryMap = {
    'personal': '个人服务',
    'equipment': '设备服务',
    'network': '网络与安全',
    'application': '应用与权限',
    'other': '其他服务'
  }
  return categoryMap[categoryId] || '未知分类'
}

const getFieldLabel = (fieldKey) => {
  // 这里可以根据字段key返回对应的标签
  const labelMap = {
    'purpose': '使用用途',
    'urgency': '紧急程度',
    'budgetSource': '预算来源',
    'accessPeriod': '访问期限',
    'businessJustification': '业务理由'
  }
  return labelMap[fieldKey] || fieldKey
}

const formatFileSize = (size) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
}

const downloadAttachment = (attachment) => {
  // 这里实现文件下载逻辑
  ElMessage.info('下载功能开发中...')
}

const addComment = async () => {
  if (!newComment.value.trim()) return
  
  try {
    const comment = {
      id: Date.now().toString(),
      author: '当前用户', // 从用户store获取
      content: newComment.value,
      createdAt: new Date().toLocaleString()
    }
    
    comments.value.unshift(comment)
    newComment.value = ''
    ElMessage.success('评论添加成功')
  } catch (error) {
    ElMessage.error('添加评论失败')
    console.error('添加评论失败:', error)
  }
}

const cancelRequest = () => {
  emit('cancel', props.request)
}

const rateRequest = () => {
  emit('rate', props.request)
}

const approveRequest = () => {
  emit('approve', props.request)
}

const handleClose = () => {
  visible.value = false
  activeTab.value = 'basic'
  newComment.value = ''
}

// 监听请求变化，加载相关数据
watch(() => props.request, (newRequest) => {
  if (newRequest) {
    loadWorkflowSteps()
    loadComments()
  }
}, { immediate: true })

const loadWorkflowSteps = () => {
  // 模拟加载工作流步骤
  workflowSteps.value = [
    {
      title: '请求提交',
      description: '用户提交服务请求',
      timestamp: props.request?.createdAt,
      type: 'success',
      icon: CircleCheck,
      operator: props.request?.requester
    },
    {
      title: '等待审批',
      description: '请求已提交，等待审批人处理',
      timestamp: props.request?.createdAt,
      type: 'warning',
      icon: Clock
    }
  ]
  
  if (props.request?.status === 'completed') {
    workflowSteps.value.push({
      title: '处理完成',
      description: '请求已完成处理',
      timestamp: props.request?.updatedAt,
      type: 'success',
      icon: CircleCheck
    })
  }
}

const loadComments = () => {
  // 模拟加载评论数据
  comments.value = []
}
</script>

<style scoped>
.request-detail-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.request-detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.request-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.request-title h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.request-meta {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-item .label {
  color: #606266;
  font-size: 14px;
}

.meta-item .value {
  color: #303133;
  font-weight: 500;
}

.request-progress {
  min-width: 200px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.progress-percent {
  font-weight: 600;
  color: #409EFF;
}

.detail-tabs {
  margin-top: 20px;
}

.custom-fields,
.remarks {
  margin-top: 20px;
}

.custom-fields h4,
.remarks h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.remarks-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  color: #606266;
  line-height: 1.6;
}

.timeline-content {
  padding-left: 12px;
}

.step-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.step-description {
  color: #606266;
  margin-bottom: 8px;
}

.step-operator,
.step-comment {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.attachment-icon {
  color: #409EFF;
}

.attachment-info {
  flex: 1;
}

.attachment-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.attachment-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 12px;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.comment-item {
  display: flex;
  gap: 12px;
}

.comment-content {
  flex: 1;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 600;
  color: #303133;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-text {
  color: #606266;
  line-height: 1.6;
}

.add-comment {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.comment-actions {
  margin-top: 12px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .request-detail-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .request-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .request-meta {
    grid-template-columns: 1fr;
  }
  
  .request-progress {
    width: 100%;
  }
}
</style>
