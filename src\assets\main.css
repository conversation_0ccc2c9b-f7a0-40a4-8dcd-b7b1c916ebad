@import './base.css';

/* ITSM 主题色彩系统 */
:root {
  /* 主色调 - 蓝灰科技色 */
  --primary-color: #1976D2;
  --primary-light: #42A5F5;
  --primary-dark: #1565C0;
  --primary-bg: #E3F2FD;

  /* 辅助色 */
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --danger-color: #F44336;
  --info-color: #2196F3;

  /* 中性色 */
  --text-primary: #212121;
  --text-secondary: #757575;
  --text-disabled: #BDBDBD;
  --divider: #E0E0E0;
  --background: #FAFAFA;
  --surface: #FFFFFF;

  /* 阴影 */
  --shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12);
  --shadow-2: 0 2px 6px rgba(0, 0, 0, 0.16);
  --shadow-3: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-4: 0 8px 24px rgba(0, 0, 0, 0.15);

  /* 圆角 */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* 全局样式重置和优化 */
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.6;
}

body {
  font-family: 'PingFang SC', 'Helvetica Neue', 'Microsoft YaHei', 'Source Han Sans SC', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif;
  color: var(--text-primary);
  background-color: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 通用工具类 */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

.shadow-1 { box-shadow: var(--shadow-1) !important; }
.shadow-2 { box-shadow: var(--shadow-2) !important; }
.shadow-3 { box-shadow: var(--shadow-3) !important; }
.shadow-4 { box-shadow: var(--shadow-4) !important; }

.rounded-sm { border-radius: var(--radius-small) !important; }
.rounded-md { border-radius: var(--radius-medium) !important; }
.rounded-lg { border-radius: var(--radius-large) !important; }

.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }

.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式断点 */
@media (max-width: 480px) {
  html {
    font-size: 13px;
  }
}

@media (min-width: 1200px) {
  html {
    font-size: 15px;
  }
}

/* 重置所有默认样式 */
#app {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

body {
  margin: 0;
  padding: 0;
  display: block;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: block;
  }

  #app {
    display: block;
    padding: 0;
  }
}
