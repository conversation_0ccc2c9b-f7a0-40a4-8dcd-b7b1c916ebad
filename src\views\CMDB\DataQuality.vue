<template>
  <div class="data-quality">
    <div class="page-header">
      <div class="header-content">
        <h2>数据质量监控</h2>
        <p>监控和管理CMDB数据质量，确保配置项数据的完整性、一致性和准确性</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="runQualityCheck">
          <el-icon><Refresh /></el-icon>
          执行质量检查
        </el-button>
        <el-button @click="exportQualityReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
        <el-button @click="showQualitySettings = true">
          <el-icon><Setting /></el-icon>
          质量设置
        </el-button>
      </div>
    </div>

    <!-- 质量概览 -->
    <div class="quality-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-score">
                <el-progress
                  type="circle"
                  :percentage="overallQuality.score"
                  :width="80"
                  :color="getQualityColor(overallQuality.score)"
                />
                <div class="score-label">总体质量分</div>
              </div>
              <div class="score-trend">
                <el-icon :size="16" :color="overallQuality.trend > 0 ? '#4CAF50' : '#F44336'">
                  <component :is="overallQuality.trend > 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
                <span :style="{ color: overallQuality.trend > 0 ? '#4CAF50' : '#F44336' }">
                  {{ Math.abs(overallQuality.trend) }}%
                </span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="18">
          <el-card class="metrics-card">
            <template #header>
              <span>质量指标</span>
            </template>
            <div class="metrics-grid">
              <div class="metric-item" v-for="metric in qualityMetrics" :key="metric.key">
                <div class="metric-header">
                  <span class="metric-name">{{ metric.name }}</span>
                  <span class="metric-score" :style="{ color: getQualityColor(metric.score) }">
                    {{ metric.score }}%
                  </span>
                </div>
                <el-progress
                  :percentage="metric.score"
                  :color="getQualityColor(metric.score)"
                  :show-text="false"
                />
                <div class="metric-details">
                  <span class="metric-count">{{ metric.issueCount }} 个问题</span>
                  <span class="metric-trend" :style="{ color: metric.trend > 0 ? '#4CAF50' : '#F44336' }">
                    {{ metric.trend > 0 ? '+' : '' }}{{ metric.trend }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 质量问题分类 -->
    <div class="quality-issues">
      <el-row :gutter="20">
        <el-col :span="8" v-for="category in issueCategories" :key="category.key">
          <el-card class="issue-category-card">
            <template #header>
              <div class="category-header">
                <el-icon :size="20" :color="category.color">
                  <component :is="category.icon" />
                </el-icon>
                <span>{{ category.name }}</span>
                <el-badge :value="category.count" :type="category.badgeType" />
              </div>
            </template>
            <div class="category-content">
              <div class="category-stats">
                <div class="stat-item">
                  <span class="stat-label">严重:</span>
                  <span class="stat-value critical">{{ category.critical }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">警告:</span>
                  <span class="stat-value warning">{{ category.warning }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">提示:</span>
                  <span class="stat-value info">{{ category.info }}</span>
                </div>
              </div>
              <el-button type="primary" size="small" @click="viewCategoryDetails(category)" style="width: 100%">
                查看详情
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 质量趋势图表 -->
    <div class="quality-trends">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>质量趋势</span>
            <el-radio-group v-model="trendPeriod" size="small">
              <el-radio-button label="7d">7天</el-radio-button>
              <el-radio-button label="30d">30天</el-radio-button>
              <el-radio-button label="90d">90天</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        <div class="trend-chart" ref="trendChart">
          <!-- 这里将渲染趋势图表 -->
          <div class="chart-placeholder">
            <el-icon :size="64" color="#C0C4CC">
              <TrendCharts />
            </el-icon>
            <p>质量趋势图表</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 问题详情列表 -->
    <div class="issue-details">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>质量问题详情</span>
            <div class="header-controls">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索问题..."
                style="width: 200px; margin-right: 12px"
                prefix-icon="Search"
              />
              <el-select
                v-model="selectedSeverity"
                placeholder="严重程度"
                style="width: 120px; margin-right: 12px"
              >
                <el-option label="全部" value="" />
                <el-option label="严重" value="critical" />
                <el-option label="警告" value="warning" />
                <el-option label="提示" value="info" />
              </el-select>
              <el-select
                v-model="selectedCategory"
                placeholder="问题类型"
                style="width: 120px"
              >
                <el-option label="全部类型" value="" />
                <el-option label="完整性" value="completeness" />
                <el-option label="一致性" value="consistency" />
                <el-option label="准确性" value="accuracy" />
                <el-option label="新鲜度" value="freshness" />
              </el-select>
            </div>
          </div>
        </template>
        <el-table :data="filteredIssues" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="问题ID" width="120" />
          <el-table-column prop="ciName" label="配置项" min-width="150">
            <template #default="scope">
              <el-button type="text" @click="viewCI(scope.row.ciId)">
                {{ scope.row.ciName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="类型" width="100">
            <template #default="scope">
              <el-tag :type="getCategoryType(scope.row.category)" size="small">
                {{ getCategoryText(scope.row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="severity" label="严重程度" width="100">
            <template #default="scope">
              <el-tag :type="getSeverityType(scope.row.severity)" size="small">
                {{ getSeverityText(scope.row.severity) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="问题描述" min-width="200" />
          <el-table-column prop="detectTime" label="检测时间" width="160" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="fixIssue(scope.row)">
                修复
              </el-button>
              <el-button type="primary" size="small" text @click="ignoreIssue(scope.row)">
                忽略
              </el-button>
              <el-button type="primary" size="small" text @click="viewIssueDetails(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalItems"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 质量设置对话框 -->
    <el-dialog v-model="showQualitySettings" title="数据质量设置" width="60%">
      <el-form :model="qualitySettings" label-width="120px">
        <el-form-item label="检查频率">
          <el-select v-model="qualitySettings.checkFrequency" style="width: 100%">
            <el-option label="每小时" value="hourly" />
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="手动" value="manual" />
          </el-select>
        </el-form-item>
        <el-form-item label="完整性阈值">
          <el-slider v-model="qualitySettings.completenessThreshold" :min="0" :max="100" show-input />
        </el-form-item>
        <el-form-item label="一致性阈值">
          <el-slider v-model="qualitySettings.consistencyThreshold" :min="0" :max="100" show-input />
        </el-form-item>
        <el-form-item label="新鲜度阈值(天)">
          <el-input-number v-model="qualitySettings.freshnessThreshold" :min="1" :max="365" />
        </el-form-item>
        <el-form-item label="自动修复">
          <el-switch v-model="qualitySettings.autoFix" />
          <span class="form-help">启用后将自动修复部分质量问题</span>
        </el-form-item>
        <el-form-item label="通知设置">
          <el-checkbox-group v-model="qualitySettings.notifications">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="webhook">Webhook通知</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showQualitySettings = false">取消</el-button>
          <el-button type="primary" @click="saveQualitySettings">保存设置</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showQualitySettings = ref(false)
const searchKeyword = ref('')
const selectedSeverity = ref('')
const selectedCategory = ref('')
const trendPeriod = ref('30d')
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const trendChart = ref(null)

// 总体质量数据
const overallQuality = reactive({
  score: 85,
  trend: 2.3
})

// 质量指标
const qualityMetrics = ref([
  {
    key: 'completeness',
    name: '完整性',
    score: 92,
    issueCount: 23,
    trend: 1.5
  },
  {
    key: 'consistency',
    name: '一致性',
    score: 88,
    issueCount: 15,
    trend: -0.8
  },
  {
    key: 'accuracy',
    name: '准确性',
    score: 94,
    issueCount: 8,
    trend: 3.2
  },
  {
    key: 'freshness',
    name: '新鲜度',
    score: 76,
    issueCount: 45,
    trend: -2.1
  }
])

// 问题分类
const issueCategories = ref([
  {
    key: 'completeness',
    name: '完整性问题',
    icon: 'Warning',
    color: '#FF9800',
    count: 23,
    badgeType: 'warning',
    critical: 5,
    warning: 12,
    info: 6
  },
  {
    key: 'consistency',
    name: '一致性问题',
    icon: 'CircleClose',
    color: '#F44336',
    count: 15,
    badgeType: 'danger',
    critical: 8,
    warning: 5,
    info: 2
  },
  {
    key: 'accuracy',
    name: '准确性问题',
    icon: 'QuestionFilled',
    color: '#2196F3',
    count: 8,
    badgeType: 'primary',
    critical: 2,
    warning: 4,
    info: 2
  }
])

// 质量问题详情
const qualityIssues = ref([
  {
    id: 'QI-001',
    ciId: 'CI-001',
    ciName: 'WEB-SRV-01',
    category: 'completeness',
    severity: 'warning',
    description: '缺少IP地址信息',
    detectTime: '2025-01-30 14:30:00',
    status: '待处理'
  },
  {
    id: 'QI-002',
    ciId: 'CI-002',
    ciName: 'DB-SRV-01',
    category: 'consistency',
    severity: 'critical',
    description: '配置项状态与监控状态不一致',
    detectTime: '2025-01-30 13:45:00',
    status: '处理中'
  },
  {
    id: 'QI-003',
    ciId: 'CI-003',
    ciName: 'APP-SRV-01',
    category: 'freshness',
    severity: 'info',
    description: '配置项信息超过30天未更新',
    detectTime: '2025-01-30 12:20:00',
    status: '已忽略'
  },
  {
    id: 'QI-004',
    ciId: 'CI-004',
    ciName: 'NET-SW-01',
    category: 'accuracy',
    severity: 'warning',
    description: '设备型号信息可能不准确',
    detectTime: '2025-01-30 11:15:00',
    status: '待处理'
  }
])

// 质量设置
const qualitySettings = reactive({
  checkFrequency: 'daily',
  completenessThreshold: 90,
  consistencyThreshold: 85,
  freshnessThreshold: 30,
  autoFix: false,
  notifications: ['email']
})

// 计算属性
const filteredIssues = computed(() => {
  let issues = qualityIssues.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    issues = issues.filter(issue =>
      issue.ciName.toLowerCase().includes(keyword) ||
      issue.description.toLowerCase().includes(keyword)
    )
  }

  // 按严重程度筛选
  if (selectedSeverity.value) {
    issues = issues.filter(issue => issue.severity === selectedSeverity.value)
  }

  // 按类型筛选
  if (selectedCategory.value) {
    issues = issues.filter(issue => issue.category === selectedCategory.value)
  }

  totalItems.value = issues.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return issues.slice(start, end)
})

// 方法函数
const runQualityCheck = () => {
  loading.value = true
  ElMessage.info('正在执行数据质量检查...')

  // 模拟质量检查过程
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据质量检查完成')

    // 更新质量指标
    qualityMetrics.value.forEach(metric => {
      metric.score = Math.max(0, metric.score + (Math.random() - 0.5) * 10)
      metric.issueCount = Math.floor(Math.random() * 50)
    })

    // 更新总体质量分
    const avgScore = qualityMetrics.value.reduce((sum, metric) => sum + metric.score, 0) / qualityMetrics.value.length
    overallQuality.score = Math.round(avgScore)
  }, 3000)
}

const exportQualityReport = () => {
  ElMessage.success('正在生成质量报告...')
  // 这里可以实现报告导出逻辑
}

const viewCategoryDetails = (category) => {
  ElMessage.info(`查看${category.name}详情`)
  selectedCategory.value = category.key
  // 这里可以实现跳转到详情页面或打开详情对话框
}

const viewCI = (ciId) => {
  // 跳转到CI详情页面
  window.open(`/cmdb/ci/${ciId}`, '_blank')
}

const fixIssue = (issue) => {
  ElMessageBox.confirm(
    `确定要修复问题 "${issue.description}" 吗？`,
    '确认修复',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    issue.status = '已修复'
    ElMessage.success('问题已修复')

    // 更新相关指标
    const metric = qualityMetrics.value.find(m => m.key === issue.category)
    if (metric && metric.issueCount > 0) {
      metric.issueCount--
      metric.score = Math.min(100, metric.score + 1)
    }
  }).catch(() => {
    ElMessage.info('已取消修复')
  })
}

const ignoreIssue = (issue) => {
  ElMessageBox.confirm(
    `确定要忽略问题 "${issue.description}" 吗？`,
    '确认忽略',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    issue.status = '已忽略'
    ElMessage.success('问题已忽略')
  }).catch(() => {
    ElMessage.info('已取消忽略')
  })
}

const viewIssueDetails = (issue) => {
  ElMessage.info(`查看问题详情: ${issue.id}`)
  // 这里可以实现打开问题详情对话框
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const saveQualitySettings = () => {
  ElMessage.success('质量设置已保存')
  showQualitySettings.value = false
  // 这里可以实现保存设置的逻辑
}

// 辅助函数
const getQualityColor = (score) => {
  if (score >= 90) return '#4CAF50'
  if (score >= 70) return '#FF9800'
  return '#F44336'
}

const getCategoryType = (category) => {
  const typeMap = {
    'completeness': 'warning',
    'consistency': 'danger',
    'accuracy': 'primary',
    'freshness': 'info'
  }
  return typeMap[category] || 'info'
}

const getCategoryText = (category) => {
  const textMap = {
    'completeness': '完整性',
    'consistency': '一致性',
    'accuracy': '准确性',
    'freshness': '新鲜度'
  }
  return textMap[category] || category
}

const getSeverityType = (severity) => {
  const typeMap = {
    'critical': 'danger',
    'warning': 'warning',
    'info': 'info'
  }
  return typeMap[severity] || 'info'
}

const getSeverityText = (severity) => {
  const textMap = {
    'critical': '严重',
    'warning': '警告',
    'info': '提示'
  }
  return textMap[severity] || severity
}

const getStatusType = (status) => {
  const typeMap = {
    '待处理': 'warning',
    '处理中': 'primary',
    '已修复': 'success',
    '已忽略': 'info'
  }
  return typeMap[status] || 'info'
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化数据
  totalItems.value = qualityIssues.value.length

  // 可以在这里加载实际的质量数据
  console.log('Data Quality component mounted')
})
</script>

<style scoped>
.data-quality {
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-content h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-content p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 质量概览样式 */
.quality-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overview-content {
  text-align: center;
}

.overview-score {
  margin-bottom: 16px;
}

.score-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.score-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.metrics-card {
  height: 200px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: 140px;
}

.metric-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metric-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.metric-score {
  font-size: 16px;
  font-weight: bold;
}

.metric-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
}

.metric-count {
  color: #666;
}

.metric-trend {
  font-weight: 500;
}

/* 问题分类样式 */
.quality-issues {
  margin-bottom: 20px;
}

.issue-category-card {
  height: 180px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-content {
  padding: 16px 0;
}

.category-stats {
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: bold;
}

.stat-value.critical {
  color: #F44336;
}

.stat-value.warning {
  color: #FF9800;
}

.stat-value.info {
  color: #2196F3;
}

/* 趋势图表样式 */
.quality-trends {
  margin-bottom: 20px;
}

.trend-chart {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.chart-placeholder {
  text-align: center;
  color: #666;
}

.chart-placeholder p {
  margin: 16px 0 0 0;
  font-size: 16px;
}

/* 问题详情样式 */
.issue-details {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 表单帮助文本 */
.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-button--text {
  padding: 4px 8px;
}

.el-progress {
  margin: 0;
}

.el-progress__text {
  font-size: 12px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    height: auto;
  }

  .overview-card,
  .metrics-card {
    height: auto;
  }
}

@media (max-width: 768px) {
  .data-quality {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .overview-card {
    height: 160px;
  }

  .metrics-card {
    height: auto;
    padding: 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .metric-item {
    padding: 12px;
  }

  .issue-category-card {
    height: auto;
    margin-bottom: 16px;
  }

  .header-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .header-controls .el-input,
  .header-controls .el-select {
    width: 100% !important;
  }

  .trend-chart {
    height: 250px;
  }

  /* 表格在移动端的优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* 对话框在移动端的优化 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .overview-content {
    padding: 12px;
  }

  .metric-item {
    padding: 8px;
  }

  .category-content {
    padding: 8px 0;
  }
}

/* 动画效果 */
.overview-card,
.metrics-card,
.issue-category-card {
  transition: all 0.3s ease;
}

.overview-card:hover,
.metrics-card:hover,
.issue-category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-item {
  transition: all 0.3s ease;
}

.metric-item:hover {
  background: #e3f2fd;
}

/* 加载状态 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa;
}

/* 进度条样式 */
.el-progress--circle {
  margin: 0;
}

.el-progress--line {
  margin: 0;
}

.el-progress-bar__outer {
  border-radius: 10px;
}

.el-progress-bar__inner {
  border-radius: 10px;
}

/* 徽章样式 */
.el-badge {
  margin-left: auto;
}

/* 滑块样式 */
.el-slider {
  margin: 12px 0;
}
</style>
