<template>
  <div class="cab-approval">
    <div class="page-header">
      <div class="header-info">
        <h2>CAB审批</h2>
        <p>变更咨询委员会审批流程管理</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createCABMeeting">
          <el-icon><Plus /></el-icon>
          创建CAB会议
        </el-button>
        <el-button @click="exportApprovals">
          <el-icon><Download /></el-icon>
          导出审批记录
        </el-button>
      </div>
    </div>

    <!-- CAB概览统计 -->
    <div class="cab-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="待审批变更" :value="pendingApprovals" />
            <div class="stat-extra">
              <el-tag type="warning" size="small">紧急: {{ urgentApprovals }}</el-tag>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="本月审批" :value="monthlyApprovals" />
            <div class="stat-extra">
              <el-tag type="success" size="small">通过率: {{ approvalRate }}%</el-tag>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="CAB成员" :value="cabMembers.length" />
            <div class="stat-extra">
              <el-tag type="info" size="small">在线: {{ onlineMembers }}</el-tag>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="平均审批时间" :value="avgApprovalTime" suffix="小时" />
            <div class="stat-extra">
              <el-tag type="primary" size="small">目标: 24小时</el-tag>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 审批流程选项卡 -->
    <div class="approval-tabs">
      <el-card>
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <!-- 待审批变更 -->
          <el-tab-pane label="待审批变更" name="pending">
            <div class="pending-approvals">
              <div class="filter-controls">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-select v-model="priorityFilter" placeholder="优先级筛选" style="width: 100%">
                      <el-option label="全部优先级" value="all" />
                      <el-option label="紧急" value="紧急" />
                      <el-option label="高" value="高" />
                      <el-option label="中" value="中" />
                      <el-option label="低" value="低" />
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-select v-model="riskFilter" placeholder="风险筛选" style="width: 100%">
                      <el-option label="全部风险" value="all" />
                      <el-option label="高风险" value="高" />
                      <el-option label="中风险" value="中" />
                      <el-option label="低风险" value="低" />
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-date-picker
                      v-model="dateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="6">
                    <el-button type="primary" @click="batchApproval" :disabled="selectedChanges.length === 0">
                      批量审批 ({{ selectedChanges.length }})
                    </el-button>
                  </el-col>
                </el-row>
              </div>

              <el-table
                :data="filteredPendingChanges"
                style="width: 100%; margin-top: 20px;"
                @selection-change="handleSelectionChange"
                v-loading="loading"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="id" label="变更ID" width="120" />
                <el-table-column prop="title" label="标题" min-width="200" />
                <el-table-column prop="type" label="类型" width="100">
                  <template #default="scope">
                    <el-tag size="small">{{ scope.row.type }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="priority" label="优先级" width="100">
                  <template #default="scope">
                    <el-tag :type="getPriorityType(scope.row.priority)" size="small">
                      {{ scope.row.priority }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="risk" label="风险等级" width="100">
                  <template #default="scope">
                    <el-tag :type="getRiskType(scope.row.risk)" size="small">
                      {{ scope.row.risk }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="submitter" label="申请人" width="100" />
                <el-table-column prop="submitTime" label="提交时间" width="160" />
                <el-table-column prop="implementDate" label="计划实施" width="160" />
                <el-table-column label="操作" width="200">
                  <template #default="scope">
                    <el-button type="primary" size="small" @click="reviewChange(scope.row)">
                      审批
                    </el-button>
                    <el-button type="info" size="small" @click="viewChangeDetail(scope.row)">
                      详情
                    </el-button>
                    <el-button type="warning" size="small" @click="requestMoreInfo(scope.row)">
                      补充信息
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <!-- CAB会议管理 -->
          <el-tab-pane label="CAB会议" name="meetings">
            <div class="cab-meetings">
              <div class="meeting-controls">
                <el-button type="primary" @click="scheduleCABMeeting">
                  <el-icon><Calendar /></el-icon>
                  安排会议
                </el-button>
                <el-button @click="viewMeetingHistory">
                  <el-icon><Clock /></el-icon>
                  会议历史
                </el-button>
              </div>

              <div class="upcoming-meetings">
                <h3>即将召开的会议</h3>
                <el-timeline>
                  <el-timeline-item
                    v-for="meeting in upcomingMeetings"
                    :key="meeting.id"
                    :timestamp="meeting.time"
                    :type="getMeetingType(meeting.status)"
                  >
                    <el-card class="meeting-card">
                      <div class="meeting-header">
                        <span class="meeting-title">{{ meeting.title }}</span>
                        <el-tag :type="getMeetingStatusType(meeting.status)">{{ meeting.status }}</el-tag>
                      </div>
                      <div class="meeting-details">
                        <p><strong>议题数量:</strong> {{ meeting.agenda.length }}</p>
                        <p><strong>参会人员:</strong> {{ meeting.attendees.join(', ') }}</p>
                        <p><strong>会议地点:</strong> {{ meeting.location }}</p>
                      </div>
                      <div class="meeting-actions">
                        <el-button type="primary" size="small" @click="joinMeeting(meeting)">
                          加入会议
                        </el-button>
                        <el-button type="info" size="small" @click="viewAgenda(meeting)">
                          查看议程
                        </el-button>
                        <el-button type="warning" size="small" @click="editMeeting(meeting)">
                          编辑
                        </el-button>
                      </div>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
          </el-tab-pane>

          <!-- 审批历史 -->
          <el-tab-pane label="审批历史" name="history">
            <div class="approval-history">
              <div class="history-filters">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-select v-model="historyStatusFilter" placeholder="审批结果" style="width: 100%">
                      <el-option label="全部结果" value="all" />
                      <el-option label="已批准" value="已批准" />
                      <el-option label="已拒绝" value="已拒绝" />
                      <el-option label="需补充信息" value="需补充信息" />
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-select v-model="historyApproverFilter" placeholder="审批人" style="width: 100%">
                      <el-option label="全部审批人" value="all" />
                      <el-option v-for="member in cabMembers" :key="member.id" :label="member.name" :value="member.name" />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-date-picker
                      v-model="historyDateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="4">
                    <el-button type="primary" @click="exportHistory">导出历史</el-button>
                  </el-col>
                </el-row>
              </div>

              <el-table :data="filteredApprovalHistory" style="width: 100%; margin-top: 20px;">
                <el-table-column prop="changeId" label="变更ID" width="120" />
                <el-table-column prop="changeTitle" label="变更标题" min-width="200" />
                <el-table-column prop="approver" label="审批人" width="100" />
                <el-table-column prop="result" label="审批结果" width="120">
                  <template #default="scope">
                    <el-tag :type="getApprovalResultType(scope.row.result)">{{ scope.row.result }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="approvalTime" label="审批时间" width="160" />
                <el-table-column prop="comments" label="审批意见" />
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button type="primary" size="small" text @click="viewApprovalDetail(scope.row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <!-- CAB成员管理 -->
          <el-tab-pane label="成员管理" name="members">
            <div class="member-management">
              <div class="member-controls">
                <el-button type="primary" @click="addCABMember">
                  <el-icon><UserFilled /></el-icon>
                  添加成员
                </el-button>
                <el-button @click="importMembers">
                  <el-icon><Upload /></el-icon>
                  批量导入
                </el-button>
              </div>

              <el-table :data="cabMembers" style="width: 100%; margin-top: 20px;">
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="role" label="角色" width="120">
                  <template #default="scope">
                    <el-tag :type="getRoleType(scope.row.role)">{{ scope.row.role }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="department" label="部门" width="150" />
                <el-table-column prop="email" label="邮箱" />
                <el-table-column prop="phone" label="电话" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === '在线' ? 'success' : 'info'">{{ scope.row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="lastActive" label="最后活跃" width="160" />
                <el-table-column label="操作" width="200">
                  <template #default="scope">
                    <el-button type="primary" size="small" @click="editMember(scope.row)">
                      编辑
                    </el-button>
                    <el-button type="warning" size="small" @click="toggleMemberStatus(scope.row)">
                      {{ scope.row.status === '在线' ? '设为离线' : '设为在线' }}
                    </el-button>
                    <el-button type="danger" size="small" @click="removeMember(scope.row)">
                      移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 变更审批对话框 -->
    <el-dialog v-model="showApprovalDialog" title="变更审批" width="70%" :before-close="handleApprovalClose">
      <div v-if="currentChange" class="approval-content">
        <!-- 变更信息 -->
        <div class="change-info">
          <h3>变更信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="变更ID">{{ currentChange.id }}</el-descriptions-item>
            <el-descriptions-item label="标题">{{ currentChange.title }}</el-descriptions-item>
            <el-descriptions-item label="类型">{{ currentChange.type }}</el-descriptions-item>
            <el-descriptions-item label="优先级">{{ currentChange.priority }}</el-descriptions-item>
            <el-descriptions-item label="风险等级">{{ currentChange.risk }}</el-descriptions-item>
            <el-descriptions-item label="申请人">{{ currentChange.submitter }}</el-descriptions-item>
            <el-descriptions-item label="计划实施时间" :span="2">{{ currentChange.implementDate }}</el-descriptions-item>
            <el-descriptions-item label="变更描述" :span="2">{{ currentChange.description }}</el-descriptions-item>
            <el-descriptions-item label="影响分析" :span="2">{{ currentChange.impact }}</el-descriptions-item>
            <el-descriptions-item label="回滚计划" :span="2">{{ currentChange.rollbackPlan }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 风险评估 -->
        <div class="risk-assessment" style="margin-top: 20px;">
          <h3>风险评估</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card>
                <el-statistic title="技术风险" :value="currentChange.riskAssessment?.technical || 0" suffix="/10" />
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card>
                <el-statistic title="业务风险" :value="currentChange.riskAssessment?.business || 0" suffix="/10" />
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card>
                <el-statistic title="安全风险" :value="currentChange.riskAssessment?.security || 0" suffix="/10" />
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- CAB投票 -->
        <div class="cab-voting" style="margin-top: 20px;">
          <h3>CAB成员投票</h3>
          <el-table :data="votingMembers" style="width: 100%">
            <el-table-column prop="name" label="成员" width="120" />
            <el-table-column prop="role" label="角色" width="120" />
            <el-table-column label="投票" width="200">
              <template #default="scope">
                <el-radio-group v-model="scope.row.vote" @change="updateVote(scope.row)">
                  <el-radio label="approve">批准</el-radio>
                  <el-radio label="reject">拒绝</el-radio>
                  <el-radio label="abstain">弃权</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>
            <el-table-column prop="comments" label="意见">
              <template #default="scope">
                <el-input
                  v-model="scope.row.comments"
                  placeholder="请输入审批意见"
                  @blur="updateComments(scope.row)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 审批决定 -->
        <div class="approval-decision" style="margin-top: 20px;">
          <h3>审批决定</h3>
          <el-form :model="approvalForm" label-width="120px">
            <el-form-item label="审批结果">
              <el-radio-group v-model="approvalForm.result">
                <el-radio label="approved">批准</el-radio>
                <el-radio label="rejected">拒绝</el-radio>
                <el-radio label="conditional">有条件批准</el-radio>
                <el-radio label="deferred">延期审批</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="审批意见">
              <el-input
                v-model="approvalForm.comments"
                type="textarea"
                :rows="4"
                placeholder="请输入详细的审批意见"
              />
            </el-form-item>
            <el-form-item label="条件要求" v-if="approvalForm.result === 'conditional'">
              <el-input
                v-model="approvalForm.conditions"
                type="textarea"
                :rows="3"
                placeholder="请输入批准条件"
              />
            </el-form-item>
            <el-form-item label="延期原因" v-if="approvalForm.result === 'deferred'">
              <el-input
                v-model="approvalForm.deferReason"
                type="textarea"
                :rows="3"
                placeholder="请输入延期原因"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <el-button @click="showApprovalDialog = false">取消</el-button>
        <el-button type="primary" @click="submitApproval" :loading="submitting">提交审批</el-button>
      </template>
    </el-dialog>

    <!-- CAB会议安排对话框 -->
    <el-dialog v-model="showMeetingDialog" title="安排CAB会议" width="60%">
      <el-form :model="meetingForm" :rules="meetingRules" ref="meetingFormRef" label-width="120px">
        <el-form-item label="会议标题" prop="title">
          <el-input v-model="meetingForm.title" placeholder="请输入会议标题" />
        </el-form-item>
        <el-form-item label="会议时间" prop="time">
          <el-date-picker
            v-model="meetingForm.time"
            type="datetime"
            placeholder="选择会议时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="会议地点" prop="location">
          <el-input v-model="meetingForm.location" placeholder="请输入会议地点" />
        </el-form-item>
        <el-form-item label="参会人员" prop="attendees">
          <el-select
            v-model="meetingForm.attendees"
            multiple
            placeholder="选择参会人员"
            style="width: 100%"
          >
            <el-option
              v-for="member in cabMembers"
              :key="member.id"
              :label="member.name"
              :value="member.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会议议程">
          <el-input
            v-model="meetingForm.agenda"
            type="textarea"
            :rows="4"
            placeholder="请输入会议议程"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showMeetingDialog = false">取消</el-button>
        <el-button type="primary" @click="scheduleMeeting">安排会议</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 响应式数据
const activeTab = ref('pending')
const loading = ref(false)
const submitting = ref(false)
const showApprovalDialog = ref(false)
const showMeetingDialog = ref(false)
const currentChange = ref(null)
const selectedChanges = ref([])

// 筛选条件
const priorityFilter = ref('all')
const riskFilter = ref('all')
const dateRange = ref([])
const historyStatusFilter = ref('all')
const historyApproverFilter = ref('all')
const historyDateRange = ref([])

// 统计数据
const pendingApprovals = ref(8)
const urgentApprovals = ref(2)
const monthlyApprovals = ref(45)
const approvalRate = ref(92)
const onlineMembers = ref(6)
const avgApprovalTime = ref(18)

// CAB成员数据
const cabMembers = ref([
  {
    id: 1,
    name: '张主任',
    role: 'CAB主席',
    department: 'IT部门',
    email: '<EMAIL>',
    phone: '13800138001',
    status: '在线',
    lastActive: '2025-01-30 15:30'
  },
  {
    id: 2,
    name: '李经理',
    role: '技术专家',
    department: '技术部',
    email: '<EMAIL>',
    phone: '13800138002',
    status: '在线',
    lastActive: '2025-01-30 14:20'
  },
  {
    id: 3,
    name: '王总监',
    role: '业务代表',
    department: '业务部',
    email: '<EMAIL>',
    phone: '13800138003',
    status: '离线',
    lastActive: '2025-01-30 10:15'
  },
  {
    id: 4,
    name: '赵工程师',
    role: '安全专家',
    department: '安全部',
    email: '<EMAIL>',
    phone: '13800138004',
    status: '在线',
    lastActive: '2025-01-30 16:00'
  }
])

// 待审批变更数据
const pendingChanges = ref([
  {
    id: 'CHG-2025-001',
    title: '服务器系统升级',
    type: '标准变更',
    priority: '高',
    risk: '中',
    submitter: '张工',
    submitTime: '2025-01-30 14:30',
    implementDate: '2025-02-01 02:00',
    description: '升级生产服务器操作系统到最新版本',
    impact: '可能影响系统性能，预计停机2小时',
    rollbackPlan: '如出现问题，立即回滚到原版本',
    riskAssessment: {
      technical: 6,
      business: 4,
      security: 3
    }
  },
  {
    id: 'CHG-2025-002',
    title: '网络设备配置更新',
    type: '紧急变更',
    priority: '紧急',
    risk: '高',
    submitter: '李工',
    submitTime: '2025-01-30 16:20',
    implementDate: '2025-01-31 20:00',
    description: '更新核心网络设备配置以修复安全漏洞',
    impact: '可能导致网络短暂中断',
    rollbackPlan: '保留原配置备份，可快速回滚',
    riskAssessment: {
      technical: 8,
      business: 7,
      security: 9
    }
  }
])

// 即将召开的会议
const upcomingMeetings = ref([
  {
    id: 1,
    title: '每周CAB例会',
    time: '2025-02-03 14:00',
    status: '已安排',
    location: '会议室A',
    attendees: ['张主任', '李经理', '王总监', '赵工程师'],
    agenda: ['审批本周变更请求', '讨论紧急变更流程', '风险评估报告']
  },
  {
    id: 2,
    title: '紧急变更审批会',
    time: '2025-01-31 18:00',
    status: '紧急',
    location: '在线会议',
    attendees: ['张主任', '李经理', '赵工程师'],
    agenda: ['审批网络设备配置更新', '评估安全风险']
  }
])

// 审批历史数据
const approvalHistory = ref([
  {
    changeId: 'CHG-2025-003',
    changeTitle: '数据库性能优化',
    approver: '张主任',
    result: '已批准',
    approvalTime: '2025-01-29 16:30',
    comments: '技术方案可行，同意实施'
  },
  {
    changeId: 'CHG-2025-004',
    changeTitle: '应用程序版本更新',
    approver: '李经理',
    result: '需补充信息',
    approvalTime: '2025-01-29 14:20',
    comments: '需要提供更详细的测试计划'
  }
])

// 投票成员数据
const votingMembers = ref([])

// 审批表单
const approvalForm = reactive({
  result: '',
  comments: '',
  conditions: '',
  deferReason: ''
})

// 会议表单
const meetingForm = reactive({
  title: '',
  time: null,
  location: '',
  attendees: [],
  agenda: ''
})

const meetingRules = {
  title: [{ required: true, message: '请输入会议标题', trigger: 'blur' }],
  time: [{ required: true, message: '请选择会议时间', trigger: 'change' }],
  location: [{ required: true, message: '请输入会议地点', trigger: 'blur' }],
  attendees: [{ required: true, message: '请选择参会人员', trigger: 'change' }]
}

const meetingFormRef = ref()

// 计算属性
const filteredPendingChanges = computed(() => {
  let filtered = pendingChanges.value

  if (priorityFilter.value !== 'all') {
    filtered = filtered.filter(c => c.priority === priorityFilter.value)
  }

  if (riskFilter.value !== 'all') {
    filtered = filtered.filter(c => c.risk === riskFilter.value)
  }

  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    filtered = filtered.filter(c => {
      const submitDate = new Date(c.submitTime)
      return submitDate >= start && submitDate <= end
    })
  }

  return filtered
})

const filteredApprovalHistory = computed(() => {
  let filtered = approvalHistory.value

  if (historyStatusFilter.value !== 'all') {
    filtered = filtered.filter(h => h.result === historyStatusFilter.value)
  }

  if (historyApproverFilter.value !== 'all') {
    filtered = filtered.filter(h => h.approver === historyApproverFilter.value)
  }

  if (historyDateRange.value && historyDateRange.value.length === 2) {
    const [start, end] = historyDateRange.value
    filtered = filtered.filter(h => {
      const approvalDate = new Date(h.approvalTime)
      return approvalDate >= start && approvalDate <= end
    })
  }

  return filtered
})

// 工具函数
const getPriorityType = (priority) => {
  const typeMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return typeMap[priority] || 'info'
}

const getRiskType = (risk) => {
  const typeMap = {
    '低': 'success',
    '中': 'warning',
    '高': 'danger'
  }
  return typeMap[risk] || 'info'
}

const getMeetingType = (status) => {
  const typeMap = {
    '已安排': 'primary',
    '进行中': 'success',
    '已完成': 'info',
    '紧急': 'danger'
  }
  return typeMap[status] || 'primary'
}

const getMeetingStatusType = (status) => {
  const typeMap = {
    '已安排': 'primary',
    '进行中': 'success',
    '已完成': 'info',
    '紧急': 'danger'
  }
  return typeMap[status] || 'primary'
}

const getApprovalResultType = (result) => {
  const typeMap = {
    '已批准': 'success',
    '已拒绝': 'danger',
    '需补充信息': 'warning',
    '有条件批准': 'warning',
    '延期审批': 'info'
  }
  return typeMap[result] || 'info'
}

const getRoleType = (role) => {
  const typeMap = {
    'CAB主席': 'danger',
    '技术专家': 'primary',
    '业务代表': 'success',
    '安全专家': 'warning'
  }
  return typeMap[role] || 'info'
}

// 事件处理函数
const createCABMeeting = () => {
  showMeetingDialog.value = true
  // 重置表单
  Object.keys(meetingForm).forEach(key => {
    if (key === 'attendees') {
      meetingForm[key] = []
    } else if (key === 'time') {
      meetingForm[key] = null
    } else {
      meetingForm[key] = ''
    }
  })
}

const exportApprovals = () => {
  ElMessage.success('正在导出审批记录...')
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName
  ElMessage.info(`切换到${getTabName(tabName)}`)
}

const getTabName = (tabName) => {
  const nameMap = {
    'pending': '待审批变更',
    'meetings': 'CAB会议',
    'history': '审批历史',
    'members': '成员管理'
  }
  return nameMap[tabName] || tabName
}

const handleSelectionChange = (selection) => {
  selectedChanges.value = selection
}

const batchApproval = () => {
  if (selectedChanges.value.length === 0) {
    ElMessage.warning('请选择要审批的变更')
    return
  }

  ElMessageBox.confirm(
    `确定要批量审批 ${selectedChanges.value.length} 个变更吗？`,
    '批量审批确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('批量审批已提交')
    selectedChanges.value = []
  }).catch(() => {
    ElMessage.info('已取消批量审批')
  })
}

const reviewChange = (change) => {
  currentChange.value = change

  // 初始化投票成员
  votingMembers.value = cabMembers.value.map(member => ({
    ...member,
    vote: '',
    comments: ''
  }))

  // 重置审批表单
  Object.keys(approvalForm).forEach(key => {
    approvalForm[key] = ''
  })

  showApprovalDialog.value = true
}

const viewChangeDetail = (change) => {
  router.push(`/change/detail/${change.id}`)
}

const requestMoreInfo = (change) => {
  ElMessageBox.prompt('请输入需要补充的信息', '请求补充信息', {
    confirmButtonText: '发送',
    cancelButtonText: '取消',
    inputPlaceholder: '请详细说明需要补充的信息'
  }).then(({ value }) => {
    ElMessage.success('补充信息请求已发送')
    // 这里可以发送通知给申请人
  }).catch(() => {
    ElMessage.info('已取消发送')
  })
}

const updateVote = (member) => {
  ElMessage.success(`${member.name} 的投票已更新`)
}

const updateComments = (member) => {
  // 自动保存意见
}

const submitApproval = () => {
  if (!approvalForm.result) {
    ElMessage.warning('请选择审批结果')
    return
  }

  if (!approvalForm.comments) {
    ElMessage.warning('请输入审批意见')
    return
  }

  submitting.value = true

  // 模拟提交过程
  setTimeout(() => {
    submitting.value = false
    showApprovalDialog.value = false

    // 更新变更状态
    const index = pendingChanges.value.findIndex(c => c.id === currentChange.value.id)
    if (index > -1) {
      pendingChanges.value.splice(index, 1)
    }

    // 添加到审批历史
    approvalHistory.value.unshift({
      changeId: currentChange.value.id,
      changeTitle: currentChange.value.title,
      approver: '当前用户',
      result: getApprovalResultText(approvalForm.result),
      approvalTime: new Date().toLocaleString(),
      comments: approvalForm.comments
    })

    ElMessage.success('审批已提交')

    // 更新统计数据
    pendingApprovals.value--
    monthlyApprovals.value++
  }, 2000)
}

const getApprovalResultText = (result) => {
  const textMap = {
    'approved': '已批准',
    'rejected': '已拒绝',
    'conditional': '有条件批准',
    'deferred': '延期审批'
  }
  return textMap[result] || result
}

const handleApprovalClose = (done) => {
  if (approvalForm.result || approvalForm.comments) {
    ElMessageBox.confirm('审批内容将会丢失，确定要关闭吗？')
      .then(() => done())
      .catch(() => {})
  } else {
    done()
  }
}

// CAB会议相关函数
const scheduleCABMeeting = () => {
  showMeetingDialog.value = true
}

const viewMeetingHistory = () => {
  router.push('/change/meeting-history')
}

const joinMeeting = (meeting) => {
  ElMessage.success(`正在加入会议: ${meeting.title}`)
  // 这里可以集成视频会议系统
}

const viewAgenda = (meeting) => {
  ElMessageBox.alert(
    meeting.agenda.join('\n'),
    `会议议程 - ${meeting.title}`,
    {
      confirmButtonText: '确定'
    }
  )
}

const editMeeting = (meeting) => {
  // 填充表单数据
  meetingForm.title = meeting.title
  meetingForm.time = new Date(meeting.time)
  meetingForm.location = meeting.location
  meetingForm.attendees = [...meeting.attendees]
  meetingForm.agenda = meeting.agenda.join('\n')

  showMeetingDialog.value = true
}

const scheduleMeeting = () => {
  meetingFormRef.value.validate((valid) => {
    if (valid) {
      ElMessage.success('会议已安排')
      showMeetingDialog.value = false

      // 添加到即将召开的会议列表
      upcomingMeetings.value.push({
        id: Date.now(),
        title: meetingForm.title,
        time: meetingForm.time.toLocaleString(),
        status: '已安排',
        location: meetingForm.location,
        attendees: [...meetingForm.attendees],
        agenda: meetingForm.agenda.split('\n').filter(item => item.trim())
      })
    } else {
      ElMessage.warning('请填写完整的会议信息')
    }
  })
}

// 审批历史相关函数
const exportHistory = () => {
  ElMessage.success('正在导出审批历史...')
}

const viewApprovalDetail = (approval) => {
  ElMessageBox.alert(
    `变更ID: ${approval.changeId}\n变更标题: ${approval.changeTitle}\n审批人: ${approval.approver}\n审批结果: ${approval.result}\n审批时间: ${approval.approvalTime}\n审批意见: ${approval.comments}`,
    '审批详情',
    {
      confirmButtonText: '确定'
    }
  )
}

// 成员管理相关函数
const addCABMember = () => {
  ElMessageBox.prompt('请输入新成员姓名', '添加CAB成员', {
    confirmButtonText: '添加',
    cancelButtonText: '取消',
    inputPlaceholder: '请输入姓名'
  }).then(({ value }) => {
    if (value) {
      cabMembers.value.push({
        id: Date.now(),
        name: value,
        role: '成员',
        department: '待分配',
        email: '',
        phone: '',
        status: '离线',
        lastActive: new Date().toLocaleString()
      })
      ElMessage.success('成员添加成功')
    }
  }).catch(() => {
    ElMessage.info('已取消添加')
  })
}

const importMembers = () => {
  ElMessage.success('批量导入功能开发中...')
}

const editMember = (member) => {
  ElMessage.info(`编辑成员: ${member.name}`)
  // 这里可以打开编辑对话框
}

const toggleMemberStatus = (member) => {
  member.status = member.status === '在线' ? '离线' : '在线'
  member.lastActive = new Date().toLocaleString()
  ElMessage.success(`${member.name} 状态已更新为${member.status}`)

  // 更新在线成员统计
  onlineMembers.value = cabMembers.value.filter(m => m.status === '在线').length
}

const removeMember = (member) => {
  ElMessageBox.confirm(
    `确定要移除成员 "${member.name}" 吗？`,
    '确认移除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = cabMembers.value.findIndex(m => m.id === member.id)
    if (index > -1) {
      cabMembers.value.splice(index, 1)
      ElMessage.success('成员已移除')

      // 更新在线成员统计
      onlineMembers.value = cabMembers.value.filter(m => m.status === '在线').length
    }
  }).catch(() => {
    ElMessage.info('已取消移除')
  })
}

// 生命周期钩子
onMounted(() => {
  // 检查是否有指定的变更ID需要审批
  const changeId = route.query.changeId
  if (changeId) {
    const change = pendingChanges.value.find(c => c.id === changeId)
    if (change) {
      reviewChange(change)
    }
  }

  ElMessage.success('CAB审批系统加载完成')
})
</script>

<style scoped>
.cab-approval {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-info h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-info p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.cab-stats,
.approval-tabs {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 16px;
}

.stat-extra {
  margin-top: 8px;
}

/* 筛选控件样式 */
.filter-controls,
.history-filters,
.member-controls,
.meeting-controls {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

/* 会议相关样式 */
.upcoming-meetings h3 {
  color: #333;
  margin-bottom: 16px;
}

.meeting-card {
  margin-bottom: 16px;
}

.meeting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.meeting-title {
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

.meeting-details p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.meeting-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

/* 审批对话框样式 */
.approval-content {
  max-height: 70vh;
  overflow-y: auto;
}

.change-info h3,
.risk-assessment h3,
.cab-voting h3,
.approval-decision h3 {
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.risk-assessment .el-card {
  text-align: center;
}

.cab-voting .el-table {
  margin-top: 16px;
}

.approval-decision .el-form {
  margin-top: 16px;
}

/* 成员管理样式 */
.member-management .el-table {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .filter-controls .el-row,
  .history-filters .el-row {
    flex-direction: column;
  }

  .filter-controls .el-col,
  .history-filters .el-col {
    margin-bottom: 12px;
  }

  .meeting-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .meeting-actions {
    flex-wrap: wrap;
  }

  .approval-content {
    max-height: 60vh;
  }

  .cab-voting .el-table {
    font-size: 12px;
  }

  .cab-voting .el-radio-group {
    flex-direction: column;
  }

  .cab-voting .el-radio {
    margin-bottom: 4px;
  }
}

/* 表格样式优化 */
.el-table .el-table__cell {
  padding: 8px 0;
}

.el-table .cell {
  padding: 0 8px;
}

/* 标签样式 */
.el-tag {
  margin: 2px;
}

/* 按钮组样式 */
.el-button-group .el-button {
  margin: 0;
}

/* 时间轴样式 */
.el-timeline {
  padding-left: 0;
}

.el-timeline-item__content {
  padding-left: 20px;
}

/* 统计卡片样式 */
.stat-card .el-statistic__content {
  text-align: center;
}

.stat-card .el-statistic__number {
  font-size: 24px;
  font-weight: bold;
}

.stat-card .el-statistic__title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

/* 对话框样式 */
.el-dialog__body {
  padding: 20px;
}

.el-dialog__header {
  padding: 20px 20px 10px;
}

.el-dialog__footer {
  padding: 10px 20px 20px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
}

/* 描述列表样式 */
.el-descriptions {
  margin-top: 16px;
}

.el-descriptions__label {
  font-weight: 500;
}

/* 进度条样式 */
.el-progress {
  margin-top: 8px;
}

/* 选择器样式 */
.el-select {
  width: 100%;
}

/* 日期选择器样式 */
.el-date-editor {
  width: 100%;
}

/* 输入框样式 */
.el-input__inner,
.el-textarea__inner {
  border-radius: 6px;
}

/* 卡片样式 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.el-card__body {
  padding: 20px;
}
</style>
