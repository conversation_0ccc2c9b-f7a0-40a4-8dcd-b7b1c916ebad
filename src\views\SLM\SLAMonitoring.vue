<template>
  <div class="sla-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>SLA监控</h2>
          <p>实时监控服务级别协议执行情况</p>
        </div>
        <div class="action-section">
          <el-select v-model="selectedPeriod" @change="loadMonitoringData">
            <el-option label="近7天" value="7d" />
            <el-option label="近30天" value="30d" />
            <el-option label="近90天" value="90d" />
          </el-select>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新
          </el-button>
          <el-button :icon="Setting" @click="showSettingsDialog = true">
            监控设置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 告警概览 -->
    <div class="alerts-overview">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8">
          <el-card class="alert-card critical">
            <div class="alert-content">
              <div class="alert-icon">
                <el-icon :size="32">
                  <CircleCloseFilled />
                </el-icon>
              </div>
              <div class="alert-info">
                <div class="alert-count">{{ alertStats.critical }}</div>
                <div class="alert-label">严重告警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8">
          <el-card class="alert-card warning">
            <div class="alert-content">
              <div class="alert-icon">
                <el-icon :size="32">
                  <Warning />
                </el-icon>
              </div>
              <div class="alert-info">
                <div class="alert-count">{{ alertStats.warning }}</div>
                <div class="alert-label">警告告警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8">
          <el-card class="alert-card info">
            <div class="alert-content">
              <div class="alert-icon">
                <el-icon :size="32">
                  <InfoFilled />
                </el-icon>
              </div>
              <div class="alert-info">
                <div class="alert-count">{{ alertStats.info }}</div>
                <div class="alert-label">信息告警</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时监控面板 -->
    <div class="monitoring-panel">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>实时监控面板</span>
            <div class="header-actions">
              <el-switch
                v-model="autoRefresh"
                active-text="自动刷新"
                @change="toggleAutoRefresh"
              />
            </div>
          </div>
        </template>

        <div class="monitoring-grid">
          <div
            v-for="monitor in monitoringData"
            :key="monitor.agreementId"
            class="monitor-item"
          >
            <div class="monitor-header">
              <div class="monitor-title">{{ monitor.serviceName }}</div>
              <el-tag :type="getAgreementTypeColor(monitor.type)">{{ monitor.type }}</el-tag>
            </div>
            <div class="monitor-subtitle">{{ monitor.agreementName }}</div>

            <div class="kpi-monitors">
              <div
                v-for="kpi in monitor.kpis"
                :key="kpi.name"
                class="kpi-monitor"
                :class="getKPIStatusClass(kpi.status)"
              >
                <div class="kpi-header">
                  <span class="kpi-name">{{ kpi.name }}</span>
                  <span class="kpi-trend">
                    <el-icon v-if="kpi.trend === 'up'" color="#67c23a">
                      <ArrowUp />
                    </el-icon>
                    <el-icon v-else-if="kpi.trend === 'down'" color="#f56c6c">
                      <ArrowDown />
                    </el-icon>
                    <el-icon v-else color="#909399">
                      <Minus />
                    </el-icon>
                  </span>
                </div>

                <div class="kpi-value">
                  <span class="current-value">{{ kpi.current }}{{ kpi.unit }}</span>
                  <span class="target-value">/ {{ kpi.target }}{{ kpi.unit }}</span>
                </div>

                <div class="kpi-progress">
                  <el-progress
                    :percentage="getKPIPercentage(kpi)"
                    :status="getProgressStatus(kpi.status)"
                    :show-text="false"
                    :stroke-width="6"
                  />
                </div>

                <div class="kpi-chart">
                  <div
                    class="mini-chart"
                    :ref="el => setChartRef(el, `${monitor.agreementId}-${kpi.name}`)"
                  ></div>
                </div>

                <!-- 倒计时或状态显示 -->
                <div class="kpi-status">
                  <span v-if="kpi.status === 'at_risk'" class="countdown">
                    <el-icon><Timer /></el-icon>
                    距离超时: {{ getCountdown(kpi) }}
                  </span>
                  <span v-else-if="kpi.status === 'breached'" class="breached">
                    <el-icon><CircleCloseFilled /></el-icon>
                    已超时
                  </span>
                  <span v-else class="normal">
                    <el-icon><SuccessFilled /></el-icon>
                    正常
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="monitoringData.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无监控数据">
            <el-button type="primary" @click="$router.push('/slm/agreements')">
              配置协议
            </el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 告警列表 -->
    <div class="alerts-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>告警列表</span>
            <div class="header-actions">
              <el-select v-model="alertFilter" @change="loadAlerts" style="width: 120px">
                <el-option label="全部" value="" />
                <el-option label="严重" value="critical" />
                <el-option label="警告" value="warning" />
                <el-option label="信息" value="info" />
              </el-select>
            </div>
          </div>
        </template>

        <el-table :data="alerts" v-loading="loading" stripe>
          <el-table-column prop="level" label="级别" width="80">
            <template #default="{ row }">
              <el-tag :type="getAlertTypeColor(row.level)">
                {{ getAlertLevelText(row.level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="serviceName" label="服务" width="120" />
          <el-table-column prop="title" label="告警标题" min-width="200" />
          <el-table-column prop="description" label="描述" min-width="250" />
          <el-table-column prop="currentValue" label="当前值" width="100">
            <template #default="{ row }">
              {{ row.currentValue }}{{ getKPIUnit(row.kpiType) }}
            </template>
          </el-table-column>
          <el-table-column prop="targetValue" label="目标值" width="100">
            <template #default="{ row }">
              {{ row.targetValue }}{{ getKPIUnit(row.kpiType) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'danger' : 'success'">
                {{ row.status === 'active' ? '活跃' : '已解决' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="160" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewAlertDetail(row)">
                查看详情
              </el-button>
              <el-button
                v-if="row.status === 'active'"
                type="text"
                size="small"
                @click="resolveAlert(row)"
              >
                标记解决
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 监控设置对话框 -->
    <el-dialog
      v-model="showSettingsDialog"
      title="监控设置"
      width="600px"
    >
      <el-form :model="settingsForm" label-width="120px">
        <el-form-item label="自动刷新间隔">
          <el-select v-model="settingsForm.refreshInterval">
            <el-option label="30秒" :value="30" />
            <el-option label="1分钟" :value="60" />
            <el-option label="5分钟" :value="300" />
            <el-option label="10分钟" :value="600" />
          </el-select>
        </el-form-item>
        <el-form-item label="告警阈值">
          <el-input-number
            v-model="settingsForm.alertThreshold"
            :min="1"
            :max="100"
            :precision="0"
          />
          <span style="margin-left: 8px; color: #666;">%</span>
        </el-form-item>
        <el-form-item label="启用邮件通知">
          <el-switch v-model="settingsForm.emailNotification" />
        </el-form-item>
        <el-form-item label="启用短信通知">
          <el-switch v-model="settingsForm.smsNotification" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSettingsDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 告警详情对话框 -->
    <el-dialog
      v-model="showAlertDialog"
      :title="selectedAlert?.title"
      width="700px"
    >
      <div v-if="selectedAlert" class="alert-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="告警级别">
            <el-tag :type="getAlertTypeColor(selectedAlert.level)">
              {{ getAlertLevelText(selectedAlert.level) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="服务名称">{{ selectedAlert.serviceName }}</el-descriptions-item>
          <el-descriptions-item label="协议ID">{{ selectedAlert.agreementId }}</el-descriptions-item>
          <el-descriptions-item label="KPI类型">{{ getKPITypeName(selectedAlert.kpiType) }}</el-descriptions-item>
          <el-descriptions-item label="当前值">
            {{ selectedAlert.currentValue }}{{ getKPIUnit(selectedAlert.kpiType) }}
          </el-descriptions-item>
          <el-descriptions-item label="目标值">
            {{ selectedAlert.targetValue }}{{ getKPIUnit(selectedAlert.kpiType) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedAlert.status === 'active' ? 'danger' : 'success'">
              {{ selectedAlert.status === 'active' ? '活跃' : '已解决' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedAlert.createdAt }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="alert-description">
          <h4>告警描述</h4>
          <p>{{ selectedAlert.description }}</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAlertDialog = false">关闭</el-button>
          <el-button
            v-if="selectedAlert?.status === 'active'"
            type="primary"
            @click="resolveAlert(selectedAlert)"
          >
            标记解决
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Setting, CircleCloseFilled, Warning, InfoFilled, ArrowUp, ArrowDown,
  Minus, Timer, SuccessFilled
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getSLAMonitoring,
  getSLAAlerts,
  kpiTypes
} from '@/api/slmApi.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const selectedPeriod = ref('7d')
const alertFilter = ref('')
const showSettingsDialog = ref(false)
const showAlertDialog = ref(false)
const selectedAlert = ref(null)

// 监控数据
const monitoringData = ref([])
const alerts = ref([])
const alertStats = reactive({
  critical: 0,
  warning: 0,
  info: 0
})

// 设置表单
const settingsForm = reactive({
  refreshInterval: 60,
  alertThreshold: 95,
  emailNotification: true,
  smsNotification: false
})

// 图表引用
const chartRefs = ref(new Map())
let refreshTimer = null

// 加载监控数据
const loadMonitoringData = async () => {
  loading.value = true
  try {
    const response = await getSLAMonitoring({ period: selectedPeriod.value })
    if (response.success) {
      monitoringData.value = response.data

      // 延迟初始化图表
      nextTick(() => {
        initMiniCharts()
      })
    }
  } catch (error) {
    ElMessage.error('加载监控数据失败')
    console.error('Load monitoring data error:', error)
  } finally {
    loading.value = false
  }
}

// 加载告警数据
const loadAlerts = async () => {
  try {
    const response = await getSLAAlerts({ level: alertFilter.value, status: 'active' })
    if (response.success) {
      alerts.value = response.data

      // 计算告警统计
      alertStats.critical = response.data.filter(a => a.level === 'critical').length
      alertStats.warning = response.data.filter(a => a.level === 'warning').length
      alertStats.info = response.data.filter(a => a.level === 'info').length
    }
  } catch (error) {
    ElMessage.error('加载告警数据失败')
    console.error('Load alerts error:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadMonitoringData(),
    loadAlerts()
  ])
}

// 设置图表引用
const setChartRef = (el, key) => {
  if (el) {
    chartRefs.value.set(key, el)
  }
}

// 初始化迷你图表
const initMiniCharts = () => {
  monitoringData.value.forEach(monitor => {
    monitor.kpis.forEach(kpi => {
      const key = `${monitor.agreementId}-${kpi.name}`
      const chartEl = chartRefs.value.get(key)

      if (!chartEl || !kpi.history) return

      const chart = echarts.init(chartEl)

      const option = {
        grid: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        },
        xAxis: {
          type: 'category',
          data: kpi.history.map(item => item.date),
          show: false
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [
          {
            type: 'line',
            data: kpi.history.map(item => item.value),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: kpi.status === 'met' ? '#67c23a' : '#f56c6c',
              width: 2
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: kpi.status === 'met' ? 'rgba(103, 194, 58, 0.3)' : 'rgba(245, 108, 108, 0.3)'
                  },
                  {
                    offset: 1,
                    color: kpi.status === 'met' ? 'rgba(103, 194, 58, 0.1)' : 'rgba(245, 108, 108, 0.1)'
                  }
                ]
              }
            }
          }
        ]
      }

      chart.setOption(option)

      // 响应式调整
      const resizeObserver = new ResizeObserver(() => {
        chart.resize()
      })
      resizeObserver.observe(chartEl)
    })
  })
}

// 获取协议类型颜色
const getAgreementTypeColor = (type) => {
  switch (type) {
    case 'SLA':
      return 'primary'
    case 'OLA':
      return 'success'
    case 'UC':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取KPI状态样式类
const getKPIStatusClass = (status) => {
  switch (status) {
    case 'met':
      return 'status-normal'
    case 'at_risk':
      return 'status-warning'
    case 'breached':
      return 'status-critical'
    default:
      return 'status-unknown'
  }
}

// 获取KPI百分比
const getKPIPercentage = (kpi) => {
  if (kpi.type === 'response_time' || kpi.type === 'resolution_time') {
    // 对于时间类指标，值越小越好
    return Math.max(0, Math.min(100, (kpi.target / kpi.current) * 100))
  } else {
    // 对于其他指标，值越大越好
    return Math.max(0, Math.min(100, (kpi.current / kpi.target) * 100))
  }
}

// 获取进度条状态
const getProgressStatus = (status) => {
  switch (status) {
    case 'met':
      return 'success'
    case 'at_risk':
      return 'warning'
    case 'breached':
      return 'exception'
    default:
      return ''
  }
}

// 获取倒计时
const getCountdown = (kpi) => {
  // 模拟倒计时计算
  const hours = Math.floor(Math.random() * 24)
  const minutes = Math.floor(Math.random() * 60)
  return `${hours}小时${minutes}分钟`
}

// 获取告警类型颜色
const getAlertTypeColor = (level) => {
  switch (level) {
    case 'critical':
      return 'danger'
    case 'warning':
      return 'warning'
    case 'info':
      return 'info'
    default:
      return 'info'
  }
}

// 获取告警级别文本
const getAlertLevelText = (level) => {
  switch (level) {
    case 'critical':
      return '严重'
    case 'warning':
      return '警告'
    case 'info':
      return '信息'
    default:
      return '未知'
  }
}

// 获取KPI单位
const getKPIUnit = (kpiType) => {
  const kpi = kpiTypes.find(k => k.key === kpiType)
  return kpi?.unit || ''
}

// 获取KPI类型名称
const getKPITypeName = (kpiType) => {
  const kpi = kpiTypes.find(k => k.key === kpiType)
  return kpi?.name || kpiType
}

// 查看告警详情
const viewAlertDetail = (alert) => {
  selectedAlert.value = alert
  showAlertDialog.value = true
}

// 解决告警
const resolveAlert = async (alert) => {
  try {
    await ElMessageBox.confirm(
      `确定要标记告警 "${alert.title}" 为已解决吗？`,
      '解决确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟解决告警
    alert.status = 'resolved'
    ElMessage.success('告警已标记为解决')

    // 关闭详情对话框
    showAlertDialog.value = false

    // 刷新告警列表
    loadAlerts()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('解决告警失败')
    console.error('Resolve alert error:', error)
  }
}

// 切换自动刷新
const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    refreshTimer = setInterval(() => {
      refreshData()
    }, settingsForm.refreshInterval * 1000)
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

// 保存设置
const saveSettings = () => {
  ElMessage.success('设置保存成功')
  showSettingsDialog.value = false

  // 如果自动刷新开启，重新设置定时器
  if (autoRefresh.value) {
    toggleAutoRefresh(false)
    toggleAutoRefresh(true)
  }
}

// 生命周期
onMounted(async () => {
  await refreshData()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.sla-monitoring {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 告警概览 */
.alerts-overview {
  margin-bottom: 20px;
}

.alert-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alert-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.alert-card.critical {
  border-left: 4px solid #f56c6c;
}

.alert-card.warning {
  border-left: 4px solid #e6a23c;
}

.alert-card.info {
  border-left: 4px solid #409eff;
}

.alert-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 8px;
}

.alert-icon {
  margin-right: 16px;
}

.alert-card.critical .alert-icon {
  color: #f56c6c;
}

.alert-card.warning .alert-icon {
  color: #e6a23c;
}

.alert-card.info .alert-icon {
  color: #409eff;
}

.alert-info {
  flex: 1;
}

.alert-count {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.alert-label {
  font-size: 14px;
  color: #666;
}

/* 监控面板 */
.monitoring-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  min-height: 300px;
}

.monitor-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.monitor-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.monitor-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.kpi-monitors {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.kpi-monitor {
  padding: 12px;
  border-radius: 6px;
  background-color: white;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.kpi-monitor.status-normal {
  border-left: 4px solid #67c23a;
}

.kpi-monitor.status-warning {
  border-left: 4px solid #e6a23c;
}

.kpi-monitor.status-critical {
  border-left: 4px solid #f56c6c;
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.kpi-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.kpi-value {
  margin-bottom: 8px;
}

.current-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.target-value {
  font-size: 14px;
  color: #999;
  margin-left: 4px;
}

.kpi-progress {
  margin-bottom: 8px;
}

.mini-chart {
  height: 40px;
  width: 100%;
  margin-bottom: 8px;
}

.kpi-status {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.kpi-status .countdown {
  color: #e6a23c;
}

.kpi-status .breached {
  color: #f56c6c;
}

.kpi-status .normal {
  color: #67c23a;
}

.kpi-status .el-icon {
  margin-right: 4px;
}

/* 告警区域 */
.alerts-section {
  margin-bottom: 20px;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 告警详情 */
.alert-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.alert-description {
  margin-top: 20px;
}

.alert-description h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.alert-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sla-monitoring {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
    flex-wrap: wrap;
  }

  .monitoring-grid {
    grid-template-columns: 1fr;
  }

  .alert-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .alert-icon {
    margin-right: 0;
  }
}

/* 对话框样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-descriptions-item__label) {
  font-weight: 500;
}

:deep(.el-table .cell) {
  padding: 8px;
}

:deep(.el-progress-bar__outer) {
  border-radius: 3px;
}
</style>
