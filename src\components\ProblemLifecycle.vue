<template>
  <div class="problem-lifecycle">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>问题生命周期管理</span>
          <el-button type="primary" size="small" @click="showWorkflowDialog = true">
            <el-icon><Setting /></el-icon>
            配置流程
          </el-button>
        </div>
      </template>
      
      <!-- 当前问题状态 -->
      <div v-if="currentProblem" class="current-problem">
        <div class="problem-header">
          <h3>{{ currentProblem.title }}</h3>
          <el-tag :type="getStatusColor(currentProblem.status)" size="large">
            {{ currentProblem.status }}
          </el-tag>
        </div>
        
        <!-- 状态流程图 -->
        <div class="status-flow">
          <el-steps :active="currentProblem.stepIndex" finish-status="success" align-center>
            <el-step
              v-for="(step, index) in problemSteps"
              :key="step.key"
              :title="step.title"
              :description="step.description"
              :status="getStepStatus(index, currentProblem.stepIndex)"
            >
              <template #icon>
                <el-icon :color="getStepIconColor(index, currentProblem.stepIndex)">
                  <component :is="step.icon" />
                </el-icon>
              </template>
            </el-step>
          </el-steps>
        </div>
        
        <!-- 当前步骤详情 -->
        <div class="current-step-details">
          <el-card>
            <template #header>
              <div class="step-header">
                <span>当前步骤: {{ problemSteps[currentProblem.stepIndex]?.title }}</span>
                <el-tag type="info" size="small">
                  进度: {{ Math.round((currentProblem.stepIndex + 1) / problemSteps.length * 100) }}%
                </el-tag>
              </div>
            </template>
            
            <div class="step-content">
              <div class="step-info">
                <p>{{ problemSteps[currentProblem.stepIndex]?.description }}</p>
                <div class="step-requirements">
                  <h4>完成要求：</h4>
                  <ul>
                    <li v-for="req in problemSteps[currentProblem.stepIndex]?.requirements" :key="req">
                      {{ req }}
                    </li>
                  </ul>
                </div>
              </div>
              
              <div class="step-actions">
                <el-button 
                  @click="prevStep" 
                  :disabled="currentProblem.stepIndex === 0"
                >
                  上一步
                </el-button>
                <el-button 
                  type="primary" 
                  @click="nextStep" 
                  :disabled="currentProblem.stepIndex === problemSteps.length - 1"
                >
                  下一步
                </el-button>
                <el-button 
                  type="success" 
                  @click="completeProblem"
                  v-if="currentProblem.stepIndex === problemSteps.length - 1"
                >
                  完成问题
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
        
        <!-- 状态变更历史 -->
        <div class="status-history">
          <el-card>
            <template #header>
              <span>状态变更历史</span>
            </template>
            
            <el-timeline>
              <el-timeline-item
                v-for="(history, index) in currentProblem.statusHistory"
                :key="index"
                :timestamp="history.timestamp"
                :type="getHistoryType(history.action)"
                :icon="getHistoryIcon(history.action)"
              >
                <div class="history-item">
                  <div class="history-header">
                    <span class="history-action">{{ history.action }}</span>
                    <span class="history-user">{{ history.user }}</span>
                  </div>
                  <div class="history-description">{{ history.description }}</div>
                  <div v-if="history.comment" class="history-comment">
                    <strong>备注：</strong>{{ history.comment }}
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </div>
      </div>
      
      <!-- 问题列表 -->
      <div v-else class="problem-list">
        <div class="list-header">
          <h3>问题列表</h3>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索问题..."
            prefix-icon="Search"
            size="small"
            style="width: 200px;"
            @input="searchProblems"
          />
        </div>
        
        <el-table :data="filteredProblems" style="width: 100%" @row-click="selectProblem">
          <el-table-column prop="id" label="问题ID" width="120" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="处理人" width="100" />
          <el-table-column prop="progress" label="进度" width="120">
            <template #default="scope">
              <el-progress :percentage="scope.row.progress" :show-text="false" />
              <span style="margin-left: 8px;">{{ scope.row.progress }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button type="primary" size="small" text @click.stop="selectProblem(scope.row)">
                管理
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <!-- 工作流配置对话框 -->
    <el-dialog v-model="showWorkflowDialog" title="问题生命周期配置" width="70%">
      <div class="workflow-config">
        <el-tabs v-model="activeConfigTab">
          <el-tab-pane label="流程步骤" name="steps">
            <div class="steps-config">
              <div class="config-header">
                <h4>配置问题处理流程</h4>
                <el-button type="primary" size="small" @click="addStep">
                  <el-icon><Plus /></el-icon>
                  添加步骤
                </el-button>
              </div>
              
              <el-table :data="problemSteps" style="width: 100%">
                <el-table-column prop="title" label="步骤名称" />
                <el-table-column prop="description" label="描述" />
                <el-table-column prop="estimatedTime" label="预计时间" width="120" />
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button type="primary" size="small" text @click="editStep(scope.row)">
                      编辑
                    </el-button>
                    <el-button type="danger" size="small" text @click="deleteStep(scope.row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="状态转换" name="transitions">
            <div class="transitions-config">
              <h4>状态转换规则</h4>
              <div class="transition-matrix">
                <el-table :data="statusTransitions" style="width: 100%">
                  <el-table-column prop="from" label="从状态" width="120" />
                  <el-table-column prop="to" label="到状态" width="120" />
                  <el-table-column prop="condition" label="转换条件" />
                  <el-table-column prop="requiredRole" label="所需角色" width="120" />
                  <el-table-column label="操作" width="100">
                    <template #default="scope">
                      <el-button type="primary" size="small" text @click="editTransition(scope.row)">
                        编辑
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="通知设置" name="notifications">
            <div class="notifications-config">
              <h4>状态变更通知</h4>
              <el-form :model="notificationSettings" label-width="120px">
                <el-form-item label="邮件通知">
                  <el-switch v-model="notificationSettings.email" />
                  <span class="form-help">状态变更时发送邮件通知</span>
                </el-form-item>
                <el-form-item label="短信通知">
                  <el-switch v-model="notificationSettings.sms" />
                  <span class="form-help">紧急状态变更时发送短信</span>
                </el-form-item>
                <el-form-item label="系统通知">
                  <el-switch v-model="notificationSettings.system" />
                  <span class="form-help">在系统内显示通知消息</span>
                </el-form-item>
                <el-form-item label="通知角色">
                  <el-checkbox-group v-model="notificationSettings.roles">
                    <el-checkbox label="问题负责人">问题负责人</el-checkbox>
                    <el-checkbox label="团队经理">团队经理</el-checkbox>
                    <el-checkbox label="相关干系人">相关干系人</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <el-button @click="showWorkflowDialog = false">取消</el-button>
        <el-button type="primary" @click="saveWorkflowConfig">保存配置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
const props = defineProps({
  problemId: {
    type: String,
    default: ''
  }
})

// 响应式数据
const showWorkflowDialog = ref(false)
const activeConfigTab = ref('steps')
const searchKeyword = ref('')
const currentProblem = ref(null)

// 问题步骤定义
const problemSteps = ref([
  {
    key: 'identification',
    title: '问题识别',
    description: '识别和记录问题，收集初始信息',
    icon: 'Search',
    estimatedTime: '1小时',
    requirements: [
      '完成问题描述',
      '确定影响范围',
      '设置优先级'
    ]
  },
  {
    key: 'investigation',
    title: '调查分析',
    description: '深入调查问题原因，收集相关证据',
    icon: 'View',
    estimatedTime: '4小时',
    requirements: [
      '收集相关日志',
      '分析错误模式',
      '确定可能原因'
    ]
  },
  {
    key: 'diagnosis',
    title: '根因诊断',
    description: '确定问题的根本原因',
    icon: 'MagicStick',
    estimatedTime: '2小时',
    requirements: [
      '完成根因分析',
      '验证根因假设',
      '记录分析结果'
    ]
  },
  {
    key: 'solution',
    title: '解决方案',
    description: '制定和实施解决方案',
    icon: 'Tools',
    estimatedTime: '6小时',
    requirements: [
      '制定临时解决方案',
      '制定永久解决方案',
      '测试解决方案'
    ]
  },
  {
    key: 'closure',
    title: '问题关闭',
    description: '验证解决效果，关闭问题',
    icon: 'CircleCheck',
    estimatedTime: '1小时',
    requirements: [
      '验证解决效果',
      '更新知识库',
      '完成问题总结'
    ]
  }
])

// 问题数据
const problems = ref([
  {
    id: 'PRB-2025-001',
    title: '服务器频繁重启问题',
    status: '调查分析',
    priority: '高',
    assignee: '张工',
    progress: 40,
    stepIndex: 1,
    createTime: '2025-01-30 14:30',
    statusHistory: [
      {
        timestamp: '2025-01-30 14:30',
        action: '问题创建',
        user: '张工',
        description: '创建问题记录',
        comment: '服务器出现频繁重启现象'
      },
      {
        timestamp: '2025-01-30 15:00',
        action: '开始调查',
        user: '张工',
        description: '开始问题调查分析',
        comment: '收集相关日志和监控数据'
      }
    ]
  },
  {
    id: 'PRB-2025-002',
    title: '网络连接不稳定',
    status: '问题识别',
    priority: '中',
    assignee: '李工',
    progress: 20,
    stepIndex: 0,
    createTime: '2025-01-29 16:20',
    statusHistory: [
      {
        timestamp: '2025-01-29 16:20',
        action: '问题创建',
        user: '李工',
        description: '创建问题记录',
        comment: '用户反馈网络连接不稳定'
      }
    ]
  }
])

// 状态转换规则
const statusTransitions = ref([
  {
    from: '问题识别',
    to: '调查分析',
    condition: '完成问题描述和影响评估',
    requiredRole: '问题负责人'
  },
  {
    from: '调查分析',
    to: '根因诊断',
    condition: '收集足够的调查证据',
    requiredRole: '问题负责人'
  },
  {
    from: '根因诊断',
    to: '解决方案',
    condition: '确定根本原因',
    requiredRole: '问题负责人'
  },
  {
    from: '解决方案',
    to: '问题关闭',
    condition: '解决方案实施完成',
    requiredRole: '问题负责人'
  }
])

// 通知设置
const notificationSettings = reactive({
  email: true,
  sms: false,
  system: true,
  roles: ['问题负责人', '团队经理']
})

// 计算属性
const filteredProblems = computed(() => {
  if (!searchKeyword.value) {
    return problems.value
  }
  const keyword = searchKeyword.value.toLowerCase()
  return problems.value.filter(p =>
    p.id.toLowerCase().includes(keyword) ||
    p.title.toLowerCase().includes(keyword)
  )
})

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '问题识别': 'info',
    '调查分析': 'warning',
    '根因诊断': 'primary',
    '解决方案': 'warning',
    '问题关闭': 'success'
  }
  return colorMap[status] || 'info'
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colorMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return colorMap[priority] || 'info'
}

// 获取步骤状态
const getStepStatus = (stepIndex, currentStepIndex) => {
  if (stepIndex < currentStepIndex) return 'finish'
  if (stepIndex === currentStepIndex) return 'process'
  return 'wait'
}

// 获取步骤图标颜色
const getStepIconColor = (stepIndex, currentStepIndex) => {
  if (stepIndex < currentStepIndex) return '#4CAF50'
  if (stepIndex === currentStepIndex) return '#1976D2'
  return '#999'
}

// 获取历史记录类型
const getHistoryType = (action) => {
  const typeMap = {
    '问题创建': 'primary',
    '开始调查': 'warning',
    '完成诊断': 'success',
    '实施解决方案': 'info',
    '问题关闭': 'success'
  }
  return typeMap[action] || 'primary'
}

// 获取历史记录图标
const getHistoryIcon = (action) => {
  const iconMap = {
    '问题创建': 'Plus',
    '开始调查': 'Search',
    '完成诊断': 'MagicStick',
    '实施解决方案': 'Tools',
    '问题关闭': 'CircleCheck'
  }
  return iconMap[action] || 'InfoFilled'
}

// 选择问题
const selectProblem = (problem) => {
  currentProblem.value = problem
}

// 搜索问题
const searchProblems = () => {
  // 搜索逻辑已在computed中实现
}

// 步骤控制
const prevStep = () => {
  if (currentProblem.value && currentProblem.value.stepIndex > 0) {
    currentProblem.value.stepIndex--
    currentProblem.value.progress = Math.round((currentProblem.value.stepIndex + 1) / problemSteps.value.length * 100)
    currentProblem.value.status = problemSteps.value[currentProblem.value.stepIndex].title

    // 添加状态变更历史
    addStatusHistory('状态回退', `回退到${currentProblem.value.status}`)

    ElMessage.success('已回退到上一步')
  }
}

const nextStep = () => {
  if (currentProblem.value && currentProblem.value.stepIndex < problemSteps.value.length - 1) {
    currentProblem.value.stepIndex++
    currentProblem.value.progress = Math.round((currentProblem.value.stepIndex + 1) / problemSteps.value.length * 100)
    currentProblem.value.status = problemSteps.value[currentProblem.value.stepIndex].title

    // 添加状态变更历史
    addStatusHistory('状态推进', `推进到${currentProblem.value.status}`)

    ElMessage.success('已推进到下一步')
  }
}

const completeProblem = () => {
  ElMessageBox.confirm(
    '确定要完成此问题吗？完成后问题将被关闭。',
    '确认完成',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    if (currentProblem.value) {
      currentProblem.value.status = '已关闭'
      currentProblem.value.progress = 100

      // 添加状态变更历史
      addStatusHistory('问题关闭', '问题已完成并关闭')

      ElMessage.success('问题已完成')
      currentProblem.value = null
    }
  })
}

// 添加状态历史
const addStatusHistory = (action, description, comment = '') => {
  if (currentProblem.value) {
    currentProblem.value.statusHistory.push({
      timestamp: new Date().toLocaleString(),
      action,
      user: '当前用户',
      description,
      comment
    })
  }
}

// 工作流配置相关函数
const addStep = () => {
  ElMessage.info('添加步骤功能')
}

const editStep = (step) => {
  ElMessage.info(`编辑步骤: ${step.title}`)
}

const deleteStep = (step) => {
  ElMessageBox.confirm(
    `确定要删除步骤 "${step.title}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = problemSteps.value.findIndex(s => s.key === step.key)
    if (index > -1) {
      problemSteps.value.splice(index, 1)
      ElMessage.success('步骤已删除')
    }
  })
}

const editTransition = (transition) => {
  ElMessage.info(`编辑转换规则: ${transition.from} -> ${transition.to}`)
}

const saveWorkflowConfig = () => {
  ElMessage.success('工作流配置已保存')
  showWorkflowDialog.value = false
}

// 初始化
if (props.problemId) {
  const problem = problems.value.find(p => p.id === props.problemId)
  if (problem) {
    currentProblem.value = problem
  }
}
</script>

<style scoped>
.problem-lifecycle {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-problem {
  padding: 20px 0;
}

.problem-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.problem-header h3 {
  margin: 0;
  color: #333;
}

.status-flow {
  margin-bottom: 30px;
}

.current-step-details {
  margin-bottom: 30px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-content {
  padding: 20px 0;
}

.step-requirements {
  margin-top: 16px;
}

.step-requirements h4 {
  color: #333;
  margin-bottom: 8px;
}

.step-requirements ul {
  margin: 0;
  padding-left: 20px;
}

.step-requirements li {
  margin-bottom: 4px;
  color: #666;
}

.step-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.status-history {
  margin-top: 30px;
}

.history-item {
  padding: 8px 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.history-action {
  font-weight: 500;
  color: #333;
}

.history-user {
  color: #666;
  font-size: 12px;
}

.history-description {
  color: #666;
  margin-bottom: 4px;
}

.history-comment {
  color: #999;
  font-size: 12px;
}

.problem-list {
  padding: 20px 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-header h3 {
  margin: 0;
  color: #333;
}

.workflow-config {
  padding: 20px 0;
}

.steps-config,
.transitions-config,
.notifications-config {
  padding: 20px 0;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.config-header h4 {
  margin: 0;
  color: #333;
}

.transition-matrix {
  margin-top: 20px;
}

.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

@media (max-width: 768px) {
  .problem-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .step-actions {
    flex-direction: column;
  }
  
  .list-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .config-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
