import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
  const permissions = ref(JSON.parse(localStorage.getItem('permissions') || '[]'))

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value.name || '未知用户')
  const userRole = computed(() => userInfo.value.role || 'user')

  // 方法
  const setToken = (newToken) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  const setUserInfo = (info) => {
    userInfo.value = info
    localStorage.setItem('userInfo', JSON.stringify(info))
  }

  const setPermissions = (perms) => {
    permissions.value = perms
    localStorage.setItem('permissions', JSON.stringify(perms))
  }

  const login = (loginData) => {
    // 模拟登录
    const mockUser = {
      id: '1',
      name: '系统管理员',
      email: '<EMAIL>',
      role: 'admin',
      department: 'IT运维部',
      avatar: ''
    }

    const mockToken = 'mock-jwt-token-' + Date.now()
    const mockPermissions = [
      'cmdb:read',
      'cmdb:write',
      'cmdb:delete',
      'topology:read',
      'discovery:read',
      'discovery:write',
      'relation:read',
      'relation:write',
      'version:read',
      'version:write',
      'quality:read'
    ]

    setToken(mockToken)
    setUserInfo(mockUser)
    setPermissions(mockPermissions)

    return Promise.resolve({
      token: mockToken,
      user: mockUser,
      permissions: mockPermissions
    })
  }

  const logout = () => {
    token.value = ''
    userInfo.value = {}
    permissions.value = []
    
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('permissions')
  }

  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }

  const hasRole = (role) => {
    return userRole.value === role
  }

  const hasAnyPermission = (permissionList) => {
    return permissionList.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissionList) => {
    return permissionList.every(permission => hasPermission(permission))
  }

  return {
    // 状态
    token,
    userInfo,
    permissions,
    
    // 计算属性
    isLoggedIn,
    userName,
    userRole,
    
    // 方法
    setToken,
    setUserInfo,
    setPermissions,
    login,
    logout,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions
  }
})
