<template>
  <div class="incident-cluster-analysis">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>事件关联分析</span>
          <div class="header-actions">
            <el-button type="primary" @click="runAnalysis" :loading="analyzing">
              <el-icon><MagicStick /></el-icon>
              AI分析
            </el-button>
            <el-button @click="showConfigDialog = true">
              <el-icon><Setting /></el-icon>
              分析配置
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 分析控制面板 -->
      <div class="analysis-controls">
        <el-form :model="analysisForm" :inline="true">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="analysisForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item label="事件类型">
            <el-select v-model="analysisForm.incidentTypes" multiple placeholder="选择事件类型" style="width: 200px">
              <el-option label="硬件故障" value="hardware" />
              <el-option label="软件缺陷" value="software" />
              <el-option label="网络问题" value="network" />
              <el-option label="性能问题" value="performance" />
              <el-option label="安全事件" value="security" />
            </el-select>
          </el-form-item>
          <el-form-item label="最小频次">
            <el-input-number v-model="analysisForm.minFrequency" :min="2" :max="100" style="width: 120px" />
          </el-form-item>
          <el-form-item label="相似度阈值">
            <el-slider
              v-model="analysisForm.similarityThreshold"
              :min="0.1"
              :max="1"
              :step="0.1"
              :format-tooltip="formatTooltip"
              style="width: 150px"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <!-- AI分析进度 -->
      <div v-if="analyzing" class="analysis-progress">
        <el-card>
          <div class="progress-content">
            <div class="progress-header">
              <el-icon class="rotating"><Loading /></el-icon>
              <span>AI正在分析事件模式...</span>
            </div>
            <el-progress :percentage="analysisProgress" :show-text="false" />
            <div class="progress-steps">
              <div class="step" :class="{ active: currentStep >= 1 }">
                <el-icon><Search /></el-icon>
                <span>数据收集</span>
              </div>
              <div class="step" :class="{ active: currentStep >= 2 }">
                <el-icon><Connection /></el-icon>
                <span>特征提取</span>
              </div>
              <div class="step" :class="{ active: currentStep >= 3 }">
                <el-icon><Share /></el-icon>
                <span>聚类分析</span>
              </div>
              <div class="step" :class="{ active: currentStep >= 4 }">
                <el-icon><TrendCharts /></el-icon>
                <span>模式识别</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 分析结果 -->
      <div v-if="analysisResults.length > 0" class="analysis-results">
        <div class="results-header">
          <h3>分析结果 ({{ analysisResults.length }} 个聚类)</h3>
          <div class="results-actions">
            <el-button type="success" size="small" @click="exportResults">
              <el-icon><Download /></el-icon>
              导出结果
            </el-button>
            <el-button type="primary" size="small" @click="createProblemsFromClusters">
              <el-icon><Plus /></el-icon>
              批量创建问题
            </el-button>
          </div>
        </div>
        
        <!-- 聚类卡片视图 -->
        <div class="cluster-cards">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :lg="8" v-for="cluster in analysisResults" :key="cluster.id">
              <el-card class="cluster-card" :class="getClusterPriorityClass(cluster.priority)">
                <template #header>
                  <div class="cluster-header">
                    <div class="cluster-title">
                      <el-icon><component :is="getClusterIcon(cluster.category)" /></el-icon>
                      <span>{{ cluster.name }}</span>
                    </div>
                    <el-tag :type="getPriorityColor(cluster.priority)" size="small">
                      {{ cluster.priority }}
                    </el-tag>
                  </div>
                </template>
                
                <div class="cluster-content">
                  <div class="cluster-stats">
                    <div class="stat-item">
                      <span class="stat-label">事件数量</span>
                      <span class="stat-value">{{ cluster.incidentCount }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">发生频率</span>
                      <span class="stat-value">{{ cluster.frequency }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">置信度</span>
                      <span class="stat-value">{{ cluster.confidence }}%</span>
                    </div>
                  </div>
                  
                  <div class="cluster-description">
                    <p>{{ cluster.description }}</p>
                  </div>
                  
                  <div class="cluster-pattern">
                    <h5>识别模式：</h5>
                    <ul>
                      <li v-for="pattern in cluster.patterns" :key="pattern">{{ pattern }}</li>
                    </ul>
                  </div>
                  
                  <div class="cluster-impact">
                    <h5>业务影响：</h5>
                    <el-progress
                      :percentage="cluster.businessImpact"
                      :status="getImpactStatus(cluster.businessImpact)"
                      :show-text="false"
                    />
                    <span class="impact-text">{{ getImpactText(cluster.businessImpact) }}</span>
                  </div>
                </div>
                
                <template #footer>
                  <div class="cluster-actions">
                    <el-button type="primary" size="small" @click="viewClusterDetails(cluster)">
                      查看详情
                    </el-button>
                    <el-button type="success" size="small" @click="createProblemFromCluster(cluster)">
                      创建问题
                    </el-button>
                    <el-button type="info" size="small" @click="addToKnownErrors(cluster)">
                      加入错误库
                    </el-button>
                  </div>
                </template>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 关联关系图 -->
        <div class="relationship-graph">
          <el-card>
            <template #header>
              <div class="graph-header">
                <span>事件关联关系图</span>
                <el-button-group size="small">
                  <el-button @click="graphLayout = 'force'" :type="graphLayout === 'force' ? 'primary' : ''">
                    力导向图
                  </el-button>
                  <el-button @click="graphLayout = 'circular'" :type="graphLayout === 'circular' ? 'primary' : ''">
                    环形图
                  </el-button>
                  <el-button @click="graphLayout = 'tree'" :type="graphLayout === 'tree' ? 'primary' : ''">
                    树形图
                  </el-button>
                </el-button-group>
              </div>
            </template>
            <div class="graph-container" ref="relationshipGraph"></div>
          </el-card>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!analyzing" class="empty-state">
        <el-empty description="暂无分析结果">
          <template #image>
            <el-icon size="100" color="#ccc"><TrendCharts /></el-icon>
          </template>
          <el-button type="primary" @click="runAnalysis">开始AI分析</el-button>
        </el-empty>
      </div>
    </el-card>
    
    <!-- 分析配置对话框 -->
    <el-dialog v-model="showConfigDialog" title="AI分析配置" width="60%">
      <div class="config-content">
        <el-tabs v-model="activeConfigTab">
          <el-tab-pane label="算法参数" name="algorithm">
            <el-form :model="algorithmConfig" label-width="120px">
              <el-form-item label="聚类算法">
                <el-select v-model="algorithmConfig.clusteringMethod">
                  <el-option label="K-Means" value="kmeans" />
                  <el-option label="DBSCAN" value="dbscan" />
                  <el-option label="层次聚类" value="hierarchical" />
                </el-select>
              </el-form-item>
              <el-form-item label="特征权重">
                <div class="feature-weights">
                  <div class="weight-item">
                    <span>时间相关性</span>
                    <el-slider v-model="algorithmConfig.timeWeight" :min="0" :max="1" :step="0.1" />
                  </div>
                  <div class="weight-item">
                    <span>内容相似性</span>
                    <el-slider v-model="algorithmConfig.contentWeight" :min="0" :max="1" :step="0.1" />
                  </div>
                  <div class="weight-item">
                    <span>影响范围</span>
                    <el-slider v-model="algorithmConfig.impactWeight" :min="0" :max="1" :step="0.1" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="最大聚类数">
                <el-input-number v-model="algorithmConfig.maxClusters" :min="2" :max="20" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <el-tab-pane label="数据源" name="datasource">
            <el-form :model="dataSourceConfig" label-width="120px">
              <el-form-item label="数据来源">
                <el-checkbox-group v-model="dataSourceConfig.sources">
                  <el-checkbox label="事件管理系统">事件管理系统</el-checkbox>
                  <el-checkbox label="监控系统">监控系统</el-checkbox>
                  <el-checkbox label="日志系统">日志系统</el-checkbox>
                  <el-checkbox label="用户反馈">用户反馈</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="数据范围">
                <el-input-number v-model="dataSourceConfig.maxRecords" :min="100" :max="10000" />
                <span class="form-help">最大分析记录数</span>
              </el-form-item>
              <el-form-item label="数据质量">
                <el-switch v-model="dataSourceConfig.dataQualityCheck" />
                <span class="form-help">启用数据质量检查</span>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <el-tab-pane label="输出设置" name="output">
            <el-form :model="outputConfig" label-width="120px">
              <el-form-item label="结果格式">
                <el-radio-group v-model="outputConfig.format">
                  <el-radio label="detailed">详细报告</el-radio>
                  <el-radio label="summary">摘要报告</el-radio>
                  <el-radio label="visual">可视化报告</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="自动创建">
                <el-switch v-model="outputConfig.autoCreateProblems" />
                <span class="form-help">自动为高置信度聚类创建问题</span>
              </el-form-item>
              <el-form-item label="通知设置">
                <el-checkbox-group v-model="outputConfig.notifications">
                  <el-checkbox label="email">邮件通知</el-checkbox>
                  <el-checkbox label="system">系统通知</el-checkbox>
                  <el-checkbox label="dashboard">仪表板推送</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <el-button @click="showConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </template>
    </el-dialog>
    
    <!-- 聚类详情对话框 -->
    <el-dialog v-model="showClusterDetail" :title="`聚类详情 - ${currentCluster?.name}`" width="80%">
      <div v-if="currentCluster" class="cluster-detail">
        <el-tabs v-model="activeDetailTab">
          <el-tab-pane label="基本信息" name="basic">
            <div class="basic-info">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="聚类名称">{{ currentCluster.name }}</el-descriptions-item>
                <el-descriptions-item label="优先级">
                  <el-tag :type="getPriorityColor(currentCluster.priority)">{{ currentCluster.priority }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="事件数量">{{ currentCluster.incidentCount }}</el-descriptions-item>
                <el-descriptions-item label="置信度">{{ currentCluster.confidence }}%</el-descriptions-item>
                <el-descriptions-item label="发生频率">{{ currentCluster.frequency }}</el-descriptions-item>
                <el-descriptions-item label="业务影响">{{ getImpactText(currentCluster.businessImpact) }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="相关事件" name="incidents">
            <div class="related-incidents">
              <el-table :data="currentCluster.incidents" style="width: 100%">
                <el-table-column prop="id" label="事件ID" width="120" />
                <el-table-column prop="title" label="标题" />
                <el-table-column prop="createTime" label="发生时间" width="160" />
                <el-table-column prop="similarity" label="相似度" width="100">
                  <template #default="scope">
                    <el-progress :percentage="scope.row.similarity" :show-text="false" />
                    <span style="margin-left: 8px;">{{ scope.row.similarity }}%</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="模式分析" name="patterns">
            <div class="pattern-analysis">
              <div class="pattern-visualization" ref="patternChart"></div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <el-button @click="showClusterDetail = false">关闭</el-button>
        <el-button type="primary" @click="createProblemFromCluster(currentCluster)">创建问题</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const analyzing = ref(false)
const analysisProgress = ref(0)
const currentStep = ref(0)
const showConfigDialog = ref(false)
const showClusterDetail = ref(false)
const activeConfigTab = ref('algorithm')
const activeDetailTab = ref('basic')
const graphLayout = ref('force')
const currentCluster = ref(null)

// 分析表单
const analysisForm = reactive({
  dateRange: [],
  incidentTypes: [],
  minFrequency: 3,
  similarityThreshold: 0.7
})

// 算法配置
const algorithmConfig = reactive({
  clusteringMethod: 'kmeans',
  timeWeight: 0.3,
  contentWeight: 0.5,
  impactWeight: 0.2,
  maxClusters: 10
})

// 数据源配置
const dataSourceConfig = reactive({
  sources: ['事件管理系统', '监控系统'],
  maxRecords: 1000,
  dataQualityCheck: true
})

// 输出配置
const outputConfig = reactive({
  format: 'detailed',
  autoCreateProblems: false,
  notifications: ['system']
})

// 分析结果
const analysisResults = ref([])

// 图表引用
const relationshipGraph = ref()
const patternChart = ref()

// 格式化提示
const formatTooltip = (val) => {
  return `${(val * 100).toFixed(0)}%`
}

// 运行分析
const runAnalysis = () => {
  analyzing.value = true
  analysisProgress.value = 0
  currentStep.value = 0

  // 模拟AI分析过程
  const steps = [
    { step: 1, progress: 25, delay: 1000 },
    { step: 2, progress: 50, delay: 1500 },
    { step: 3, progress: 75, delay: 2000 },
    { step: 4, progress: 100, delay: 1000 }
  ]

  let currentStepIndex = 0

  const runStep = () => {
    if (currentStepIndex < steps.length) {
      const stepInfo = steps[currentStepIndex]

      setTimeout(() => {
        currentStep.value = stepInfo.step
        analysisProgress.value = stepInfo.progress

        if (currentStepIndex === steps.length - 1) {
          // 分析完成，生成结果
          generateAnalysisResults()
          analyzing.value = false
          ElMessage.success('AI分析完成')
        } else {
          currentStepIndex++
          runStep()
        }
      }, stepInfo.delay)
    }
  }

  runStep()
}

// 生成分析结果
const generateAnalysisResults = () => {
  analysisResults.value = [
    {
      id: 1,
      name: '打印机驱动冲突模式',
      category: 'hardware',
      priority: '高',
      incidentCount: 15,
      frequency: '每天2-3次',
      confidence: 92,
      businessImpact: 75,
      description: '多个打印机驱动程序之间存在冲突，导致打印服务不稳定',
      patterns: [
        '错误代码集中在0x00000050-0x00000060范围',
        '故障时间多发生在系统更新后',
        '影响范围主要集中在办公区域'
      ],
      incidents: [
        { id: 'INC-001', title: '打印机无法工作', createTime: '2025-01-30 09:15', similarity: 95 },
        { id: 'INC-002', title: '打印驱动错误', createTime: '2025-01-30 10:30', similarity: 88 },
        { id: 'INC-003', title: '打印服务崩溃', createTime: '2025-01-30 14:20', similarity: 92 }
      ]
    },
    {
      id: 2,
      name: 'VPN连接超时聚类',
      category: 'network',
      priority: '中',
      incidentCount: 8,
      frequency: '每天1-2次',
      confidence: 85,
      businessImpact: 60,
      description: 'VPN连接在特定时间段出现超时，影响远程办公',
      patterns: [
        '超时多发生在网络高峰期',
        '特定VPN服务器负载过高',
        '主要影响远程办公用户'
      ],
      incidents: [
        { id: 'INC-004', title: 'VPN连接失败', createTime: '2025-01-30 08:45', similarity: 90 },
        { id: 'INC-005', title: 'VPN超时断开', createTime: '2025-01-30 13:15', similarity: 85 },
        { id: 'INC-006', title: 'VPN无法连接', createTime: '2025-01-30 16:30', similarity: 88 }
      ]
    },
    {
      id: 3,
      name: '邮件服务响应缓慢',
      category: 'performance',
      priority: '中',
      incidentCount: 12,
      frequency: '高峰期频发',
      confidence: 78,
      businessImpact: 45,
      description: '邮件服务在高峰期响应缓慢，影响邮件收发效率',
      patterns: [
        '响应时间在上午9-11点显著增加',
        '服务器CPU使用率超过80%',
        '主要影响大附件邮件处理'
      ],
      incidents: [
        { id: 'INC-007', title: '邮件发送缓慢', createTime: '2025-01-30 09:30', similarity: 82 },
        { id: 'INC-008', title: '邮件接收延迟', createTime: '2025-01-30 10:15', similarity: 78 },
        { id: 'INC-009', title: '邮件服务卡顿', createTime: '2025-01-30 11:00', similarity: 85 }
      ]
    }
  ]

  // 初始化关系图
  nextTick(() => {
    initRelationshipGraph()
  })
}

// 获取聚类优先级样式类
const getClusterPriorityClass = (priority) => {
  const classMap = {
    '高': 'high-priority',
    '中': 'medium-priority',
    '低': 'low-priority'
  }
  return classMap[priority] || 'low-priority'
}

// 获取聚类图标
const getClusterIcon = (category) => {
  const iconMap = {
    'hardware': 'Monitor',
    'software': 'Document',
    'network': 'Connection',
    'performance': 'TrendCharts',
    'security': 'Lock'
  }
  return iconMap[category] || 'QuestionFilled'
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colorMap = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success'
  }
  return colorMap[priority] || 'info'
}

// 获取影响状态
const getImpactStatus = (impact) => {
  if (impact >= 70) return 'exception'
  if (impact >= 40) return 'warning'
  return 'success'
}

// 获取影响文本
const getImpactText = (impact) => {
  if (impact >= 70) return '高影响'
  if (impact >= 40) return '中等影响'
  return '低影响'
}

// 查看聚类详情
const viewClusterDetails = (cluster) => {
  currentCluster.value = cluster
  showClusterDetail.value = true
  activeDetailTab.value = 'basic'

  nextTick(() => {
    initPatternChart()
  })
}

// 从聚类创建问题
const createProblemFromCluster = (cluster) => {
  ElMessageBox.confirm(
    `确定要为聚类 "${cluster.name}" 创建问题记录吗？`,
    '创建问题',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    ElMessage.success(`已为聚类 "${cluster.name}" 创建问题记录`)
    // 这里可以调用创建问题的API
  })
}

// 批量创建问题
const createProblemsFromClusters = () => {
  const highConfidenceClusters = analysisResults.value.filter(c => c.confidence >= 80)

  if (highConfidenceClusters.length === 0) {
    ElMessage.warning('没有找到高置信度的聚类')
    return
  }

  ElMessageBox.confirm(
    `发现 ${highConfidenceClusters.length} 个高置信度聚类，确定要批量创建问题吗？`,
    '批量创建问题',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    ElMessage.success(`已为 ${highConfidenceClusters.length} 个聚类创建问题记录`)
  })
}

// 加入已知错误库
const addToKnownErrors = (cluster) => {
  ElMessage.success(`聚类 "${cluster.name}" 已加入已知错误库`)
}

// 导出结果
const exportResults = () => {
  ElMessage.success('正在导出分析结果...')
}

// 保存配置
const saveConfig = () => {
  ElMessage.success('分析配置已保存')
  showConfigDialog.value = false
}

// 初始化关系图
const initRelationshipGraph = () => {
  if (!relationshipGraph.value) return

  const chartInstance = echarts.init(relationshipGraph.value)

  // 构建图数据
  const nodes = analysisResults.value.map(cluster => ({
    id: cluster.id,
    name: cluster.name,
    symbolSize: cluster.incidentCount * 3,
    category: cluster.category,
    value: cluster.confidence
  }))

  const links = []
  // 添加一些示例连接
  if (nodes.length > 1) {
    links.push({ source: nodes[0].id, target: nodes[1].id, value: 0.6 })
  }
  if (nodes.length > 2) {
    links.push({ source: nodes[1].id, target: nodes[2].id, value: 0.4 })
  }

  const categories = [
    { name: 'hardware' },
    { name: 'network' },
    { name: 'performance' },
    { name: 'software' },
    { name: 'security' }
  ]

  chartInstance.setOption({
    title: {
      text: '事件聚类关联图',
      left: 'center'
    },
    tooltip: {
      formatter: (params) => {
        if (params.dataType === 'node') {
          return `${params.data.name}<br/>置信度: ${params.data.value}%`
        } else {
          return `关联强度: ${(params.data.value * 100).toFixed(1)}%`
        }
      }
    },
    legend: {
      data: categories.map(c => c.name),
      bottom: 10
    },
    series: [{
      type: 'graph',
      layout: graphLayout.value,
      data: nodes,
      links: links,
      categories: categories,
      roam: true,
      focusNodeAdjacency: true,
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1,
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{b}'
      },
      lineStyle: {
        color: 'source',
        curveness: 0.3
      },
      emphasis: {
        lineStyle: {
          width: 10
        }
      }
    }]
  })
}

// 初始化模式图表
const initPatternChart = () => {
  if (!patternChart.value || !currentCluster.value) return

  const chartInstance = echarts.init(patternChart.value)
  chartInstance.setOption({
    title: {
      text: '事件时间分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '事件数量',
      type: 'line',
      data: [1, 0, 3, 8, 5, 2],
      itemStyle: { color: '#1976D2' },
      areaStyle: {
        color: 'rgba(25, 118, 210, 0.1)'
      }
    }]
  })
}

onMounted(() => {
  // 设置默认时间范围为最近7天
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)
  analysisForm.dateRange = [startDate, endDate]
})
</script>

<style scoped>
.incident-cluster-analysis {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.analysis-controls {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-progress {
  margin-bottom: 30px;
}

.progress-content {
  text-align: center;
  padding: 20px;
}

.progress-header {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  font-size: 16px;
  color: #333;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-steps {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #999;
  transition: color 0.3s ease;
}

.step.active {
  color: #1976D2;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.results-header h3 {
  margin: 0;
  color: #333;
}

.results-actions {
  display: flex;
  gap: 12px;
}

.cluster-cards {
  margin-bottom: 30px;
}

.cluster-card {
  height: 100%;
  transition: all 0.3s ease;
}

.cluster-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cluster-card.high-priority {
  border-left: 4px solid #F44336;
}

.cluster-card.medium-priority {
  border-left: 4px solid #FF9800;
}

.cluster-card.low-priority {
  border-left: 4px solid #4CAF50;
}

.cluster-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cluster-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.cluster-content {
  padding: 16px 0;
}

.cluster-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.cluster-description {
  margin-bottom: 16px;
  color: #666;
  line-height: 1.4;
}

.cluster-pattern {
  margin-bottom: 16px;
}

.cluster-pattern h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.cluster-pattern ul {
  margin: 0;
  padding-left: 16px;
}

.cluster-pattern li {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.cluster-impact {
  margin-bottom: 16px;
}

.cluster-impact h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.impact-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.cluster-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.relationship-graph {
  margin-top: 30px;
}

.graph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.graph-container {
  height: 400px;
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.config-content {
  padding: 20px 0;
}

.feature-weights {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.weight-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.weight-item span {
  width: 100px;
  font-size: 14px;
}

.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

.cluster-detail {
  padding: 20px 0;
}

.basic-info {
  padding: 20px 0;
}

.related-incidents {
  padding: 20px 0;
}

.pattern-analysis {
  padding: 20px 0;
}

.pattern-visualization {
  height: 300px;
}

@media (max-width: 768px) {
  .progress-steps {
    flex-direction: column;
    gap: 20px;
  }
  
  .cluster-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .cluster-actions {
    flex-direction: column;
  }
  
  .results-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .graph-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
