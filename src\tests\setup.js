import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn(),
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.sessionStorage = sessionStorageMock

// Configure Vue Test Utils
config.global.stubs = {
  'el-icon': true,
  'el-button': true,
  'el-input': true,
  'el-select': true,
  'el-option': true,
  'el-table': true,
  'el-table-column': true,
  'el-pagination': true,
  'el-card': true,
  'el-tabs': true,
  'el-tab-pane': true,
  'el-form': true,
  'el-form-item': true,
  'el-dialog': true,
  'el-drawer': true,
  'el-popover': true,
  'el-tooltip': true,
  'el-dropdown': true,
  'el-dropdown-menu': true,
  'el-dropdown-item': true,
  'el-menu': true,
  'el-menu-item': true,
  'el-submenu': true,
  'el-breadcrumb': true,
  'el-breadcrumb-item': true,
  'el-tag': true,
  'el-badge': true,
  'el-alert': true,
  'el-loading': true,
  'el-progress': true,
  'el-rate': true,
  'el-switch': true,
  'el-checkbox': true,
  'el-checkbox-group': true,
  'el-radio': true,
  'el-radio-group': true,
  'el-radio-button': true,
  'el-date-picker': true,
  'el-time-picker': true,
  'el-upload': true,
  'el-image': true,
  'el-avatar': true,
  'el-empty': true,
  'el-result': true,
  'el-skeleton': true,
  'el-timeline': true,
  'el-timeline-item': true,
  'el-descriptions': true,
  'el-descriptions-item': true,
  'el-collapse': true,
  'el-collapse-item': true,
  'el-tree': true,
  'el-transfer': true,
  'el-steps': true,
  'el-step': true,
  'el-carousel': true,
  'el-carousel-item': true,
  'el-backtop': true,
  'el-divider': true,
  'el-link': true,
  'el-text': true,
  'el-space': true,
  'el-row': true,
  'el-col': true,
  'el-container': true,
  'el-header': true,
  'el-aside': true,
  'el-main': true,
  'el-footer': true,
  'router-link': true,
  'router-view': true
}

// Mock Element Plus components that need special handling
config.global.mocks = {
  $message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  $messageBox: {
    confirm: vi.fn(),
    prompt: vi.fn(),
    alert: vi.fn()
  },
  $notify: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  $loading: vi.fn(() => ({
    close: vi.fn()
  }))
}

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  warn: vi.fn(),
  error: vi.fn(),
  log: vi.fn(),
  info: vi.fn()
}
