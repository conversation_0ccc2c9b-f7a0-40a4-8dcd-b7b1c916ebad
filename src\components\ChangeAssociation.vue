<template>
  <div class="change-association">
    <el-card class="association-card">
      <template #header>
        <div class="card-header">
          <span>变更关联管理</span>
          <div class="header-actions">
            <el-button type="primary" :icon="Link" @click="showLinkDialog = true">
              关联变更
            </el-button>
            <el-button :icon="Refresh" @click="refreshData" :loading="loading">
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 强制关联验证状态 -->
      <div class="validation-status">
        <el-alert
          v-if="validationResult"
          :title="validationResult.valid ? '变更关联验证通过' : '变更关联验证失败'"
          :type="validationResult.valid ? 'success' : 'error'"
          :description="validationResult.message"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 关联的变更列表 -->
      <div class="associated-changes">
        <h3>已关联变更</h3>
        <div v-if="associatedChanges.length === 0" class="empty-state">
          <el-empty description="暂无关联的变更请求">
            <el-button type="primary" @click="showLinkDialog = true">
              关联变更
            </el-button>
          </el-empty>
        </div>
        <div v-else class="changes-list">
          <div 
            v-for="change in associatedChanges" 
            :key="change.id"
            class="change-item"
            :class="{ 'invalid': !isChangeValid(change) }"
          >
            <div class="change-header">
              <div class="change-id">{{ change.id }}</div>
              <div class="change-status">
                <el-tag :type="getChangeStatusType(change.status)">
                  {{ getChangeStatusLabel(change.status) }}
                </el-tag>
                <el-tag 
                  v-if="change.priority" 
                  :type="getPriorityType(change.priority)"
                  size="small"
                >
                  {{ getPriorityLabel(change.priority) }}
                </el-tag>
              </div>
            </div>
            
            <div class="change-content">
              <h4 class="change-title">{{ change.title }}</h4>
              <p class="change-description">{{ change.description }}</p>
              
              <div class="change-details">
                <div class="detail-row">
                  <span class="detail-label">申请人：</span>
                  <span class="detail-value">{{ change.requester }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">批准人：</span>
                  <span class="detail-value">{{ change.approver }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">申请时间：</span>
                  <span class="detail-value">{{ change.requestDate }}</span>
                </div>
                <div class="detail-row" v-if="change.approvalDate">
                  <span class="detail-label">批准时间：</span>
                  <span class="detail-value">{{ change.approvalDate }}</span>
                </div>
              </div>

              <div class="change-risk">
                <div class="risk-item">
                  <span class="risk-label">影响程度：</span>
                  <el-tag :type="getImpactType(change.impact)" size="small">
                    {{ getImpactLabel(change.impact) }}
                  </el-tag>
                </div>
                <div class="risk-item">
                  <span class="risk-label">紧急程度：</span>
                  <el-tag :type="getUrgencyType(change.urgency)" size="small">
                    {{ getUrgencyLabel(change.urgency) }}
                  </el-tag>
                </div>
                <div class="risk-item">
                  <span class="risk-label">风险等级：</span>
                  <el-tag :type="getRiskType(change.riskLevel)" size="small">
                    {{ getRiskLabel(change.riskLevel) }}
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="change-actions">
              <el-button type="text" size="small" @click="viewChangeDetail(change)">
                查看详情
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="validateChange(change)"
                :loading="validatingChanges.includes(change.id)"
              >
                验证关联
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="unlinkChange(change)"
                style="color: #f56c6c"
              >
                取消关联
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 发布前置条件检查 -->
      <div class="prerequisites-check" v-if="prerequisites">
        <h3>发布前置条件检查</h3>
        <div class="check-summary">
          <el-progress 
            :percentage="getCheckProgress()" 
            :status="prerequisites.canProceed ? 'success' : 'exception'"
            :stroke-width="8"
          />
          <div class="summary-text">
            必需项：{{ prerequisites.summary.requiredPassed }}/{{ prerequisites.summary.requiredTotal }} 通过
            | 总计：{{ prerequisites.summary.passed }}/{{ prerequisites.summary.total }} 通过
          </div>
        </div>
        
        <div class="checks-list">
          <div 
            v-for="check in prerequisites.checks" 
            :key="check.name"
            class="check-item"
            :class="{ 
              'required': check.required,
              'passed': check.status === 'passed',
              'failed': check.status === 'failed',
              'warning': check.status === 'warning'
            }"
          >
            <div class="check-icon">
              <el-icon v-if="check.status === 'passed'">
                <SuccessFilled />
              </el-icon>
              <el-icon v-else-if="check.status === 'failed'">
                <CircleCloseFilled />
              </el-icon>
              <el-icon v-else>
                <WarningFilled />
              </el-icon>
            </div>
            <div class="check-content">
              <div class="check-name">
                {{ check.name }}
                <el-tag v-if="check.required" type="danger" size="small">必需</el-tag>
              </div>
              <div class="check-description">{{ check.description }}</div>
            </div>
            <div class="check-status">
              <el-tag 
                :type="check.status === 'passed' ? 'success' : 
                       check.status === 'failed' ? 'danger' : 'warning'"
                size="small"
              >
                {{ check.status === 'passed' ? '通过' : 
                   check.status === 'failed' ? '失败' : '警告' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 关联变更对话框 -->
    <el-dialog
      v-model="showLinkDialog"
      title="关联变更请求"
      width="800px"
    >
      <div class="link-dialog-content">
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索变更请求ID或标题"
            :prefix-icon="Search"
            @input="searchChanges"
            style="margin-bottom: 16px"
          />
        </div>
        
        <div class="available-changes">
          <el-table 
            :data="availableChanges" 
            @selection-change="handleSelectionChange"
            v-loading="searchLoading"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="变更ID" width="140" />
            <el-table-column prop="title" label="标题" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getChangeStatusType(row.status)" size="small">
                  {{ getChangeStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="100">
              <template #default="{ row }">
                <el-tag :type="getPriorityType(row.priority)" size="small">
                  {{ getPriorityLabel(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="requester" label="申请人" width="100" />
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showLinkDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="linkSelectedChanges"
            :disabled="selectedChanges.length === 0"
            :loading="linkLoading"
          >
            关联选中的变更 ({{ selectedChanges.length }})
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Link, Refresh, Search, SuccessFilled, CircleCloseFilled, WarningFilled
} from '@element-plus/icons-vue'
import { 
  getReleaseChanges, 
  linkChangeToRelease, 
  unlinkChangeFromRelease,
  validateReleaseChangeMapping,
  checkReleasePrerequisites,
  enforceChangeAssociation
} from '@/api/releaseApi.js'

// Props
const props = defineProps({
  releaseId: {
    type: String,
    required: true
  }
})

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const linkLoading = ref(false)
const showLinkDialog = ref(false)
const searchKeyword = ref('')
const validatingChanges = ref([])

const associatedChanges = ref([])
const availableChanges = ref([])
const selectedChanges = ref([])
const validationResult = ref(null)
const prerequisites = ref(null)

// 生命周期
onMounted(() => {
  loadAssociatedChanges()
  checkPrerequisites()
  validateAssociation()
})

// 方法
const loadAssociatedChanges = async () => {
  loading.value = true
  try {
    const response = await getReleaseChanges(props.releaseId)
    associatedChanges.value = response.data
  } catch (error) {
    ElMessage.error('加载关联变更失败')
    console.error('Load associated changes error:', error)
  } finally {
    loading.value = false
  }
}

const checkPrerequisites = async () => {
  try {
    const response = await checkReleasePrerequisites(props.releaseId)
    prerequisites.value = response.data
  } catch (error) {
    console.error('Check prerequisites error:', error)
  }
}

const validateAssociation = async () => {
  try {
    const response = await enforceChangeAssociation(props.releaseId)
    validationResult.value = response.data
  } catch (error) {
    validationResult.value = {
      valid: false,
      message: error.msg || '变更关联验证失败'
    }
  }
}

const refreshData = () => {
  loadAssociatedChanges()
  checkPrerequisites()
  validateAssociation()
}

const getCheckProgress = () => {
  if (!prerequisites.value) return 0
  const { requiredPassed, requiredTotal } = prerequisites.value.summary
  return Math.round((requiredPassed / requiredTotal) * 100)
}

// 工具方法
const isChangeValid = (change) => {
  return change.status === 'approved'
}

const getChangeStatusType = (status) => {
  const statusMap = {
    'draft': 'info',
    'submitted': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'implemented': 'success',
    'closed': 'info'
  }
  return statusMap[status] || 'info'
}

const getChangeStatusLabel = (status) => {
  const statusMap = {
    'draft': '草稿',
    'submitted': '已提交',
    'approved': '已批准',
    'rejected': '已拒绝',
    'implemented': '已实施',
    'closed': '已关闭'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority) => {
  const priorityMap = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return priorityMap[priority] || 'info'
}

const getPriorityLabel = (priority) => {
  const priorityMap = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'critical': '紧急'
  }
  return priorityMap[priority] || priority
}

const getImpactType = (impact) => {
  const impactMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return impactMap[impact] || 'info'
}

const getImpactLabel = (impact) => {
  const impactMap = {
    'low': '低影响',
    'medium': '中影响',
    'high': '高影响'
  }
  return impactMap[impact] || impact
}

const getUrgencyType = (urgency) => {
  const urgencyMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return urgencyMap[urgency] || 'info'
}

const getUrgencyLabel = (urgency) => {
  const urgencyMap = {
    'low': '低紧急',
    'medium': '中紧急',
    'high': '高紧急'
  }
  return urgencyMap[urgency] || urgency
}

const getRiskType = (risk) => {
  const riskMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return riskMap[risk] || 'info'
}

const getRiskLabel = (risk) => {
  const riskMap = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return riskMap[risk] || risk
}

// 操作方法
const viewChangeDetail = (change) => {
  ElMessage.info(`查看变更详情: ${change.id}`)
}

const validateChange = async (change) => {
  validatingChanges.value.push(change.id)
  try {
    const response = await validateReleaseChangeMapping(props.releaseId, change.id)
    ElMessage.success('变更关联验证通过')
  } catch (error) {
    ElMessage.error('变更关联验证失败')
  } finally {
    validatingChanges.value = validatingChanges.value.filter(id => id !== change.id)
  }
}

const unlinkChange = async (change) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消与变更 "${change.id}" 的关联吗？`,
      '取消关联确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await unlinkChangeFromRelease(props.releaseId, change.id)
    ElMessage.success(response.msg)
    refreshData()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('取消关联失败')
  }
}

const searchChanges = () => {
  // 模拟搜索变更请求
  searchLoading.value = true
  setTimeout(() => {
    availableChanges.value = [
      {
        id: 'CHG-2025-0003',
        title: '系统安全升级',
        status: 'approved',
        priority: 'high',
        requester: '安全团队'
      },
      {
        id: 'CHG-2025-0004',
        title: '界面优化改进',
        status: 'approved',
        priority: 'medium',
        requester: '产品团队'
      }
    ]
    searchLoading.value = false
  }, 500)
}

const handleSelectionChange = (selection) => {
  selectedChanges.value = selection
}

const linkSelectedChanges = async () => {
  linkLoading.value = true
  try {
    for (const change of selectedChanges.value) {
      await linkChangeToRelease(props.releaseId, change.id)
    }
    ElMessage.success(`成功关联 ${selectedChanges.value.length} 个变更请求`)
    showLinkDialog.value = false
    selectedChanges.value = []
    refreshData()
  } catch (error) {
    ElMessage.error('关联变更失败')
  } finally {
    linkLoading.value = false
  }
}
</script>

<style scoped>
.change-association {
  padding: 20px;
}

.association-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 验证状态 */
.validation-status {
  margin-bottom: 24px;
}

/* 关联变更列表 */
.associated-changes h3 {
  color: #333;
  margin-bottom: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.changes-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.change-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  transition: all 0.2s;
}

.change-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.change-item.invalid {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.change-id {
  font-weight: 600;
  color: #1976D2;
  font-size: 16px;
}

.change-status {
  display: flex;
  gap: 8px;
}

.change-content {
  margin-bottom: 16px;
}

.change-title {
  color: #333;
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.change-description {
  color: #666;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.change-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-weight: 600;
  color: #666;
  min-width: 80px;
}

.detail-value {
  color: #333;
}

.change-risk {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.risk-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.risk-label {
  font-size: 14px;
  color: #666;
}

.change-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

/* 前置条件检查 */
.prerequisites-check {
  margin-top: 32px;
}

.prerequisites-check h3 {
  color: #333;
  margin-bottom: 16px;
}

.check-summary {
  margin-bottom: 20px;
}

.summary-text {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
}

.checks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.check-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
}

.check-item.required {
  border-left: 4px solid #f56c6c;
}

.check-item.passed {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.check-item.failed {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.check-item.warning {
  border-color: #e6a23c;
  background-color: #fdf6ec;
}

.check-icon {
  margin-right: 12px;
  font-size: 20px;
}

.check-icon .el-icon {
  color: #67c23a;
}

.check-item.failed .check-icon .el-icon {
  color: #f56c6c;
}

.check-item.warning .check-icon .el-icon {
  color: #e6a23c;
}

.check-content {
  flex: 1;
}

.check-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.check-description {
  font-size: 14px;
  color: #666;
}

.check-status {
  margin-left: 12px;
}

/* 对话框内容 */
.link-dialog-content {
  max-height: 500px;
}

.search-section {
  margin-bottom: 16px;
}

.available-changes {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .change-association {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .change-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .change-details {
    grid-template-columns: 1fr;
  }

  .change-risk {
    flex-direction: column;
    gap: 8px;
  }

  .change-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .check-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .check-icon {
    margin-right: 0;
  }
}
</style>
