<template>
  <el-dialog
    v-model="visible"
    :title="`申请服务 - ${service?.name || ''}`"
    width="600px"
    :before-close="handleClose"
    class="service-apply-dialog"
  >
    <div v-if="service" class="apply-content">
      <!-- 服务信息展示 -->
      <div class="service-info">
        <div class="service-header">
          <div class="service-icon">
            <el-icon :size="24">
              <component :is="service.icon" />
            </el-icon>
          </div>
          <div class="service-details">
            <h4>{{ service.name }}</h4>
            <p>{{ service.description }}</p>
            <div class="service-meta">
              <span><el-icon><Clock /></el-icon> {{ service.estimatedTime }}</span>
              <span><el-icon><Money /></el-icon> {{ service.cost }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 申请表单 -->
      <div class="apply-form">
        <el-form 
          ref="formRef" 
          :model="formData" 
          :rules="formRules" 
          label-width="100px"
          @submit.prevent="handleSubmit"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h5>基本信息</h5>
            <el-form-item label="申请人" prop="requester">
              <el-input v-model="formData.requester" disabled />
            </el-form-item>
            
            <el-form-item label="所属部门" prop="department">
              <el-input v-model="formData.department" disabled />
            </el-form-item>
            
            <el-form-item label="联系方式" prop="contact">
              <el-input v-model="formData.contact" placeholder="请输入联系电话或邮箱" />
            </el-form-item>
            
            <el-form-item label="紧急程度" prop="priority">
              <el-radio-group v-model="formData.priority">
                <el-radio value="低">低</el-radio>
                <el-radio value="中">中</el-radio>
                <el-radio value="高">高</el-radio>
                <el-radio value="紧急">紧急</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 动态表单字段 -->
          <div class="form-section" v-if="service.formFields && service.formFields.length > 0">
            <h5>申请详情</h5>
            <el-form-item 
              v-for="field in service.formFields" 
              :key="field.name"
              :label="field.label"
              :prop="`customFields.${field.name}`"
              :rules="getFieldRules(field)"
            >
              <!-- 文本输入 -->
              <el-input 
                v-if="field.type === 'input'"
                v-model="formData.customFields[field.name]"
                :placeholder="`请输入${field.label}`"
              />
              
              <!-- 多行文本 -->
              <el-input 
                v-else-if="field.type === 'textarea'"
                v-model="formData.customFields[field.name]"
                type="textarea"
                :rows="3"
                :placeholder="`请输入${field.label}`"
              />
              
              <!-- 下拉选择 -->
              <el-select 
                v-else-if="field.type === 'select'"
                v-model="formData.customFields[field.name]"
                :placeholder="`请选择${field.label}`"
                style="width: 100%"
              >
                <el-option 
                  v-for="option in field.options" 
                  :key="option"
                  :label="option" 
                  :value="option" 
                />
              </el-select>
              
              <!-- 多选框 -->
              <el-checkbox-group 
                v-else-if="field.type === 'checkbox'"
                v-model="formData.customFields[field.name]"
              >
                <el-checkbox 
                  v-for="option in field.options" 
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </el-checkbox>
              </el-checkbox-group>
              
              <!-- 单选框 -->
              <el-radio-group 
                v-else-if="field.type === 'radio'"
                v-model="formData.customFields[field.name]"
              >
                <el-radio 
                  v-for="option in field.options" 
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </el-radio>
              </el-radio-group>
              
              <!-- 日期选择 -->
              <el-date-picker 
                v-else-if="field.type === 'date'"
                v-model="formData.customFields[field.name]"
                type="date"
                :placeholder="`请选择${field.label}`"
                style="width: 100%"
              />
              
              <!-- 日期范围 -->
              <el-date-picker 
                v-else-if="field.type === 'daterange'"
                v-model="formData.customFields[field.name]"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
              
              <!-- 数字输入 -->
              <el-input-number 
                v-else-if="field.type === 'number'"
                v-model="formData.customFields[field.name]"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </div>

          <!-- 附件上传 -->
          <div class="form-section">
            <h5>附件上传</h5>
            <el-form-item label="相关文件">
              <el-upload
                v-model:file-list="fileList"
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                multiple
              >
                <el-button type="primary">
                  <el-icon><Upload /></el-icon>
                  选择文件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持上传jpg/png/pdf/doc/docx文件，且不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>

          <!-- 备注说明 -->
          <div class="form-section">
            <el-form-item label="备注说明">
              <el-input 
                v-model="formData.remarks"
                type="textarea"
                :rows="3"
                placeholder="请输入其他需要说明的信息..."
              />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <!-- 审批流程预览 -->
      <div class="approval-preview" v-if="service.approvalRequired">
        <h5>审批流程</h5>
        <div class="approval-flow">
          <div 
            v-for="(step, index) in service.approvalFlow" 
            :key="index"
            class="approval-step"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-name">{{ step }}</div>
            <div v-if="index < service.approvalFlow.length - 1" class="step-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="saveDraft" :loading="saving">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          提交申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, Money, Upload, ArrowRight } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  service: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const userStore = useUserStore()
const formRef = ref()
const fileList = ref([])
const submitting = ref(false)
const saving = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
  requester: userStore.userName,
  department: userStore.userInfo.department || '',
  contact: userStore.userInfo.email || '',
  priority: '中',
  customFields: {},
  remarks: '',
  attachments: []
})

// 表单验证规则
const formRules = {
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ]
}

// 获取字段验证规则
const getFieldRules = (field) => {
  if (!field.required) return []
  
  return [
    { 
      required: true, 
      message: `请${field.type === 'select' ? '选择' : '输入'}${field.label}`, 
      trigger: field.type === 'select' ? 'change' : 'blur' 
    }
  ]
}

// 文件处理
const handleFileChange = (file, fileList) => {
  // 文件大小检查
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  
  // 文件类型检查
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  if (!allowedTypes.includes(file.raw.type)) {
    ElMessage.error('不支持的文件类型')
    return false
  }
  
  formData.attachments = fileList
}

const handleFileRemove = (file, fileList) => {
  formData.attachments = fileList
}

// 保存草稿
const saveDraft = async () => {
  saving.value = true
  try {
    // 这里可以调用保存草稿的API
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('草稿保存成功')
  } catch (error) {
    ElMessage.error('保存草稿失败')
    console.error('保存草稿失败:', error)
  } finally {
    saving.value = false
  }
}

// 提交申请
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const requestData = {
      serviceId: props.service.id,
      serviceName: props.service.name,
      category: props.service.category,
      requester: formData.requester,
      department: formData.department,
      contact: formData.contact,
      priority: formData.priority,
      customFields: formData.customFields,
      remarks: formData.remarks,
      attachments: formData.attachments.map(file => ({
        name: file.name,
        size: file.size,
        type: file.raw?.type || file.type
      }))
    }
    
    emit('submit', requestData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  fileList.value = []
  formData.customFields = {}
  formData.remarks = ''
  formData.attachments = []
}

// 监听服务变化，初始化自定义字段
watch(() => props.service, (newService) => {
  if (newService && newService.formFields) {
    const customFields = {}
    newService.formFields.forEach(field => {
      if (field.type === 'checkbox') {
        customFields[field.name] = []
      } else {
        customFields[field.name] = ''
      }
    })
    formData.customFields = customFields
  }
}, { immediate: true })
</script>

<style scoped>
.service-apply-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.apply-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.service-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.service-header {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.service-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.service-details h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.service-details p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.service-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.service-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 20px;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.form-section h5 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.approval-preview h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.approval-flow {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.approval-step {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.step-name {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

.step-arrow {
  color: #C0C4CC;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .service-apply-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .service-header {
    flex-direction: column;
    text-align: center;
  }
  
  .service-meta {
    justify-content: center;
  }
  
  .approval-flow {
    flex-direction: column;
    align-items: stretch;
  }
  
  .approval-step {
    justify-content: center;
  }
  
  .step-arrow {
    transform: rotate(90deg);
  }
}
</style>
