<template>
  <div class="visualization-simple">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>可视化大屏</h2>
          <p>创建和管理数据可视化大屏，支持拖拽式编辑和实时数据展示</p>
        </div>
        <div class="action-section">
          <el-button type="primary" :icon="Plus" @click="createNewDashboard">
            新建大屏
          </el-button>
          <el-button :icon="Refresh" @click="refreshList" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">总大屏数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon published">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.published }}</div>
                <div class="stat-label">已发布</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon draft">
                <el-icon><Edit /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.draft }}</div>
                <div class="stat-label">草稿</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon views">
                <el-icon><View /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.totalViews }}</div>
                <div class="stat-label">总浏览量</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 大屏列表 -->
    <div class="dashboard-grid">
      <el-row :gutter="20" v-loading="loading">
        <el-col :span="6" v-for="dashboard in dashboardList" :key="dashboard.id">
          <el-card class="dashboard-card" shadow="hover">
            <div class="card-header">
              <div class="card-title">{{ dashboard.name }}</div>
              <el-dropdown @command="(command) => handleAction(command, dashboard)">
                <el-button type="text" :icon="More" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="preview">预览</el-dropdown-item>
                    <el-dropdown-item command="copy">复制</el-dropdown-item>
                    <el-dropdown-item command="export">导出</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
            <div class="card-preview" @click="previewDashboard(dashboard)">
              <div class="preview-placeholder">
                <el-icon :size="48"><Monitor /></el-icon>
                <p>点击预览</p>
              </div>
            </div>
            
            <div class="card-info">
              <div class="info-row">
                <span class="info-label">状态：</span>
                <el-tag :type="getStatusType(dashboard.status)" size="small">
                  {{ getStatusText(dashboard.status) }}
                </el-tag>
              </div>
              <div class="info-row">
                <span class="info-label">分类：</span>
                <span>{{ getCategoryText(dashboard.category) }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">更新时间：</span>
                <span>{{ dashboard.updateTime }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">浏览量：</span>
                <span>{{ dashboard.views }}</span>
              </div>
            </div>
            
            <div class="card-actions">
              <el-button type="primary" size="small" @click="editDashboard(dashboard)">
                编辑
              </el-button>
              <el-button size="small" @click="previewDashboard(dashboard)">
                预览
              </el-button>
            </div>
          </el-card>
        </el-col>
        
        <!-- 空状态 -->
        <el-col :span="24" v-if="dashboardList.length === 0 && !loading">
          <el-empty description="暂无大屏数据">
            <el-button type="primary" @click="createNewDashboard">创建第一个大屏</el-button>
          </el-empty>
        </el-col>
      </el-row>
    </div>

    <!-- 新建大屏对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建大屏"
      width="600px"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        label-width="100px"
      >
        <el-form-item label="大屏名称">
          <el-input v-model="createForm.name" placeholder="请输入大屏名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入大屏描述（可选）"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="createForm.category" placeholder="选择分类" style="width: 100%">
            <el-option label="智慧城市" value="smart-city" />
            <el-option label="企业运营" value="business" />
            <el-option label="IoT监控" value="iot" />
            <el-option label="电商数据" value="ecommerce" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" :loading="createLoading" @click="handleCreate">
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, Monitor, SuccessFilled, Edit, View, More
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showCreateDialog = ref(false)

// 统计数据
const statistics = ref({
  total: 12,
  published: 8,
  draft: 3,
  totalViews: 1580
})

// 大屏列表
const dashboardList = ref([
  {
    id: '1',
    name: '智慧城市监控大屏',
    description: '城市运行状态实时监控',
    status: 'published',
    category: 'smart-city',
    updateTime: '2024-01-15 14:30',
    views: 256
  },
  {
    id: '2',
    name: '企业销售数据分析',
    description: '销售业绩和趋势分析',
    status: 'published',
    category: 'business',
    updateTime: '2024-01-14 16:20',
    views: 189
  },
  {
    id: '3',
    name: 'IoT设备监控面板',
    description: '物联网设备状态监控',
    status: 'draft',
    category: 'iot',
    updateTime: '2024-01-13 09:45',
    views: 67
  }
])

// 创建表单
const createFormRef = ref(null)
const createForm = reactive({
  name: '',
  description: '',
  category: ''
})

// 生命周期
onMounted(() => {
  loadDashboardList()
})

// 加载大屏列表
const loadDashboardList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('加载大屏列表失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新列表
const refreshList = () => {
  loadDashboardList()
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    'published': 'success',
    'draft': 'warning',
    'archived': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'published': '已发布',
    'draft': '草稿',
    'archived': '已归档'
  }
  return textMap[status] || status
}

// 获取分类文本
const getCategoryText = (category) => {
  const textMap = {
    'smart-city': '智慧城市',
    'business': '企业运营',
    'iot': 'IoT监控',
    'ecommerce': '电商数据',
    'other': '其他'
  }
  return textMap[category] || category
}

// 新建大屏
const createNewDashboard = () => {
  showCreateDialog.value = true
}

// 处理创建
const handleCreate = async () => {
  createLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('大屏创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    
    // 跳转到编辑器
    router.push('/visualization/editor/new')
  } catch (error) {
    console.error('创建大屏失败:', error)
    ElMessage.error('创建大屏失败')
  } finally {
    createLoading.value = false
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.keys(createForm).forEach(key => {
    createForm[key] = ''
  })
}

// 编辑大屏
const editDashboard = (dashboard) => {
  router.push(`/visualization/editor/${dashboard.id}`)
}

// 预览大屏
const previewDashboard = (dashboard) => {
  router.push(`/visualization/preview/${dashboard.id}`)
}

// 处理操作
const handleAction = async (command, dashboard) => {
  switch (command) {
    case 'edit':
      editDashboard(dashboard)
      break
    case 'preview':
      previewDashboard(dashboard)
      break
    case 'copy':
      await copyDashboard(dashboard)
      break
    case 'export':
      await exportDashboard(dashboard)
      break
    case 'delete':
      await deleteDashboard(dashboard)
      break
  }
}

// 复制大屏
const copyDashboard = async (dashboard) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 创建复制的大屏
    const newDashboard = {
      ...dashboard,
      id: Date.now().toString(),
      name: `${dashboard.name} - 副本`,
      status: 'draft',
      updateTime: new Date().toLocaleString('zh-CN'),
      views: 0
    }

    // 添加到列表
    dashboardList.value.unshift(newDashboard)

    // 更新统计数据
    statistics.value.total++
    statistics.value.draft++

    ElMessage.success(`大屏 "${dashboard.name}" 复制成功`)
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 删除大屏
const deleteDashboard = async (dashboard) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除大屏 "${dashboard.name}" 吗？此操作不可逆。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    ElMessage.success('删除成功')
    loadDashboardList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 导出大屏
const exportDashboard = async (dashboard) => {
  try {
    // 模拟导出配置
    const config = {
      id: dashboard.id,
      name: dashboard.name,
      description: dashboard.description,
      category: dashboard.category,
      components: [
        // 这里应该是实际的组件配置数据
        {
          id: 'comp1',
          type: 'title',
          x: 50,
          y: 30,
          width: 400,
          height: 60,
          data: { text: dashboard.name }
        }
      ],
      settings: {
        width: 1920,
        height: 1080,
        background: '#0a0a0a'
      }
    }

    // 创建下载链接
    const dataStr = JSON.stringify(config, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = `${dashboard.name}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    URL.revokeObjectURL(url)

    ElMessage.success(`大屏 "${dashboard.name}" 导出成功`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}
</script>

<style scoped>
.visualization-simple {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.title-section p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.published {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.draft {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #e6a23c;
}

.stat-icon.views {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #67c23a;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 大屏网格 */
.dashboard-grid {
  margin-bottom: 20px;
}

.dashboard-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-preview {
  height: 120px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.card-preview:hover {
  background: #e9ecef;
}

.preview-placeholder {
  text-align: center;
  color: #909399;
}

.preview-placeholder p {
  margin: 8px 0 0 0;
  font-size: 12px;
}

.card-info {
  margin-bottom: 12px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.info-label {
  color: #909399;
  min-width: 60px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

/* 对话框 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式 */
@media (max-width: 768px) {
  .visualization-simple {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
  }

  .dashboard-grid .el-col {
    margin-bottom: 16px;
  }
}
</style>
