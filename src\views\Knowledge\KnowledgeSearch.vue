<template>
  <div class="knowledge-search">
    <div class="page-header">
      <h2>知识搜索</h2>
      <p>智能搜索知识库内容，快速找到所需信息</p>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-section">
      <div class="search-container">
        <div class="main-search">
          <el-input
            v-model="searchQuery"
            placeholder="输入关键词搜索知识..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
            @input="handleSearchInput"
            @clear="clearSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch">
                搜索
              </el-button>
            </template>
          </el-input>
          
          <!-- 搜索建议 -->
          <div v-if="showSuggestions && suggestions.length > 0" class="search-suggestions">
            <div 
              v-for="suggestion in suggestions" 
              :key="suggestion.id"
              class="suggestion-item"
              @click="selectSuggestion(suggestion)"
            >
              <el-icon><Search /></el-icon>
              <span class="suggestion-text" v-html="highlightKeyword(suggestion.text, searchQuery)"></span>
              <span class="suggestion-type">{{ suggestion.type }}</span>
            </div>
          </div>
        </div>

        <!-- 高级搜索 -->
        <div class="advanced-search" v-if="showAdvanced">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select v-model="searchFilters.category" placeholder="选择分类" clearable>
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="searchFilters.author" placeholder="选择作者" clearable filterable>
                <el-option
                  v-for="author in authors"
                  :key="author.id"
                  :label="author.name"
                  :value="author.id"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="searchFilters.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-col>
            <el-col :span="6">
              <el-select v-model="searchFilters.sortBy" placeholder="排序方式">
                <el-option label="相关度" value="relevance" />
                <el-option label="最新更新" value="updateTime" />
                <el-option label="浏览量" value="views" />
                <el-option label="评分" value="rating" />
              </el-select>
            </el-col>
          </el-row>
        </div>

        <div class="search-actions">
          <el-button 
            type="text" 
            @click="showAdvanced = !showAdvanced"
            class="advanced-toggle"
          >
            {{ showAdvanced ? '收起' : '高级搜索' }}
            <el-icon><component :is="showAdvanced ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
          </el-button>
          <el-button type="text" @click="clearAllFilters">清空筛选</el-button>
        </div>
      </div>
    </el-card>

    <!-- 搜索结果 -->
    <div class="search-results" v-if="hasSearched">
      <div class="results-header">
        <div class="results-info">
          <span class="results-count">
            找到 <strong>{{ searchResults.total }}</strong> 条相关结果
          </span>
          <span class="search-time">
            (用时 {{ searchTime }}ms)
          </span>
        </div>
        <div class="results-actions">
          <el-button-group>
            <el-button 
              :type="viewMode === 'list' ? 'primary' : ''"
              @click="viewMode = 'list'"
            >
              <el-icon><List /></el-icon>
            </el-button>
            <el-button 
              :type="viewMode === 'grid' ? 'primary' : ''"
              @click="viewMode = 'grid'"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 搜索过滤标签 -->
      <div class="search-filters-tags" v-if="hasActiveFilters">
        <el-tag
          v-for="filter in activeFilters"
          :key="filter.key"
          closable
          @close="removeFilter(filter.key)"
          class="filter-tag"
        >
          {{ filter.label }}: {{ filter.value }}
        </el-tag>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="results-list">
        <el-card 
          v-for="item in searchResults.list" 
          :key="item.id"
          class="result-item"
          @click="viewKnowledge(item)"
        >
          <div class="result-content">
            <div class="result-header">
              <h3 class="result-title" v-html="highlightKeyword(item.title, searchQuery)"></h3>
              <div class="result-meta">
                <el-tag size="small" :type="getCategoryType(item.category)">
                  {{ item.category }}
                </el-tag>
                <span class="result-author">{{ item.author }}</span>
                <span class="result-time">{{ formatTime(item.updateTime) }}</span>
                <span class="result-score">相关度: {{ item.relevanceScore }}%</span>
              </div>
            </div>
            <div class="result-summary" v-html="highlightKeyword(item.summary, searchQuery)"></div>
            <div class="result-stats">
              <span class="stat-item">
                <el-icon><View /></el-icon>
                {{ item.views }} 浏览
              </span>
              <span class="stat-item">
                <el-icon><Star /></el-icon>
                {{ item.rating }} 评分
              </span>
              <span class="stat-item">
                <el-icon><ChatRound /></el-icon>
                {{ item.comments }} 评论
              </span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 网格视图 -->
      <div v-else class="results-grid">
        <el-row :gutter="20">
          <el-col 
            :xs="24" :sm="12" :md="8" :lg="6"
            v-for="item in searchResults.list" 
            :key="item.id"
          >
            <el-card class="grid-item" @click="viewKnowledge(item)">
              <div class="grid-content">
                <h3 class="grid-title" v-html="highlightKeyword(item.title, searchQuery)"></h3>
                <div class="grid-summary" v-html="highlightKeyword(item.summary, searchQuery)"></div>
                <div class="grid-meta">
                  <el-tag size="small" :type="getCategoryType(item.category)">
                    {{ item.category }}
                  </el-tag>
                  <span class="grid-score">{{ item.relevanceScore }}%</span>
                </div>
                <div class="grid-stats">
                  <span class="stat-item">
                    <el-icon><View /></el-icon>
                    {{ item.views }}
                  </span>
                  <span class="stat-item">
                    <el-icon><Star /></el-icon>
                    {{ item.rating }}
                  </span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50]"
          :total="searchResults.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="hasSearched && searchResults.total === 0" class="empty-results">
      <el-empty description="没有找到相关结果">
        <el-button type="primary" @click="clearSearch">重新搜索</el-button>
      </el-empty>
    </div>

    <!-- 热门搜索和推荐 -->
    <div v-else class="search-recommendations">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>热门搜索</span>
            </template>
            <div class="hot-searches">
              <el-tag
                v-for="keyword in hotKeywords"
                :key="keyword.id"
                class="hot-keyword"
                @click="searchByKeyword(keyword.text)"
              >
                {{ keyword.text }}
              </el-tag>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>推荐知识</span>
            </template>
            <div class="recommended-knowledge">
              <div 
                v-for="item in recommendedKnowledge" 
                :key="item.id"
                class="recommended-item"
                @click="viewKnowledge(item)"
              >
                <div class="recommended-title">{{ item.title }}</div>
                <div class="recommended-meta">
                  <el-tag size="small" :type="getCategoryType(item.category)">
                    {{ item.category }}
                  </el-tag>
                  <span class="recommended-views">{{ item.views }} 浏览</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Search, ArrowUp, ArrowDown, List, Grid, View, Star, ChatRound
} from '@element-plus/icons-vue'
import { 
  searchKnowledge,
  getSearchSuggestions,
  getHotSearchKeywords,
  getPopularKnowledge
} from '@/api/knowledgeApi'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const showSuggestions = ref(false)
const showAdvanced = ref(false)
const hasSearched = ref(false)
const viewMode = ref('list')
const searchTime = ref(0)

// 搜索建议
const suggestions = ref([])

// 搜索过滤器
const searchFilters = reactive({
  category: '',
  author: '',
  dateRange: [],
  sortBy: 'relevance'
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 搜索结果
const searchResults = reactive({
  total: 0,
  list: []
})

// 分类数据
const categories = ref([
  { id: 1, name: '技术文档' },
  { id: 2, name: '流程规范' },
  { id: 3, name: 'FAQ' },
  { id: 4, name: '解决方案' },
  { id: 5, name: '操作指南' }
])

// 作者数据
const authors = ref([
  { id: 1, name: '张工' },
  { id: 2, name: '李工' },
  { id: 3, name: '王工' },
  { id: 4, name: '赵工' }
])

// 热门关键词
const hotKeywords = ref([
  { id: 1, text: '服务器监控' },
  { id: 2, text: '网络故障' },
  { id: 3, text: '数据库备份' },
  { id: 4, text: 'VPN配置' },
  { id: 5, text: '邮箱设置' },
  { id: 6, text: '安全策略' }
])

// 推荐知识
const recommendedKnowledge = ref([
  {
    id: 1,
    title: '服务器性能监控配置指南',
    category: '技术文档',
    views: 2456
  },
  {
    id: 2,
    title: '网络故障快速排除流程',
    category: '流程规范',
    views: 1892
  },
  {
    id: 3,
    title: '数据库备份恢复操作手册',
    category: '操作指南',
    views: 1654
  }
])

// 计算属性
const hasActiveFilters = computed(() => {
  return searchFilters.category || 
         searchFilters.author || 
         searchFilters.dateRange.length > 0
})

const activeFilters = computed(() => {
  const filters = []
  if (searchFilters.category) {
    const category = categories.value.find(c => c.id === searchFilters.category)
    filters.push({ key: 'category', label: '分类', value: category?.name })
  }
  if (searchFilters.author) {
    const author = authors.value.find(a => a.id === searchFilters.author)
    filters.push({ key: 'author', label: '作者', value: author?.name })
  }
  if (searchFilters.dateRange.length > 0) {
    filters.push({ 
      key: 'dateRange', 
      label: '时间范围', 
      value: `${searchFilters.dateRange[0]} 至 ${searchFilters.dateRange[1]}` 
    })
  }
  return filters
})

// 监听搜索输入
watch(searchQuery, (newValue) => {
  if (newValue && newValue.length > 1) {
    getSuggestions(newValue)
  } else {
    suggestions.value = []
    showSuggestions.value = false
  }
})

// 生命周期
onMounted(() => {
  loadInitialData()
})

// 加载初始数据
const loadInitialData = async () => {
  try {
    // const hotKeywordsRes = await getHotSearchKeywords()
    // const recommendedRes = await getPopularKnowledge({ limit: 3 })
    // hotKeywords.value = hotKeywordsRes.data
    // recommendedKnowledge.value = recommendedRes.data
  } catch (error) {
    console.error('加载初始数据失败:', error)
  }
}

// 搜索相关
const handleSearchInput = (value) => {
  if (!value) {
    showSuggestions.value = false
  }
}

const getSuggestions = async (keyword) => {
  try {
    // const response = await getSearchSuggestions(keyword)
    // suggestions.value = response.data

    // 模拟搜索建议
    suggestions.value = [
      { id: 1, text: '服务器监控配置', type: '知识标题' },
      { id: 2, text: '服务器性能优化', type: '知识内容' },
      { id: 3, text: '服务器', type: '标签' }
    ].filter(item => item.text.includes(keyword))

    showSuggestions.value = suggestions.value.length > 0
  } catch (error) {
    console.error('获取搜索建议失败:', error)
  }
}

const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion.text
  showSuggestions.value = false
  handleSearch()
}

const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  const startTime = Date.now()

  try {
    const params = {
      keyword: searchQuery.value,
      ...searchFilters,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    // const response = await searchKnowledge(params)
    // searchResults.total = response.data.total
    // searchResults.list = response.data.list

    // 模拟搜索结果
    searchResults.total = 156
    searchResults.list = [
      {
        id: 1,
        title: '服务器性能监控配置完整指南',
        summary: '详细介绍如何配置服务器性能监控系统，包括CPU、内存、磁盘等关键指标的监控设置。',
        category: '技术文档',
        author: '张工',
        updateTime: '2025-01-30 14:30',
        views: 2456,
        rating: 4.8,
        comments: 23,
        relevanceScore: 95
      },
      {
        id: 2,
        title: '网络故障快速排除流程',
        summary: '标准化的网络故障排除流程，包括问题诊断步骤、常用工具使用方法和解决方案模板。',
        category: '流程规范',
        author: '李工',
        updateTime: '2025-01-29 16:45',
        views: 1892,
        rating: 4.6,
        comments: 18,
        relevanceScore: 87
      }
    ]

    hasSearched.value = true
    showSuggestions.value = false
    searchTime.value = Date.now() - startTime

  } catch (error) {
    ElMessage.error('搜索失败')
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  hasSearched.value = false
  searchResults.total = 0
  searchResults.list = []
  clearAllFilters()
}

const clearAllFilters = () => {
  searchFilters.category = ''
  searchFilters.author = ''
  searchFilters.dateRange = []
  searchFilters.sortBy = 'relevance'
  pagination.currentPage = 1
}

const removeFilter = (filterKey) => {
  switch (filterKey) {
    case 'category':
      searchFilters.category = ''
      break
    case 'author':
      searchFilters.author = ''
      break
    case 'dateRange':
      searchFilters.dateRange = []
      break
  }
  handleSearch()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  handleSearch()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  handleSearch()
}

// 导航相关
const viewKnowledge = (item) => {
  router.push(`/knowledge/articles/${item.id}`)
}

const searchByKeyword = (keyword) => {
  searchQuery.value = keyword
  handleSearch()
}

// 工具函数
const highlightKeyword = (text, keyword) => {
  if (!keyword || !text) return text
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const getCategoryType = (category) => {
  const types = {
    '技术文档': 'primary',
    '流程规范': 'success',
    'FAQ': 'warning',
    '解决方案': 'danger',
    '操作指南': 'info'
  }
  return types[category] || ''
}

const formatTime = (time) => {
  return time
}
</script>

<style scoped>
.knowledge-search {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  color: #616161;
  margin: 0;
  font-size: 16px;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-container {
  max-width: 800px;
  margin: 0 auto;
}

.main-search {
  position: relative;
  margin-bottom: 20px;
}

.main-search :deep(.el-input) {
  font-size: 16px;
}

.main-search :deep(.el-input__inner) {
  height: 50px;
  border-radius: 25px;
  padding-left: 50px;
}

.main-search :deep(.el-input-group__append) {
  border-radius: 0 25px 25px 0;
}

/* 搜索建议样式 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-text {
  flex: 1;
  margin: 0 12px;
  font-size: 14px;
  color: #303133;
}

.suggestion-text :deep(mark) {
  background-color: #fff3cd;
  color: #856404;
  padding: 0 2px;
}

.suggestion-type {
  font-size: 12px;
  color: #909399;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 高级搜索样式 */
.advanced-search {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.advanced-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 搜索结果样式 */
.search-results {
  margin-top: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.results-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.results-count {
  font-size: 16px;
  color: #303133;
}

.search-time {
  font-size: 14px;
  color: #909399;
}

/* 过滤标签样式 */
.search-filters-tags {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  cursor: pointer;
}

/* 列表视图样式 */
.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.result-content {
  padding: 4px;
}

.result-header {
  margin-bottom: 12px;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  color: #1976D2;
  margin: 0 0 8px 0;
  line-height: 1.4;
  cursor: pointer;
}

.result-title:hover {
  text-decoration: underline;
}

.result-title :deep(mark) {
  background-color: #fff3cd;
  color: #856404;
  padding: 0 2px;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #606266;
}

.result-summary {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 12px;
}

.result-summary :deep(mark) {
  background-color: #fff3cd;
  color: #856404;
  padding: 0 2px;
}

.result-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.result-score {
  color: #67C23A;
  font-weight: 500;
}

/* 网格视图样式 */
.results-grid {
  margin-bottom: 20px;
}

.grid-item {
  height: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 16px;
}

.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.grid-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 4px;
}

.grid-title {
  font-size: 16px;
  font-weight: 600;
  color: #1976D2;
  margin: 0 0 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.grid-title :deep(mark) {
  background-color: #fff3cd;
  color: #856404;
  padding: 0 2px;
}

.grid-summary {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 8px;
}

.grid-summary :deep(mark) {
  background-color: #fff3cd;
  color: #856404;
  padding: 0 2px;
}

.grid-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.grid-score {
  font-size: 12px;
  color: #67C23A;
  font-weight: 500;
}

.grid-stats {
  display: flex;
  justify-content: space-between;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 空状态样式 */
.empty-results {
  text-align: center;
  padding: 60px 20px;
}

/* 推荐区域样式 */
.search-recommendations {
  margin-top: 40px;
}

.hot-searches {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hot-keyword {
  cursor: pointer;
  transition: all 0.3s ease;
}

.hot-keyword:hover {
  transform: scale(1.05);
}

.recommended-knowledge {
  max-height: 300px;
  overflow-y: auto;
}

.recommended-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.recommended-item:hover {
  background-color: #f8f9fa;
}

.recommended-item:last-child {
  border-bottom: none;
}

.recommended-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recommended-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommended-views {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-search {
    padding: 10px;
  }

  .search-container {
    max-width: 100%;
  }

  .main-search :deep(.el-input__inner) {
    height: 44px;
    border-radius: 22px;
  }

  .main-search :deep(.el-input-group__append) {
    border-radius: 0 22px 22px 0;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .result-meta {
    flex-wrap: wrap;
    gap: 8px;
  }

  .grid-item {
    height: 160px;
  }

  .advanced-search .el-row {
    flex-direction: column;
  }

  .advanced-search .el-col {
    margin-bottom: 12px;
  }
}
</style>
