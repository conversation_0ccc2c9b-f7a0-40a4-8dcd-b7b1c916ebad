<template>
  <div class="knowledge-base">
    <div class="page-header">
      <h2>知识库</h2>
      <p>浏览和搜索知识库内容</p>
      <div class="header-actions">
        <el-button type="primary" @click="createKnowledge">
          <el-icon><Plus /></el-icon>
          创建知识
        </el-button>
        <el-button @click="importKnowledge">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="exportKnowledge">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <el-card class="search-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索知识标题、内容..."
            prefix-icon="Search"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :xs="24" :sm="12" :md="4">
          <el-select
            v-model="searchForm.category"
            placeholder="选择分类"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="4">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
            <el-option label="审核中" value="review" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="4">
          <el-select
            v-model="searchForm.sortBy"
            placeholder="排序方式"
            @change="handleSearch"
          >
            <el-option label="最新更新" value="updateTime" />
            <el-option label="创建时间" value="createTime" />
            <el-option label="浏览量" value="views" />
            <el-option label="评分" value="rating" />
            <el-option label="标题" value="title" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="4">
          <div class="view-controls">
            <el-button-group>
              <el-button
                :type="viewMode === 'list' ? 'primary' : ''"
                @click="viewMode = 'list'"
              >
                <el-icon><List /></el-icon>
              </el-button>
              <el-button
                :type="viewMode === 'grid' ? 'primary' : ''"
                @click="viewMode = 'grid'"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧分类树 -->
        <el-col :xs="24" :lg="6">
          <el-card class="category-card">
            <template #header>
              <div class="card-header">
                <span>知识分类</span>
                <el-button type="text" size="small" @click="manageCategoriesDialog = true">
                  管理
                </el-button>
              </div>
            </template>
            <el-tree
              :data="categoryTree"
              :props="{ children: 'children', label: 'name' }"
              :default-expand-all="true"
              :highlight-current="true"
              node-key="id"
              @node-click="handleCategoryClick"
            >
              <template #default="{ node, data }">
                <div class="category-node">
                  <span class="category-name">{{ data.name }}</span>
                  <span class="category-count">({{ data.count || 0 }})</span>
                </div>
              </template>
            </el-tree>
          </el-card>

          <!-- 热门标签 -->
          <el-card class="tags-card">
            <template #header>
              <span>热门标签</span>
            </template>
            <div class="tags-container">
              <el-tag
                v-for="tag in hotTags"
                :key="tag.id"
                :type="getTagType(tag.count)"
                class="tag-item"
                @click="handleTagClick(tag)"
              >
                {{ tag.name }} ({{ tag.count }})
              </el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧知识列表 -->
        <el-col :xs="24" :lg="18">
          <el-card class="content-card">
            <template #header>
              <div class="card-header">
                <span>知识列表 ({{ pagination.total }} 篇)</span>
                <div class="header-actions">
                  <el-button
                    type="text"
                    size="small"
                    @click="batchOperationDialog = true"
                    :disabled="selectedKnowledge.length === 0"
                  >
                    批量操作 ({{ selectedKnowledge.length }})
                  </el-button>
                </div>
              </div>
            </template>

            <!-- 列表视图 -->
            <div v-if="viewMode === 'list'" class="list-view">
              <el-table
                :data="knowledgeList"
                v-loading="loading"
                @selection-change="handleSelectionChange"
                @sort-change="handleSortChange"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="title" label="标题" min-width="200" sortable="custom">
                  <template #default="{ row }">
                    <div class="title-cell">
                      <el-link
                        type="primary"
                        @click="viewKnowledge(row)"
                        class="knowledge-title"
                      >
                        {{ row.title }}
                      </el-link>
                      <div class="knowledge-summary">{{ row.summary }}</div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="category" label="分类" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" :type="getCategoryType(row.category)">
                      {{ row.category }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="author" label="作者" width="100" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag size="small" :type="getStatusType(row.status)">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="views" label="浏览" width="80" sortable="custom" />
                <el-table-column prop="rating" label="评分" width="80" sortable="custom">
                  <template #default="{ row }">
                    <el-rate
                      v-model="row.rating"
                      disabled
                      show-score
                      text-color="#ff9900"
                      score-template="{value}"
                      size="small"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="更新时间" width="150" sortable="custom">
                  <template #default="{ row }">
                    {{ formatTime(row.updateTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button type="text" size="small" @click="viewKnowledge(row)">
                      <el-icon><View /></el-icon>
                      查看
                    </el-button>
                    <el-button type="text" size="small" @click="editKnowledge(row)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-dropdown @command="(command) => handleKnowledgeAction(command, row)">
                      <el-button type="text" size="small">
                        更多<el-icon><ArrowDown /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="copy">复制</el-dropdown-item>
                          <el-dropdown-item command="share">分享</el-dropdown-item>
                          <el-dropdown-item command="export">导出</el-dropdown-item>
                          <el-dropdown-item command="archive" divided>归档</el-dropdown-item>
                          <el-dropdown-item command="delete" class="danger">删除</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 网格视图 -->
            <div v-else class="grid-view">
              <el-row :gutter="20">
                <el-col
                  :xs="24" :sm="12" :md="8" :lg="6"
                  v-for="item in knowledgeList"
                  :key="item.id"
                >
                  <el-card class="knowledge-card" @click="viewKnowledge(item)">
                    <div class="card-header">
                      <el-checkbox
                        v-model="item.selected"
                        @change="handleCardSelection(item)"
                        @click.stop
                      />
                      <el-dropdown @command="(command) => handleKnowledgeAction(command, item)">
                        <el-button type="text" size="small" @click.stop>
                          <el-icon><MoreFilled /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="edit">编辑</el-dropdown-item>
                            <el-dropdown-item command="copy">复制</el-dropdown-item>
                            <el-dropdown-item command="share">分享</el-dropdown-item>
                            <el-dropdown-item command="export">导出</el-dropdown-item>
                            <el-dropdown-item command="archive" divided>归档</el-dropdown-item>
                            <el-dropdown-item command="delete" class="danger">删除</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                    <div class="card-content">
                      <h3 class="card-title">{{ item.title }}</h3>
                      <p class="card-summary">{{ item.summary }}</p>
                      <div class="card-meta">
                        <el-tag size="small" :type="getCategoryType(item.category)">
                          {{ item.category }}
                        </el-tag>
                        <el-tag size="small" :type="getStatusType(item.status)">
                          {{ getStatusText(item.status) }}
                        </el-tag>
                      </div>
                      <div class="card-stats">
                        <span class="stat-item">
                          <el-icon><View /></el-icon>
                          {{ item.views }}
                        </span>
                        <span class="stat-item">
                          <el-icon><Star /></el-icon>
                          {{ item.rating }}
                        </span>
                        <span class="stat-item">
                          <el-icon><User /></el-icon>
                          {{ item.author }}
                        </span>
                      </div>
                      <div class="card-time">
                        {{ formatTime(item.updateTime) }}
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="batchOperationDialog"
      title="批量操作"
      width="500px"
    >
      <div class="batch-operations">
        <p>已选择 {{ selectedKnowledge.length }} 篇知识</p>
        <el-button-group>
          <el-button @click="batchPublish">批量发布</el-button>
          <el-button @click="batchArchive">批量归档</el-button>
          <el-button @click="batchDelete" type="danger">批量删除</el-button>
        </el-button-group>
      </div>
      <template #footer>
        <el-button @click="batchOperationDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 分类管理对话框 -->
    <el-dialog
      v-model="manageCategoriesDialog"
      title="分类管理"
      width="600px"
    >
      <div class="category-management">
        <el-button type="primary" @click="addCategory">
          <el-icon><Plus /></el-icon>
          添加分类
        </el-button>
        <!-- 这里可以添加分类管理的具体内容 -->
      </div>
      <template #footer>
        <el-button @click="manageCategoriesDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Upload, Download, Search, List, Grid, View, Edit,
  ArrowDown, MoreFilled, Star, User
} from '@element-plus/icons-vue'
import {
  getKnowledgeList,
  getKnowledgeCategories,
  deleteKnowledge,
  batchOperateKnowledge
} from '@/api/knowledgeApi'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const viewMode = ref('list') // 'list' | 'grid'
const batchOperationDialog = ref(false)
const manageCategoriesDialog = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  status: '',
  sortBy: 'updateTime'
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 选中的知识
const selectedKnowledge = ref([])

// 知识列表
const knowledgeList = ref([
  {
    id: 1,
    title: '服务器性能监控配置完整指南',
    summary: '详细介绍如何配置服务器性能监控系统，包括CPU、内存、磁盘等关键指标的监控设置，以及告警规则的配置方法。',
    category: '技术文档',
    author: '张工',
    status: 'published',
    views: 2456,
    rating: 4.8,
    updateTime: '2025-01-30 14:30',
    createTime: '2025-01-25 09:15',
    selected: false
  },
  {
    id: 2,
    title: '网络故障快速排除流程',
    summary: '标准化的网络故障排除流程，包括问题诊断步骤、常用工具使用方法和解决方案模板。',
    category: '流程规范',
    author: '李工',
    status: 'published',
    views: 1892,
    rating: 4.6,
    updateTime: '2025-01-29 16:45',
    createTime: '2025-01-20 11:30',
    selected: false
  },
  {
    id: 3,
    title: '邮箱配置常见问题解答',
    summary: '用户在配置邮箱时经常遇到的问题及解决方案，包括SMTP、POP3、IMAP等配置详解。',
    category: 'FAQ',
    author: '王工',
    status: 'review',
    views: 1654,
    rating: 4.5,
    updateTime: '2025-01-28 10:20',
    createTime: '2025-01-22 14:45',
    selected: false
  },
  {
    id: 4,
    title: 'VPN连接问题处理方案',
    summary: '针对VPN连接失败、速度慢等常见问题的诊断和解决方案，包括客户端配置和服务器端排查。',
    category: '解决方案',
    author: '赵工',
    status: 'draft',
    views: 1432,
    rating: 4.7,
    updateTime: '2025-01-27 15:10',
    createTime: '2025-01-18 16:20',
    selected: false
  },
  {
    id: 5,
    title: '数据库备份恢复操作手册',
    summary: '详细的数据库备份和恢复操作指南，包括定时备份设置、增量备份策略和灾难恢复流程。',
    category: '操作指南',
    author: '孙工',
    status: 'published',
    views: 1298,
    rating: 4.4,
    updateTime: '2025-01-26 09:30',
    createTime: '2025-01-15 13:45',
    selected: false
  }
])

// 分类数据
const categories = ref([
  { id: '', name: '全部分类' },
  { id: 1, name: '技术文档' },
  { id: 2, name: '流程规范' },
  { id: 3, name: 'FAQ' },
  { id: 4, name: '解决方案' },
  { id: 5, name: '操作指南' }
])

// 分类树数据
const categoryTree = ref([
  {
    id: 1,
    name: '技术文档',
    count: 456,
    children: [
      { id: 11, name: '系统配置', count: 123 },
      { id: 12, name: '故障排除', count: 89 },
      { id: 13, name: '性能优化', count: 67 }
    ]
  },
  {
    id: 2,
    name: '流程规范',
    count: 234,
    children: [
      { id: 21, name: '事件处理', count: 78 },
      { id: 22, name: '变更管理', count: 56 },
      { id: 23, name: '服务请求', count: 45 }
    ]
  },
  {
    id: 3,
    name: 'FAQ',
    count: 189,
    children: [
      { id: 31, name: '常见问题', count: 123 },
      { id: 32, name: '用户指南', count: 66 }
    ]
  },
  {
    id: 4,
    name: '解决方案',
    count: 167
  },
  {
    id: 5,
    name: '操作指南',
    count: 123
  }
])

// 热门标签
const hotTags = ref([
  { id: 1, name: '服务器', count: 156 },
  { id: 2, name: '网络', count: 134 },
  { id: 3, name: '数据库', count: 98 },
  { id: 4, name: '安全', count: 87 },
  { id: 5, name: '监控', count: 76 },
  { id: 6, name: '备份', count: 65 },
  { id: 7, name: 'VPN', count: 54 },
  { id: 8, name: '邮箱', count: 43 }
])

// 生命周期
onMounted(() => {
  loadKnowledgeList()
  loadCategories()
})

// 加载知识列表
const loadKnowledgeList = async () => {
  loading.value = true
  try {
    // 这里调用实际的API
    // const response = await getKnowledgeList({
    //   ...searchForm,
    //   page: pagination.currentPage,
    //   pageSize: pagination.pageSize
    // })
    // knowledgeList.value = response.data.list
    // pagination.total = response.data.total

    // 模拟数据
    pagination.total = 156
    ElMessage.success('知识列表加载完成')
  } catch (error) {
    ElMessage.error('加载知识列表失败')
  } finally {
    loading.value = false
  }
}

// 加载分类
const loadCategories = async () => {
  try {
    // const response = await getKnowledgeCategories()
    // categories.value = response.data
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  loadKnowledgeList()
}

// 分类点击
const handleCategoryClick = (data) => {
  searchForm.category = data.id
  handleSearch()
}

// 标签点击
const handleTagClick = (tag) => {
  searchForm.keyword = tag.name
  handleSearch()
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedKnowledge.value = selection
}

// 卡片选择
const handleCardSelection = (item) => {
  if (item.selected) {
    selectedKnowledge.value.push(item)
  } else {
    const index = selectedKnowledge.value.findIndex(k => k.id === item.id)
    if (index > -1) {
      selectedKnowledge.value.splice(index, 1)
    }
  }
}

// 排序变化
const handleSortChange = ({ prop, order }) => {
  searchForm.sortBy = prop
  searchForm.sortOrder = order
  loadKnowledgeList()
}

// 分页变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadKnowledgeList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadKnowledgeList()
}

// 知识操作
const createKnowledge = () => {
  router.push('/knowledge/create')
}

const viewKnowledge = (item) => {
  router.push(`/knowledge/articles/${item.id}`)
}

const editKnowledge = (item) => {
  router.push(`/knowledge/articles/${item.id}/edit`)
}

const handleKnowledgeAction = async (command, item) => {
  switch (command) {
    case 'edit':
      editKnowledge(item)
      break
    case 'copy':
      ElMessage.info('复制功能开发中...')
      break
    case 'share':
      ElMessage.info('分享功能开发中...')
      break
    case 'export':
      ElMessage.info('导出功能开发中...')
      break
    case 'archive':
      await handleArchive(item)
      break
    case 'delete':
      await handleDelete(item)
      break
  }
}

// 归档知识
const handleArchive = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要归档知识"${item.title}"吗？`,
      '确认归档',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // await archiveKnowledge(item.id)
    ElMessage.success('归档成功')
    loadKnowledgeList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('归档失败')
    }
  }
}

// 删除知识
const handleDelete = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除知识"${item.title}"吗？删除后无法恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    // await deleteKnowledge(item.id)
    ElMessage.success('删除成功')
    loadKnowledgeList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量操作
const batchPublish = async () => {
  try {
    const ids = selectedKnowledge.value.map(item => item.id)
    // await batchOperateKnowledge({ action: 'publish', ids })
    ElMessage.success('批量发布成功')
    batchOperationDialog.value = false
    selectedKnowledge.value = []
    loadKnowledgeList()
  } catch (error) {
    ElMessage.error('批量发布失败')
  }
}

const batchArchive = async () => {
  try {
    const ids = selectedKnowledge.value.map(item => item.id)
    // await batchOperateKnowledge({ action: 'archive', ids })
    ElMessage.success('批量归档成功')
    batchOperationDialog.value = false
    selectedKnowledge.value = []
    loadKnowledgeList()
  } catch (error) {
    ElMessage.error('批量归档失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedKnowledge.value.length} 篇知识吗？删除后无法恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const ids = selectedKnowledge.value.map(item => item.id)
    // await batchOperateKnowledge({ action: 'delete', ids })
    ElMessage.success('批量删除成功')
    batchOperationDialog.value = false
    selectedKnowledge.value = []
    loadKnowledgeList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 导入导出
const importKnowledge = () => {
  ElMessage.info('批量导入功能开发中...')
}

const exportKnowledge = () => {
  ElMessage.info('导出功能开发中...')
}

// 分类管理
const addCategory = () => {
  ElMessage.info('添加分类功能开发中...')
}

// 工具函数
const getCategoryType = (category) => {
  const types = {
    '技术文档': 'primary',
    '流程规范': 'success',
    'FAQ': 'warning',
    '解决方案': 'danger',
    '操作指南': 'info'
  }
  return types[category] || ''
}

const getStatusType = (status) => {
  const types = {
    'published': 'success',
    'draft': 'info',
    'review': 'warning',
    'archived': 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    'published': '已发布',
    'draft': '草稿',
    'review': '审核中',
    'archived': '已归档'
  }
  return texts[status] || status
}

const getTagType = (count) => {
  if (count > 100) return 'danger'
  if (count > 50) return 'warning'
  if (count > 20) return 'primary'
  return ''
}

const formatTime = (time) => {
  // 简单的时间格式化
  return time
}
</script>

<style scoped>
.knowledge-base {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-section {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-controls {
  display: flex;
  justify-content: flex-end;
}

.main-content {
  margin-top: 20px;
}

.category-card,
.tags-card,
.content-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.category-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.category-name {
  flex: 1;
}

.category-count {
  color: #909399;
  font-size: 12px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag-item:hover {
  transform: scale(1.05);
}

/* 列表视图样式 */
.list-view {
  margin-bottom: 20px;
}

.title-cell {
  max-width: 300px;
}

.knowledge-title {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
  display: block;
}

.knowledge-summary {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 网格视图样式 */
.grid-view {
  margin-bottom: 20px;
}

.knowledge-card {
  height: 280px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.knowledge-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.knowledge-card .card-header {
  position: absolute;
  top: 12px;
  right: 12px;
  left: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1;
}

.knowledge-card .card-content {
  padding: 40px 16px 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-summary {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin: 0 0 12px 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.card-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  color: #909399;
}

.card-time {
  font-size: 12px;
  color: #C0C4CC;
  text-align: right;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 对话框样式 */
.batch-operations {
  text-align: center;
  padding: 20px 0;
}

.batch-operations p {
  margin-bottom: 20px;
  color: #606266;
}

.category-management {
  padding: 20px 0;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
}

/* 下拉菜单危险项样式 */
:deep(.el-dropdown-menu__item.danger) {
  color: #F56C6C;
}

:deep(.el-dropdown-menu__item.danger:hover) {
  background-color: #fef0f0;
  color: #F56C6C;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-base {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .search-section .el-row {
    flex-direction: column;
  }

  .search-section .el-col {
    margin-bottom: 12px;
  }

  .knowledge-card {
    height: 240px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-summary {
    -webkit-line-clamp: 2;
  }
}

@media (max-width: 480px) {
  .grid-view .el-col {
    padding: 0 5px;
    margin-bottom: 10px;
  }

  .knowledge-card {
    height: 200px;
  }

  .card-stats {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
