import * as cmdbApi from '@/api/cmdb'
import { ElMessage } from 'element-plus'

/**
 * CMDB 数据服务类
 * 提供统一的数据处理和业务逻辑
 */
class CMDBService {
  constructor() {
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据
   */
  getCache(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }
    this.cache.delete(key)
    return null
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} data - 数据
   */
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 清除缓存
   * @param {string} key - 缓存键，不传则清除所有
   */
  clearCache(key) {
    if (key) {
      this.cache.delete(key)
    } else {
      this.cache.clear()
    }
  }

  // 配置项管理相关方法

  /**
   * 获取配置项列表（带缓存）
   * @param {Object} params - 查询参数
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise}
   */
  async getCIList(params = {}, useCache = true) {
    const cacheKey = `ci_list_${JSON.stringify(params)}`
    
    if (useCache) {
      const cached = this.getCache(cacheKey)
      if (cached) return cached
    }

    try {
      const response = await cmdbApi.getCIList(params)
      const data = this.processCIListData(response.data)
      
      if (useCache) {
        this.setCache(cacheKey, data)
      }
      
      return data
    } catch (error) {
      ElMessage.error('获取配置项列表失败')
      throw error
    }
  }

  /**
   * 处理配置项列表数据
   * @param {Object} data - 原始数据
   * @returns {Object} 处理后的数据
   */
  processCIListData(data) {
    if (!data || !data.items) return { items: [], total: 0 }

    // 数据处理逻辑
    const items = data.items.map(item => ({
      ...item,
      statusColor: this.getStatusColor(item.status),
      typeIcon: this.getTypeIcon(item.type),
      lastUpdateTime: this.formatTime(item.updateTime)
    }))

    return {
      items,
      total: data.total || items.length,
      stats: data.stats || this.calculateStats(items)
    }
  }

  /**
   * 获取配置项详情
   * @param {string} id - 配置项ID
   * @returns {Promise}
   */
  async getCIDetail(id) {
    const cacheKey = `ci_detail_${id}`
    const cached = this.getCache(cacheKey)
    if (cached) return cached

    try {
      const response = await cmdbApi.getCIDetail(id)
      const data = this.processCIDetailData(response.data)
      this.setCache(cacheKey, data)
      return data
    } catch (error) {
      ElMessage.error('获取配置项详情失败')
      throw error
    }
  }

  /**
   * 处理配置项详情数据
   * @param {Object} data - 原始数据
   * @returns {Object} 处理后的数据
   */
  processCIDetailData(data) {
    return {
      ...data,
      statusColor: this.getStatusColor(data.status),
      typeIcon: this.getTypeIcon(data.type),
      healthScore: this.calculateHealthScore(data),
      relatedEvents: data.relatedEvents || [],
      relatedChanges: data.relatedChanges || [],
      versions: data.versions || []
    }
  }

  /**
   * 创建或更新配置项
   * @param {Object} data - 配置项数据
   * @param {string} id - 配置项ID（更新时需要）
   * @returns {Promise}
   */
  async saveCI(data, id = null) {
    try {
      // 数据验证
      this.validateCIData(data)

      let response
      if (id) {
        response = await cmdbApi.updateCI(id, data)
        ElMessage.success('配置项更新成功')
      } else {
        response = await cmdbApi.createCI(data)
        ElMessage.success('配置项创建成功')
      }

      // 清除相关缓存
      this.clearCache()
      
      return response.data
    } catch (error) {
      ElMessage.error(id ? '配置项更新失败' : '配置项创建失败')
      throw error
    }
  }

  /**
   * 删除配置项
   * @param {string} id - 配置项ID
   * @returns {Promise}
   */
  async deleteCI(id) {
    try {
      await cmdbApi.deleteCI(id)
      ElMessage.success('配置项删除成功')
      this.clearCache()
    } catch (error) {
      ElMessage.error('配置项删除失败')
      throw error
    }
  }

  // 拓扑图相关方法

  /**
   * 获取拓扑图数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getTopologyData(params = {}) {
    const cacheKey = `topology_${JSON.stringify(params)}`
    const cached = this.getCache(cacheKey)
    if (cached) return cached

    try {
      const response = await cmdbApi.getTopologyData(params)
      const data = this.processTopologyData(response.data)
      this.setCache(cacheKey, data)
      return data
    } catch (error) {
      ElMessage.error('获取拓扑图数据失败')
      throw error
    }
  }

  /**
   * 处理拓扑图数据
   * @param {Object} data - 原始数据
   * @returns {Object} 处理后的数据
   */
  processTopologyData(data) {
    const nodes = (data.nodes || []).map(node => ({
      ...node,
      icon: this.getTypeIcon(node.type),
      color: this.getStatusColor(node.status),
      size: this.calculateNodeSize(node)
    }))

    const connections = (data.connections || []).map(conn => ({
      ...conn,
      color: this.getConnectionColor(conn.type),
      style: this.getConnectionStyle(conn.type)
    }))

    return { nodes, connections }
  }

  // 数据质量相关方法

  /**
   * 获取数据质量报告
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getQualityReport(params = {}) {
    try {
      const response = await cmdbApi.getQualityReport(params)
      return this.processQualityData(response.data)
    } catch (error) {
      ElMessage.error('获取数据质量报告失败')
      throw error
    }
  }

  /**
   * 处理数据质量数据
   * @param {Object} data - 原始数据
   * @returns {Object} 处理后的数据
   */
  processQualityData(data) {
    return {
      ...data,
      overallScore: this.calculateOverallQualityScore(data),
      issues: (data.issues || []).map(issue => ({
        ...issue,
        severityColor: this.getSeverityColor(issue.severity),
        categoryIcon: this.getCategoryIcon(issue.category)
      }))
    }
  }

  // 工具方法

  /**
   * 验证配置项数据
   * @param {Object} data - 配置项数据
   */
  validateCIData(data) {
    if (!data.name) {
      throw new Error('配置项名称不能为空')
    }
    if (!data.type) {
      throw new Error('配置项类型不能为空')
    }
    if (data.ip && !this.isValidIP(data.ip)) {
      throw new Error('IP地址格式不正确')
    }
  }

  /**
   * 验证IP地址格式
   * @param {string} ip - IP地址
   * @returns {boolean}
   */
  isValidIP(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    return ipRegex.test(ip)
  }

  /**
   * 获取状态颜色
   * @param {string} status - 状态
   * @returns {string} 颜色
   */
  getStatusColor(status) {
    const colorMap = {
      '运行中': '#4CAF50',
      '正常': '#4CAF50',
      '维护中': '#FF9800',
      '警告': '#FF9800',
      '故障': '#F44336',
      '错误': '#F44336',
      '离线': '#9E9E9E'
    }
    return colorMap[status] || '#9E9E9E'
  }

  /**
   * 获取类型图标
   * @param {string} type - 类型
   * @returns {string} 图标名称
   */
  getTypeIcon(type) {
    const iconMap = {
      'server': 'Monitor',
      'database': 'Coin',
      'network': 'Share',
      'application': 'Service',
      'storage': 'Box'
    }
    return iconMap[type] || 'Box'
  }

  /**
   * 格式化时间
   * @param {string} time - 时间字符串
   * @returns {string} 格式化后的时间
   */
  formatTime(time) {
    if (!time) return ''
    const date = new Date(time)
    const now = new Date()
    const diff = now - date
    
    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
    return date.toLocaleDateString()
  }

  /**
   * 计算统计数据
   * @param {Array} items - 配置项列表
   * @returns {Object} 统计数据
   */
  calculateStats(items) {
    const stats = {
      total: items.length,
      byType: {},
      byStatus: {}
    }

    items.forEach(item => {
      // 按类型统计
      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1
      // 按状态统计
      stats.byStatus[item.status] = (stats.byStatus[item.status] || 0) + 1
    })

    return stats
  }

  /**
   * 计算健康分数
   * @param {Object} ci - 配置项数据
   * @returns {number} 健康分数
   */
  calculateHealthScore(ci) {
    let score = 100
    
    // 根据状态扣分
    if (ci.status === '故障') score -= 50
    else if (ci.status === '警告') score -= 20
    else if (ci.status === '维护中') score -= 10
    
    // 根据最后更新时间扣分
    const lastUpdate = new Date(ci.updateTime)
    const daysSinceUpdate = (Date.now() - lastUpdate) / (1000 * 60 * 60 * 24)
    if (daysSinceUpdate > 30) score -= 10
    else if (daysSinceUpdate > 7) score -= 5
    
    return Math.max(0, Math.min(100, score))
  }

  /**
   * 计算节点大小
   * @param {Object} node - 节点数据
   * @returns {number} 节点大小
   */
  calculateNodeSize(node) {
    const baseSize = 30
    const importance = node.importance || 1
    return baseSize + (importance - 1) * 5
  }

  /**
   * 获取连接线颜色
   * @param {string} type - 连接类型
   * @returns {string} 颜色
   */
  getConnectionColor(type) {
    const colorMap = {
      'depends': '#F44336',
      'connects': '#2196F3',
      'contains': '#4CAF50',
      'monitors': '#FF9800'
    }
    return colorMap[type] || '#666'
  }

  /**
   * 获取连接线样式
   * @param {string} type - 连接类型
   * @returns {string} 样式
   */
  getConnectionStyle(type) {
    const styleMap = {
      'depends': 'dashed',
      'connects': 'solid',
      'contains': 'solid',
      'monitors': 'dotted'
    }
    return styleMap[type] || 'solid'
  }

  /**
   * 计算总体质量分数
   * @param {Object} data - 质量数据
   * @returns {number} 总体分数
   */
  calculateOverallQualityScore(data) {
    if (!data.metrics) return 0
    
    const scores = Object.values(data.metrics).map(metric => metric.score)
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
  }

  /**
   * 获取严重程度颜色
   * @param {string} severity - 严重程度
   * @returns {string} 颜色
   */
  getSeverityColor(severity) {
    const colorMap = {
      'critical': '#F44336',
      'warning': '#FF9800',
      'info': '#2196F3'
    }
    return colorMap[severity] || '#9E9E9E'
  }

  /**
   * 获取分类图标
   * @param {string} category - 分类
   * @returns {string} 图标名称
   */
  getCategoryIcon(category) {
    const iconMap = {
      'completeness': 'Warning',
      'consistency': 'CircleClose',
      'accuracy': 'QuestionFilled',
      'freshness': 'Timer'
    }
    return iconMap[category] || 'Warning'
  }
}

// 创建单例实例
const cmdbService = new CMDBService()

export default cmdbService
