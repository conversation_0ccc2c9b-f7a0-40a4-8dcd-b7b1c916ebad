import { reactive } from 'vue'

/**
 * 事件总线类
 * 用于组件间和模块间的通信
 */
class EventBus {
  constructor() {
    this.events = reactive({})
    this.listeners = new Map()
  }

  /**
   * 监听事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    
    this.listeners.get(event).add(callback)
    
    // 返回取消监听的函数
    return () => {
      this.off(event, callback)
    }
  }

  /**
   * 监听一次事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  once(event, callback) {
    const onceCallback = (...args) => {
      callback(...args)
      this.off(event, onceCallback)
    }
    
    return this.on(event, onceCallback)
  }

  /**
   * 取消监听事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback)
      
      // 如果没有监听器了，删除事件
      if (this.listeners.get(event).size === 0) {
        this.listeners.delete(event)
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {...any} args - 参数
   */
  emit(event, ...args) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`Event listener error for "${event}":`, error)
        }
      })
    }
  }

  /**
   * 清除所有监听器
   */
  clear() {
    this.listeners.clear()
  }

  /**
   * 获取事件监听器数量
   * @param {string} event - 事件名称
   * @returns {number} 监听器数量
   */
  listenerCount(event) {
    return this.listeners.has(event) ? this.listeners.get(event).size : 0
  }

  /**
   * 获取所有事件名称
   * @returns {Array} 事件名称数组
   */
  eventNames() {
    return Array.from(this.listeners.keys())
  }
}

// 创建全局事件总线实例
const eventBus = new EventBus()

// CMDB 相关事件常量
export const CMDB_EVENTS = {
  // 配置项相关事件
  CI_CREATED: 'ci:created',
  CI_UPDATED: 'ci:updated',
  CI_DELETED: 'ci:deleted',
  CI_STATUS_CHANGED: 'ci:status_changed',
  CI_SELECTED: 'ci:selected',
  
  // 关系相关事件
  RELATION_CREATED: 'relation:created',
  RELATION_UPDATED: 'relation:updated',
  RELATION_DELETED: 'relation:deleted',
  RELATION_VALIDATED: 'relation:validated',
  
  // 版本相关事件
  VERSION_CREATED: 'version:created',
  VERSION_RESTORED: 'version:restored',
  VERSION_COMPARED: 'version:compared',
  
  // 发现相关事件
  DISCOVERY_STARTED: 'discovery:started',
  DISCOVERY_COMPLETED: 'discovery:completed',
  DISCOVERY_FAILED: 'discovery:failed',
  
  // 质量相关事件
  QUALITY_CHECK_STARTED: 'quality:check_started',
  QUALITY_CHECK_COMPLETED: 'quality:check_completed',
  QUALITY_ISSUE_FIXED: 'quality:issue_fixed',
  
  // 拓扑相关事件
  TOPOLOGY_UPDATED: 'topology:updated',
  TOPOLOGY_NODE_SELECTED: 'topology:node_selected',
  TOPOLOGY_LAYOUT_CHANGED: 'topology:layout_changed',
  
  // 数据同步事件
  DATA_SYNC_STARTED: 'data:sync_started',
  DATA_SYNC_COMPLETED: 'data:sync_completed',
  DATA_SYNC_FAILED: 'data:sync_failed',
  
  // 缓存相关事件
  CACHE_CLEARED: 'cache:cleared',
  CACHE_UPDATED: 'cache:updated'
}

// 事件处理器工厂函数
export const createEventHandler = (eventType, handler) => {
  return {
    eventType,
    handler,
    unsubscribe: eventBus.on(eventType, handler)
  }
}

// 批量事件监听器
export const createBatchEventListener = (eventHandlers) => {
  const unsubscribers = eventHandlers.map(({ eventType, handler }) => 
    eventBus.on(eventType, handler)
  )
  
  return {
    unsubscribeAll: () => {
      unsubscribers.forEach(unsubscribe => unsubscribe())
    }
  }
}

// 事件日志记录器
export const createEventLogger = (events = []) => {
  const logs = reactive([])
  const maxLogs = 100
  
  const logEvent = (event, ...args) => {
    const log = {
      id: Date.now() + Math.random(),
      event,
      args,
      timestamp: new Date().toISOString()
    }
    
    logs.unshift(log)
    
    // 保持日志数量在限制内
    if (logs.length > maxLogs) {
      logs.splice(maxLogs)
    }
  }
  
  // 监听指定事件
  const unsubscribers = events.map(event => 
    eventBus.on(event, (...args) => logEvent(event, ...args))
  )
  
  return {
    logs,
    clear: () => logs.splice(0),
    destroy: () => unsubscribers.forEach(unsubscribe => unsubscribe())
  }
}

// 事件防抖处理器
export const createDebouncedEventHandler = (eventType, handler, delay = 300) => {
  let timeoutId = null
  
  const debouncedHandler = (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      handler(...args)
      timeoutId = null
    }, delay)
  }
  
  return {
    unsubscribe: eventBus.on(eventType, debouncedHandler),
    cancel: () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    }
  }
}

// 事件节流处理器
export const createThrottledEventHandler = (eventType, handler, delay = 300) => {
  let lastExecuted = 0
  let timeoutId = null
  
  const throttledHandler = (...args) => {
    const now = Date.now()
    
    if (now - lastExecuted >= delay) {
      handler(...args)
      lastExecuted = now
    } else if (!timeoutId) {
      timeoutId = setTimeout(() => {
        handler(...args)
        lastExecuted = Date.now()
        timeoutId = null
      }, delay - (now - lastExecuted))
    }
  }
  
  return {
    unsubscribe: eventBus.on(eventType, throttledHandler),
    cancel: () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    }
  }
}

// 条件事件处理器
export const createConditionalEventHandler = (eventType, condition, handler) => {
  const conditionalHandler = (...args) => {
    if (condition(...args)) {
      handler(...args)
    }
  }
  
  return eventBus.on(eventType, conditionalHandler)
}

// 事件链处理器
export const createEventChain = (chains) => {
  const unsubscribers = []
  
  chains.forEach(({ trigger, target, transform }) => {
    const unsubscribe = eventBus.on(trigger, (...args) => {
      const transformedArgs = transform ? transform(...args) : args
      eventBus.emit(target, ...transformedArgs)
    })
    unsubscribers.push(unsubscribe)
  })
  
  return {
    destroy: () => unsubscribers.forEach(unsubscribe => unsubscribe())
  }
}

// 导出事件总线实例
export default eventBus
