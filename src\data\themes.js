// 可视化大屏主题系统
export const themes = {
  // 深色主题
  'dark-cyber': {
    name: '深空蓝',
    type: 'dark',
    colors: {
      primary: '#409eff',
      success: '#67c23a',
      warning: '#e6a23c',
      danger: '#f56c6c',
      info: '#909399',
      accent: '#00ff88',
      purple: '#6b4eff'
    },
    background: {
      primary: '#0a1a2f',
      secondary: '#1a2a4f',
      tertiary: '#2a3a6f',
      surface: '#252525',
      card: 'rgba(255,255,255,0.1)'
    },
    text: {
      primary: '#ffffff',
      secondary: '#cccccc',
      tertiary: '#909399',
      disabled: '#606266'
    },
    border: {
      primary: '#404040',
      secondary: '#606060',
      light: '#dcdfe6'
    },
    chart: {
      colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#6b4eff', '#00ff88'],
      grid: '#404040',
      axis: '#909399',
      tooltip: 'rgba(0,0,0,0.8)'
    }
  },

  // 浅色主题
  'light': {
    name: '简约白',
    type: 'light',
    colors: {
      primary: '#409eff',
      success: '#67c23a',
      warning: '#e6a23c',
      danger: '#f56c6c',
      info: '#909399',
      accent: '#00d4aa',
      purple: '#6b4eff'
    },
    background: {
      primary: '#ffffff',
      secondary: '#f5f7fa',
      tertiary: '#ebeef5',
      surface: '#ffffff',
      card: '#ffffff'
    },
    text: {
      primary: '#303133',
      secondary: '#606266',
      tertiary: '#909399',
      disabled: '#c0c4cc'
    },
    border: {
      primary: '#dcdfe6',
      secondary: '#e4e7ed',
      light: '#ebeef5'
    },
    chart: {
      colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#6b4eff', '#00d4aa'],
      grid: '#f0f0f0',
      axis: '#606266',
      tooltip: 'rgba(255,255,255,0.95)'
    }
  },

  // 科技蓝主题
  'tech-blue': {
    name: '科技蓝',
    type: 'dark',
    colors: {
      primary: '#00d4ff',
      success: '#00ff88',
      warning: '#ffb800',
      danger: '#ff4757',
      info: '#74b9ff',
      accent: '#00cec9',
      purple: '#a29bfe'
    },
    background: {
      primary: '#0c1426',
      secondary: '#1e2a3a',
      tertiary: '#2a3a4a',
      surface: '#1a2332',
      card: 'rgba(0,212,255,0.1)'
    },
    text: {
      primary: '#ffffff',
      secondary: '#b2d8ff',
      tertiary: '#74b9ff',
      disabled: '#4a5568'
    },
    border: {
      primary: '#00d4ff',
      secondary: '#74b9ff',
      light: '#b2d8ff'
    },
    chart: {
      colors: ['#00d4ff', '#00ff88', '#ffb800', '#ff4757', '#a29bfe', '#00cec9'],
      grid: '#2a3a4a',
      axis: '#74b9ff',
      tooltip: 'rgba(0,0,0,0.9)'
    }
  },

  // 赛博朋克主题
  'cyberpunk': {
    name: '赛博朋克',
    type: 'dark',
    colors: {
      primary: '#ff0080',
      success: '#00ff41',
      warning: '#ffff00',
      danger: '#ff073a',
      info: '#00ffff',
      accent: '#ff6b35',
      purple: '#bf00ff'
    },
    background: {
      primary: '#0a0a0a',
      secondary: '#1a0a1a',
      tertiary: '#2a1a2a',
      surface: '#1a1a1a',
      card: 'rgba(255,0,128,0.1)'
    },
    text: {
      primary: '#ffffff',
      secondary: '#ff6b35',
      tertiary: '#00ffff',
      disabled: '#666666'
    },
    border: {
      primary: '#ff0080',
      secondary: '#00ffff',
      light: '#ff6b35'
    },
    chart: {
      colors: ['#ff0080', '#00ff41', '#ffff00', '#ff073a', '#00ffff', '#bf00ff'],
      grid: '#2a1a2a',
      axis: '#00ffff',
      tooltip: 'rgba(0,0,0,0.95)'
    }
  }
}

// 获取所有主题
export const getAllThemes = () => {
  return Object.keys(themes).map(key => ({
    key,
    ...themes[key]
  }))
}

// 获取主题
export const getTheme = (themeKey) => {
  return themes[themeKey] || themes['dark-cyber']
}

// 应用主题到CSS变量
export const applyTheme = (themeKey) => {
  const theme = getTheme(themeKey)
  const root = document.documentElement

  // 应用颜色变量
  Object.keys(theme.colors).forEach(key => {
    root.style.setProperty(`--color-${key}`, theme.colors[key])
  })

  // 应用背景变量
  Object.keys(theme.background).forEach(key => {
    root.style.setProperty(`--bg-${key}`, theme.background[key])
  })

  // 应用文本变量
  Object.keys(theme.text).forEach(key => {
    root.style.setProperty(`--text-${key}`, theme.text[key])
  })

  // 应用边框变量
  Object.keys(theme.border).forEach(key => {
    root.style.setProperty(`--border-${key}`, theme.border[key])
  })

  // 应用图表变量
  Object.keys(theme.chart).forEach(key => {
    if (Array.isArray(theme.chart[key])) {
      theme.chart[key].forEach((color, index) => {
        root.style.setProperty(`--chart-color-${index}`, color)
      })
    } else {
      root.style.setProperty(`--chart-${key}`, theme.chart[key])
    }
  })

  // 保存当前主题
  localStorage.setItem('visualization-theme', themeKey)
}

// 获取当前主题
export const getCurrentTheme = () => {
  return localStorage.getItem('visualization-theme') || 'dark-cyber'
}

// 初始化主题
export const initTheme = () => {
  const currentTheme = getCurrentTheme()
  applyTheme(currentTheme)
  return currentTheme
}

// 主题预设配置
export const themePresets = {
  // ITSM服务台主题
  'itsm-service': {
    name: 'ITSM服务台',
    base: 'dark-cyber',
    customColors: {
      primary: '#409eff',
      success: '#67c23a',
      warning: '#e6a23c',
      danger: '#f56c6c'
    }
  },

  // ITSM监控主题
  'itsm-monitor': {
    name: 'ITSM监控',
    base: 'tech-blue',
    customColors: {
      primary: '#00d4ff',
      success: '#00ff88',
      warning: '#ffb800',
      danger: '#ff4757'
    }
  },

  // ITSM安全主题
  'itsm-security': {
    name: 'ITSM安全',
    base: 'cyberpunk',
    customColors: {
      primary: '#ff0080',
      success: '#00ff41',
      warning: '#ffff00',
      danger: '#ff073a'
    }
  }
}

// 创建自定义主题
export const createCustomTheme = (baseTheme, customizations) => {
  const base = getTheme(baseTheme)
  return {
    ...base,
    ...customizations,
    colors: {
      ...base.colors,
      ...(customizations.colors || {})
    },
    background: {
      ...base.background,
      ...(customizations.background || {})
    },
    text: {
      ...base.text,
      ...(customizations.text || {})
    },
    border: {
      ...base.border,
      ...(customizations.border || {})
    },
    chart: {
      ...base.chart,
      ...(customizations.chart || {})
    }
  }
}

// 导出主题配置
export const exportTheme = (themeKey) => {
  const theme = getTheme(themeKey)
  return JSON.stringify(theme, null, 2)
}

// 导入主题配置
export const importTheme = (themeConfig) => {
  try {
    const theme = typeof themeConfig === 'string' ? JSON.parse(themeConfig) : themeConfig
    const themeKey = `custom_${Date.now()}`
    themes[themeKey] = theme
    return themeKey
  } catch (error) {
    console.error('导入主题失败:', error)
    return null
  }
}

// 生成主题CSS
export const generateThemeCSS = (themeKey) => {
  const theme = getTheme(themeKey)
  
  let css = `:root {\n`
  
  // 颜色变量
  Object.keys(theme.colors).forEach(key => {
    css += `  --color-${key}: ${theme.colors[key]};\n`
  })
  
  // 背景变量
  Object.keys(theme.background).forEach(key => {
    css += `  --bg-${key}: ${theme.background[key]};\n`
  })
  
  // 文本变量
  Object.keys(theme.text).forEach(key => {
    css += `  --text-${key}: ${theme.text[key]};\n`
  })
  
  // 边框变量
  Object.keys(theme.border).forEach(key => {
    css += `  --border-${key}: ${theme.border[key]};\n`
  })
  
  // 图表变量
  Object.keys(theme.chart).forEach(key => {
    if (Array.isArray(theme.chart[key])) {
      theme.chart[key].forEach((color, index) => {
        css += `  --chart-color-${index}: ${color};\n`
      })
    } else {
      css += `  --chart-${key}: ${theme.chart[key]};\n`
    }
  })
  
  css += `}\n`
  
  return css
}
