<template>
  <el-dialog
    v-model="visible"
    :title="service?.name || '服务详情'"
    width="800px"
    :before-close="handleClose"
    class="service-detail-dialog"
  >
    <div v-if="service" class="service-detail-content">
      <!-- 服务基本信息 -->
      <div class="service-header">
        <div class="service-icon">
          <el-icon :size="32">
            <component :is="service.icon" />
          </el-icon>
        </div>
        <div class="service-info">
          <h3 class="service-name">{{ service.name }}</h3>
          <p class="service-description">{{ service.description }}</p>
          <div class="service-badges">
            <el-tag v-if="service.automationSupported" type="success">
              <el-icon><MagicStick /></el-icon>
              自动化处理
            </el-tag>
            <el-tag v-if="service.approvalRequired" type="warning">
              <el-icon><CircleCheck /></el-icon>
              需要审批
            </el-tag>
            <el-tag v-if="service.popularity > 80" type="info">
              <el-icon><Star /></el-icon>
              热门服务
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 服务详细信息 -->
      <div class="service-details">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-card">
              <h4>处理信息</h4>
              <div class="detail-items">
                <div class="detail-item">
                  <el-icon><Clock /></el-icon>
                  <span class="label">预计处理时间：</span>
                  <span class="value">{{ service.estimatedTime }}</span>
                </div>
                <div class="detail-item">
                  <el-icon><Money /></el-icon>
                  <span class="label">服务费用：</span>
                  <span class="value">{{ service.cost }}</span>
                </div>
                <div class="detail-item">
                  <el-icon><TrendCharts /></el-icon>
                  <span class="label">服务热度：</span>
                  <span class="value">{{ service.popularity }}%</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="detail-card">
              <h4>审批流程</h4>
              <div class="approval-flow" v-if="service.approvalRequired">
                <div 
                  v-for="(step, index) in service.approvalFlow" 
                  :key="index"
                  class="approval-step"
                >
                  <div class="step-number">{{ index + 1 }}</div>
                  <div class="step-name">{{ step }}</div>
                  <div v-if="index < service.approvalFlow.length - 1" class="step-arrow">
                    <el-icon><ArrowRight /></el-icon>
                  </div>
                </div>
              </div>
              <div v-else class="no-approval">
                <el-icon><CircleCheck /></el-icon>
                <span>无需审批，提交后直接处理</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 申请表单字段预览 -->
      <div class="form-preview" v-if="service.formFields && service.formFields.length > 0">
        <h4>申请表单字段</h4>
        <div class="form-fields">
          <div 
            v-for="field in service.formFields" 
            :key="field.name"
            class="form-field"
          >
            <div class="field-info">
              <span class="field-label">
                {{ field.label }}
                <el-tag v-if="field.required" type="danger" size="small">必填</el-tag>
              </span>
              <span class="field-type">{{ getFieldTypeLabel(field.type) }}</span>
            </div>
            <div v-if="field.options" class="field-options">
              选项：{{ field.options.join('、') }}
            </div>
          </div>
        </div>
      </div>

      <!-- 服务标签 -->
      <div class="service-tags" v-if="service.tags && service.tags.length > 0">
        <h4>服务标签</h4>
        <div class="tags-list">
          <el-tag 
            v-for="tag in service.tags" 
            :key="tag"
            effect="plain"
            class="service-tag"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>

      <!-- 相关流程说明 -->
      <div class="process-info" v-if="service.relatedProcess">
        <h4>处理流程</h4>
        <div class="process-description">
          <el-icon><Operation /></el-icon>
          <span>{{ service.relatedProcess }}</span>
        </div>
      </div>

      <!-- 相关服务推荐 -->
      <div class="related-services" v-if="service.relatedServices && service.relatedServices.length > 0">
        <h4>相关服务推荐</h4>
        <div class="related-list">
          <div 
            v-for="relatedService in service.relatedServices" 
            :key="relatedService.id"
            class="related-item"
            @click="viewRelatedService(relatedService)"
          >
            <el-icon>
              <component :is="relatedService.icon" />
            </el-icon>
            <span>{{ relatedService.name }}</span>
            <el-icon class="arrow"><ArrowRight /></el-icon>
          </div>
        </div>
      </div>

      <!-- 最近申请记录 -->
      <div class="recent-requests" v-if="service.recentRequests && service.recentRequests.length > 0">
        <h4>最近申请记录</h4>
        <div class="requests-list">
          <div 
            v-for="request in service.recentRequests" 
            :key="request.id"
            class="request-item"
          >
            <div class="request-info">
              <span class="request-number">{{ request.requestNumber }}</span>
              <span class="request-user">{{ request.requester }}</span>
            </div>
            <div class="request-status">
              <el-tag :type="getStatusType(request.status)" size="small">
                {{ request.statusLabel }}
              </el-tag>
            </div>
            <div class="request-time">{{ request.createdAt }}</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleApply">
          <el-icon><Plus /></el-icon>
          立即申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { 
  MagicStick, CircleCheck, Star, Clock, Money, TrendCharts,
  ArrowRight, Operation, Plus
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  service: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'apply'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const getFieldTypeLabel = (type) => {
  const typeMap = {
    input: '文本输入',
    textarea: '多行文本',
    select: '下拉选择',
    checkbox: '多选框',
    radio: '单选框',
    date: '日期选择',
    daterange: '日期范围',
    number: '数字输入'
  }
  return typeMap[type] || type
}

const getStatusType = (status) => {
  const statusMap = {
    submitted: 'info',
    pending_approval: 'warning',
    approved: 'success',
    rejected: 'danger',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const handleClose = () => {
  visible.value = false
}

const handleApply = () => {
  emit('apply', props.service)
}

const viewRelatedService = (relatedService) => {
  // 可以触发查看相关服务的事件
  emit('apply', relatedService)
}
</script>

<style scoped>
.service-detail-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.service-detail-content {
  font-size: 14px;
}

.service-header {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.service-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.service-info {
  flex: 1;
}

.service-name {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.service-description {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.6;
}

.service-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.service-badges .el-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.service-details {
  margin-bottom: 24px;
}

.detail-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  height: 100%;
}

.detail-card h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.detail-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item .el-icon {
  color: #409EFF;
}

.detail-item .label {
  color: #606266;
  min-width: 80px;
}

.detail-item .value {
  color: #303133;
  font-weight: 500;
}

.approval-flow {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.approval-step {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.step-name {
  color: #303133;
  font-weight: 500;
}

.step-arrow {
  color: #C0C4CC;
}

.no-approval {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #67C23A;
}

.form-preview,
.service-tags,
.process-info,
.related-services,
.recent-requests {
  margin-bottom: 24px;
}

.form-preview h4,
.service-tags h4,
.process-info h4,
.related-services h4,
.recent-requests h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-field {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.field-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.field-label {
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-type {
  color: #909399;
  font-size: 12px;
}

.field-options {
  color: #606266;
  font-size: 12px;
}

.tags-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.service-tag {
  margin: 0;
}

.process-description {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.related-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.related-item:hover {
  background: #409EFF;
  color: white;
}

.related-item .arrow {
  margin-left: auto;
  color: #C0C4CC;
  transition: all 0.3s ease;
}

.related-item:hover .arrow {
  color: white;
  transform: translateX(4px);
}

.requests-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.request-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.request-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.request-number {
  font-weight: 500;
  color: #303133;
}

.request-user {
  font-size: 12px;
  color: #909399;
}

.request-time {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .service-detail-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .service-header {
    flex-direction: column;
    text-align: center;
  }
  
  .approval-flow {
    flex-direction: column;
    align-items: stretch;
  }
  
  .approval-step {
    justify-content: center;
  }
  
  .step-arrow {
    transform: rotate(90deg);
  }
}
</style>
