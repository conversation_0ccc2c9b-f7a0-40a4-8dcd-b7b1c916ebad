// 扩展图表组件库 - 30+种图表组件
export const chartComponentLibrary = {
  // 基础图表类
  basic: [
    {
      type: 'bar-chart',
      name: '柱状图',
      icon: 'Histogram',
      category: 'basic',
      description: '展示分类数据对比',
      props: {
        title: '柱状图',
        colors: ['#409eff', '#67c23a', '#e6a23c'],
        showValues: true,
        horizontal: false,
        stack: false
      },
      dataConfig: {
        xAxis: 'category',
        yAxis: 'value',
        series: 'series'
      }
    },
    {
      type: 'line-chart',
      name: '折线图',
      icon: 'TrendCharts',
      category: 'basic',
      description: '趋势分析',
      props: {
        title: '折线图',
        colors: ['#409eff', '#67c23a', '#e6a23c'],
        smooth: true,
        showPoints: true,
        showArea: false
      },
      dataConfig: {
        xAxis: 'time',
        yAxis: 'value',
        series: 'series'
      }
    },
    {
      type: 'pie-chart',
      name: '饼图',
      icon: 'PieChart',
      category: 'basic',
      description: '占比分析',
      props: {
        title: '饼图',
        colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c'],
        showLegend: true,
        labelPosition: 'outside',
        radius: ['0%', '70%']
      },
      dataConfig: {
        name: 'name',
        value: 'value'
      }
    },
    {
      type: 'scatter-plot',
      name: '散点图',
      icon: 'Coordinate',
      category: 'basic',
      description: '相关性分析',
      props: {
        title: '散点图',
        colors: ['#409eff'],
        symbolSize: 8,
        showRegression: false
      },
      dataConfig: {
        xAxis: 'x',
        yAxis: 'y',
        size: 'size'
      }
    },
    {
      type: 'area-chart',
      name: '面积图',
      icon: 'DataLine',
      category: 'basic',
      description: '累积趋势',
      props: {
        title: '面积图',
        colors: ['#409eff', '#67c23a'],
        areaOpacity: 0.6,
        stack: false,
        smooth: true
      },
      dataConfig: {
        xAxis: 'time',
        yAxis: 'value',
        series: 'series'
      }
    }
  ],

  // 高级图表类
  advanced: [
    {
      type: 'radar-chart',
      name: '雷达图',
      icon: 'Aim',
      category: 'advanced',
      description: '多维指标对比',
      props: {
        title: '雷达图',
        colors: ['#409eff', '#67c23a'],
        showArea: true,
        areaOpacity: 0.3,
        splitNumber: 5
      },
      dataConfig: {
        indicator: 'indicator',
        value: 'value',
        series: 'series'
      }
    },
    {
      type: 'funnel-chart',
      name: '漏斗图',
      icon: 'Sort',
      category: 'advanced',
      description: '转化率分析',
      props: {
        title: '漏斗图',
        colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c'],
        sort: 'descending',
        gap: 2,
        showLabels: true
      },
      dataConfig: {
        name: 'name',
        value: 'value'
      }
    },
    {
      type: 'heatmap',
      name: '热力图',
      icon: 'Grid',
      category: 'advanced',
      description: '密度分布',
      props: {
        title: '热力图',
        colorRange: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'],
        showLabels: true
      },
      dataConfig: {
        xAxis: 'x',
        yAxis: 'y',
        value: 'value'
      }
    },
    {
      type: 'sankey-diagram',
      name: '桑基图',
      icon: 'Connection',
      category: 'advanced',
      description: '流量分析',
      props: {
        title: '桑基图',
        nodeColor: '#409eff',
        linkOpacity: 0.6,
        nodeWidth: 20,
        nodePadding: 8
      },
      dataConfig: {
        nodes: 'nodes',
        links: 'links'
      }
    },
    {
      type: 'tree-map',
      name: '树图',
      icon: 'Menu',
      category: 'advanced',
      description: '层级数据展示',
      props: {
        title: '树图',
        colorRange: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c'],
        showLabels: true,
        leafDepth: 2
      },
      dataConfig: {
        name: 'name',
        value: 'value',
        children: 'children'
      }
    },
    {
      type: 'sunburst-chart',
      name: '旭日图',
      icon: 'Sunny',
      category: 'advanced',
      description: '多层级占比',
      props: {
        title: '旭日图',
        colors: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#6b4eff'],
        radius: ['15%', '80%'],
        showLabels: true
      },
      dataConfig: {
        name: 'name',
        value: 'value',
        children: 'children'
      }
    }
  ],

  // 大屏专用组件
  dashboard: [
    {
      type: 'number-card',
      name: '数字卡片',
      icon: 'Document',
      category: 'dashboard',
      description: 'KPI关键指标展示',
      props: {
        title: '指标名称',
        value: 12345,
        unit: '个',
        trend: 12.5,
        trendText: '较昨日',
        color: '#409eff',
        fontSize: '32px',
        showTrend: true
      },
      dataConfig: {
        value: 'value',
        trend: 'trend'
      }
    },
    {
      type: 'progress-ring',
      name: '进度环',
      icon: 'Refresh',
      category: 'dashboard',
      description: '进度可视化',
      props: {
        title: '完成进度',
        value: 75,
        unit: '%',
        color: '#67c23a',
        strokeWidth: 8,
        showText: true,
        animation: true
      },
      dataConfig: {
        value: 'value'
      }
    },
    {
      type: 'gauge-chart',
      name: '仪表盘',
      icon: 'Stopwatch',
      category: 'dashboard',
      description: '指标监控',
      props: {
        title: '仪表盘',
        value: 65,
        unit: '%',
        min: 0,
        max: 100,
        color: '#409eff',
        showPointer: true,
        splitNumber: 10
      },
      dataConfig: {
        value: 'value'
      }
    },
    {
      type: 'liquid-fill',
      name: '水位图',
      icon: 'ColdDrink',
      category: 'dashboard',
      description: '完成度展示',
      props: {
        title: '水位图',
        value: 0.68,
        color: '#409eff',
        waveAnimation: true,
        outline: true,
        backgroundStyle: { color: '#f0f0f0' }
      },
      dataConfig: {
        value: 'value'
      }
    },
    {
      type: 'digital-flipper',
      name: '数字翻牌器',
      icon: 'Timer',
      category: 'dashboard',
      description: 'KPI动态翻转',
      props: {
        title: '翻牌器',
        value: 123456,
        unit: '',
        digits: 6,
        flipDuration: 800,
        color: '#00ff88',
        fontSize: '48px'
      },
      dataConfig: {
        value: 'value'
      }
    },
    {
      type: 'scrolling-table',
      name: '轮播表格',
      icon: 'Menu',
      category: 'dashboard',
      description: '多行数据滚动',
      props: {
        title: '轮播表格',
        columns: ['序号', '名称', '数值', '状态'],
        rowHeight: 40,
        scrollSpeed: 2000,
        maxRows: 8,
        showHeader: true
      },
      dataConfig: {
        data: 'rows'
      }
    }
  ],

  // 动态组件
  dynamic: [
    {
      type: 'carousel',
      name: '走马灯',
      icon: 'Picture',
      category: 'dynamic',
      description: '图文轮播',
      props: {
        autoplay: true,
        interval: 3000,
        showIndicators: true,
        showArrows: 'hover',
        height: '300px'
      },
      dataConfig: {
        items: 'items'
      }
    },
    {
      type: 'real-time-clock',
      name: '实时时间',
      icon: 'Clock',
      category: 'dynamic',
      description: '显示当前时间',
      props: {
        format: 'YYYY-MM-DD HH:mm:ss',
        fontSize: '24px',
        color: '#409eff',
        showSeconds: true,
        timezone: 'Asia/Shanghai'
      }
    },
    {
      type: 'weather-widget',
      name: '天气组件',
      icon: 'Sunny',
      category: 'dynamic',
      description: '城市天气信息',
      props: {
        city: '北京',
        showForecast: true,
        showDetails: true,
        updateInterval: 600000
      },
      dataConfig: {
        source: 'api',
        url: '/api/weather'
      }
    },
    {
      type: 'video-player',
      name: '视频播放器',
      icon: 'VideoPlay',
      category: 'dynamic',
      description: '视频流播放',
      props: {
        src: '',
        autoplay: false,
        controls: true,
        loop: false,
        muted: false
      }
    }
  ],

  // 文本类组件
  text: [
    {
      type: 'title',
      name: '标题',
      icon: 'Document',
      category: 'text',
      description: '大屏标题',
      props: {
        text: '标题文本',
        fontSize: '32px',
        fontWeight: 'bold',
        color: '#ffffff',
        textAlign: 'center',
        textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
      }
    },
    {
      type: 'text-box',
      name: '文本框',
      icon: 'EditPen',
      category: 'text',
      description: '自由文本说明',
      props: {
        text: '文本内容',
        fontSize: '16px',
        color: '#ffffff',
        lineHeight: 1.5,
        textAlign: 'left',
        backgroundColor: 'transparent'
      }
    },
    {
      type: 'marquee-text',
      name: '滚动文字',
      icon: 'Right',
      category: 'text',
      description: '文字滚动效果',
      props: {
        text: '滚动文字内容',
        direction: 'left',
        speed: 50,
        fontSize: '18px',
        color: '#00ff88'
      }
    }
  ],

  // 装饰类组件
  decoration: [
    {
      type: 'border-decoration',
      name: '边框装饰',
      icon: 'Grid',
      category: 'decoration',
      description: '科技感边框',
      props: {
        borderType: 'tech',
        color: '#00ff88',
        width: 2,
        animation: true,
        glowEffect: true
      }
    },
    {
      type: 'divider',
      name: '分割线',
      icon: 'Minus',
      category: 'decoration',
      description: '区块分隔',
      props: {
        type: 'horizontal',
        color: '#409eff',
        width: 2,
        style: 'solid',
        gradient: false
      }
    },
    {
      type: 'particle-background',
      name: '粒子背景',
      icon: 'Star',
      category: 'decoration',
      description: '动态粒子效果',
      props: {
        particleCount: 100,
        particleColor: '#409eff',
        particleSize: 2,
        speed: 1,
        connections: true
      }
    },
    {
      type: 'glow-effect',
      name: '光效装饰',
      icon: 'Sunny',
      category: 'decoration',
      description: '发光效果',
      props: {
        glowColor: '#00ff88',
        glowSize: 20,
        animation: true,
        pulseDuration: 2000
      }
    }
  ],

  // 容器类组件
  container: [
    {
      type: 'card-container',
      name: '卡片容器',
      icon: 'Collection',
      category: 'container',
      description: '内容聚合容器',
      props: {
        title: '卡片标题',
        backgroundColor: 'rgba(255,255,255,0.1)',
        borderColor: '#409eff',
        borderRadius: 8,
        padding: 16,
        showHeader: true
      }
    },
    {
      type: 'carousel-container',
      name: '轮播容器',
      icon: 'Refresh',
      category: 'container',
      description: '自动切换子组件',
      props: {
        autoplay: true,
        interval: 5000,
        showIndicators: false,
        transition: 'slide'
      }
    },
    {
      type: 'grid-container',
      name: '网格容器',
      icon: 'Grid',
      category: 'container',
      description: '多图表整齐排列',
      props: {
        columns: 2,
        rows: 2,
        gap: 16,
        responsive: true
      }
    },
    {
      type: 'tab-container',
      name: '标签容器',
      icon: 'Folder',
      category: 'container',
      description: '标签页容器',
      props: {
        tabPosition: 'top',
        tabType: 'card',
        closable: false,
        animated: true
      }
    }
  ]
}

// 获取所有组件
export const getAllComponents = () => {
  const allComponents = []
  Object.values(chartComponentLibrary).forEach(category => {
    allComponents.push(...category)
  })
  return allComponents
}

// 根据分类获取组件
export const getComponentsByCategory = (category) => {
  return chartComponentLibrary[category] || []
}

// 根据类型获取组件
export const getComponentByType = (type) => {
  const allComponents = getAllComponents()
  return allComponents.find(component => component.type === type)
}

// 搜索组件
export const searchComponents = (keyword) => {
  const allComponents = getAllComponents()
  const lowerKeyword = keyword.toLowerCase()
  return allComponents.filter(component => 
    component.name.toLowerCase().includes(lowerKeyword) ||
    component.description.toLowerCase().includes(lowerKeyword)
  )
}
