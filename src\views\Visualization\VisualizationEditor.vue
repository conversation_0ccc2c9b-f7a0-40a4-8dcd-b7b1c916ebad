<template>
  <div class="visualization-editor">
    <!-- 顶部工具栏 -->
    <header class="editor-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="goBack" class="back-btn">
          返回
        </el-button>
        <div class="project-info">
          <el-input
            v-model="projectName"
            class="project-name-input"
            placeholder="未命名大屏"
          />
          <span class="project-status">{{ getStatusText(projectStatus) }}</span>
        </div>
      </div>
      
      <div class="header-center">
        <div class="canvas-controls">
          <el-button-group>
            <el-button :icon="ZoomOut" @click="zoomOut" :disabled="canvasScale <= 0.5">
              {{ Math.round(canvasScale * 100) }}%
            </el-button>
            <el-button :icon="ZoomIn" @click="zoomIn" :disabled="canvasScale >= 2">
              放大
            </el-button>
          </el-button-group>
        </div>
      </div>

      <div class="header-right">
        <el-dropdown @command="handleThemeChange">
          <el-button :icon="Sunny">
            主题 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="theme in availableThemes"
                :key="theme.key"
                :command="theme.key"
                :class="{ 'is-active': currentTheme === theme.key }"
              >
                {{ theme.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-button :icon="Plus" @click="showTemplateDialog = true">
          模板
        </el-button>

        <el-button :icon="View" @click="preview">
          预览
        </el-button>

        <el-button type="primary" :icon="Upload" @click="publish" :loading="publishLoading">
          发布
        </el-button>

        <el-button :icon="Download" @click="save" :loading="saveLoading">
          保存
        </el-button>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="editor-main">
      <!-- 左侧组件库 -->
      <aside class="component-panel">
        <div class="panel-header">
          <span>组件库</span>
        </div>
        
        <div class="panel-content">
          <el-scrollbar>
            <el-collapse v-model="activeComponentCategories">
              <!-- 基础图表 -->
              <el-collapse-item title="基础图表" name="basic">
                <div class="component-grid">
                  <div
                    v-for="component in basicComponents"
                    :key="component.type"
                    class="component-item"
                    draggable="true"
                    @dragstart="handleDragStart($event, component)"
                  >
                    <div class="component-icon">
                      <el-icon><component :is="component.icon" /></el-icon>
                    </div>
                    <span class="component-name">{{ component.name }}</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 高级图表 -->
              <el-collapse-item title="高级图表" name="advanced">
                <div class="component-grid">
                  <div
                    v-for="component in advancedComponents"
                    :key="component.type"
                    class="component-item"
                    draggable="true"
                    @dragstart="handleDragStart($event, component)"
                  >
                    <div class="component-icon">
                      <el-icon><component :is="component.icon" /></el-icon>
                    </div>
                    <span class="component-name">{{ component.name }}</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 大屏专用 -->
              <el-collapse-item title="大屏专用" name="dashboard">
                <div class="component-grid">
                  <div
                    v-for="component in dashboardComponents"
                    :key="component.type"
                    class="component-item"
                    draggable="true"
                    @dragstart="handleDragStart($event, component)"
                  >
                    <div class="component-icon">
                      <el-icon><component :is="component.icon" /></el-icon>
                    </div>
                    <span class="component-name">{{ component.name }}</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 动态组件 -->
              <el-collapse-item title="动态组件" name="dynamic">
                <div class="component-grid">
                  <div
                    v-for="component in dynamicComponents"
                    :key="component.type"
                    class="component-item"
                    draggable="true"
                    @dragstart="handleDragStart($event, component)"
                  >
                    <div class="component-icon">
                      <el-icon><component :is="component.icon" /></el-icon>
                    </div>
                    <span class="component-name">{{ component.name }}</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 文本类组件 -->
              <el-collapse-item title="文本类" name="text">
                <div class="component-grid">
                  <div
                    v-for="component in textComponents"
                    :key="component.type"
                    class="component-item"
                    draggable="true"
                    @dragstart="handleDragStart($event, component)"
                  >
                    <div class="component-icon">
                      <el-icon><component :is="component.icon" /></el-icon>
                    </div>
                    <span class="component-name">{{ component.name }}</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 装饰类 -->
              <el-collapse-item title="装饰类" name="decoration">
                <div class="component-grid">
                  <div
                    v-for="component in decorationComponents"
                    :key="component.type"
                    class="component-item"
                    draggable="true"
                    @dragstart="handleDragStart($event, component)"
                  >
                    <div class="component-icon">
                      <el-icon><component :is="component.icon" /></el-icon>
                    </div>
                    <span class="component-name">{{ component.name }}</span>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 容器类 -->
              <el-collapse-item title="容器类" name="container">
                <div class="component-grid">
                  <div
                    v-for="component in containerComponents"
                    :key="component.type"
                    class="component-item"
                    draggable="true"
                    @dragstart="handleDragStart($event, component)"
                  >
                    <div class="component-icon">
                      <el-icon><component :is="component.icon" /></el-icon>
                    </div>
                    <span class="component-name">{{ component.name }}</span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-scrollbar>
        </div>
      </aside>

      <!-- 中央画布区域 -->
      <main class="canvas-area">
        <div class="canvas-toolbar">
          <div class="toolbar-left">
            <el-button-group size="small">
              <el-button :icon="Grid" @click="toggleGrid" :type="showGrid ? 'primary' : ''">
                网格
              </el-button>
            </el-button-group>
          </div>
          
          <div class="toolbar-right">
            <span class="canvas-info">画布尺寸: 1920 × 1080</span>
          </div>
        </div>

        <div class="canvas-container">
          <div 
            class="canvas"
            :style="canvasStyle"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @click="clearSelection"
          >
            <!-- 网格背景 -->
            <div v-if="showGrid" class="grid-background"></div>
            
            <!-- 组件渲染区域 -->
            <div 
              v-for="component in canvasComponents" 
              :key="component.id"
              class="canvas-component"
              :class="{ 'selected': selectedComponentId === component.id }"
              :style="getComponentStyle(component)"
              @click.stop="selectComponent(component.id)"
            >
              <!-- 组件占位符 -->
              <div class="component-placeholder">
                <div class="placeholder-icon">
                  <el-icon><Histogram /></el-icon>
                </div>
                <div class="placeholder-text">{{ component.name }}</div>
                <div class="placeholder-data" v-if="component.data && component.data.text">
                  {{ component.data.text }}
                </div>
                <div class="placeholder-data" v-else-if="component.data && component.data.value">
                  {{ component.data.value }} {{ component.data.unit }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <!-- 右侧属性面板 -->
      <aside class="property-panel">
        <div class="panel-header">
          <span>属性面板</span>
        </div>
        
        <div class="panel-content">
          <el-scrollbar>
            <div v-if="selectedComponent" class="property-form">
              <el-tabs v-model="activePropertyTab" type="card">
                <!-- 数据配置 -->
                <el-tab-pane label="数据" name="data">
                  <DataSourceConfig
                    v-model="selectedComponent.dataConfig"
                    :component-id="selectedComponent.id"
                  />
                </el-tab-pane>

                <!-- 样式配置 -->
                <el-tab-pane label="样式" name="style">
                  <div class="property-section">
                    <h4>位置和尺寸</h4>
                    <el-form label-width="60px" size="small">
                      <el-row :gutter="10">
                        <el-col :span="12">
                          <el-form-item label="X">
                            <el-input-number v-model="selectedComponent.x" :min="0" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="Y">
                            <el-input-number v-model="selectedComponent.y" :min="0" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row :gutter="10">
                        <el-col :span="12">
                          <el-form-item label="宽度">
                            <el-input-number v-model="selectedComponent.width" :min="10" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="高度">
                            <el-input-number v-model="selectedComponent.height" :min="10" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>
                  </div>

                  <div class="property-section">
                    <h4>外观样式</h4>
                    <el-form label-width="80px" size="small">
                      <el-form-item label="背景色">
                        <el-color-picker v-model="selectedComponent.props.backgroundColor" />
                      </el-form-item>
                      <el-form-item label="边框色">
                        <el-color-picker v-model="selectedComponent.props.borderColor" />
                      </el-form-item>
                      <el-form-item label="边框宽度">
                        <el-input-number v-model="selectedComponent.props.borderWidth" :min="0" :max="10" />
                      </el-form-item>
                      <el-form-item label="圆角">
                        <el-input-number v-model="selectedComponent.props.borderRadius" :min="0" :max="50" />
                      </el-form-item>
                      <el-form-item label="透明度">
                        <el-slider v-model="selectedComponent.props.opacity" :min="0" :max="1" :step="0.1" />
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>

                <!-- 交互配置 -->
                <el-tab-pane label="交互" name="interaction">
                  <div class="property-section">
                    <h4>事件配置</h4>
                    <el-form label-width="80px" size="small">
                      <el-form-item label="点击事件">
                        <el-select v-model="selectedComponent.events.click" placeholder="选择点击事件">
                          <el-option label="无" value="" />
                          <el-option label="跳转链接" value="link" />
                          <el-option label="刷新组件" value="refresh" />
                          <el-option label="联动其他组件" value="linkage" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="悬停效果">
                        <el-switch v-model="selectedComponent.events.hover" />
                      </el-form-item>
                      <el-form-item label="动画效果">
                        <el-switch v-model="selectedComponent.props.animation" />
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>

            <div v-else class="no-selection">
              <el-empty description="请选择一个组件" />
            </div>
          </el-scrollbar>
        </div>
      </aside>
    </div>

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      title="选择模板"
      width="80%"
      :before-close="handleTemplateDialogClose"
    >
      <div class="template-gallery">
        <div class="template-search">
          <el-input
            v-model="templateSearchKeyword"
            placeholder="搜索模板..."
            :prefix-icon="Search"
            clearable
          />
        </div>

        <div class="template-grid">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="template-card"
            @click="selectTemplate(template)"
          >
            <div class="template-thumbnail">
              <img :src="template.thumbnail" :alt="template.name" />
              <div class="template-overlay">
                <el-button type="primary" size="small">使用模板</el-button>
              </div>
            </div>
            <div class="template-info">
              <h4>{{ template.name }}</h4>
              <p>{{ template.description }}</p>
              <div class="template-tags">
                <el-tag
                  v-for="tag in template.tags"
                  :key="tag"
                  size="small"
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTemplateDialog = false">取消</el-button>
          <el-button type="primary" @click="applyTemplate" :disabled="!selectedTemplate">
            应用模板
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft, ZoomIn, ZoomOut, View, Upload, Download, Grid,
  Histogram, Document, TrendCharts, PieChart, Coordinate, DataLine,
  Aim, Sort, Connection, Menu, Sunny, Refresh, Stopwatch, ColdDrink,
  Timer, Picture, Clock, VideoPlay, EditPen, Right, Star, Collection,
  Folder, Plus, Search, Minus, ArrowDown
} from '@element-plus/icons-vue'
import { getAllTemplates } from '@/data/visualizationTemplates'
import { chartComponentLibrary, getComponentsByCategory } from '@/data/chartComponents'
import { getAllThemes, getCurrentTheme, applyTheme } from '@/data/themes'
import DataSourceConfig from '@/components/Visualization/DataSourceConfig.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const projectName = ref('未命名大屏')
const projectStatus = ref('draft')
const saveLoading = ref(false)
const publishLoading = ref(false)

// 画布控制
const canvasScale = ref(1)
const showGrid = ref(true)

// 组件相关
const activeComponentCategories = ref(['basic', 'dashboard'])
const selectedComponentId = ref(null)
const canvasComponents = ref([])
const activePropertyTab = ref('data')

// 选中的组件
const selectedComponent = computed(() => {
  return canvasComponents.value.find(comp => comp.id === selectedComponentId.value)
})

// 组件库定义 - 使用扩展的组件库
const basicComponents = ref(getComponentsByCategory('basic'))
const advancedComponents = ref(getComponentsByCategory('advanced'))
const dashboardComponents = ref(getComponentsByCategory('dashboard'))
const dynamicComponents = ref(getComponentsByCategory('dynamic'))
const textComponents = ref(getComponentsByCategory('text'))
const decorationComponents = ref(getComponentsByCategory('decoration'))
const containerComponents = ref(getComponentsByCategory('container'))

// 模板系统
const templates = ref(getAllTemplates())
const showTemplateDialog = ref(false)
const selectedTemplate = ref(null)
const templateSearchKeyword = ref('')

// 主题系统
const availableThemes = ref(getAllThemes())
const currentTheme = ref(getCurrentTheme())

// 过滤模板
const filteredTemplates = computed(() => {
  if (!templateSearchKeyword.value) {
    return templates.value
  }
  const keyword = templateSearchKeyword.value.toLowerCase()
  return templates.value.filter(template =>
    template.name.toLowerCase().includes(keyword) ||
    template.description.toLowerCase().includes(keyword) ||
    template.tags.some(tag => tag.toLowerCase().includes(keyword))
  )
})

// 画布样式
const canvasStyle = computed(() => ({
  transform: `scale(${canvasScale.value})`,
  transformOrigin: 'center center',
  width: '1920px',
  height: '1080px'
}))

// 生命周期
onMounted(() => {
  initEditor()
  // 初始化主题
  applyTheme(currentTheme.value)
})

// 初始化编辑器
const initEditor = () => {
  const id = route.params.id
  if (id && id !== 'new') {
    loadProject(id)
  } else {
    projectName.value = '未命名大屏'
    canvasComponents.value = []
  }
}

// 加载项目
const loadProject = async (id) => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    projectName.value = '智慧城市监控大屏'
    ElMessage.success('项目加载成功')
  } catch (error) {
    console.error('加载项目失败:', error)
    ElMessage.error('加载项目失败')
  }
}

// 返回
const goBack = () => {
  router.push('/visualization')
}

// 画布缩放
const zoomIn = () => {
  if (canvasScale.value < 2) {
    canvasScale.value = Math.min(2, canvasScale.value + 0.1)
  }
}

const zoomOut = () => {
  if (canvasScale.value > 0.5) {
    canvasScale.value = Math.max(0.5, canvasScale.value - 0.1)
  }
}

// 网格切换
const toggleGrid = () => {
  showGrid.value = !showGrid.value
}

// 拖拽处理
const handleDragStart = (event, component) => {
  event.dataTransfer.setData('component', JSON.stringify(component))
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDrop = (event) => {
  event.preventDefault()
  const componentData = JSON.parse(event.dataTransfer.getData('component'))

  // 计算放置位置
  const rect = event.currentTarget.getBoundingClientRect()
  const x = (event.clientX - rect.left) / canvasScale.value
  const y = (event.clientY - rect.top) / canvasScale.value

  // 创建新组件
  const newComponent = {
    id: `component_${Date.now()}`,
    type: componentData.type,
    name: componentData.name,
    x: Math.max(0, x - 50),
    y: Math.max(0, y - 25),
    width: 200,
    height: 150,
    data: getDefaultData(componentData.type),
    dataConfig: {
      source: 'mock',
      mockType: 'timeSeries',
      interval: 2000,
      mockOptions: {
        count: 24,
        baseValue: 100,
        variance: 20
      },
      mapping: '{}'
    },
    props: {
      backgroundColor: 'transparent',
      borderColor: '#409eff',
      borderWidth: 1,
      borderRadius: 4,
      opacity: 1,
      animation: true,
      ...componentData.props
    },
    events: {
      click: '',
      hover: false
    }
  }

  canvasComponents.value.push(newComponent)
  selectedComponentId.value = newComponent.id
}

// 获取默认数据
const getDefaultData = (type) => {
  const defaultDataMap = {
    'bar-chart': {
      xAxis: ['一月', '二月', '三月', '四月', '五月'],
      series: [120, 200, 150, 80, 70]
    },
    'number-card': { value: 12345, title: '总数量', unit: '个' },
    'title': { text: '标题文本' },
    'text': { text: '这是一段文本内容' }
  }

  return defaultDataMap[type] || {}
}

// 组件选择和操作
const selectComponent = (id) => {
  selectedComponentId.value = id
}

const clearSelection = () => {
  selectedComponentId.value = null
}

const getComponentStyle = (component) => ({
  position: 'absolute',
  left: `${component.x}px`,
  top: `${component.y}px`,
  width: `${component.width}px`,
  height: `${component.height}px`,
  border: selectedComponentId.value === component.id ? '2px solid #409eff' : 'none'
})

// 状态文本
const getStatusText = (status) => {
  const textMap = {
    'draft': '草稿',
    'published': '已发布'
  }
  return textMap[status] || status
}

// 预览
const preview = () => {
  const id = route.params.id || 'new'
  router.push(`/visualization/preview/${id}`)
}

// 发布
const publish = async () => {
  publishLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    projectStatus.value = 'published'
    ElMessage.success('发布成功')
  } catch (error) {
    ElMessage.error('发布失败')
  } finally {
    publishLoading.value = false
  }
}

// 保存
const save = async () => {
  saveLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 模板相关方法
const selectTemplate = (template) => {
  selectedTemplate.value = template
}

const handleTemplateDialogClose = () => {
  selectedTemplate.value = null
  templateSearchKeyword.value = ''
}

const applyTemplate = () => {
  if (!selectedTemplate.value) return

  try {
    // 应用模板配置
    projectName.value = selectedTemplate.value.meta.title
    canvasComponents.value = selectedTemplate.value.components.map(comp => ({
      ...comp,
      id: `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }))

    // 清除选中状态
    selectedComponentId.value = null

    // 关闭对话框
    showTemplateDialog.value = false
    selectedTemplate.value = null

    ElMessage.success('模板应用成功')
  } catch (error) {
    console.error('应用模板失败:', error)
    ElMessage.error('应用模板失败')
  }
}

// 主题切换
const handleThemeChange = (themeKey) => {
  currentTheme.value = themeKey
  applyTheme(themeKey)
  ElMessage.success(`已切换到${availableThemes.value.find(t => t.key === themeKey)?.name}主题`)
}
</script>

<style scoped>
.visualization-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  color: #ffffff;
}

/* 顶部工具栏 */
.editor-header {
  height: 60px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  background: transparent;
  border: 1px solid #404040;
  color: #ffffff;
}

.back-btn:hover {
  background: #404040;
  border-color: #606060;
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.project-name-input {
  width: 200px;
}

.project-name-input :deep(.el-input__wrapper) {
  background: transparent;
  border: 1px solid #404040;
  color: #ffffff;
}

.project-status {
  font-size: 12px;
  color: #909399;
}

.header-center {
  display: flex;
  align-items: center;
}

.canvas-controls .el-button-group .el-button {
  background: #404040;
  border-color: #606060;
  color: #ffffff;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right .el-button {
  background: #404040;
  border-color: #606060;
  color: #ffffff;
}

.header-right .el-button--primary {
  background: #409eff;
  border-color: #409eff;
}

/* 主要内容区域 */
.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧组件库 */
.component-panel {
  width: 280px;
  background: #252525;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
}

.panel-header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #404040;
  font-weight: 600;
}

.panel-content {
  flex: 1;
  overflow: hidden;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 12px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
}

.component-item:hover {
  background: #353535;
  border-color: #606060;
  transform: translateY(-1px);
}

.component-item:active {
  cursor: grabbing;
}

.component-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 6px;
}

.component-name {
  font-size: 12px;
  color: #cccccc;
  text-align: center;
}

/* 中央画布区域 */
.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
}

.canvas-toolbar {
  height: 40px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.toolbar-left .el-button {
  background: #404040;
  border-color: #606060;
  color: #ffffff;
}

.toolbar-left .el-button--primary {
  background: #409eff;
  border-color: #409eff;
}

.canvas-info {
  font-size: 12px;
  color: #909399;
}

.canvas-container {
  flex: 1;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.canvas {
  position: relative;
  background: #ffffff;
  border: 1px solid #404040;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(to right, #f0f0f0 1px, transparent 1px),
    linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.canvas-component {
  position: absolute;
  cursor: move;
  transition: border 0.2s ease;
}

.canvas-component.selected {
  border: 2px solid #409eff !important;
}

/* 组件占位符 */
.component-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  color: #909399;
  text-align: center;
  padding: 16px;
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #409eff;
}

.placeholder-text {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #606266;
}

.placeholder-data {
  font-size: 12px;
  color: #909399;
}

/* 右侧属性面板 */
.property-panel {
  width: 320px;
  background: #252525;
  border-left: 1px solid #404040;
  display: flex;
  flex-direction: column;
}

.property-section {
  padding: 16px;
  border-bottom: 1px solid #404040;
}

.property-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #ffffff;
}

.property-section .el-form-item {
  margin-bottom: 12px;
}

.property-section :deep(.el-form-item__label) {
  color: #cccccc;
}

.property-section :deep(.el-input__wrapper) {
  background: #2d2d2d;
  border-color: #404040;
  color: #ffffff;
}

.no-selection {
  padding: 40px 20px;
  text-align: center;
}

/* 深色主题适配 */
:deep(.el-collapse) {
  background: transparent;
  border: none;
}

:deep(.el-collapse-item__header) {
  background: #2d2d2d;
  color: #ffffff;
  border-bottom: 1px solid #404040;
}

:deep(.el-collapse-item__content) {
  background: #252525;
  color: #ffffff;
}

:deep(.el-scrollbar__thumb) {
  background: #404040;
}

/* 模板选择对话框 */
.template-gallery {
  padding: 20px 0;
}

.template-search {
  margin-bottom: 20px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-height: 500px;
  overflow-y: auto;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.template-thumbnail {
  position: relative;
  height: 180px;
  background: #f5f7fa;
  overflow: hidden;
}

.template-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover .template-overlay {
  opacity: 1;
}

.template-info {
  padding: 16px;
}

.template-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.template-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.template-tags .el-tag {
  font-size: 12px;
}

/* 响应式 */
@media (max-width: 1200px) {
  .component-panel {
    width: 240px;
  }

  .property-panel {
    width: 280px;
  }

  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}
</style>
