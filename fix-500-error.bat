@echo off
echo ========================================
echo 修复CMDB项目500错误
echo ========================================
echo.

echo [步骤1] 停止可能运行的开发服务器...
taskkill /f /im node.exe 2>nul
echo ✓ 已尝试停止Node.js进程

echo.
echo [步骤2] 清理项目缓存和依赖...
if exist node_modules (
    echo 删除 node_modules 文件夹...
    rmdir /s /q node_modules
)

if exist package-lock.json (
    echo 删除 package-lock.json 文件...
    del package-lock.json
)

echo 清理npm缓存...
npm cache clean --force

echo ✓ 清理完成

echo.
echo [步骤3] 重新安装基础依赖...
echo 安装Vue和Vite...
npm install vue@latest vite@latest

echo 安装Element Plus...
npm install element-plus @element-plus/icons-vue

echo ✓ 基础依赖安装完成

echo.
echo [步骤4] 安装CMDB特定依赖...
echo 安装状态管理...
npm install pinia

echo 安装HTTP客户端...
npm install axios

echo 安装模拟数据...
npm install mockjs

echo ✓ CMDB依赖安装完成

echo.
echo [步骤5] 验证安装...
npm list vue vite element-plus pinia axios mockjs

echo.
echo [步骤6] 启动开发服务器...
echo 正在启动项目...
echo 请在新的命令行窗口中运行: npm run dev
echo 或者按任意键自动启动...
pause

start cmd /k "npm run dev"

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 如果仍有问题，请：
echo 1. 检查 http://localhost:5173 是否正常显示
echo 2. 查看控制台是否有错误信息
echo 3. 运行 test-fix.bat 进行诊断
echo.
pause
