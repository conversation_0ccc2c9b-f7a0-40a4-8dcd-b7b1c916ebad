<template>
  <div class="incident-list">
    <div class="page-header">
      <h2>事件列表</h2>
      <p>查看和管理所有事件</p>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <el-card>
        <el-form :model="searchForm" :inline="true">
          <el-form-item label="事件ID">
            <el-input v-model="searchForm.incidentId" placeholder="请输入事件ID" clearable />
          </el-form-item>
          <el-form-item label="标题">
            <el-input v-model="searchForm.title" placeholder="请输入标题关键词" clearable />
          </el-form-item>
          <el-form-item label="优先级">
            <el-select v-model="searchForm.priority" placeholder="请选择优先级" clearable>
              <el-option label="P1-紧急" value="P1" />
              <el-option label="P2-高" value="P2" />
              <el-option label="P3-中" value="P3" />
              <el-option label="P4-低" value="P4" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="新建" value="new" />
              <el-option label="已分配" value="assigned" />
              <el-option label="处理中" value="in-progress" />
              <el-option label="已解决" value="resolved" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchIncidents">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 事件列表 -->
    <div class="incidents-table">
      <el-card>
        <template #header>
          <div class="table-header">
            <span>事件列表 ({{ incidents.length }})</span>
            <el-button type="primary" @click="createIncident">
              <el-icon><Plus /></el-icon>
              创建事件
            </el-button>
          </div>
        </template>

        <el-table :data="incidents" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="事件ID" width="120" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)" size="small">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="处理人" width="120" />
          <el-table-column prop="category" label="分类" width="150" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewIncident(scope.row)">
                查看
              </el-button>
              <el-button type="primary" size="small" text @click="assignIncident(scope.row)">
                分配
              </el-button>
              <el-button type="warning" size="small" text @click="escalateIncident(scope.row)">
                升级
              </el-button>
              <el-button type="success" size="small" text @click="resolveIncident(scope.row)">
                解决
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getIncidentList } from '@/api/incidentApi.js'

// 搜索表单
const searchForm = reactive({
  incidentId: '',
  title: '',
  priority: '',
  status: ''
})

// 数据状态
const incidents = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 加载事件列表
const loadIncidents = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      incidentId: searchForm.incidentId,
      title: searchForm.title,
      priority: searchForm.priority,
      status: searchForm.status
    }
    
    const response = await getIncidentList(params)
    if (response.code === 200) {
      incidents.value = response.data.list
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载事件列表失败:', error)
    ElMessage.error('加载事件列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索事件
const searchIncidents = () => {
  currentPage.value = 1
  loadIncidents()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  currentPage.value = 1
  loadIncidents()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadIncidents()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadIncidents()
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const typeMap = {
    'P1': 'danger',
    'P2': 'warning',
    'P3': 'info',
    'P4': 'success'
  }
  return typeMap[priority] || 'info'
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    'new': 'info',
    'assigned': 'warning',
    'in-progress': 'primary',
    'resolved': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'new': '新建',
    'assigned': '已分配',
    'in-progress': '处理中',
    'resolved': '已解决'
  }
  return textMap[status] || status
}

// 操作方法
const createIncident = () => {
  ElMessage.info('创建事件功能开发中...')
}

const viewIncident = (incident) => {
  ElMessage.info(`查看事件: ${incident.id}`)
}

const assignIncident = (incident) => {
  ElMessage.info(`分配事件: ${incident.id}`)
}

const escalateIncident = (incident) => {
  ElMessage.info(`升级事件: ${incident.id}`)
}

const resolveIncident = (incident) => {
  ElMessage.info(`解决事件: ${incident.id}`)
}

// 组件挂载时加载数据
onMounted(() => {
  loadIncidents()
})
</script>

<style scoped>
.incident-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #616161;
  margin: 0;
}

.search-filters {
  margin-bottom: 20px;
}

.incidents-table {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
