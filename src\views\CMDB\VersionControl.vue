<template>
  <div class="version-control">
    <div class="page-header">
      <div class="header-content">
        <h2>版本控制与历史回溯</h2>
        <p>管理配置项版本历史，提供版本对比、回溯和审计功能</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createSnapshot">
          <el-icon><Document /></el-icon>
          创建快照
        </el-button>
        <el-button @click="showBatchOperationDialog = true">
          <el-icon><Operation /></el-icon>
          批量操作
        </el-button>
        <el-button @click="exportVersionHistory">
          <el-icon><Download /></el-icon>
          导出历史
        </el-button>
        <el-button @click="showVersionSettings = true">
          <el-icon><Setting /></el-icon>
          版本设置
        </el-button>
      </div>
    </div>

    <!-- 版本统计概览 -->
    <div class="version-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="stat in versionStats" :key="stat.key">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24" :color="stat.color">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" v-if="stat.trend">
                  <el-icon :size="12" :color="stat.trend > 0 ? '#4CAF50' : '#F44336'">
                    <component :is="stat.trend > 0 ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                  <span :style="{ color: stat.trend > 0 ? '#4CAF50' : '#F44336' }">
                    {{ Math.abs(stat.trend) }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 版本策略和配置 -->
    <div class="version-policies">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>版本策略</span>
                <el-button type="primary" size="small" @click="editVersionPolicy">
                  <el-icon><Edit /></el-icon>
                  编辑策略
                </el-button>
              </div>
            </template>
            <div class="policy-list">
              <div class="policy-item" v-for="policy in versionPolicies" :key="policy.id">
                <div class="policy-header">
                  <div class="policy-name">{{ policy.name }}</div>
                  <el-switch v-model="policy.enabled" @change="updatePolicy(policy)" />
                </div>
                <div class="policy-desc">{{ policy.description }}</div>
                <div class="policy-config">
                  <span class="config-item">保留期限: {{ policy.retentionDays }}天</span>
                  <span class="config-item">最大版本数: {{ policy.maxVersions }}</span>
                  <span class="config-item">触发条件: {{ policy.triggerCondition }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>版本趋势</span>
            </template>
            <div class="version-trend-chart" ref="versionTrendChart">
              <!-- 版本趋势图表将在这里渲染 -->
              <div class="chart-placeholder">
                <el-icon :size="64" color="#C0C4CC">
                  <TrendCharts />
                </el-icon>
                <p>版本创建趋势图</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 版本历史列表 -->
    <div class="version-history">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>版本历史</span>
            <div class="header-controls">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索版本..."
                style="width: 200px; margin-right: 12px"
                prefix-icon="Search"
                @input="handleSearch"
              />
              <el-select
                v-model="selectedCI"
                placeholder="配置项筛选"
                style="width: 150px; margin-right: 12px"
                @change="handleCIFilter"
                filterable
                clearable
              >
                <el-option
                  v-for="ci in ciOptions"
                  :key="ci.id"
                  :label="ci.name"
                  :value="ci.id"
                />
              </el-select>
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px; margin-right: 12px"
                @change="handleDateFilter"
              />
              <el-select
                v-model="selectedVersionType"
                placeholder="版本类型"
                style="width: 120px"
                @change="handleTypeFilter"
              >
                <el-option label="全部类型" value="" />
                <el-option label="自动快照" value="auto" />
                <el-option label="手动快照" value="manual" />
                <el-option label="变更快照" value="change" />
                <el-option label="备份快照" value="backup" />
              </el-select>
            </div>
          </div>
        </template>
        <el-table 
          :data="filteredVersions" 
          style="width: 100%" 
          v-loading="loading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="版本ID" width="120" />
          <el-table-column prop="ciName" label="配置项" min-width="150">
            <template #default="scope">
              <el-button type="text" @click="viewCI(scope.row.ciId)">
                {{ scope.row.ciName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="version" label="版本号" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.isCurrent ? 'success' : 'info'" size="small">
                {{ scope.row.version }}
                <el-icon v-if="scope.row.isCurrent" style="margin-left: 4px">
                  <Star />
                </el-icon>
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="100">
            <template #default="scope">
              <el-tag :type="getVersionTypeColor(scope.row.type)" size="small">
                {{ getVersionTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column prop="creator" label="创建人" width="100" />
          <el-table-column prop="size" label="大小" width="80">
            <template #default="scope">
              <span class="version-size">{{ formatSize(scope.row.size) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewVersion(scope.row)">
                查看
              </el-button>
              <el-button 
                type="warning" 
                size="small" 
                text 
                @click="restoreVersion(scope.row)"
                :disabled="scope.row.isCurrent"
              >
                恢复
              </el-button>
              <el-button type="primary" size="small" text @click="compareVersion(scope.row)">
                对比
              </el-button>
              <el-dropdown @command="handleVersionAction" trigger="click">
                <el-button type="primary" size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'download', version: scope.row}">
                      <el-icon><Download /></el-icon>
                      下载
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'tag', version: scope.row}">
                      <el-icon><CollectionTag /></el-icon>
                      标记
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'branch', version: scope.row}">
                      <el-icon><Share /></el-icon>
                      创建分支
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="{action: 'delete', version: scope.row}"
                      :disabled="scope.row.isCurrent"
                      divided
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalItems"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 版本对比对话框 -->
    <el-dialog v-model="showCompareDialog" title="版本对比" width="80%" top="5vh">
      <div class="version-compare">
        <div class="compare-header">
          <div class="compare-selector">
            <el-select v-model="compareVersions.source" placeholder="选择源版本" style="width: 200px">
              <el-option
                v-for="version in versions"
                :key="version.id"
                :label="`${version.version} (${version.createTime})`"
                :value="version.id"
              />
            </el-select>
            <el-icon style="margin: 0 16px; color: #666"><ArrowRight /></el-icon>
            <el-select v-model="compareVersions.target" placeholder="选择目标版本" style="width: 200px">
              <el-option
                v-for="version in versions"
                :key="version.id"
                :label="`${version.version} (${version.createTime})`"
                :value="version.id"
              />
            </el-select>
            <el-button type="primary" @click="performCompare" style="margin-left: 16px">
              开始对比
            </el-button>
          </div>
        </div>
        <div class="compare-content" v-if="compareResult">
          <el-tabs v-model="activeCompareTab">
            <el-tab-pane label="字段变更" name="fields">
              <div class="field-changes">
                <div class="change-item" v-for="change in compareResult.fieldChanges" :key="change.field">
                  <div class="change-header">
                    <span class="field-name">{{ change.field }}</span>
                    <el-tag :type="getChangeType(change.type)" size="small">
                      {{ getChangeText(change.type) }}
                    </el-tag>
                  </div>
                  <div class="change-content">
                    <div class="change-before">
                      <div class="change-label">变更前:</div>
                      <div class="change-value">{{ change.oldValue || '(空)' }}</div>
                    </div>
                    <div class="change-after">
                      <div class="change-label">变更后:</div>
                      <div class="change-value">{{ change.newValue || '(空)' }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="关系变更" name="relations">
              <div class="relation-changes">
                <div class="change-section" v-for="section in compareResult.relationChanges" :key="section.type">
                  <h4>{{ section.title }}</h4>
                  <el-table :data="section.changes" style="width: 100%">
                    <el-table-column prop="relation" label="关系" />
                    <el-table-column prop="target" label="目标CI" />
                    <el-table-column prop="action" label="操作">
                      <template #default="scope">
                        <el-tag :type="getActionType(scope.row.action)" size="small">
                          {{ scope.row.action }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="统计信息" name="stats">
              <div class="compare-stats">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="总变更数">{{ compareResult.stats.totalChanges }}</el-descriptions-item>
                  <el-descriptions-item label="字段变更">{{ compareResult.stats.fieldChanges }}</el-descriptions-item>
                  <el-descriptions-item label="关系变更">{{ compareResult.stats.relationChanges }}</el-descriptions-item>
                  <el-descriptions-item label="相似度">{{ compareResult.stats.similarity }}%</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCompareDialog = false">关闭</el-button>
          <el-button type="primary" @click="exportCompareResult" v-if="compareResult">
            导出对比结果
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 版本设置对话框 -->
    <el-dialog v-model="showVersionSettings" title="版本控制设置" width="60%">
      <el-form :model="versionSettings" label-width="120px">
        <el-form-item label="自动快照">
          <el-switch v-model="versionSettings.autoSnapshot" />
          <span class="form-help">启用后将在CI变更时自动创建快照</span>
        </el-form-item>
        <el-form-item label="快照频率">
          <el-select v-model="versionSettings.snapshotFrequency" style="width: 100%">
            <el-option label="每次变更" value="change" />
            <el-option label="每小时" value="hourly" />
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
          </el-select>
        </el-form-item>
        <el-form-item label="保留策略">
          <el-radio-group v-model="versionSettings.retentionPolicy">
            <el-radio label="time">按时间保留</el-radio>
            <el-radio label="count">按数量保留</el-radio>
            <el-radio label="size">按大小保留</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="保留期限" v-if="versionSettings.retentionPolicy === 'time'">
          <el-input-number v-model="versionSettings.retentionDays" :min="1" :max="3650" />
          <span style="margin-left: 8px">天</span>
        </el-form-item>
        <el-form-item label="最大版本数" v-if="versionSettings.retentionPolicy === 'count'">
          <el-input-number v-model="versionSettings.maxVersions" :min="1" :max="1000" />
        </el-form-item>
        <el-form-item label="最大存储" v-if="versionSettings.retentionPolicy === 'size'">
          <el-input-number v-model="versionSettings.maxStorageGB" :min="1" :max="10000" />
          <span style="margin-left: 8px">GB</span>
        </el-form-item>
        <el-form-item label="压缩存储">
          <el-switch v-model="versionSettings.compression" />
          <span class="form-help">启用压缩可节省存储空间</span>
        </el-form-item>
        <el-form-item label="审计日志">
          <el-switch v-model="versionSettings.auditLog" />
          <span class="form-help">记录所有版本操作的审计日志</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showVersionSettings = false">取消</el-button>
          <el-button type="primary" @click="saveVersionSettings">保存设置</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showBatchOperationDialog = ref(false)
const showVersionSettings = ref(false)
const showCompareDialog = ref(false)
const versionTrendChart = ref(null)

// 搜索和筛选
const searchKeyword = ref('')
const selectedCI = ref('')
const selectedVersionType = ref('')
const dateRange = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const selectedVersions = ref([])

// 版本对比
const activeCompareTab = ref('fields')
const compareVersions = reactive({
  source: '',
  target: ''
})
const compareResult = ref(null)

// 版本统计
const versionStats = ref([
  {
    key: 'total',
    label: '总版本数',
    value: '2,847',
    icon: 'Document',
    color: '#1976D2',
    trend: 12.5
  },
  {
    key: 'today',
    label: '今日创建',
    value: '23',
    icon: 'Plus',
    color: '#4CAF50',
    trend: 8.3
  },
  {
    key: 'storage',
    label: '存储占用',
    value: '1.2TB',
    icon: 'Box',
    color: '#FF9800',
    trend: 5.7
  },
  {
    key: 'restored',
    label: '本月恢复',
    value: '15',
    icon: 'RefreshLeft',
    color: '#9C27B0',
    trend: -2.1
  }
])

// 版本策略
const versionPolicies = ref([
  {
    id: 'auto_snapshot',
    name: '自动快照策略',
    description: '在配置项发生重要变更时自动创建版本快照',
    enabled: true,
    retentionDays: 90,
    maxVersions: 50,
    triggerCondition: '重要字段变更'
  },
  {
    id: 'scheduled_backup',
    name: '定时备份策略',
    description: '按照设定的时间间隔定期创建备份版本',
    enabled: true,
    retentionDays: 365,
    maxVersions: 12,
    triggerCondition: '每月1日'
  },
  {
    id: 'change_tracking',
    name: '变更跟踪策略',
    description: '跟踪所有配置项变更并创建对应的版本记录',
    enabled: false,
    retentionDays: 30,
    maxVersions: 100,
    triggerCondition: '任何变更'
  }
])

// CI选项
const ciOptions = ref([
  { id: 'CI-001', name: 'WEB-SRV-01' },
  { id: 'CI-002', name: 'DB-SRV-01' },
  { id: 'CI-003', name: 'APP-SRV-01' },
  { id: 'CI-004', name: 'LB-01' },
  { id: 'CI-005', name: 'CACHE-SRV-01' }
])

// 版本历史数据
const versions = ref([
  {
    id: 'VER-001',
    ciId: 'CI-001',
    ciName: 'WEB-SRV-01',
    version: 'v2.1.3',
    type: 'manual',
    description: '手动创建的稳定版本快照',
    createTime: '2025-01-30 14:30:00',
    creator: '张工',
    size: 2048576,
    isCurrent: true
  },
  {
    id: 'VER-002',
    ciId: 'CI-001',
    ciName: 'WEB-SRV-01',
    version: 'v2.1.2',
    type: 'auto',
    description: '配置变更自动快照',
    createTime: '2025-01-25 09:15:00',
    creator: '系统',
    size: 1945600,
    isCurrent: false
  },
  {
    id: 'VER-003',
    ciId: 'CI-002',
    ciName: 'DB-SRV-01',
    version: 'v1.5.8',
    type: 'change',
    description: '数据库配置更新快照',
    createTime: '2025-01-28 16:45:00',
    creator: '李工',
    size: 3145728,
    isCurrent: true
  },
  {
    id: 'VER-004',
    ciId: 'CI-003',
    ciName: 'APP-SRV-01',
    version: 'v3.2.1',
    type: 'backup',
    description: '定期备份快照',
    createTime: '2025-01-20 02:00:00',
    creator: '系统',
    size: 1572864,
    isCurrent: false
  }
])

// 版本设置
const versionSettings = reactive({
  autoSnapshot: true,
  snapshotFrequency: 'change',
  retentionPolicy: 'time',
  retentionDays: 90,
  maxVersions: 50,
  maxStorageGB: 100,
  compression: true,
  auditLog: true
})

// 计算属性
const filteredVersions = computed(() => {
  let filtered = versions.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(version =>
      version.ciName.toLowerCase().includes(keyword) ||
      version.version.toLowerCase().includes(keyword) ||
      version.description.toLowerCase().includes(keyword)
    )
  }

  // 按CI筛选
  if (selectedCI.value) {
    filtered = filtered.filter(version => version.ciId === selectedCI.value)
  }

  // 按版本类型筛选
  if (selectedVersionType.value) {
    filtered = filtered.filter(version => version.type === selectedVersionType.value)
  }

  // 按日期范围筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(version => {
      const versionDate = new Date(version.createTime)
      return versionDate >= startDate && versionDate <= endDate
    })
  }

  totalItems.value = filtered.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 方法函数
const createSnapshot = () => {
  ElMessage.info('正在创建版本快照...')

  // 模拟创建快照
  setTimeout(() => {
    const newVersion = {
      id: `VER-${Date.now()}`,
      ciId: 'CI-001',
      ciName: 'WEB-SRV-01',
      version: `v2.1.${versions.value.length + 1}`,
      type: 'manual',
      description: '手动创建的版本快照',
      createTime: new Date().toLocaleString(),
      creator: '当前用户',
      size: Math.floor(Math.random() * 5000000) + 1000000,
      isCurrent: false
    }

    // 将之前的当前版本标记为非当前
    versions.value.forEach(v => {
      if (v.ciId === newVersion.ciId && v.isCurrent) {
        v.isCurrent = false
      }
    })

    newVersion.isCurrent = true
    versions.value.unshift(newVersion)

    ElMessage.success('版本快照创建成功')
  }, 2000)
}

const exportVersionHistory = () => {
  ElMessage.success('正在导出版本历史...')
  // 这里可以实现导出逻辑
}

const editVersionPolicy = () => {
  ElMessage.info('打开版本策略编辑器')
  // 这里可以实现策略编辑逻辑
}

const updatePolicy = (policy) => {
  ElMessage.success(`版本策略 "${policy.name}" 已${policy.enabled ? '启用' : '禁用'}`)
  // 这里可以实现策略更新逻辑
}

const viewCI = (ciId) => {
  // 跳转到CI详情页面
  window.open(`/cmdb/ci/${ciId}`, '_blank')
}

const viewVersion = (version) => {
  ElMessage.info(`查看版本详情: ${version.version}`)
  // 这里可以实现查看版本详情的逻辑
}

const restoreVersion = (version) => {
  ElMessageBox.confirm(
    `确定要恢复到版本 ${version.version} 吗？当前版本将被替换！`,
    '确认恢复版本',
    {
      confirmButtonText: '确定恢复',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
      message: `
        <div>
          <p>此操作将会：</p>
          <ul>
            <li>将配置项恢复到版本 <strong>${version.version}</strong></li>
            <li>当前版本将自动创建备份快照</li>
            <li>所有相关关系将同步更新</li>
          </ul>
          <p style="color: #E6A23C;">请确认您了解此操作的影响！</p>
        </div>
      `
    }
  ).then(() => {
    // 创建当前版本的备份
    const currentVersion = versions.value.find(v => v.ciId === version.ciId && v.isCurrent)
    if (currentVersion) {
      currentVersion.isCurrent = false
      const backupVersion = {
        ...currentVersion,
        id: `VER-${Date.now()}-backup`,
        version: `${currentVersion.version}-backup`,
        type: 'backup',
        description: `恢复前自动备份: ${currentVersion.description}`,
        createTime: new Date().toLocaleString(),
        creator: '系统'
      }
      versions.value.unshift(backupVersion)
    }

    // 设置目标版本为当前版本
    version.isCurrent = true

    ElMessage.success(`已成功恢复到版本 ${version.version}`)
  }).catch(() => {
    ElMessage.info('已取消恢复操作')
  })
}

const compareVersion = (version) => {
  compareVersions.source = version.id
  showCompareDialog.value = true
}

const performCompare = () => {
  if (!compareVersions.source || !compareVersions.target) {
    ElMessage.warning('请选择要对比的两个版本')
    return
  }

  if (compareVersions.source === compareVersions.target) {
    ElMessage.warning('不能对比相同的版本')
    return
  }

  ElMessage.info('正在对比版本差异...')

  // 模拟版本对比
  setTimeout(() => {
    compareResult.value = {
      fieldChanges: [
        {
          field: 'IP地址',
          type: 'modified',
          oldValue: '************',
          newValue: '************'
        },
        {
          field: '内存配置',
          type: 'modified',
          oldValue: '8GB',
          newValue: '16GB'
        },
        {
          field: '操作系统版本',
          type: 'modified',
          oldValue: 'CentOS 7.8',
          newValue: 'CentOS 7.9'
        },
        {
          field: '监控端口',
          type: 'added',
          oldValue: null,
          newValue: '9090'
        }
      ],
      relationChanges: [
        {
          type: 'added',
          title: '新增关系',
          changes: [
            { relation: '依赖于', target: 'CACHE-SRV-01', action: '新增' }
          ]
        },
        {
          type: 'removed',
          title: '删除关系',
          changes: [
            { relation: '连接到', target: 'OLD-SW-01', action: '删除' }
          ]
        }
      ],
      stats: {
        totalChanges: 5,
        fieldChanges: 4,
        relationChanges: 2,
        similarity: 87
      }
    }

    ElMessage.success('版本对比完成')
  }, 1500)
}

const exportCompareResult = () => {
  ElMessage.success('正在导出对比结果...')
  // 这里可以实现导出对比结果的逻辑
}

const handleVersionAction = (command) => {
  const { action, version } = command

  switch (action) {
    case 'download':
      ElMessage.success(`正在下载版本 ${version.version}...`)
      break
    case 'tag':
      ElMessage.info(`为版本 ${version.version} 添加标签`)
      break
    case 'branch':
      ElMessage.info(`从版本 ${version.version} 创建分支`)
      break
    case 'delete':
      ElMessageBox.confirm(
        `确定要删除版本 ${version.version} 吗？此操作不可恢复！`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        const index = versions.value.findIndex(v => v.id === version.id)
        if (index > -1) {
          versions.value.splice(index, 1)
          ElMessage.success(`版本 ${version.version} 已删除`)
        }
      }).catch(() => {
        ElMessage.info('已取消删除')
      })
      break
  }
}

const handleSelectionChange = (selection) => {
  selectedVersions.value = selection
}

const saveVersionSettings = () => {
  ElMessage.success('版本控制设置已保存')
  showVersionSettings.value = false
  // 这里可以实现保存设置的逻辑
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleCIFilter = () => {
  currentPage.value = 1
}

const handleDateFilter = () => {
  currentPage.value = 1
}

const handleTypeFilter = () => {
  currentPage.value = 1
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 辅助函数
const getVersionTypeColor = (type) => {
  const colorMap = {
    'auto': 'primary',
    'manual': 'success',
    'change': 'warning',
    'backup': 'info'
  }
  return colorMap[type] || 'info'
}

const getVersionTypeText = (type) => {
  const textMap = {
    'auto': '自动快照',
    'manual': '手动快照',
    'change': '变更快照',
    'backup': '备份快照'
  }
  return textMap[type] || type
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getChangeType = (type) => {
  const typeMap = {
    'added': 'success',
    'modified': 'warning',
    'removed': 'danger'
  }
  return typeMap[type] || 'info'
}

const getChangeText = (type) => {
  const textMap = {
    'added': '新增',
    'modified': '修改',
    'removed': '删除'
  }
  return textMap[type] || type
}

const getActionType = (action) => {
  const typeMap = {
    '新增': 'success',
    '修改': 'warning',
    '删除': 'danger'
  }
  return typeMap[action] || 'info'
}

// 组件挂载时初始化
onMounted(() => {
  totalItems.value = versions.value.length

  // 可以在这里初始化图表
  console.log('Version Control component mounted')
})
</script>

<style scoped>
.version-control {
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-content h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-content p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 版本概览样式 */
.version-overview {
  margin-bottom: 20px;
}

.stat-card {
  height: 90px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 12px;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
}

/* 版本策略样式 */
.version-policies {
  margin-bottom: 20px;
}

.policy-list {
  max-height: 300px;
  overflow-y: auto;
}

.policy-item {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafafa;
}

.policy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.policy-name {
  font-weight: 500;
  color: #333;
}

.policy-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.policy-config {
  display: flex;
  gap: 16px;
  font-size: 11px;
  color: #999;
}

.config-item {
  padding: 2px 6px;
  background: #e0e0e0;
  border-radius: 4px;
}

/* 版本趋势图表样式 */
.version-trend-chart {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.chart-placeholder {
  text-align: center;
  color: #666;
}

.chart-placeholder p {
  margin: 16px 0 0 0;
  font-size: 16px;
}

/* 版本历史样式 */
.version-history {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.version-size {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
}

/* 版本对比样式 */
.version-compare {
  padding: 16px 0;
}

.compare-header {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.compare-selector {
  display: flex;
  align-items: center;
  justify-content: center;
}

.compare-content {
  min-height: 400px;
}

.field-changes {
  padding: 16px 0;
}

.change-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.field-name {
  font-weight: 500;
  color: #333;
}

.change-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.change-before,
.change-after {
  padding: 8px;
  border-radius: 4px;
}

.change-before {
  background: #ffebee;
  border-left: 3px solid #f44336;
}

.change-after {
  background: #e8f5e8;
  border-left: 3px solid #4caf50;
}

.change-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.change-value {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #333;
  word-break: break-all;
}

.relation-changes {
  padding: 16px 0;
}

.change-section {
  margin-bottom: 24px;
}

.change-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
}

.compare-stats {
  padding: 16px 0;
}

/* 表单帮助文本 */
.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-button--text {
  padding: 4px 8px;
}

.el-progress {
  margin: 0;
}

.el-progress__text {
  font-size: 12px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .header-controls > * {
    margin-bottom: 8px;
  }

  .compare-selector {
    flex-direction: column;
    gap: 12px;
  }

  .change-content {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .version-control {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stat-card {
    margin-bottom: 12px;
    height: 80px;
  }

  .stat-value {
    font-size: 16px;
  }

  .policy-item {
    padding: 12px;
  }

  .policy-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .policy-config {
    flex-direction: column;
    gap: 4px;
  }

  .version-trend-chart {
    height: 250px;
  }

  .compare-header {
    padding: 12px;
  }

  .change-item {
    padding: 8px;
  }

  /* 表格在移动端的优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* 对话框在移动端的优化 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .policy-config {
    text-align: center;
  }

  .compare-selector {
    align-items: stretch;
  }

  .compare-selector .el-select {
    width: 100%;
  }

  .change-before,
  .change-after {
    margin-bottom: 8px;
  }
}

/* 动画效果 */
.stat-card,
.policy-item,
.change-item {
  transition: all 0.3s ease;
}

.policy-item:hover,
.change-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa;
}

/* 标签样式 */
.el-tag {
  font-weight: 500;
}

/* 开关样式 */
.el-switch {
  margin-left: auto;
}

/* 自定义滚动条 */
.policy-list::-webkit-scrollbar {
  width: 6px;
}

.policy-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.policy-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.policy-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 版本对比特殊样式 */
.el-dialog__body {
  padding: 20px;
}

.el-tabs__content {
  padding: 20px 0;
}

/* 描述列表样式 */
.el-descriptions {
  margin: 0;
}

.el-descriptions__body .el-descriptions__table {
  border-radius: 8px;
  overflow: hidden;
}
</style>
