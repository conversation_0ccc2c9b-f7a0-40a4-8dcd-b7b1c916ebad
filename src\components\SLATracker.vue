<template>
  <div class="sla-tracker">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>SLA跟踪与告警</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshSLA">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" @click="showSettings = true">
              <el-icon><Setting /></el-icon>
              设置
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- SLA概览 -->
      <div class="sla-overview">
        <el-row :gutter="20">
          <el-col :xs="12" :sm="6" v-for="metric in slaMetrics" :key="metric.key">
            <div class="metric-card" :class="metric.status">
              <div class="metric-icon">
                <el-icon :size="24">
                  <component :is="metric.icon" />
                </el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-trend" :class="metric.trend">
                  <el-icon><component :is="metric.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ metric.change }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- SLA告警列表 -->
      <div class="sla-alerts">
        <div class="section-header">
          <h3>SLA告警 ({{ slaAlerts.length }})</h3>
          <el-button-group size="small">
            <el-button @click="filterAlerts('all')" :type="alertFilter === 'all' ? 'primary' : ''">
              全部
            </el-button>
            <el-button @click="filterAlerts('critical')" :type="alertFilter === 'critical' ? 'primary' : ''">
              严重
            </el-button>
            <el-button @click="filterAlerts('warning')" :type="alertFilter === 'warning' ? 'primary' : ''">
              警告
            </el-button>
          </el-button-group>
        </div>
        
        <div class="alerts-list">
          <div
            v-for="alert in filteredAlerts"
            :key="alert.id"
            class="alert-item"
            :class="alert.level"
            @click="viewAlertDetail(alert)"
          >
            <div class="alert-icon">
              <el-icon :size="20">
                <component :is="getAlertIcon(alert.level)" />
              </el-icon>
            </div>
            <div class="alert-content">
              <div class="alert-header">
                <span class="alert-title">{{ alert.title }}</span>
                <span class="alert-time">{{ alert.time }}</span>
              </div>
              <div class="alert-description">{{ alert.description }}</div>
              <div class="alert-progress">
                <div class="progress-info">
                  <span>SLA进度: {{ alert.slaProgress }}%</span>
                  <span class="remaining-time">剩余: {{ alert.remainingTime }}</span>
                </div>
                <el-progress
                  :percentage="alert.slaProgress"
                  :status="getSLAStatus(alert.slaProgress)"
                  :stroke-width="6"
                />
              </div>
            </div>
            <div class="alert-actions">
              <el-button type="primary" size="small" @click.stop="handleAlert(alert)">
                处理
              </el-button>
              <el-button size="small" @click.stop="snoozeAlert(alert)">
                暂停
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- SLA趋势图 -->
      <div class="sla-trends">
        <div class="section-header">
          <h3>SLA趋势分析</h3>
          <el-radio-group v-model="trendPeriod" size="small">
            <el-radio-button label="24h">24小时</el-radio-button>
            <el-radio-button label="7d">7天</el-radio-button>
            <el-radio-button label="30d">30天</el-radio-button>
          </el-radio-group>
        </div>
        <div class="trend-chart" ref="slaChart"></div>
      </div>
      
      <!-- 工单SLA详情 -->
      <div class="ticket-sla-details">
        <div class="section-header">
          <h3>工单SLA详情</h3>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索工单..."
            prefix-icon="Search"
            size="small"
            style="width: 200px;"
            @input="searchTickets"
          />
        </div>
        
        <el-table :data="filteredTickets" style="width: 100%">
          <el-table-column prop="id" label="工单号" width="120" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="priority" label="优先级" width="80">
            <template #default="scope">
              <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="SLA进度" width="200">
            <template #default="scope">
              <div class="sla-progress-cell">
                <div class="progress-text">
                  {{ scope.row.slaProgress }}% ({{ scope.row.remainingTime }})
                </div>
                <el-progress
                  :percentage="scope.row.slaProgress"
                  :status="getSLAStatus(scope.row.slaProgress)"
                  :stroke-width="4"
                  :show-text="false"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="处理人" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewTicket(scope.row)">
                查看
              </el-button>
              <el-button type="warning" size="small" text @click="escalateTicket(scope.row)">
                升级
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <!-- SLA设置对话框 -->
    <el-dialog v-model="showSettings" title="SLA设置" width="60%">
      <el-tabs v-model="activeSettingTab">
        <el-tab-pane label="SLA规则" name="rules">
          <div class="sla-rules">
            <el-table :data="slaRules" style="width: 100%">
              <el-table-column prop="name" label="规则名称" />
              <el-table-column prop="priority" label="优先级" width="100" />
              <el-table-column prop="responseTime" label="响应时间" width="100" />
              <el-table-column prop="resolutionTime" label="解决时间" width="100" />
              <el-table-column prop="enabled" label="状态" width="80">
                <template #default="scope">
                  <el-switch v-model="scope.row.enabled" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button type="primary" size="small" text @click="editSLARule(scope.row)">
                    编辑
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="告警设置" name="alerts">
          <div class="alert-settings">
            <el-form :model="alertSettings" label-width="120px">
              <el-form-item label="告警阈值">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-input-number
                      v-model="alertSettings.warningThreshold"
                      :min="0"
                      :max="100"
                      :precision="0"
                    />
                    <span class="threshold-label">警告阈值(%)</span>
                  </el-col>
                  <el-col :span="8">
                    <el-input-number
                      v-model="alertSettings.criticalThreshold"
                      :min="0"
                      :max="100"
                      :precision="0"
                    />
                    <span class="threshold-label">严重阈值(%)</span>
                  </el-col>
                </el-row>
              </el-form-item>
              
              <el-form-item label="通知方式">
                <el-checkbox-group v-model="alertSettings.notificationMethods">
                  <el-checkbox label="email">邮件通知</el-checkbox>
                  <el-checkbox label="sms">短信通知</el-checkbox>
                  <el-checkbox label="wechat">企业微信</el-checkbox>
                  <el-checkbox label="popup">弹窗提醒</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="自动升级">
                <el-switch v-model="alertSettings.autoEscalate" />
                <span class="form-help">SLA超时时自动升级工单</span>
              </el-form-item>
              
              <el-form-item label="升级延迟">
                <el-input-number
                  v-model="alertSettings.escalateDelay"
                  :min="5"
                  :max="120"
                  :disabled="!alertSettings.autoEscalate"
                />
                <span class="form-help">分钟</span>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSLASettings">保存设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'

const router = useRouter()

// 响应式数据
const showSettings = ref(false)
const activeSettingTab = ref('rules')
const alertFilter = ref('all')
const trendPeriod = ref('24h')
const searchKeyword = ref('')

// SLA指标
const slaMetrics = ref([
  {
    key: 'overall',
    label: '总体达成率',
    value: '92.5%',
    icon: 'TrendCharts',
    status: 'success',
    trend: 'up',
    change: '+2.3%'
  },
  {
    key: 'response',
    label: '响应时间达成',
    value: '95.8%',
    icon: 'Timer',
    status: 'success',
    trend: 'up',
    change: '+1.2%'
  },
  {
    key: 'resolution',
    label: '解决时间达成',
    value: '88.9%',
    icon: 'CircleCheck',
    status: 'warning',
    trend: 'down',
    change: '-0.8%'
  },
  {
    key: 'overdue',
    label: '超时工单',
    value: '5',
    icon: 'Warning',
    status: 'danger',
    trend: 'up',
    change: '+2'
  }
])

// SLA告警
const slaAlerts = ref([
  {
    id: 1,
    title: 'INC-2025-001 即将超时',
    description: '服务器CPU使用率过高告警，距离SLA超时还有15分钟',
    level: 'critical',
    time: '2025-01-30 17:45',
    slaProgress: 15,
    remainingTime: '15分钟',
    ticketId: 'INC-2025-001'
  },
  {
    id: 2,
    title: 'REQ-2025-002 响应时间预警',
    description: '新员工邮箱账号申请，响应时间即将超时',
    level: 'warning',
    time: '2025-01-30 17:30',
    slaProgress: 35,
    remainingTime: '2小时',
    ticketId: 'REQ-2025-002'
  }
])

// 工单SLA数据
const tickets = ref([
  {
    id: 'INC-2025-001',
    title: '服务器CPU使用率过高告警',
    priority: '高',
    slaProgress: 15,
    remainingTime: '15分钟',
    assignee: '张工',
    createTime: '2025-01-30 14:30'
  },
  {
    id: 'REQ-2025-002',
    title: '新员工邮箱账号申请',
    priority: '中',
    slaProgress: 35,
    remainingTime: '2小时',
    assignee: '李工',
    createTime: '2025-01-30 13:45'
  }
])

// SLA规则
const slaRules = ref([
  {
    id: 1,
    name: '紧急事件',
    priority: '紧急',
    responseTime: '15分钟',
    resolutionTime: '4小时',
    enabled: true
  },
  {
    id: 2,
    name: '高优先级事件',
    priority: '高',
    responseTime: '1小时',
    resolutionTime: '8小时',
    enabled: true
  }
])

// 告警设置
const alertSettings = reactive({
  warningThreshold: 30,
  criticalThreshold: 15,
  notificationMethods: ['email', 'popup'],
  autoEscalate: true,
  escalateDelay: 30
})

// 图表引用
const slaChart = ref()

// 过滤后的告警
const filteredAlerts = computed(() => {
  if (alertFilter.value === 'all') {
    return slaAlerts.value
  }
  return slaAlerts.value.filter(alert => alert.level === alertFilter.value)
})

// 过滤后的工单
const filteredTickets = computed(() => {
  if (!searchKeyword.value) {
    return tickets.value
  }
  const keyword = searchKeyword.value.toLowerCase()
  return tickets.value.filter(ticket =>
    ticket.id.toLowerCase().includes(keyword) ||
    ticket.title.toLowerCase().includes(keyword)
  )
})

// 刷新SLA数据
const refreshSLA = () => {
  ElMessage.success('SLA数据已刷新')
  // 模拟数据更新
  slaMetrics.value.forEach(metric => {
    if (metric.key === 'overdue') {
      const change = Math.floor(Math.random() * 3) - 1
      const currentValue = parseInt(metric.value) || 0
      metric.value = Math.max(0, currentValue + change).toString()
    }
  })
}

// 过滤告警
const filterAlerts = (level) => {
  alertFilter.value = level
}

// 获取告警图标
const getAlertIcon = (level) => {
  const iconMap = {
    'critical': 'Warning',
    'warning': 'InfoFilled',
    'info': 'Bell'
  }
  return iconMap[level] || 'Bell'
}

// 获取SLA状态
const getSLAStatus = (progress) => {
  if (progress <= 15) return 'exception'
  if (progress <= 30) return 'warning'
  return 'success'
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colorMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return colorMap[priority] || 'info'
}

// 查看告警详情
const viewAlertDetail = (alert) => {
  router.push(`/service-desk/tickets/${alert.ticketId}`)
}

// 处理告警
const handleAlert = (alert) => {
  router.push(`/service-desk/tickets/${alert.ticketId}/process`)
}

// 暂停告警
const snoozeAlert = (alert) => {
  ElMessageBox.prompt('请输入暂停时间（分钟）', '暂停告警', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^\d+$/,
    inputErrorMessage: '请输入有效的分钟数'
  }).then(({ value }) => {
    ElMessage.success(`告警已暂停 ${value} 分钟`)
    // 这里可以实现暂停告警的逻辑
  }).catch(() => {
    ElMessage.info('已取消暂停')
  })
}

// 搜索工单
const searchTickets = () => {
  // 搜索逻辑已在computed中实现
}

// 查看工单
const viewTicket = (ticket) => {
  router.push(`/service-desk/tickets/${ticket.id}`)
}

// 升级工单
const escalateTicket = (ticket) => {
  ElMessageBox.confirm(
    `确定要升级工单 "${ticket.title}" 吗？`,
    '确认升级',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('工单已升级')
    // 这里可以实现升级工单的逻辑
  })
}

// 编辑SLA规则
const editSLARule = (rule) => {
  ElMessage.info(`编辑SLA规则: ${rule.name}`)
  // 这里可以实现编辑规则的逻辑
}

// 保存SLA设置
const saveSLASettings = () => {
  ElMessage.success('SLA设置已保存')
  showSettings.value = false
  // 这里可以实现保存设置的API调用
}

// 初始化图表
const initChart = () => {
  if (!slaChart.value) return

  const chartInstance = echarts.init(slaChart.value)
  chartInstance.setOption({
    title: {
      text: 'SLA达成率趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['响应时间SLA', '解决时间SLA', '总体SLA']
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '响应时间SLA',
        type: 'line',
        data: [95, 96, 94, 97, 95, 98, 96],
        itemStyle: { color: '#4CAF50' }
      },
      {
        name: '解决时间SLA',
        type: 'line',
        data: [88, 90, 87, 92, 89, 91, 88],
        itemStyle: { color: '#FF9800' }
      },
      {
        name: '总体SLA',
        type: 'line',
        data: [92, 93, 91, 94, 92, 95, 92],
        itemStyle: { color: '#1976D2' }
      }
    ]
  })
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })

  // 模拟实时SLA更新
  setInterval(() => {
    // 随机更新SLA进度
    tickets.value.forEach(ticket => {
      if (ticket.slaProgress > 0) {
        ticket.slaProgress = Math.max(0, ticket.slaProgress - Math.random() * 2)

        // 更新剩余时间显示
        if (ticket.slaProgress < 20) {
          const minutes = Math.floor(ticket.slaProgress * 3)
          ticket.remainingTime = `${minutes}分钟`
        }
      }
    })

    // 更新告警
    slaAlerts.value.forEach(alert => {
      if (alert.slaProgress > 0) {
        alert.slaProgress = Math.max(0, alert.slaProgress - Math.random() * 2)

        if (alert.slaProgress < 20) {
          const minutes = Math.floor(alert.slaProgress * 3)
          alert.remainingTime = `${minutes}分钟`
        }
      }
    })
  }, 30000) // 每30秒更新一次
})
</script>

<style scoped>
.sla-tracker {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.sla-overview {
  margin-bottom: 30px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background: white;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-card.success {
  border-left: 4px solid #4CAF50;
}

.metric-card.warning {
  border-left: 4px solid #FF9800;
}

.metric-card.danger {
  border-left: 4px solid #F44336;
}

.metric-icon {
  margin-right: 16px;
  color: #1976D2;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.metric-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.metric-trend.up {
  color: #4CAF50;
}

.metric-trend.down {
  color: #F44336;
}

.sla-alerts,
.sla-trends,
.ticket-sla-details {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alert-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.alert-item.critical {
  border-left: 4px solid #F44336;
  background: #fff5f5;
}

.alert-item.warning {
  border-left: 4px solid #FF9800;
  background: #fff8e1;
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alert-title {
  font-weight: 500;
  color: #333;
}

.alert-time {
  font-size: 12px;
  color: #999;
}

.alert-description {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.4;
}

.alert-progress {
  margin-bottom: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.remaining-time {
  color: #F44336;
  font-weight: 500;
}

.alert-actions {
  margin-left: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trend-chart {
  height: 300px;
  margin-top: 20px;
}

.sla-progress-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #666;
}

.threshold-label {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.form-help {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

@media (max-width: 768px) {
  .metric-card {
    flex-direction: column;
    text-align: center;
  }
  
  .metric-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .alert-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .alert-actions {
    margin-left: 0;
    flex-direction: row;
    justify-content: flex-end;
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
