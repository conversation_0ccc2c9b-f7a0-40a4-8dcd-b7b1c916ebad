<template>
  <div class="data-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <h3 v-if="title">{{ title }}</h3>
        </slot>
      </div>
      
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <!-- 搜索框 -->
          <el-input
            v-if="searchable"
            v-model="searchKeyword"
            placeholder="搜索..."
            clearable
            @input="handleSearch"
            style="width: 200px; margin-right: 12px;"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <!-- 刷新按钮 -->
          <el-button @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          
          <!-- 导出按钮 -->
          <el-button v-if="exportable" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          
          <!-- 列设置 -->
          <el-popover placement="bottom" width="200" trigger="click">
            <template #reference>
              <el-button>
                <el-icon><Setting /></el-icon>
                列设置
              </el-button>
            </template>
            <div class="column-settings">
              <el-checkbox-group v-model="visibleColumns">
                <div 
                  v-for="column in allColumns" 
                  :key="column.prop"
                  class="column-item"
                >
                  <el-checkbox :value="column.prop">
                    {{ column.label }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </el-popover>
        </slot>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="table-filters" v-if="showFilters && filters.length > 0">
      <el-form :model="filterValues" inline>
        <el-form-item 
          v-for="filter in filters" 
          :key="filter.prop"
          :label="filter.label"
        >
          <!-- 下拉选择筛选 -->
          <el-select 
            v-if="filter.type === 'select'"
            v-model="filterValues[filter.prop]"
            :placeholder="`选择${filter.label}`"
            clearable
            @change="handleFilterChange"
          >
            <el-option 
              v-for="option in filter.options" 
              :key="option.value"
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
          
          <!-- 日期范围筛选 -->
          <el-date-picker
            v-else-if="filter.type === 'daterange'"
            v-model="filterValues[filter.prop]"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleFilterChange"
          />
          
          <!-- 文本输入筛选 -->
          <el-input
            v-else
            v-model="filterValues[filter.prop]"
            :placeholder="`输入${filter.label}`"
            clearable
            @input="handleFilterChange"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        :height="tableHeight"
        :stripe="stripe"
        :border="border"
        :size="size"
        :row-key="rowKey"
        :default-sort="defaultSort"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
        @row-dblclick="handleRowDblClick"
        v-bind="$attrs"
      >
        <!-- 选择列 -->
        <el-table-column 
          v-if="selectable"
          type="selection" 
          width="55"
          :selectable="selectableFunction"
        />
        
        <!-- 序号列 -->
        <el-table-column 
          v-if="showIndex"
          type="index" 
          label="序号" 
          width="60"
          :index="getIndex"
        />
        
        <!-- 动态列 -->
        <el-table-column
          v-for="column in displayColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template #default="scope" v-if="column.slot">
            <slot :name="column.slot" :row="scope.row" :column="column" :$index="scope.$index" />
          </template>
          
          <template #default="scope" v-else-if="column.formatter">
            <span v-html="column.formatter(scope.row, column, scope.row[column.prop], scope.$index)"></span>
          </template>
          
          <template #default="scope" v-else-if="column.type === 'tag'">
            <el-tag 
              :type="getTagType(scope.row[column.prop], column.tagMap)"
              size="small"
            >
              {{ getTagText(scope.row[column.prop], column.tagMap) }}
            </el-tag>
          </template>
          
          <template #default="scope" v-else-if="column.type === 'image'">
            <el-image 
              :src="scope.row[column.prop]" 
              :preview-src-list="[scope.row[column.prop]]"
              fit="cover"
              style="width: 40px; height: 40px; border-radius: 4px;"
            />
          </template>
          
          <template #default="scope" v-else-if="column.type === 'link'">
            <el-link 
              :href="scope.row[column.prop]" 
              target="_blank"
              type="primary"
            >
              {{ scope.row[column.prop] }}
            </el-link>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column 
          v-if="showActions"
          label="操作" 
          :width="actionWidth"
          :fixed="actionFixed"
        >
          <template #default="scope">
            <slot name="actions" :row="scope.row" :$index="scope.$index">
              <el-button 
                v-for="action in actions" 
                :key="action.name"
                :type="action.type || 'primary'"
                :size="action.size || 'small'"
                :disabled="action.disabled && action.disabled(scope.row)"
                :loading="action.loading && action.loading(scope.row)"
                @click="handleAction(action.name, scope.row, scope.$index)"
              >
                <el-icon v-if="action.icon">
                  <component :is="action.icon" />
                </el-icon>
                {{ action.label }}
              </el-button>
            </slot>
          </template>
        </el-table-column>
        
        <!-- 空数据 -->
        <template #empty>
          <el-empty :description="emptyText" />
        </template>
      </el-table>
    </div>

    <!-- 分页器 -->
    <div class="table-pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download, Setting } from '@element-plus/icons-vue'

const props = defineProps({
  // 数据相关
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  
  // 表格配置
  title: {
    type: String,
    default: ''
  },
  height: {
    type: [String, Number],
    default: undefined
  },
  stripe: {
    type: Boolean,
    default: true
  },
  border: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'default'
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  defaultSort: {
    type: Object,
    default: () => ({})
  },
  
  // 功能开关
  showToolbar: {
    type: Boolean,
    default: true
  },
  showFilters: {
    type: Boolean,
    default: true
  },
  showIndex: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: true
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  selectable: {
    type: Boolean,
    default: false
  },
  searchable: {
    type: Boolean,
    default: true
  },
  exportable: {
    type: Boolean,
    default: false
  },
  
  // 选择相关
  selectableFunction: {
    type: Function,
    default: () => true
  },
  
  // 操作相关
  actions: {
    type: Array,
    default: () => []
  },
  actionWidth: {
    type: [String, Number],
    default: 'auto'
  },
  actionFixed: {
    type: String,
    default: 'right'
  },
  
  // 筛选相关
  filters: {
    type: Array,
    default: () => []
  },
  
  // 分页相关
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  total: {
    type: Number,
    default: 0
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  
  // 其他
  emptyText: {
    type: String,
    default: '暂无数据'
  }
})

const emit = defineEmits([
  'refresh', 'export', 'search', 'filter-change', 'selection-change',
  'sort-change', 'row-click', 'row-dblclick', 'action', 'size-change', 'current-change'
])

// 响应式数据
const tableRef = ref()
const searchKeyword = ref('')
const visibleColumns = ref([])
const filterValues = reactive({})

// 搜索防抖定时器
let searchTimer = null

// 计算属性
const tableData = computed(() => props.data)
const tableHeight = computed(() => props.height)

const allColumns = computed(() => props.columns)

const displayColumns = computed(() => {
  return allColumns.value.filter(column => 
    visibleColumns.value.includes(column.prop)
  )
})

// 初始化可见列
watch(() => props.columns, (newColumns) => {
  if (newColumns.length > 0 && visibleColumns.value.length === 0) {
    visibleColumns.value = newColumns.map(col => col.prop)
  }
}, { immediate: true })

// 方法定义
const handleRefresh = () => {
  emit('refresh')
}

const handleExport = () => {
  emit('export')
}

const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  searchTimer = setTimeout(() => {
    emit('search', searchKeyword.value)
  }, 500)
}

const handleFilterChange = () => {
  emit('filter-change', filterValues)
}

const resetFilters = () => {
  Object.keys(filterValues).forEach(key => {
    filterValues[key] = undefined
  })
  handleFilterChange()
}

const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleSortChange = (sort) => {
  emit('sort-change', sort)
}

const handleRowClick = (row, column, event) => {
  emit('row-click', row, column, event)
}

const handleRowDblClick = (row, column, event) => {
  emit('row-dblclick', row, column, event)
}

const handleAction = (actionName, row, index) => {
  emit('action', actionName, row, index)
}

const handleSizeChange = (size) => {
  emit('size-change', size)
}

const handleCurrentChange = (page) => {
  emit('current-change', page)
}

const getIndex = (index) => {
  return (props.currentPage - 1) * props.pageSize + index + 1
}

const getTagType = (value, tagMap) => {
  return tagMap && tagMap[value] ? tagMap[value].type : 'info'
}

const getTagText = (value, tagMap) => {
  return tagMap && tagMap[value] ? tagMap[value].text : value
}

// 暴露方法
defineExpose({
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row, selected) => tableRef.value?.toggleRowSelection(row, selected),
  toggleAllSelection: () => tableRef.value?.toggleAllSelection(),
  setCurrentRow: (row) => tableRef.value?.setCurrentRow(row),
  clearSort: () => tableRef.value?.clearSort(),
  clearFilter: (columnKey) => tableRef.value?.clearFilter(columnKey),
  doLayout: () => tableRef.value?.doLayout(),
  sort: (prop, order) => tableRef.value?.sort(prop, order)
})
</script>

<style scoped>
.data-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.toolbar-left h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-filters {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.table-container {
  overflow: hidden;
}

.table-pagination {
  display: flex;
  justify-content: center;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
}

.column-settings {
  max-height: 300px;
  overflow-y: auto;
}

.column-item {
  padding: 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .table-filters :deep(.el-form) {
    flex-direction: column;
  }
  
  .table-filters :deep(.el-form-item) {
    margin-bottom: 12px;
    margin-right: 0;
  }
}
</style>
