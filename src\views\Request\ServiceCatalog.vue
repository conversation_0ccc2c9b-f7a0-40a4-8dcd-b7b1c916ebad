<template>
  <div class="service-catalog">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>服务目录</h2>
          <p>浏览和申请可用的IT服务</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="$router.push('/request/portal')">
            <el-icon><Plus /></el-icon>
            快速申请
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-card>
        <div class="search-content">
          <div class="search-bar">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索服务名称、描述或标签..."
              size="large"
              clearable
              @input="handleSearch"
              @clear="handleSearchClear"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="filter-bar">
            <div class="filter-item">
              <span class="filter-label">分类：</span>
              <el-radio-group v-model="selectedCategory" @change="handleCategoryChange">
                <el-radio-button value="all">全部</el-radio-button>
                <el-radio-button
                  v-for="category in serviceCategories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </el-radio-button>
              </el-radio-group>
            </div>

            <div class="filter-item">
              <span class="filter-label">排序：</span>
              <el-select v-model="sortBy" @change="handleSortChange" style="width: 120px;">
                <el-option label="热门度" value="popularity" />
                <el-option label="名称" value="name" />
                <el-option label="处理时间" value="time" />
              </el-select>
            </div>

            <div class="filter-item">
              <el-checkbox v-model="showAutomationOnly" @change="handleFilterChange">
                仅显示自动化服务
              </el-checkbox>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 服务分类导航 -->
    <div class="category-nav" v-if="selectedCategory === 'all'">
      <el-row :gutter="16">
        <el-col
          v-for="category in serviceCategories"
          :key="category.id"
          :xs="12" :sm="8" :md="6" :lg="4"
        >
          <div
            class="category-card"
            @click="selectCategory(category.id)"
          >
            <div class="category-icon">
              <el-icon :size="32">
                <component :is="category.icon" />
              </el-icon>
            </div>
            <div class="category-info">
              <div class="category-name">{{ category.name }}</div>
              <div class="category-count">{{ getCategoryServiceCount(category.id) }} 个服务</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 服务列表 -->
    <div class="services-section">
      <div class="section-header">
        <h3>
          {{ selectedCategory === 'all' ? '全部服务' : getCategoryName(selectedCategory) }}
          <span class="service-count">({{ pagination.total }} 个服务)</span>
        </h3>
        <div class="view-toggle">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button value="grid">
              <el-icon><Grid /></el-icon>
            </el-radio-button>
            <el-radio-button value="list">
              <el-icon><Menu /></el-icon>
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="services-grid">
        <el-row :gutter="20" v-loading="loading.catalog">
          <el-col
            v-for="service in serviceCatalog"
            :key="service.id"
            :xs="24" :sm="12" :md="8" :lg="6"
            class="service-col"
          >
            <div class="service-card" @click="viewServiceDetail(service)">
              <div class="service-header">
                <div class="service-icon">
                  <el-icon :size="24">
                    <component :is="service.icon" />
                  </el-icon>
                </div>
                <div class="service-badges">
                  <el-tag v-if="service.automationSupported" type="success" size="small">
                    自动化
                  </el-tag>
                  <el-tag v-if="service.popularity > 80" type="warning" size="small">
                    热门
                  </el-tag>
                </div>
              </div>

              <div class="service-content">
                <h4 class="service-name">{{ service.name }}</h4>
                <p class="service-description">{{ service.description }}</p>

                <div class="service-details">
                  <div class="detail-item">
                    <el-icon><Clock /></el-icon>
                    <span>{{ service.estimatedTime }}</span>
                  </div>
                  <div class="detail-item">
                    <el-icon><Money /></el-icon>
                    <span>{{ service.cost }}</span>
                  </div>
                </div>

                <div class="service-approval" v-if="service.approvalRequired">
                  <el-icon><CircleCheck /></el-icon>
                  <span>需要审批：{{ service.approvalFlow.join(' → ') }}</span>
                </div>
              </div>

              <div class="service-footer">
                <div class="service-tags">
                  <el-tag
                    v-for="tag in service.tags.slice(0, 2)"
                    :key="tag"
                    size="small"
                    effect="plain"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
                <el-button type="primary" size="small" @click.stop="applyService(service)">
                  立即申请
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 列表视图 -->
      <div v-else class="services-list">
        <el-table
          :data="serviceCatalog"
          v-loading="loading.catalog"
          @row-click="viewServiceDetail"
          style="width: 100%"
        >
          <el-table-column label="服务名称" min-width="200">
            <template #default="{ row }">
              <div class="service-name-cell">
                <el-icon :size="20" class="service-icon-small">
                  <component :is="row.icon" />
                </el-icon>
                <div>
                  <div class="service-name">{{ row.name }}</div>
                  <div class="service-category">{{ getCategoryName(row.category) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="描述" prop="description" min-width="300" show-overflow-tooltip />

          <el-table-column label="处理时间" prop="estimatedTime" width="120" />

          <el-table-column label="费用" prop="cost" width="150" show-overflow-tooltip />

          <el-table-column label="特性" width="120">
            <template #default="{ row }">
              <div class="service-features">
                <el-tag v-if="row.automationSupported" type="success" size="small">
                  自动化
                </el-tag>
                <el-tag v-if="row.approvalRequired" type="warning" size="small">
                  需审批
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="热度" width="80">
            <template #default="{ row }">
              <div class="popularity-cell">
                {{ row.popularity }}%
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click.stop="applyService(row)"
              >
                申请
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[12, 24, 48, 96]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 服务详情对话框 -->
    <ServiceDetailDialog
      v-model="showDetailDialog"
      :service="selectedService"
      @apply="handleApplyFromDetail"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Grid, Menu, Clock, Money, CircleCheck,
  User, Monitor, Connection, Key, Service
} from '@element-plus/icons-vue'
import { useRequestStore } from '@/stores/request'
import ServiceDetailDialog from '@/components/Request/ServiceDetailDialog.vue'

const router = useRouter()
const route = useRoute()
const requestStore = useRequestStore()

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref('all')
const sortBy = ref('popularity')
const showAutomationOnly = ref(false)
const viewMode = ref('grid')
const showDetailDialog = ref(false)
const selectedService = ref(null)

// 搜索防抖定时器
let searchTimer = null

// 计算属性
const serviceCategories = computed(() => requestStore.serviceCategories)
const serviceCatalog = computed(() => requestStore.serviceCatalog)
const loading = computed(() => requestStore.loading)
const pagination = computed(() => requestStore.pagination.catalog)

// 方法定义
const getCategoryServiceCount = (categoryId) => {
  return serviceCatalog.value.filter(service => service.category === categoryId).length
}

const getCategoryName = (categoryId) => {
  const category = serviceCategories.value.find(cat => cat.id === categoryId)
  return category ? category.name : '未知分类'
}

const selectCategory = (categoryId) => {
  selectedCategory.value = categoryId
  handleCategoryChange(categoryId)
}

const handleSearch = () => {
  // 防抖处理
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(() => {
    loadServices()
  }, 500)
}

const handleSearchClear = () => {
  searchKeyword.value = ''
  loadServices()
}

const handleCategoryChange = (category) => {
  requestStore.setCatalogFilter({ category })
  loadServices()
}

const handleSortChange = () => {
  loadServices()
}

const handleFilterChange = () => {
  loadServices()
}

const handleSizeChange = (size) => {
  requestStore.pagination.catalog.pageSize = size
  requestStore.pagination.catalog.page = 1
  loadServices()
}

const handleCurrentChange = (page) => {
  requestStore.pagination.catalog.page = page
  loadServices()
}

const viewServiceDetail = (service) => {
  selectedService.value = service
  showDetailDialog.value = true
}

const applyService = async (service) => {
  try {
    // 检查是否需要登录
    // 这里可以添加登录检查逻辑

    // 跳转到申请页面
    router.push({
      path: '/request/portal',
      query: {
        tab: 'apply',
        serviceId: service.id
      }
    })
  } catch (error) {
    ElMessage.error('申请服务失败')
    console.error('申请服务失败:', error)
  }
}

const handleApplyFromDetail = (service) => {
  showDetailDialog.value = false
  applyService(service)
}

const loadServices = async () => {
  try {
    const params = {
      category: selectedCategory.value,
      search: searchKeyword.value,
      sortBy: sortBy.value,
      automationOnly: showAutomationOnly.value
    }

    await requestStore.loadServiceCatalog(params)
  } catch (error) {
    ElMessage.error('加载服务列表失败')
    console.error('加载服务失败:', error)
  }
}

// 监听路由参数变化
watch(() => route.query, (newQuery) => {
  if (newQuery.category) {
    selectedCategory.value = newQuery.category
  }
  if (newQuery.search) {
    searchKeyword.value = newQuery.search
  }
  if (newQuery.service) {
    // 如果指定了服务ID，直接显示详情
    const service = serviceCatalog.value.find(s => s.id === newQuery.service)
    if (service) {
      viewServiceDetail(service)
    }
  }
}, { immediate: true })

// 生命周期
onMounted(async () => {
  try {
    // 加载服务分类和服务列表
    await Promise.all([
      requestStore.loadServiceCategories(),
      loadServices()
    ])
  } catch (error) {
    ElMessage.error('页面初始化失败')
    console.error('页面初始化失败:', error)
  }
})
</script>

<style scoped>
.service-catalog {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

/* 搜索筛选区域 */
.search-section {
  margin-bottom: 24px;
}

.search-content {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

/* 分类导航样式 */
.category-nav {
  margin-bottom: 24px;
}

.category-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
}

.category-icon {
  margin-bottom: 12px;
  color: #409EFF;
  transition: color 0.3s ease;
}

.category-card:hover .category-icon {
  color: white;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.category-count {
  font-size: 12px;
  color: #909399;
  transition: color 0.3s ease;
}

.category-card:hover .category-count {
  color: rgba(255, 255, 255, 0.8);
}

/* 服务列表区域 */
.services-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.service-count {
  color: #909399;
  font-weight: normal;
  font-size: 14px;
}

/* 服务网格视图 */
.services-grid {
  margin-bottom: 24px;
}

.service-col {
  margin-bottom: 20px;
}

.service-card {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.service-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.service-badges {
  display: flex;
  gap: 4px;
  flex-direction: column;
}

.service-content {
  flex: 1;
  margin-bottom: 16px;
}

.service-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.service-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.service-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #909399;
}

.detail-item .el-icon {
  color: #409EFF;
}

.service-approval {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #E6A23C;
  background: #FDF6EC;
  padding: 6px 8px;
  border-radius: 4px;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.service-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 服务列表视图 */
.services-list {
  margin-bottom: 24px;
}

.service-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.service-icon-small {
  color: #409EFF;
}

.service-name-cell .service-name {
  font-weight: 600;
  color: #303133;
}

.service-category {
  font-size: 12px;
  color: #909399;
}

.service-features {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.popularity-cell {
  font-weight: 600;
  color: #67C23A;
}

/* 分页样式 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-catalog {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .search-content {
    padding: 16px;
  }

  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-item {
    justify-content: space-between;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .service-card {
    padding: 16px;
  }

  .category-card {
    height: 100px;
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .filter-bar {
    gap: 12px;
  }

  .service-details {
    gap: 6px;
  }

  .service-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .service-footer .el-button {
    width: 100%;
  }
}
</style>
