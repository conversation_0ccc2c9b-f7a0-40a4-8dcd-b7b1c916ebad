<template>
  <div class="service-catalog">
    <div class="page-header">
      <h2>服务目录</h2>
      <p>浏览可用的IT服务</p>
    </div>
    <el-card>
      <div class="placeholder">
        <p>服务目录功能开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
</script>

<style scoped>
.service-catalog {
  padding: 20px;
}
.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}
.page-header p {
  color: #616161;
  margin: 0 0 20px 0;
}
.placeholder {
  text-align: center;
  padding: 40px;
  color: #666;
}
</style>
