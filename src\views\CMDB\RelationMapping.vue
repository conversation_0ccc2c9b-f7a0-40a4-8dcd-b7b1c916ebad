<template>
  <div class="relation-mapping">
    <div class="page-header">
      <div class="header-content">
        <h2>关系映射管理</h2>
        <p>管理配置项之间的依赖关系，构建完整的IT基础设施关系图谱</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showAddRelationDialog = true">
          <el-icon><Plus /></el-icon>
          添加关系
        </el-button>
        <el-button @click="showBatchImportDialog = true">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="validateAllRelations">
          <el-icon><CircleCheck /></el-icon>
          关系验证
        </el-button>
        <el-button @click="showRecommendations">
          <el-icon><Magic /></el-icon>
          智能推荐
        </el-button>
      </div>
    </div>

    <!-- 关系统计 -->
    <div class="relation-stats">
      <el-row :gutter="20">
        <el-col :span="6" v-for="stat in relationStats" :key="stat.key">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24" :color="stat.color">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" v-if="stat.trend">
                  <el-icon :size="12" :color="stat.trend > 0 ? '#4CAF50' : '#F44336'">
                    <component :is="stat.trend > 0 ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                  <span :style="{ color: stat.trend > 0 ? '#4CAF50' : '#F44336' }">
                    {{ Math.abs(stat.trend) }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 关系类型管理 -->
    <div class="relation-types">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>关系类型</span>
                <el-button type="primary" size="small" @click="showAddTypeDialog = true">
                  <el-icon><Plus /></el-icon>
                  添加类型
                </el-button>
              </div>
            </template>
            <div class="relation-type-list">
              <div 
                class="relation-type-item" 
                v-for="type in relationTypes" 
                :key="type.id"
                :class="{ active: selectedType === type.id }"
                @click="selectRelationType(type.id)"
              >
                <div class="type-icon">
                  <el-icon :size="20" :color="type.color">
                    <component :is="type.icon" />
                  </el-icon>
                </div>
                <div class="type-info">
                  <div class="type-name">{{ type.name }}</div>
                  <div class="type-desc">{{ type.description }}</div>
                  <div class="type-stats">
                    <span>{{ type.count }} 个关系</span>
                    <span>{{ type.direction === 'bidirectional' ? '双向' : '单向' }}</span>
                  </div>
                </div>
                <div class="type-actions">
                  <el-button type="text" size="small" @click.stop="editRelationType(type)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button type="text" size="small" @click.stop="deleteRelationType(type)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>关系验证规则</span>
            </template>
            <div class="validation-rules">
              <div class="rule-item" v-for="rule in validationRules" :key="rule.id">
                <div class="rule-header">
                  <span class="rule-name">{{ rule.name }}</span>
                  <el-switch v-model="rule.enabled" @change="updateRule(rule)" />
                </div>
                <div class="rule-desc">{{ rule.description }}</div>
                <div class="rule-stats">
                  <span class="rule-violations">{{ rule.violations }} 个违规</span>
                  <span class="rule-last-check">最后检查: {{ rule.lastCheck }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 关系列表 -->
    <div class="relation-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>关系列表</span>
            <div class="header-controls">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索关系..."
                style="width: 200px; margin-right: 12px"
                prefix-icon="Search"
                @input="handleSearch"
              />
              <el-select
                v-model="selectedRelationType"
                placeholder="关系类型"
                style="width: 120px; margin-right: 12px"
                @change="handleTypeFilter"
              >
                <el-option label="全部类型" value="" />
                <el-option 
                  v-for="type in relationTypes" 
                  :key="type.id" 
                  :label="type.name" 
                  :value="type.id" 
                />
              </el-select>
              <el-select
                v-model="selectedStatus"
                placeholder="状态筛选"
                style="width: 120px"
                @change="handleStatusFilter"
              >
                <el-option label="全部状态" value="" />
                <el-option label="正常" value="normal" />
                <el-option label="异常" value="abnormal" />
                <el-option label="待验证" value="pending" />
              </el-select>
            </div>
          </div>
        </template>
        <el-table :data="filteredRelations" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="关系ID" width="120" />
          <el-table-column prop="sourceName" label="源配置项" min-width="150">
            <template #default="scope">
              <el-button type="text" @click="viewCI(scope.row.sourceId)">
                {{ scope.row.sourceName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="relationTypeName" label="关系类型" width="120">
            <template #default="scope">
              <el-tag :color="scope.row.relationTypeColor" size="small">
                {{ scope.row.relationTypeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="targetName" label="目标配置项" min-width="150">
            <template #default="scope">
              <el-button type="text" @click="viewCI(scope.row.targetId)">
                {{ scope.row.targetName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="confidence" label="置信度" width="100">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.confidence"
                :color="getConfidenceColor(scope.row.confidence)"
                :show-text="false"
                style="width: 60px"
              />
              <span style="margin-left: 8px; font-size: 12px">{{ scope.row.confidence }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column prop="creator" label="创建人" width="100" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="editRelation(scope.row)">
                编辑
              </el-button>
              <el-button type="primary" size="small" text @click="validateRelation(scope.row)">
                验证
              </el-button>
              <el-button type="primary" size="small" text @click="viewRelationDetails(scope.row)">
                详情
              </el-button>
              <el-button type="danger" size="small" text @click="deleteRelation(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalItems"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加关系对话框 -->
    <el-dialog v-model="showAddRelationDialog" title="添加关系" width="60%">
      <el-form :model="relationForm" :rules="relationRules" ref="relationFormRef" label-width="120px">
        <el-form-item label="源配置项" prop="sourceId">
          <el-select
            v-model="relationForm.sourceId"
            placeholder="请选择源配置项"
            style="width: 100%"
            filterable
            remote
            :remote-method="searchCIs"
            :loading="ciSearchLoading"
          >
            <el-option
              v-for="ci in sourceCIOptions"
              :key="ci.id"
              :label="ci.name"
              :value="ci.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关系类型" prop="relationTypeId">
          <el-select v-model="relationForm.relationTypeId" placeholder="请选择关系类型" style="width: 100%">
            <el-option
              v-for="type in relationTypes"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="目标配置项" prop="targetId">
          <el-select
            v-model="relationForm.targetId"
            placeholder="请选择目标配置项"
            style="width: 100%"
            filterable
            remote
            :remote-method="searchCIs"
            :loading="ciSearchLoading"
          >
            <el-option
              v-for="ci in targetCIOptions"
              :key="ci.id"
              :label="ci.name"
              :value="ci.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="relationForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入关系描述"
          />
        </el-form-item>
        <el-form-item label="置信度">
          <el-slider v-model="relationForm.confidence" :min="0" :max="100" show-input />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddRelationDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRelation">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 智能推荐对话框 -->
    <el-dialog v-model="showRecommendationDialog" title="智能关系推荐" width="70%">
      <div class="recommendation-content">
        <div class="recommendation-header">
          <span>基于配置项属性和网络拓扑分析，为您推荐以下可能的关系：</span>
          <el-button type="primary" size="small" @click="refreshRecommendations">
            <el-icon><Refresh /></el-icon>
            刷新推荐
          </el-button>
        </div>
        <el-table :data="recommendations" style="width: 100%">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="sourceName" label="源配置项" />
          <el-table-column prop="relationTypeName" label="推荐关系" />
          <el-table-column prop="targetName" label="目标配置项" />
          <el-table-column prop="confidence" label="置信度" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.confidence"
                :color="getConfidenceColor(scope.row.confidence)"
                :show-text="false"
                style="width: 60px"
              />
              <span style="margin-left: 8px; font-size: 12px">{{ scope.row.confidence }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="推荐理由" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="acceptRecommendation(scope.row)">
                采纳
              </el-button>
              <el-button type="danger" size="small" text @click="rejectRecommendation(scope.row)">
                拒绝
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRecommendationDialog = false">关闭</el-button>
          <el-button type="primary" @click="batchAcceptRecommendations">批量采纳</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showAddRelationDialog = ref(false)
const showBatchImportDialog = ref(false)
const showRecommendationDialog = ref(false)
const showAddTypeDialog = ref(false)
const ciSearchLoading = ref(false)

// 搜索和筛选
const searchKeyword = ref('')
const selectedRelationType = ref('')
const selectedStatus = ref('')
const selectedType = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)

// 表单数据
const relationFormRef = ref(null)
const relationForm = reactive({
  sourceId: '',
  targetId: '',
  relationTypeId: '',
  description: '',
  confidence: 95
})

const relationRules = {
  sourceId: [{ required: true, message: '请选择源配置项', trigger: 'change' }],
  targetId: [{ required: true, message: '请选择目标配置项', trigger: 'change' }],
  relationTypeId: [{ required: true, message: '请选择关系类型', trigger: 'change' }]
}

// CI选项
const sourceCIOptions = ref([])
const targetCIOptions = ref([])

// 关系统计
const relationStats = ref([
  {
    key: 'total',
    label: '总关系数',
    value: '1,245',
    icon: 'Share',
    color: '#1976D2',
    trend: 5.2
  },
  {
    key: 'dependencies',
    label: '依赖关系',
    value: '456',
    icon: 'Connection',
    color: '#4CAF50',
    trend: 8.1
  },
  {
    key: 'connections',
    label: '连接关系',
    value: '789',
    icon: 'Link',
    color: '#FF9800',
    trend: -2.3
  },
  {
    key: 'invalid',
    label: '异常关系',
    value: '23',
    icon: 'Warning',
    color: '#F44336',
    trend: -15.6
  }
])

// 关系类型
const relationTypes = ref([
  {
    id: 'depends_on',
    name: '依赖于',
    description: '表示源配置项依赖于目标配置项',
    icon: 'ArrowRight',
    color: '#F44336',
    direction: 'unidirectional',
    count: 456
  },
  {
    id: 'connects_to',
    name: '连接到',
    description: '表示源配置项与目标配置项之间存在网络连接',
    icon: 'Link',
    color: '#2196F3',
    direction: 'bidirectional',
    count: 789
  },
  {
    id: 'runs_on',
    name: '运行于',
    description: '表示源配置项运行在目标配置项上',
    icon: 'Monitor',
    color: '#4CAF50',
    direction: 'unidirectional',
    count: 234
  },
  {
    id: 'contains',
    name: '包含',
    description: '表示源配置项包含目标配置项',
    icon: 'Box',
    color: '#9C27B0',
    direction: 'unidirectional',
    count: 123
  },
  {
    id: 'monitors',
    name: '监控',
    description: '表示源配置项监控目标配置项',
    icon: 'View',
    color: '#FF9800',
    direction: 'unidirectional',
    count: 67
  }
])

// 验证规则
const validationRules = ref([
  {
    id: 'circular_dependency',
    name: '循环依赖检查',
    description: '检测配置项之间是否存在循环依赖关系',
    enabled: true,
    violations: 3,
    lastCheck: '2025-01-30 14:30:00'
  },
  {
    id: 'orphan_ci',
    name: '孤立CI检查',
    description: '检测没有任何关系的孤立配置项',
    enabled: true,
    violations: 12,
    lastCheck: '2025-01-30 14:25:00'
  },
  {
    id: 'invalid_relation',
    name: '无效关系检查',
    description: '检测指向不存在配置项的关系',
    enabled: true,
    violations: 5,
    lastCheck: '2025-01-30 14:20:00'
  },
  {
    id: 'duplicate_relation',
    name: '重复关系检查',
    description: '检测相同配置项之间的重复关系',
    enabled: false,
    violations: 8,
    lastCheck: '2025-01-30 13:45:00'
  }
])

// 关系数据
const relations = ref([
  {
    id: 'REL-001',
    sourceId: 'CI-001',
    sourceName: 'WEB-SRV-01',
    targetId: 'CI-002',
    targetName: 'DB-SRV-01',
    relationTypeId: 'depends_on',
    relationTypeName: '依赖于',
    relationTypeColor: '#F44336',
    status: 'normal',
    confidence: 95,
    createTime: '2025-01-30 10:00:00',
    creator: '张工',
    description: 'Web服务器依赖于数据库服务器'
  },
  {
    id: 'REL-002',
    sourceId: 'CI-003',
    sourceName: 'LB-01',
    targetId: 'CI-001',
    targetName: 'WEB-SRV-01',
    relationTypeId: 'connects_to',
    relationTypeName: '连接到',
    relationTypeColor: '#2196F3',
    status: 'normal',
    confidence: 98,
    createTime: '2025-01-30 09:30:00',
    creator: '李工',
    description: '负载均衡器连接到Web服务器'
  },
  {
    id: 'REL-003',
    sourceId: 'CI-004',
    sourceName: 'APP-SRV-01',
    targetId: 'CI-001',
    targetName: 'WEB-SRV-01',
    relationTypeId: 'runs_on',
    relationTypeName: '运行于',
    relationTypeColor: '#4CAF50',
    status: 'pending',
    confidence: 85,
    createTime: '2025-01-30 08:45:00',
    creator: '王工',
    description: '应用服务运行在Web服务器上'
  }
])

// 智能推荐
const recommendations = ref([
  {
    sourceId: 'CI-005',
    sourceName: 'CACHE-SRV-01',
    targetId: 'CI-002',
    targetName: 'DB-SRV-01',
    relationTypeId: 'depends_on',
    relationTypeName: '依赖于',
    confidence: 88,
    reason: '基于网络流量分析和配置相似性'
  },
  {
    sourceId: 'CI-006',
    sourceName: 'MON-SRV-01',
    targetId: 'CI-001',
    targetName: 'WEB-SRV-01',
    relationTypeId: 'monitors',
    relationTypeName: '监控',
    confidence: 92,
    reason: '基于监控配置和SNMP连接'
  }
])

// 计算属性
const filteredRelations = computed(() => {
  let filtered = relations.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(relation =>
      relation.sourceName.toLowerCase().includes(keyword) ||
      relation.targetName.toLowerCase().includes(keyword) ||
      relation.relationTypeName.toLowerCase().includes(keyword)
    )
  }

  // 按关系类型筛选
  if (selectedRelationType.value) {
    filtered = filtered.filter(relation => relation.relationTypeId === selectedRelationType.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    filtered = filtered.filter(relation => relation.status === selectedStatus.value)
  }

  totalItems.value = filtered.length

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 方法函数
const selectRelationType = (typeId) => {
  selectedType.value = typeId
  selectedRelationType.value = typeId
}

const validateAllRelations = () => {
  loading.value = true
  ElMessage.info('正在验证所有关系...')

  setTimeout(() => {
    loading.value = false
    ElMessage.success('关系验证完成，发现3个异常关系')

    // 更新验证规则统计
    validationRules.value.forEach(rule => {
      if (rule.enabled) {
        rule.violations = Math.floor(Math.random() * 10)
        rule.lastCheck = new Date().toLocaleString()
      }
    })
  }, 3000)
}

const showRecommendations = () => {
  showRecommendationDialog.value = true
  refreshRecommendations()
}

const refreshRecommendations = () => {
  ElMessage.info('正在分析配置项关系...')
  // 这里可以实现智能推荐逻辑
}

const searchCIs = (query) => {
  if (!query) return

  ciSearchLoading.value = true

  // 模拟CI搜索
  setTimeout(() => {
    const mockCIs = [
      { id: 'CI-001', name: 'WEB-SRV-01' },
      { id: 'CI-002', name: 'DB-SRV-01' },
      { id: 'CI-003', name: 'LB-01' },
      { id: 'CI-004', name: 'APP-SRV-01' },
      { id: 'CI-005', name: 'CACHE-SRV-01' }
    ].filter(ci => ci.name.toLowerCase().includes(query.toLowerCase()))

    sourceCIOptions.value = mockCIs
    targetCIOptions.value = mockCIs
    ciSearchLoading.value = false
  }, 500)
}

const saveRelation = () => {
  relationFormRef.value?.validate((valid) => {
    if (valid) {
      const newRelation = {
        id: `REL-${Date.now()}`,
        sourceId: relationForm.sourceId,
        sourceName: sourceCIOptions.value.find(ci => ci.id === relationForm.sourceId)?.name || '',
        targetId: relationForm.targetId,
        targetName: targetCIOptions.value.find(ci => ci.id === relationForm.targetId)?.name || '',
        relationTypeId: relationForm.relationTypeId,
        relationTypeName: relationTypes.value.find(type => type.id === relationForm.relationTypeId)?.name || '',
        relationTypeColor: relationTypes.value.find(type => type.id === relationForm.relationTypeId)?.color || '',
        status: 'normal',
        confidence: relationForm.confidence,
        createTime: new Date().toLocaleString(),
        creator: '当前用户',
        description: relationForm.description
      }

      relations.value.unshift(newRelation)
      showAddRelationDialog.value = false

      // 重置表单
      Object.keys(relationForm).forEach(key => {
        if (key === 'confidence') {
          relationForm[key] = 95
        } else {
          relationForm[key] = ''
        }
      })

      ElMessage.success('关系添加成功')
    }
  })
}

const editRelation = (relation) => {
  ElMessage.info(`编辑关系: ${relation.id}`)
  // 这里可以实现编辑关系的逻辑
}

const validateRelation = (relation) => {
  ElMessage.info(`验证关系: ${relation.id}`)

  setTimeout(() => {
    relation.status = 'normal'
    relation.confidence = Math.min(100, relation.confidence + 5)
    ElMessage.success('关系验证通过')
  }, 1000)
}

const viewRelationDetails = (relation) => {
  ElMessage.info(`查看关系详情: ${relation.id}`)
  // 这里可以实现查看关系详情的逻辑
}

const deleteRelation = (relation) => {
  ElMessageBox.confirm(
    `确定要删除关系 "${relation.sourceName} ${relation.relationTypeName} ${relation.targetName}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = relations.value.findIndex(r => r.id === relation.id)
    if (index > -1) {
      relations.value.splice(index, 1)
      ElMessage.success('关系已删除')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const viewCI = (ciId) => {
  // 跳转到CI详情页面
  window.open(`/cmdb/ci/${ciId}`, '_blank')
}

const editRelationType = (type) => {
  ElMessage.info(`编辑关系类型: ${type.name}`)
  // 这里可以实现编辑关系类型的逻辑
}

const deleteRelationType = (type) => {
  if (type.count > 0) {
    ElMessage.warning('该关系类型下还有关系，无法删除')
    return
  }

  ElMessageBox.confirm(
    `确定要删除关系类型 "${type.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = relationTypes.value.findIndex(t => t.id === type.id)
    if (index > -1) {
      relationTypes.value.splice(index, 1)
      ElMessage.success('关系类型已删除')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const updateRule = (rule) => {
  ElMessage.success(`验证规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`)
  // 这里可以实现更新验证规则的逻辑
}

const acceptRecommendation = (recommendation) => {
  const newRelation = {
    id: `REL-${Date.now()}`,
    sourceId: recommendation.sourceId,
    sourceName: recommendation.sourceName,
    targetId: recommendation.targetId,
    targetName: recommendation.targetName,
    relationTypeId: recommendation.relationTypeId,
    relationTypeName: recommendation.relationTypeName,
    relationTypeColor: relationTypes.value.find(type => type.id === recommendation.relationTypeId)?.color || '',
    status: 'normal',
    confidence: recommendation.confidence,
    createTime: new Date().toLocaleString(),
    creator: '智能推荐',
    description: `智能推荐: ${recommendation.reason}`
  }

  relations.value.unshift(newRelation)

  // 从推荐列表中移除
  const index = recommendations.value.findIndex(r =>
    r.sourceId === recommendation.sourceId &&
    r.targetId === recommendation.targetId &&
    r.relationTypeId === recommendation.relationTypeId
  )
  if (index > -1) {
    recommendations.value.splice(index, 1)
  }

  ElMessage.success('推荐关系已采纳')
}

const rejectRecommendation = (recommendation) => {
  const index = recommendations.value.findIndex(r =>
    r.sourceId === recommendation.sourceId &&
    r.targetId === recommendation.targetId &&
    r.relationTypeId === recommendation.relationTypeId
  )
  if (index > -1) {
    recommendations.value.splice(index, 1)
    ElMessage.success('推荐关系已拒绝')
  }
}

const batchAcceptRecommendations = () => {
  ElMessage.info('批量采纳推荐关系功能')
  // 这里可以实现批量采纳的逻辑
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleTypeFilter = () => {
  currentPage.value = 1
}

const handleStatusFilter = () => {
  currentPage.value = 1
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 辅助函数
const getStatusType = (status) => {
  const typeMap = {
    'normal': 'success',
    'abnormal': 'danger',
    'pending': 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'normal': '正常',
    'abnormal': '异常',
    'pending': '待验证'
  }
  return textMap[status] || status
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 90) return '#4CAF50'
  if (confidence >= 70) return '#FF9800'
  return '#F44336'
}

// 组件挂载时初始化
onMounted(() => {
  totalItems.value = relations.value.length

  // 初始化CI选项
  searchCIs('')
})
</script>

<style scoped>
.relation-mapping {
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-content h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-content p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 关系统计样式 */
.relation-stats {
  margin-bottom: 20px;
}

.stat-card {
  height: 90px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 12px;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
}

/* 关系类型样式 */
.relation-types {
  margin-bottom: 20px;
}

.relation-type-list {
  max-height: 400px;
  overflow-y: auto;
}

.relation-type-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.relation-type-item:hover {
  background: #f8f9fa;
  border-color: #1976D2;
}

.relation-type-item.active {
  background: #e3f2fd;
  border-color: #1976D2;
}

.type-icon {
  margin-right: 12px;
}

.type-info {
  flex: 1;
}

.type-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.type-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.type-stats {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #999;
}

.type-actions {
  display: flex;
  gap: 4px;
}

/* 验证规则样式 */
.validation-rules {
  max-height: 400px;
  overflow-y: auto;
}

.rule-item {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #fafafa;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rule-name {
  font-weight: 500;
  color: #333;
}

.rule-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.rule-stats {
  display: flex;
  gap: 16px;
  font-size: 11px;
  color: #999;
}

.rule-violations {
  color: #F44336;
  font-weight: 500;
}

/* 关系列表样式 */
.relation-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 推荐内容样式 */
.recommendation-content {
  padding: 16px 0;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-button--text {
  padding: 4px 8px;
}

.el-progress {
  margin: 0;
}

.el-progress__text {
  font-size: 12px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .relation-type-list,
  .validation-rules {
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .relation-mapping {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stat-card {
    margin-bottom: 12px;
    height: 80px;
  }

  .stat-value {
    font-size: 16px;
  }

  .header-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .header-controls .el-input,
  .header-controls .el-select {
    width: 100% !important;
  }

  .relation-type-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .type-info {
    text-align: center;
  }

  .type-actions {
    justify-content: center;
  }

  .rule-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .rule-stats {
    flex-direction: column;
    gap: 4px;
  }

  .recommendation-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  /* 表格在移动端的优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* 对话框在移动端的优化 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .relation-type-item {
    padding: 8px;
  }

  .rule-item {
    padding: 8px;
  }

  .type-stats {
    justify-content: center;
  }
}

/* 动画效果 */
.stat-card,
.relation-type-item,
.rule-item {
  transition: all 0.3s ease;
}

.relation-type-item:hover,
.rule-item:hover {
  transform: translateY(-1px);
}

/* 加载状态 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa;
}

/* 进度条样式 */
.el-progress--line {
  margin: 0;
}

.el-progress-bar__outer {
  border-radius: 10px;
}

.el-progress-bar__inner {
  border-radius: 10px;
}

/* 标签样式 */
.el-tag {
  font-weight: 500;
}

/* 滑块样式 */
.el-slider {
  margin: 12px 0;
}

/* 开关样式 */
.el-switch {
  margin-left: auto;
}

/* 自定义滚动条 */
.relation-type-list::-webkit-scrollbar,
.validation-rules::-webkit-scrollbar {
  width: 6px;
}

.relation-type-list::-webkit-scrollbar-track,
.validation-rules::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.relation-type-list::-webkit-scrollbar-thumb,
.validation-rules::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.relation-type-list::-webkit-scrollbar-thumb:hover,
.validation-rules::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
