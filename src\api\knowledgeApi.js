import request from '@/utils/request'

// 知识库管理 API

/**
 * 获取知识库统计信息
 * @returns {Promise}
 */
export function getKnowledgeStats() {
  return request({
    url: '/api/knowledge/stats',
    method: 'get'
  })
}

/**
 * 获取知识文章列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getKnowledgeList(params) {
  return request({
    url: '/api/knowledge/articles',
    method: 'get',
    params
  })
}

/**
 * 获取知识文章详情
 * @param {string} id - 文章ID
 * @returns {Promise}
 */
export function getKnowledgeDetail(id) {
  return request({
    url: `/api/knowledge/articles/${id}`,
    method: 'get'
  })
}

/**
 * 创建知识文章
 * @param {Object} data - 文章数据
 * @returns {Promise}
 */
export function createKnowledge(data) {
  return request({
    url: '/api/knowledge/articles',
    method: 'post',
    data
  })
}

/**
 * 更新知识文章
 * @param {string} id - 文章ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateKnowledge(id, data) {
  return request({
    url: `/api/knowledge/articles/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除知识文章
 * @param {string} id - 文章ID
 * @returns {Promise}
 */
export function deleteKnowledge(id) {
  return request({
    url: `/api/knowledge/articles/${id}`,
    method: 'delete'
  })
}

/**
 * 批量操作知识文章
 * @param {Object} data - 批量操作数据
 * @returns {Promise}
 */
export function batchOperateKnowledge(data) {
  return request({
    url: '/api/knowledge/articles/batch',
    method: 'post',
    data
  })
}

// 知识分类管理 API

/**
 * 获取知识分类树
 * @returns {Promise}
 */
export function getKnowledgeCategories() {
  return request({
    url: '/api/knowledge/categories',
    method: 'get'
  })
}

/**
 * 创建知识分类
 * @param {Object} data - 分类数据
 * @returns {Promise}
 */
export function createKnowledgeCategory(data) {
  return request({
    url: '/api/knowledge/categories',
    method: 'post',
    data
  })
}

/**
 * 更新知识分类
 * @param {string} id - 分类ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateKnowledgeCategory(id, data) {
  return request({
    url: `/api/knowledge/categories/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除知识分类
 * @param {string} id - 分类ID
 * @returns {Promise}
 */
export function deleteKnowledgeCategory(id) {
  return request({
    url: `/api/knowledge/categories/${id}`,
    method: 'delete'
  })
}

// 知识搜索 API

/**
 * 搜索知识文章
 * @param {Object} params - 搜索参数
 * @returns {Promise}
 */
export function searchKnowledge(params) {
  return request({
    url: '/api/knowledge/search',
    method: 'get',
    params
  })
}

/**
 * 获取搜索建议
 * @param {string} keyword - 关键词
 * @returns {Promise}
 */
export function getSearchSuggestions(keyword) {
  return request({
    url: '/api/knowledge/search/suggestions',
    method: 'get',
    params: { keyword }
  })
}

/**
 * 获取热门搜索词
 * @returns {Promise}
 */
export function getHotSearchKeywords() {
  return request({
    url: '/api/knowledge/search/hot-keywords',
    method: 'get'
  })
}

// 知识版本管理 API

/**
 * 获取文章版本列表
 * @param {string} articleId - 文章ID
 * @returns {Promise}
 */
export function getKnowledgeVersions(articleId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/versions`,
    method: 'get'
  })
}

/**
 * 获取版本详情
 * @param {string} articleId - 文章ID
 * @param {string} versionId - 版本ID
 * @returns {Promise}
 */
export function getKnowledgeVersion(articleId, versionId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/versions/${versionId}`,
    method: 'get'
  })
}

/**
 * 恢复到指定版本
 * @param {string} articleId - 文章ID
 * @param {string} versionId - 版本ID
 * @returns {Promise}
 */
export function restoreKnowledgeVersion(articleId, versionId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/versions/${versionId}/restore`,
    method: 'post'
  })
}

/**
 * 版本对比
 * @param {string} articleId - 文章ID
 * @param {Object} data - 对比数据
 * @returns {Promise}
 */
export function compareKnowledgeVersions(articleId, data) {
  return request({
    url: `/api/knowledge/articles/${articleId}/versions/compare`,
    method: 'post',
    data
  })
}

// 知识评价与统计 API

/**
 * 评价知识文章
 * @param {string} articleId - 文章ID
 * @param {Object} data - 评价数据
 * @returns {Promise}
 */
export function rateKnowledge(articleId, data) {
  return request({
    url: `/api/knowledge/articles/${articleId}/rating`,
    method: 'post',
    data
  })
}

/**
 * 获取文章评价
 * @param {string} articleId - 文章ID
 * @returns {Promise}
 */
export function getKnowledgeRating(articleId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/rating`,
    method: 'get'
  })
}

/**
 * 增加浏览次数
 * @param {string} articleId - 文章ID
 * @returns {Promise}
 */
export function incrementKnowledgeViews(articleId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/views`,
    method: 'post'
  })
}

/**
 * 获取文章统计信息
 * @param {string} articleId - 文章ID
 * @returns {Promise}
 */
export function getKnowledgeStatistics(articleId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/statistics`,
    method: 'get'
  })
}

// 知识审核流程 API

/**
 * 提交审核
 * @param {string} articleId - 文章ID
 * @param {Object} data - 提交数据
 * @returns {Promise}
 */
export function submitKnowledgeForReview(articleId, data) {
  return request({
    url: `/api/knowledge/articles/${articleId}/submit`,
    method: 'post',
    data
  })
}

/**
 * 审核知识文章
 * @param {string} articleId - 文章ID
 * @param {Object} data - 审核数据
 * @returns {Promise}
 */
export function reviewKnowledge(articleId, data) {
  return request({
    url: `/api/knowledge/articles/${articleId}/review`,
    method: 'post',
    data
  })
}

/**
 * 发布知识文章
 * @param {string} articleId - 文章ID
 * @returns {Promise}
 */
export function publishKnowledge(articleId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/publish`,
    method: 'post'
  })
}

/**
 * 撤回知识文章
 * @param {string} articleId - 文章ID
 * @returns {Promise}
 */
export function withdrawKnowledge(articleId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/withdraw`,
    method: 'post'
  })
}

/**
 * 归档知识文章
 * @param {string} articleId - 文章ID
 * @returns {Promise}
 */
export function archiveKnowledge(articleId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/archive`,
    method: 'post'
  })
}

// 知识模板管理 API

/**
 * 获取知识模板列表
 * @returns {Promise}
 */
export function getKnowledgeTemplates() {
  return request({
    url: '/api/knowledge/templates',
    method: 'get'
  })
}

/**
 * 获取模板详情
 * @param {string} templateId - 模板ID
 * @returns {Promise}
 */
export function getKnowledgeTemplate(templateId) {
  return request({
    url: `/api/knowledge/templates/${templateId}`,
    method: 'get'
  })
}

/**
 * 创建知识模板
 * @param {Object} data - 模板数据
 * @returns {Promise}
 */
export function createKnowledgeTemplate(data) {
  return request({
    url: '/api/knowledge/templates',
    method: 'post',
    data
  })
}

// 附件管理 API

/**
 * 上传附件
 * @param {FormData} data - 文件数据
 * @returns {Promise}
 */
export function uploadKnowledgeAttachment(data) {
  return request({
    url: '/api/knowledge/attachments/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 删除附件
 * @param {string} attachmentId - 附件ID
 * @returns {Promise}
 */
export function deleteKnowledgeAttachment(attachmentId) {
  return request({
    url: `/api/knowledge/attachments/${attachmentId}`,
    method: 'delete'
  })
}

// 知识推荐 API

/**
 * 获取相关知识推荐
 * @param {string} articleId - 文章ID
 * @returns {Promise}
 */
export function getRelatedKnowledge(articleId) {
  return request({
    url: `/api/knowledge/articles/${articleId}/related`,
    method: 'get'
  })
}

/**
 * 获取热门知识
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getPopularKnowledge(params) {
  return request({
    url: '/api/knowledge/popular',
    method: 'get',
    params
  })
}

/**
 * 获取最新知识
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getLatestKnowledge(params) {
  return request({
    url: '/api/knowledge/latest',
    method: 'get',
    params
  })
}

// 知识标签管理 API

/**
 * 获取知识标签列表
 * @returns {Promise}
 */
export function getKnowledgeTags() {
  return request({
    url: '/api/knowledge/tags',
    method: 'get'
  })
}

/**
 * 创建知识标签
 * @param {Object} data - 标签数据
 * @returns {Promise}
 */
export function createKnowledgeTag(data) {
  return request({
    url: '/api/knowledge/tags',
    method: 'post',
    data
  })
}

/**
 * 根据标签获取知识
 * @param {string} tagId - 标签ID
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getKnowledgeByTag(tagId, params) {
  return request({
    url: `/api/knowledge/tags/${tagId}/articles`,
    method: 'get',
    params
  })
}

// 知识导入导出 API

/**
 * 导出知识数据
 * @param {Object} params - 导出参数
 * @returns {Promise}
 */
export function exportKnowledgeData(params) {
  return request({
    url: '/api/knowledge/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 导入知识数据
 * @param {FormData} data - 导入数据
 * @returns {Promise}
 */
export function importKnowledgeData(data) {
  return request({
    url: '/api/knowledge/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 与其他模块集成的 API

/**
 * 从工单创建知识
 * @param {string} ticketId - 工单ID
 * @param {Object} data - 知识数据
 * @returns {Promise}
 */
export function createKnowledgeFromTicket(ticketId, data) {
  return request({
    url: `/api/knowledge/from-ticket/${ticketId}`,
    method: 'post',
    data
  })
}

/**
 * 获取工单相关知识推荐
 * @param {string} ticketId - 工单ID
 * @returns {Promise}
 */
export function getTicketRelatedKnowledge(ticketId) {
  return request({
    url: `/api/knowledge/ticket-related/${ticketId}`,
    method: 'get'
  })
}

/**
 * 获取CI相关知识
 * @param {string} ciId - 配置项ID
 * @returns {Promise}
 */
export function getCIRelatedKnowledge(ciId) {
  return request({
    url: `/api/knowledge/ci-related/${ciId}`,
    method: 'get'
  })
}
