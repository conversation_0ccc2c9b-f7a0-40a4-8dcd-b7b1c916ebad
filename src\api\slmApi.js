// 服务级别管理 API
import { formatDateTime } from './index.js'

// 工具函数
const createApiResponse = (data, msg = '操作成功') => ({
  success: true,
  data,
  msg,
  timestamp: new Date().toISOString()
})

const createApiError = (msg, code = 500) => ({
  success: false,
  msg,
  code,
  timestamp: new Date().toISOString()
})

// 服务协议类型
export const agreementTypes = [
  { value: 'SLA', label: 'Service Level Agreement', description: 'IT部门与业务部门之间的正式服务承诺' },
  { value: 'OLA', label: 'Operational Level Agreement', description: 'IT内部团队之间的支持承诺' },
  { value: 'UC', label: 'Underpinning Contract', description: '与第三方供应商的服务合同' }
]

// KPI指标类型
export const kpiTypes = [
  {
    key: 'availability',
    name: '服务可用性',
    unit: '%',
    calculation: '(1 - 中断时间 / 总时间) × 100%',
    defaultTarget: 99.9
  },
  {
    key: 'response_time',
    name: '事件响应时间',
    unit: '分钟',
    calculation: '平均首次响应时长',
    defaultTarget: 15
  },
  {
    key: 'resolution_time',
    name: '事件解决时间',
    unit: '小时',
    calculation: '平均MTTR',
    defaultTarget: 8
  },
  {
    key: 'sla_compliance',
    name: 'SLA达成率',
    unit: '%',
    calculation: '达标事件数 / 总事件数',
    defaultTarget: 95
  },
  {
    key: 'change_success_rate',
    name: '变更成功率',
    unit: '%',
    calculation: '成功变更数 / 总变更数',
    defaultTarget: 98
  },
  {
    key: 'customer_satisfaction',
    name: '用户满意度',
    unit: '分',
    calculation: '平均评分（1-5）',
    defaultTarget: 4.5
  }
]

// 服务健康评分等级
export const healthScoreLevels = [
  { min: 90, max: 100, level: '健康', color: '#67c23a', icon: 'SuccessFilled' },
  { min: 70, max: 89, level: '良好', color: '#e6a23c', icon: 'Warning' },
  { min: 50, max: 69, level: '风险', color: '#f56c6c', icon: 'CircleCloseFilled' },
  { min: 0, max: 49, level: '危险', color: '#f56c6c', icon: 'CircleCloseFilled' }
]

// Mock数据 - 服务列表
const mockServices = [
  {
    id: 'SRV-001',
    name: 'ERP系统',
    description: '企业资源规划系统',
    category: '业务系统',
    owner: '张三',
    department: 'IT部门',
    businessOwner: '李四',
    businessDepartment: '财务部',
    status: 'active',
    healthScore: 92,
    createdAt: formatDateTime(new Date(Date.now() - 180 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date())
  },
  {
    id: 'SRV-002',
    name: 'CRM系统',
    description: '客户关系管理系统',
    category: '业务系统',
    owner: '王五',
    department: 'IT部门',
    businessOwner: '赵六',
    businessDepartment: '销售部',
    status: 'active',
    healthScore: 87,
    createdAt: formatDateTime(new Date(Date.now() - 150 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date())
  },
  {
    id: 'SRV-003',
    name: 'OA办公系统',
    description: '办公自动化系统',
    category: '办公系统',
    owner: '孙七',
    department: 'IT部门',
    businessOwner: '周八',
    businessDepartment: '行政部',
    status: 'active',
    healthScore: 78,
    createdAt: formatDateTime(new Date(Date.now() - 120 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date())
  },
  {
    id: 'SRV-004',
    name: '邮件系统',
    description: '企业邮件服务',
    category: '基础设施',
    owner: '吴九',
    department: 'IT部门',
    businessOwner: '郑十',
    businessDepartment: '全公司',
    status: 'active',
    healthScore: 95,
    createdAt: formatDateTime(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date())
  },
  {
    id: 'SRV-005',
    name: '数据库服务',
    description: 'MySQL数据库集群',
    category: '基础设施',
    owner: '陈十一',
    department: 'IT部门',
    businessOwner: '刘十二',
    businessDepartment: '技术部',
    status: 'maintenance',
    healthScore: 65,
    createdAt: formatDateTime(new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date())
  }
]

// Mock数据 - SLA协议
const mockAgreements = [
  {
    id: 'SLA-001',
    name: 'ERP系统服务级别协议',
    type: 'SLA',
    serviceId: 'SRV-001',
    serviceName: 'ERP系统',
    status: 'active',
    version: '1.2',
    effectiveDate: formatDateTime(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)),
    expiryDate: formatDateTime(new Date(Date.now() + 275 * 24 * 60 * 60 * 1000)),
    owner: '张三',
    businessOwner: '李四',
    kpis: [
      {
        type: 'availability',
        name: '服务可用性',
        target: 99.9,
        unit: '%',
        period: 'monthly',
        current: 99.95,
        status: 'met'
      },
      {
        type: 'response_time',
        name: '事件响应时间',
        target: 15,
        unit: '分钟',
        period: 'monthly',
        current: 12,
        status: 'met'
      },
      {
        type: 'resolution_time',
        name: '事件解决时间',
        target: 8,
        unit: '小时',
        period: 'monthly',
        current: 6.5,
        status: 'met'
      }
    ],
    createdAt: formatDateTime(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date())
  },
  {
    id: 'OLA-001',
    name: '网络运维支持协议',
    type: 'OLA',
    serviceId: 'SRV-004',
    serviceName: '邮件系统',
    status: 'active',
    version: '1.0',
    effectiveDate: formatDateTime(new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)),
    expiryDate: formatDateTime(new Date(Date.now() + 305 * 24 * 60 * 60 * 1000)),
    owner: '吴九',
    businessOwner: '网络组',
    kpis: [
      {
        type: 'response_time',
        name: '响应时间',
        target: 30,
        unit: '分钟',
        period: 'monthly',
        current: 25,
        status: 'met'
      }
    ],
    createdAt: formatDateTime(new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date())
  },
  {
    id: 'UC-001',
    name: '云服务供应商合同',
    type: 'UC',
    serviceId: 'SRV-005',
    serviceName: '数据库服务',
    status: 'active',
    version: '2.1',
    effectiveDate: formatDateTime(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
    expiryDate: formatDateTime(new Date(Date.now() + 335 * 24 * 60 * 60 * 1000)),
    owner: '陈十一',
    businessOwner: '阿里云',
    kpis: [
      {
        type: 'availability',
        name: '服务可用性',
        target: 99.95,
        unit: '%',
        period: 'monthly',
        current: 99.92,
        status: 'at_risk'
      }
    ],
    createdAt: formatDateTime(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDateTime(new Date())
  }
]

// API函数

/**
 * 获取SLM概览统计数据
 */
export function getSLMOverview() {
  const totalServices = mockServices.length
  const activeAgreements = mockAgreements.filter(a => a.status === 'active').length
  const avgHealthScore = Math.round(mockServices.reduce((sum, s) => sum + s.healthScore, 0) / totalServices)
  
  // 计算SLA达成率
  const metKPIs = mockAgreements.reduce((sum, agreement) => {
    return sum + agreement.kpis.filter(kpi => kpi.status === 'met').length
  }, 0)
  const totalKPIs = mockAgreements.reduce((sum, agreement) => sum + agreement.kpis.length, 0)
  const slaComplianceRate = totalKPIs > 0 ? Math.round((metKPIs / totalKPIs) * 100) : 0
  
  // 健康评分分布
  const healthDistribution = healthScoreLevels.map(level => ({
    level: level.level,
    color: level.color,
    count: mockServices.filter(s => s.healthScore >= level.min && s.healthScore <= level.max).length
  }))
  
  // 近期告警
  const recentAlerts = [
    {
      id: 'ALT-001',
      service: 'ERP系统',
      type: 'warning',
      message: '响应时间接近阈值',
      time: formatDateTime(new Date(Date.now() - 2 * 60 * 60 * 1000))
    },
    {
      id: 'ALT-002',
      service: '数据库服务',
      type: 'critical',
      message: '可用性低于目标值',
      time: formatDateTime(new Date(Date.now() - 4 * 60 * 60 * 1000))
    }
  ]
  
  return createApiResponse({
    totalServices,
    activeAgreements,
    avgHealthScore,
    slaComplianceRate,
    healthDistribution,
    recentAlerts,
    trendData: {
      availability: [99.8, 99.9, 99.95, 99.92, 99.88, 99.94],
      responseTime: [14, 12, 15, 13, 16, 12],
      satisfaction: [4.2, 4.3, 4.5, 4.4, 4.1, 4.6]
    }
  })
}

/**
 * 获取服务列表
 */
export function getServiceList(params = {}) {
  let services = [...mockServices]
  
  // 筛选
  if (params.status) {
    services = services.filter(s => s.status === params.status)
  }
  if (params.category) {
    services = services.filter(s => s.category === params.category)
  }
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    services = services.filter(s => 
      s.name.toLowerCase().includes(keyword) || 
      s.description.toLowerCase().includes(keyword)
    )
  }
  
  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 10
  const total = services.length
  const start = (page - 1) * pageSize
  const list = services.slice(start, start + pageSize)
  
  return createApiResponse({
    list,
    total,
    page,
    pageSize
  })
}

/**
 * 获取服务详情
 */
export function getServiceDetail(serviceId) {
  const service = mockServices.find(s => s.id === serviceId)
  if (!service) {
    return createApiError('服务不存在', 404)
  }
  
  // 获取关联的协议
  const agreements = mockAgreements.filter(a => a.serviceId === serviceId)
  
  return createApiResponse({
    ...service,
    agreements
  })
}

/**
 * 获取协议列表
 */
export function getAgreementList(params = {}) {
  let agreements = [...mockAgreements]
  
  // 筛选
  if (params.type) {
    agreements = agreements.filter(a => a.type === params.type)
  }
  if (params.status) {
    agreements = agreements.filter(a => a.status === params.status)
  }
  if (params.serviceId) {
    agreements = agreements.filter(a => a.serviceId === params.serviceId)
  }
  
  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 10
  const total = agreements.length
  const start = (page - 1) * pageSize
  const list = agreements.slice(start, start + pageSize)
  
  return createApiResponse({
    list,
    total,
    page,
    pageSize
  })
}

/**
 * 获取协议详情
 */
export function getAgreementDetail(agreementId) {
  const agreement = mockAgreements.find(a => a.id === agreementId)
  if (!agreement) {
    return createApiError('协议不存在', 404)
  }
  
  return createApiResponse(agreement)
}

/**
 * 创建协议
 */
export function createAgreement(data) {
  const newAgreement = {
    id: `${data.type}-${String(Date.now()).slice(-3)}`,
    ...data,
    status: 'draft',
    version: '1.0',
    createdAt: formatDateTime(new Date()),
    updatedAt: formatDateTime(new Date())
  }
  
  mockAgreements.push(newAgreement)
  
  return createApiResponse(newAgreement, '协议创建成功')
}

/**
 * 更新协议
 */
export function updateAgreement(agreementId, data) {
  const index = mockAgreements.findIndex(a => a.id === agreementId)
  if (index === -1) {
    return createApiError('协议不存在', 404)
  }
  
  mockAgreements[index] = {
    ...mockAgreements[index],
    ...data,
    updatedAt: formatDateTime(new Date())
  }
  
  return createApiResponse(mockAgreements[index], '协议更新成功')
}

/**
 * 删除协议
 */
export function deleteAgreement(agreementId) {
  const index = mockAgreements.findIndex(a => a.id === agreementId)
  if (index === -1) {
    return createApiError('协议不存在', 404)
  }

  mockAgreements.splice(index, 1)

  return createApiResponse(null, '协议删除成功')
}

/**
 * 激活协议
 */
export function activateAgreement(agreementId) {
  const agreement = mockAgreements.find(a => a.id === agreementId)
  if (!agreement) {
    return createApiError('协议不存在', 404)
  }

  agreement.status = 'active'
  agreement.effectiveDate = formatDateTime(new Date())
  agreement.updatedAt = formatDateTime(new Date())

  return createApiResponse(agreement, '协议激活成功')
}

/**
 * 获取SLA监控数据
 */
export function getSLAMonitoring(params = {}) {
  const period = params.period || '7d' // 7d, 30d, 90d

  // 生成监控数据
  const monitoringData = mockAgreements.map(agreement => {
    const kpiData = agreement.kpis.map(kpi => {
      // 生成历史数据
      const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
      const history = []

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
        let value = kpi.current

        // 添加一些随机波动
        if (kpi.type === 'availability') {
          value = Math.max(95, Math.min(100, value + (Math.random() - 0.5) * 2))
        } else if (kpi.type === 'response_time') {
          value = Math.max(5, value + (Math.random() - 0.5) * 10)
        } else if (kpi.type === 'resolution_time') {
          value = Math.max(1, value + (Math.random() - 0.5) * 4)
        }

        history.push({
          date: formatDateTime(date).split(' ')[0],
          value: Math.round(value * 100) / 100,
          status: value >= kpi.target ? 'met' : 'missed'
        })
      }

      return {
        ...kpi,
        history,
        trend: history.length > 1 ?
          (history[history.length - 1].value > history[0].value ? 'up' : 'down') : 'stable'
      }
    })

    return {
      agreementId: agreement.id,
      agreementName: agreement.name,
      serviceName: agreement.serviceName,
      type: agreement.type,
      kpis: kpiData
    }
  })

  return createApiResponse(monitoringData)
}

/**
 * 获取SLA报告数据
 */
export function getSLAReports(params = {}) {
  const period = params.period || 'monthly' // monthly, quarterly, yearly
  const serviceId = params.serviceId

  let agreements = [...mockAgreements]
  if (serviceId) {
    agreements = agreements.filter(a => a.serviceId === serviceId)
  }

  const reports = agreements.map(agreement => {
    const kpiReports = agreement.kpis.map(kpi => {
      // 生成报告数据
      const targetValue = kpi.target
      const actualValue = kpi.current
      const compliance = actualValue >= targetValue
      const deviation = actualValue - targetValue

      // 生成历史趋势数据
      const months = period === 'monthly' ? 1 : period === 'quarterly' ? 3 : 12
      const trendData = []

      for (let i = months - 1; i >= 0; i--) {
        const date = new Date()
        date.setMonth(date.getMonth() - i)

        let value = actualValue
        if (kpi.type === 'availability') {
          value = Math.max(95, Math.min(100, value + (Math.random() - 0.5) * 3))
        } else if (kpi.type === 'response_time') {
          value = Math.max(5, value + (Math.random() - 0.5) * 8)
        }

        trendData.push({
          month: date.toISOString().slice(0, 7),
          value: Math.round(value * 100) / 100,
          target: targetValue,
          compliance: value >= targetValue
        })
      }

      return {
        ...kpi,
        targetValue,
        actualValue,
        compliance,
        deviation: Math.round(deviation * 100) / 100,
        trendData
      }
    })

    const overallCompliance = kpiReports.filter(k => k.compliance).length / kpiReports.length

    return {
      agreementId: agreement.id,
      agreementName: agreement.name,
      serviceName: agreement.serviceName,
      type: agreement.type,
      period,
      overallCompliance: Math.round(overallCompliance * 100),
      kpiReports,
      generatedAt: formatDateTime(new Date())
    }
  })

  return createApiResponse(reports)
}

/**
 * 计算服务健康评分
 */
export function calculateHealthScore(serviceId) {
  const service = mockServices.find(s => s.id === serviceId)
  if (!service) {
    return createApiError('服务不存在', 404)
  }

  const agreements = mockAgreements.filter(a => a.serviceId === serviceId)

  if (agreements.length === 0) {
    return createApiResponse({
      serviceId,
      serviceName: service.name,
      healthScore: 0,
      level: '未配置',
      factors: [],
      recommendations: ['请为该服务配置SLA协议']
    })
  }

  // 计算各维度得分
  let availabilityScore = 0
  let responseScore = 0
  let satisfactionScore = 0
  let complianceScore = 0

  let totalKPIs = 0
  let metKPIs = 0

  agreements.forEach(agreement => {
    agreement.kpis.forEach(kpi => {
      totalKPIs++
      if (kpi.status === 'met') {
        metKPIs++
      }

      const ratio = kpi.current / kpi.target

      switch (kpi.type) {
        case 'availability':
          availabilityScore = Math.max(availabilityScore, Math.min(100, ratio * 100))
          break
        case 'response_time':
          responseScore = Math.max(responseScore, Math.min(100, (2 - ratio) * 50))
          break
        case 'customer_satisfaction':
          satisfactionScore = Math.max(satisfactionScore, (kpi.current / 5) * 100)
          break
      }
    })
  })

  complianceScore = totalKPIs > 0 ? (metKPIs / totalKPIs) * 100 : 0

  // 综合评分
  const healthScore = Math.round(
    (availabilityScore * 0.3 +
     responseScore * 0.25 +
     satisfactionScore * 0.2 +
     complianceScore * 0.25)
  )

  // 确定等级
  const level = healthScoreLevels.find(l => healthScore >= l.min && healthScore <= l.max)

  // 生成改进建议
  const recommendations = []
  if (availabilityScore < 90) recommendations.push('提升系统可用性')
  if (responseScore < 80) recommendations.push('优化响应时间')
  if (satisfactionScore < 85) recommendations.push('改善用户体验')
  if (complianceScore < 95) recommendations.push('加强SLA合规性')

  return createApiResponse({
    serviceId,
    serviceName: service.name,
    healthScore,
    level: level?.level || '未知',
    color: level?.color || '#999',
    factors: [
      { name: '可用性', score: Math.round(availabilityScore), weight: '30%' },
      { name: '响应性能', score: Math.round(responseScore), weight: '25%' },
      { name: '用户满意度', score: Math.round(satisfactionScore), weight: '20%' },
      { name: 'SLA合规性', score: Math.round(complianceScore), weight: '25%' }
    ],
    recommendations
  })
}

/**
 * 获取SLA告警列表
 */
export function getSLAAlerts(params = {}) {
  const level = params.level // critical, warning, info
  const status = params.status // active, resolved

  // 生成告警数据
  const alerts = [
    {
      id: 'ALT-001',
      level: 'warning',
      title: 'ERP系统响应时间告警',
      description: '当前响应时间14分钟，接近15分钟阈值',
      serviceId: 'SRV-001',
      serviceName: 'ERP系统',
      agreementId: 'SLA-001',
      kpiType: 'response_time',
      currentValue: 14,
      targetValue: 15,
      status: 'active',
      createdAt: formatDateTime(new Date(Date.now() - 2 * 60 * 60 * 1000)),
      updatedAt: formatDateTime(new Date(Date.now() - 2 * 60 * 60 * 1000))
    },
    {
      id: 'ALT-002',
      level: 'critical',
      title: '数据库服务可用性告警',
      description: '当前可用性99.92%，低于99.95%目标值',
      serviceId: 'SRV-005',
      serviceName: '数据库服务',
      agreementId: 'UC-001',
      kpiType: 'availability',
      currentValue: 99.92,
      targetValue: 99.95,
      status: 'active',
      createdAt: formatDateTime(new Date(Date.now() - 4 * 60 * 60 * 1000)),
      updatedAt: formatDateTime(new Date(Date.now() - 4 * 60 * 60 * 1000))
    },
    {
      id: 'ALT-003',
      level: 'info',
      title: 'CRM系统性能优化完成',
      description: '响应时间从18分钟优化至10分钟',
      serviceId: 'SRV-002',
      serviceName: 'CRM系统',
      agreementId: 'SLA-002',
      kpiType: 'response_time',
      currentValue: 10,
      targetValue: 15,
      status: 'resolved',
      createdAt: formatDateTime(new Date(Date.now() - 24 * 60 * 60 * 1000)),
      updatedAt: formatDateTime(new Date(Date.now() - 2 * 60 * 60 * 1000))
    }
  ]

  let filteredAlerts = alerts

  if (level) {
    filteredAlerts = filteredAlerts.filter(a => a.level === level)
  }
  if (status) {
    filteredAlerts = filteredAlerts.filter(a => a.status === status)
  }

  return createApiResponse(filteredAlerts)
}

/**
 * 协议审批流程
 */
export function submitAgreementForApproval(agreementId) {
  const agreement = mockAgreements.find(a => a.id === agreementId)
  if (!agreement) {
    return createApiError('协议不存在', 404)
  }

  if (agreement.status !== 'draft') {
    return createApiError('只有草稿状态的协议才能提交审批', 400)
  }

  agreement.status = 'pending_approval'
  agreement.submittedAt = formatDateTime(new Date())
  agreement.updatedAt = formatDateTime(new Date())

  return createApiResponse(agreement, '协议已提交审批')
}

/**
 * 审批协议
 */
export function approveAgreement(agreementId, approvalData) {
  const agreement = mockAgreements.find(a => a.id === agreementId)
  if (!agreement) {
    return createApiError('协议不存在', 404)
  }

  if (agreement.status !== 'pending_approval') {
    return createApiError('协议状态不正确', 400)
  }

  agreement.status = approvalData.approved ? 'approved' : 'rejected'
  agreement.approver = approvalData.approver
  agreement.approvalDate = formatDateTime(new Date())
  agreement.approvalComments = approvalData.comments
  agreement.updatedAt = formatDateTime(new Date())

  return createApiResponse(agreement, approvalData.approved ? '协议审批通过' : '协议审批拒绝')
}

/**
 * 协议版本管理
 */
export function createAgreementVersion(agreementId, versionData) {
  const agreement = mockAgreements.find(a => a.id === agreementId)
  if (!agreement) {
    return createApiError('协议不存在', 404)
  }

  // 创建新版本
  const newVersion = {
    ...agreement,
    id: `${agreement.id}-v${versionData.version}`,
    version: versionData.version,
    parentId: agreementId,
    status: 'draft',
    createdAt: formatDateTime(new Date()),
    updatedAt: formatDateTime(new Date()),
    ...versionData.changes
  }

  mockAgreements.push(newVersion)

  return createApiResponse(newVersion, '新版本创建成功')
}

/**
 * 获取协议版本历史
 */
export function getAgreementVersions(agreementId) {
  const versions = mockAgreements.filter(a =>
    a.id === agreementId || a.parentId === agreementId
  ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

  return createApiResponse(versions)
}

/**
 * 协议生命周期管理
 */
export function getAgreementLifecycle(agreementId) {
  const agreement = mockAgreements.find(a => a.id === agreementId)
  if (!agreement) {
    return createApiError('协议不存在', 404)
  }

  // 模拟生命周期事件
  const lifecycle = [
    {
      stage: 'created',
      status: 'completed',
      date: agreement.createdAt,
      operator: agreement.owner,
      description: '协议创建'
    },
    {
      stage: 'submitted',
      status: agreement.submittedAt ? 'completed' : 'pending',
      date: agreement.submittedAt || null,
      operator: agreement.owner,
      description: '提交审批'
    },
    {
      stage: 'approved',
      status: agreement.status === 'approved' ? 'completed' :
              agreement.status === 'rejected' ? 'failed' : 'pending',
      date: agreement.approvalDate || null,
      operator: agreement.approver || null,
      description: '审批决定'
    },
    {
      stage: 'activated',
      status: agreement.status === 'active' ? 'completed' : 'pending',
      date: agreement.status === 'active' ? agreement.effectiveDate : null,
      operator: agreement.owner,
      description: '协议激活'
    }
  ]

  return createApiResponse({
    agreementId,
    currentStage: agreement.status,
    lifecycle
  })
}

/**
 * 协议续签
 */
export function renewAgreement(agreementId, renewalData) {
  const agreement = mockAgreements.find(a => a.id === agreementId)
  if (!agreement) {
    return createApiError('协议不存在', 404)
  }

  // 创建续签协议
  const renewedAgreement = {
    ...agreement,
    id: `${agreement.type}-${String(Date.now()).slice(-3)}`,
    version: '1.0',
    status: 'draft',
    effectiveDate: renewalData.effectiveDate,
    expiryDate: renewalData.expiryDate,
    parentId: agreementId,
    renewalOf: agreementId,
    createdAt: formatDateTime(new Date()),
    updatedAt: formatDateTime(new Date())
  }

  mockAgreements.push(renewedAgreement)

  return createApiResponse(renewedAgreement, '协议续签成功')
}

/**
 * 协议终止
 */
export function terminateAgreement(agreementId, terminationData) {
  const agreement = mockAgreements.find(a => a.id === agreementId)
  if (!agreement) {
    return createApiError('协议不存在', 404)
  }

  agreement.status = 'terminated'
  agreement.terminationDate = formatDateTime(new Date())
  agreement.terminationReason = terminationData.reason
  agreement.terminatedBy = terminationData.operator
  agreement.updatedAt = formatDateTime(new Date())

  return createApiResponse(agreement, '协议终止成功')
}

/**
 * 获取协议变更历史
 */
export function getAgreementChangeHistory(agreementId) {
  // 模拟变更历史数据
  const changes = [
    {
      id: 'CHG-001',
      date: formatDateTime(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
      operator: '张三',
      action: 'update',
      field: 'kpis',
      oldValue: '响应时间目标: 20分钟',
      newValue: '响应时间目标: 15分钟',
      reason: '业务需求变更'
    },
    {
      id: 'CHG-002',
      date: formatDateTime(new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)),
      operator: '李四',
      action: 'approve',
      field: 'status',
      oldValue: 'pending_approval',
      newValue: 'approved',
      reason: '审批通过'
    },
    {
      id: 'CHG-003',
      date: formatDateTime(new Date(Date.now() - 21 * 24 * 60 * 60 * 1000)),
      operator: '张三',
      action: 'create',
      field: 'agreement',
      oldValue: null,
      newValue: '协议创建',
      reason: '新建协议'
    }
  ]

  return createApiResponse(changes)
}

/**
 * SLA违约处理
 */
export function handleSLABreach(breachData) {
  const breach = {
    id: `BREACH-${String(Date.now()).slice(-6)}`,
    agreementId: breachData.agreementId,
    serviceName: breachData.serviceName,
    kpiType: breachData.kpiType,
    targetValue: breachData.targetValue,
    actualValue: breachData.actualValue,
    breachDate: formatDateTime(new Date()),
    severity: breachData.severity || 'medium',
    status: 'open',
    assignee: breachData.assignee,
    escalationLevel: 0,
    actions: [],
    createdAt: formatDateTime(new Date())
  }

  // 模拟自动创建改进措施
  const actions = [
    {
      id: 'ACT-001',
      type: 'immediate',
      description: '立即检查服务状态',
      assignee: breachData.assignee,
      dueDate: formatDateTime(new Date(Date.now() + 2 * 60 * 60 * 1000)), // 2小时后
      status: 'pending'
    },
    {
      id: 'ACT-002',
      type: 'corrective',
      description: '分析根本原因',
      assignee: breachData.assignee,
      dueDate: formatDateTime(new Date(Date.now() + 24 * 60 * 60 * 1000)), // 24小时后
      status: 'pending'
    },
    {
      id: 'ACT-003',
      type: 'preventive',
      description: '制定预防措施',
      assignee: breachData.assignee,
      dueDate: formatDateTime(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7天后
      status: 'pending'
    }
  ]

  breach.actions = actions

  return createApiResponse(breach, 'SLA违约记录创建成功')
}

/**
 * 获取SLA违约记录
 */
export function getSLABreaches(params = {}) {
  // 模拟违约记录数据
  const breaches = [
    {
      id: 'BREACH-001',
      agreementId: 'SLA-001',
      serviceName: 'ERP系统',
      kpiType: 'response_time',
      targetValue: 15,
      actualValue: 18,
      breachDate: formatDateTime(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
      severity: 'medium',
      status: 'resolved',
      assignee: '张三',
      escalationLevel: 1,
      resolutionDate: formatDateTime(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
      resolutionSummary: '通过优化数据库查询解决了响应时间问题'
    },
    {
      id: 'BREACH-002',
      agreementId: 'UC-001',
      serviceName: '数据库服务',
      kpiType: 'availability',
      targetValue: 99.95,
      actualValue: 99.92,
      breachDate: formatDateTime(new Date(Date.now() - 4 * 60 * 60 * 1000)),
      severity: 'high',
      status: 'open',
      assignee: '陈十一',
      escalationLevel: 0
    }
  ]

  let filteredBreaches = breaches

  if (params.status) {
    filteredBreaches = filteredBreaches.filter(b => b.status === params.status)
  }
  if (params.severity) {
    filteredBreaches = filteredBreaches.filter(b => b.severity === params.severity)
  }
  if (params.agreementId) {
    filteredBreaches = filteredBreaches.filter(b => b.agreementId === params.agreementId)
  }

  return createApiResponse(filteredBreaches)
}
