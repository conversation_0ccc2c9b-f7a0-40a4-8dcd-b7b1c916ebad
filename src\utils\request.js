import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '', // API基础URL
  timeout: 30000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    const userStore = useUserStore()
    
    // 添加认证token
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    
    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    // 打印请求日志（开发环境）
    if (import.meta.env.DEV) {
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data
      })
    }
    
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('[Request Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const { data, config } = response
    
    // 打印响应日志（开发环境）
    if (import.meta.env.DEV) {
      console.log(`[API Response] ${config.method?.toUpperCase()} ${config.url}`, data)
    }
    
    // 统一处理响应格式
    if (data.code !== undefined) {
      if (data.code === 200) {
        return data
      } else {
        // 业务错误处理
        handleBusinessError(data)
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    }
    
    // 直接返回数据（适用于不同的后端响应格式）
    return data
  },
  error => {
    // 对响应错误做点什么
    console.error('[Response Error]', error)
    
    if (error.response) {
      // 服务器返回了错误状态码
      handleHttpError(error.response)
    } else if (error.request) {
      // 请求已发出但没有收到响应
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      ElMessage.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

/**
 * 处理HTTP错误
 * @param {Object} response - 响应对象
 */
function handleHttpError(response) {
  const { status, data } = response
  
  switch (status) {
    case 400:
      ElMessage.error(data?.message || '请求参数错误')
      break
    case 401:
      ElMessage.error('登录已过期，请重新登录')
      // 清除用户信息并跳转到登录页
      const userStore = useUserStore()
      userStore.logout()
      window.location.href = '/login'
      break
    case 403:
      ElMessage.error('没有权限访问该资源')
      break
    case 404:
      ElMessage.error('请求的资源不存在')
      break
    case 422:
      ElMessage.error(data?.message || '数据验证失败')
      break
    case 429:
      ElMessage.error('请求过于频繁，请稍后再试')
      break
    case 500:
      ElMessage.error('服务器内部错误')
      break
    case 502:
      ElMessage.error('网关错误')
      break
    case 503:
      ElMessage.error('服务暂时不可用')
      break
    case 504:
      ElMessage.error('网关超时')
      break
    default:
      ElMessage.error(data?.message || `请求失败 (${status})`)
  }
}

/**
 * 处理业务错误
 * @param {Object} data - 响应数据
 */
function handleBusinessError(data) {
  const { code, message } = data
  
  switch (code) {
    case 1001:
      ElMessage.error('参数验证失败')
      break
    case 1002:
      ElMessage.error('数据不存在')
      break
    case 1003:
      ElMessage.error('数据已存在')
      break
    case 1004:
      ElMessage.error('操作失败')
      break
    case 1005:
      ElMessageBox.confirm(
        '当前操作可能影响其他数据，是否继续？',
        '操作确认',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 用户确认后的处理
      }).catch(() => {
        // 用户取消的处理
      })
      break
    default:
      ElMessage.error(message || '操作失败')
  }
}

/**
 * 生成请求ID
 * @returns {string} 请求ID
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 请求重试装饰器
 * @param {Function} requestFn - 请求函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟
 * @returns {Function} 装饰后的请求函数
 */
export function withRetry(requestFn, maxRetries = 3, delay = 1000) {
  return async function(...args) {
    let lastError
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn.apply(this, args)
      } catch (error) {
        lastError = error
        
        if (i < maxRetries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
          console.log(`Request retry ${i + 1}/${maxRetries}`)
        }
      }
    }
    
    throw lastError
  }
}

/**
 * 请求缓存装饰器
 * @param {Function} requestFn - 请求函数
 * @param {number} cacheTime - 缓存时间（毫秒）
 * @returns {Function} 装饰后的请求函数
 */
export function withCache(requestFn, cacheTime = 5 * 60 * 1000) {
  const cache = new Map()
  
  return async function(...args) {
    const cacheKey = JSON.stringify(args)
    const cached = cache.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < cacheTime) {
      return cached.data
    }
    
    try {
      const data = await requestFn.apply(this, args)
      cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      })
      return data
    } catch (error) {
      // 如果有缓存数据，在请求失败时返回缓存
      if (cached) {
        console.warn('Request failed, returning cached data')
        return cached.data
      }
      throw error
    }
  }
}

/**
 * 请求防抖装饰器
 * @param {Function} requestFn - 请求函数
 * @param {number} delay - 防抖延迟
 * @returns {Function} 装饰后的请求函数
 */
export function withDebounce(requestFn, delay = 300) {
  let timeoutId = null
  let lastResolve = null
  let lastReject = null
  
  return function(...args) {
    return new Promise((resolve, reject) => {
      // 清除之前的定时器
      if (timeoutId) {
        clearTimeout(timeoutId)
        // 拒绝之前的Promise
        if (lastReject) {
          lastReject(new Error('Request debounced'))
        }
      }
      
      lastResolve = resolve
      lastReject = reject
      
      timeoutId = setTimeout(async () => {
        try {
          const result = await requestFn.apply(this, args)
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          timeoutId = null
          lastResolve = null
          lastReject = null
        }
      }, delay)
    })
  }
}

/**
 * 并发请求控制
 * @param {Array} requests - 请求数组
 * @param {number} concurrency - 并发数
 * @returns {Promise} 所有请求的结果
 */
export async function concurrentRequests(requests, concurrency = 5) {
  const results = []
  const executing = []
  
  for (const request of requests) {
    const promise = Promise.resolve().then(() => request()).then(result => {
      results.push(result)
      return result
    })
    
    executing.push(promise)
    
    if (executing.length >= concurrency) {
      await Promise.race(executing)
      executing.splice(executing.findIndex(p => p === promise), 1)
    }
  }
  
  await Promise.all(executing)
  return results
}

/**
 * 上传文件
 * @param {string} url - 上传地址
 * @param {File} file - 文件对象
 * @param {Object} options - 选项
 * @returns {Promise}
 */
export function uploadFile(url, file, options = {}) {
  const formData = new FormData()
  formData.append('file', file)
  
  // 添加额外的字段
  if (options.data) {
    Object.keys(options.data).forEach(key => {
      formData.append(key, options.data[key])
    })
  }
  
  return service.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: options.onProgress,
    ...options
  })
}

/**
 * 下载文件
 * @param {string} url - 下载地址
 * @param {string} filename - 文件名
 * @param {Object} params - 参数
 * @returns {Promise}
 */
export async function downloadFile(url, filename, params = {}) {
  try {
    const response = await service.get(url, {
      params,
      responseType: 'blob'
    })
    
    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
    
    ElMessage.success('文件下载成功')
  } catch (error) {
    ElMessage.error('文件下载失败')
    throw error
  }
}

export default service
