<template>
  <div class="service-health">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>服务健康评分</h2>
          <p>综合评估服务运行状态和质量水平</p>
        </div>
        <div class="action-section">
          <el-select v-model="selectedCategory" @change="loadServices" clearable placeholder="服务分类">
            <el-option label="业务系统" value="业务系统" />
            <el-option label="基础设施" value="基础设施" />
            <el-option label="办公系统" value="办公系统" />
          </el-select>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新评分
          </el-button>
          <el-button :icon="Download" @click="exportHealthReport">
            导出报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 健康评分概览 -->
    <div class="health-overview">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="6">
          <el-card class="health-card excellent">
            <div class="health-content">
              <div class="health-icon">
                <el-icon :size="32">
                  <SuccessFilled />
                </el-icon>
              </div>
              <div class="health-info">
                <div class="health-count">{{ healthStats.excellent }}</div>
                <div class="health-label">健康 (90-100)</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-card class="health-card good">
            <div class="health-content">
              <div class="health-icon">
                <el-icon :size="32">
                  <Warning />
                </el-icon>
              </div>
              <div class="health-info">
                <div class="health-count">{{ healthStats.good }}</div>
                <div class="health-label">良好 (70-89)</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-card class="health-card fair">
            <div class="health-content">
              <div class="health-icon">
                <el-icon :size="32">
                  <CircleCloseFilled />
                </el-icon>
              </div>
              <div class="health-info">
                <div class="health-count">{{ healthStats.fair }}</div>
                <div class="health-label">风险 (50-69)</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-card class="health-card poor">
            <div class="health-content">
              <div class="health-icon">
                <el-icon :size="32">
                  <CircleCloseFilled />
                </el-icon>
              </div>
              <div class="health-info">
                <div class="health-count">{{ healthStats.poor }}</div>
                <div class="health-label">危险 (<50)</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 服务健康列表 -->
    <div class="health-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>服务健康评分详情</span>
            <div class="header-actions">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索服务"
                clearable
                style="width: 200px"
                @input="filterServices"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </template>

        <div class="services-grid">
          <div
            v-for="service in filteredServices"
            :key="service.serviceId"
            class="service-health-card"
            @click="viewServiceDetail(service)"
          >
            <!-- 服务基本信息 -->
            <div class="service-header">
              <div class="service-info">
                <div class="service-name">{{ service.serviceName }}</div>
                <div class="service-category">{{ service.category || '未分类' }}</div>
              </div>
              <div class="health-score-display">
                <div class="score-circle" :class="getHealthLevelClass(service.healthScore)">
                  <span class="score-value">{{ service.healthScore }}</span>
                </div>
                <div class="score-level">{{ service.level }}</div>
              </div>
            </div>

            <!-- 评分因子 -->
            <div class="health-factors">
              <div
                v-for="factor in service.factors"
                :key="factor.name"
                class="factor-item"
              >
                <div class="factor-header">
                  <span class="factor-name">{{ factor.name }}</span>
                  <span class="factor-weight">{{ factor.weight }}</span>
                </div>
                <div class="factor-progress">
                  <el-progress
                    :percentage="factor.score"
                    :status="getFactorStatus(factor.score)"
                    :show-text="false"
                    :stroke-width="6"
                  />
                  <span class="factor-score">{{ factor.score }}</span>
                </div>
              </div>
            </div>

            <!-- 改进建议 -->
            <div class="recommendations" v-if="service.recommendations.length > 0">
              <div class="recommendations-header">
                <el-icon><BulbFilled /></el-icon>
                <span>改进建议</span>
              </div>
              <ul class="recommendations-list">
                <li
                  v-for="(recommendation, index) in service.recommendations.slice(0, 2)"
                  :key="index"
                >
                  {{ recommendation }}
                </li>
                <li v-if="service.recommendations.length > 2" class="more-recommendations">
                  +{{ service.recommendations.length - 2 }}个建议
                </li>
              </ul>
            </div>

            <!-- 操作按钮 -->
            <div class="service-actions">
              <el-button type="text" size="small" @click.stop="viewServiceDetail(service)">
                查看详情
              </el-button>
              <el-button type="text" size="small" @click.stop="createImprovement(service)">
                创建改进
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredServices.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无服务数据">
            <el-button type="primary" @click="$router.push('/slm')">
              配置服务
            </el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 健康趋势图表 -->
    <div class="health-trends">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <span>健康评分分布</span>
            </template>
            <div class="chart-container" ref="distributionChart"></div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="12">
          <el-card>
            <template #header>
              <span>健康评分趋势</span>
            </template>
            <div class="chart-container" ref="trendChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 服务详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="selectedService?.serviceName + ' - 健康评分详情'"
      width="80%"
      top="5vh"
    >
      <div v-if="selectedService" class="service-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="服务名称">{{ selectedService.serviceName }}</el-descriptions-item>
            <el-descriptions-item label="服务分类">{{ selectedService.category || '未分类' }}</el-descriptions-item>
            <el-descriptions-item label="健康评分">
              <div class="score-display">
                <span class="score-value" :style="{ color: selectedService.color }">
                  {{ selectedService.healthScore }}
                </span>
                <el-tag :type="getHealthTagType(selectedService.level)" size="small">
                  {{ selectedService.level }}
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="评估时间">{{ formatDateTime(new Date()) }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 评分详情 -->
        <div class="detail-section">
          <h3>评分详情</h3>
          <el-table :data="selectedService.factors" border>
            <el-table-column prop="name" label="评分因子" width="150" />
            <el-table-column prop="weight" label="权重" width="100" />
            <el-table-column prop="score" label="得分" width="100">
              <template #default="{ row }">
                <span :class="getScoreClass(row.score)">{{ row.score }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getFactorTagType(row.score)">
                  {{ getFactorStatusText(row.score) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="进度条" min-width="200">
              <template #default="{ row }">
                <el-progress
                  :percentage="row.score"
                  :status="getFactorStatus(row.score)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 改进建议 -->
        <div class="detail-section" v-if="selectedService.recommendations.length > 0">
          <h3>改进建议</h3>
          <ul class="detail-recommendations">
            <li
              v-for="(recommendation, index) in selectedService.recommendations"
              :key="index"
            >
              {{ recommendation }}
            </li>
          </ul>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="createImprovement(selectedService)">
            创建改进计划
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建改进计划对话框 -->
    <el-dialog
      v-model="showImprovementDialog"
      title="创建改进计划"
      width="600px"
    >
      <el-form :model="improvementForm" :rules="improvementRules" ref="improvementFormRef" label-width="100px">
        <el-form-item label="服务名称">
          <el-input v-model="improvementService" readonly />
        </el-form-item>
        <el-form-item label="改进标题" prop="title">
          <el-input v-model="improvementForm.title" placeholder="请输入改进标题" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="improvementForm.priority" placeholder="请选择优先级">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" prop="assignee">
          <el-input v-model="improvementForm.assignee" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="预期完成时间" prop="dueDate">
          <el-date-picker
            v-model="improvementForm.dueDate"
            type="date"
            placeholder="选择完成时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="改进描述" prop="description">
          <el-input
            v-model="improvementForm.description"
            type="textarea"
            :rows="4"
            placeholder="请描述具体的改进措施"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showImprovementDialog = false">取消</el-button>
          <el-button type="primary" @click="saveImprovement" :loading="saveLoading">
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Download, SuccessFilled, Warning, CircleCloseFilled, Search, BulbFilled
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getServiceList,
  calculateHealthScore,
  healthScoreLevels
} from '@/api/slmApi.js'
import { formatDateTime } from '@/api/index.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const selectedCategory = ref('')
const searchKeyword = ref('')
const showDetailDialog = ref(false)
const showImprovementDialog = ref(false)
const selectedService = ref(null)
const improvementService = ref('')

// 服务数据
const services = ref([])
const healthScores = ref([])

// 健康统计
const healthStats = reactive({
  excellent: 0,
  good: 0,
  fair: 0,
  poor: 0
})

// 改进表单
const improvementForm = reactive({
  title: '',
  priority: '',
  assignee: '',
  dueDate: '',
  description: ''
})

const improvementFormRef = ref()

// 表单验证规则
const improvementRules = {
  title: [
    { required: true, message: '请输入改进标题', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  assignee: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ],
  dueDate: [
    { required: true, message: '请选择完成时间', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入改进描述', trigger: 'blur' }
  ]
}

// 图表引用
const distributionChart = ref()
const trendChart = ref()

// 过滤后的服务列表
const filteredServices = computed(() => {
  let filtered = healthScores.value

  if (selectedCategory.value) {
    filtered = filtered.filter(service =>
      services.value.find(s => s.id === service.serviceId)?.category === selectedCategory.value
    )
  }

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(service =>
      service.serviceName.toLowerCase().includes(keyword)
    )
  }

  return filtered.sort((a, b) => b.healthScore - a.healthScore)
})

// 加载服务列表
const loadServices = async () => {
  loading.value = true
  try {
    const response = await getServiceList({
      pageSize: 100,
      category: selectedCategory.value
    })
    if (response.success) {
      services.value = response.data.list
      await loadHealthScores()
    }
  } catch (error) {
    ElMessage.error('加载服务列表失败')
    console.error('Load services error:', error)
  } finally {
    loading.value = false
  }
}

// 加载健康评分
const loadHealthScores = async () => {
  const scores = []

  for (const service of services.value) {
    try {
      const response = await calculateHealthScore(service.id)
      if (response.success) {
        scores.push({
          ...response.data,
          category: service.category
        })
      }
    } catch (error) {
      console.error(`Calculate health score error for ${service.id}:`, error)
    }
  }

  healthScores.value = scores
  calculateHealthStats()

  // 延迟初始化图表
  nextTick(() => {
    initDistributionChart()
    initTrendChart()
  })
}

// 计算健康统计
const calculateHealthStats = () => {
  const stats = {
    excellent: 0,
    good: 0,
    fair: 0,
    poor: 0
  }

  healthScores.value.forEach(service => {
    const score = service.healthScore
    if (score >= 90) {
      stats.excellent++
    } else if (score >= 70) {
      stats.good++
    } else if (score >= 50) {
      stats.fair++
    } else {
      stats.poor++
    }
  })

  Object.assign(healthStats, stats)
}

// 刷新数据
const refreshData = async () => {
  await loadServices()
}

// 筛选服务
const filterServices = () => {
  // 触发计算属性重新计算
}

// 获取健康等级样式类
const getHealthLevelClass = (score) => {
  if (score >= 90) return 'excellent'
  if (score >= 70) return 'good'
  if (score >= 50) return 'fair'
  return 'poor'
}

// 获取因子状态
const getFactorStatus = (score) => {
  if (score >= 90) return 'success'
  if (score >= 70) return 'warning'
  return 'exception'
}

// 获取健康标签类型
const getHealthTagType = (level) => {
  switch (level) {
    case '健康':
      return 'success'
    case '良好':
      return 'warning'
    case '风险':
      return 'danger'
    case '危险':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取评分样式类
const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 70) return 'score-good'
  if (score >= 50) return 'score-fair'
  return 'score-poor'
}

// 获取因子标签类型
const getFactorTagType = (score) => {
  if (score >= 90) return 'success'
  if (score >= 70) return 'warning'
  return 'danger'
}

// 获取因子状态文本
const getFactorStatusText = (score) => {
  if (score >= 90) return '优秀'
  if (score >= 70) return '良好'
  if (score >= 50) return '一般'
  return '较差'
}

// 查看服务详情
const viewServiceDetail = (service) => {
  selectedService.value = service
  showDetailDialog.value = true
}

// 创建改进计划
const createImprovement = (service) => {
  improvementService.value = service.serviceName

  // 根据服务健康状况预填改进标题
  let title = ''
  if (service.healthScore < 50) {
    title = `${service.serviceName} 紧急改进计划`
  } else if (service.healthScore < 70) {
    title = `${service.serviceName} 服务优化计划`
  } else {
    title = `${service.serviceName} 持续改进计划`
  }

  Object.assign(improvementForm, {
    title,
    priority: service.healthScore < 50 ? 'high' : service.healthScore < 70 ? 'medium' : 'low',
    assignee: '',
    dueDate: '',
    description: service.recommendations.join('；')
  })

  showImprovementDialog.value = true
}

// 保存改进计划
const saveImprovement = async () => {
  if (!improvementFormRef.value) return

  try {
    await improvementFormRef.value.validate()

    saveLoading.value = true

    // 模拟保存改进计划
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('改进计划创建成功')
    showImprovementDialog.value = false
    resetImprovementForm()
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('创建改进计划失败')
      console.error('Save improvement error:', error)
    }
  } finally {
    saveLoading.value = false
  }
}

// 重置改进表单
const resetImprovementForm = () => {
  Object.assign(improvementForm, {
    title: '',
    priority: '',
    assignee: '',
    dueDate: '',
    description: ''
  })
  improvementFormRef.value?.resetFields()
}

// 导出健康报告
const exportHealthReport = () => {
  if (healthScores.value.length === 0) {
    ElMessage.warning('暂无健康评分数据可导出')
    return
  }

  // 模拟导出功能
  ElMessage.success('健康报告导出成功')
}

// 初始化分布图表
const initDistributionChart = () => {
  if (!distributionChart.value) return

  const chart = echarts.init(distributionChart.value)

  const data = [
    { value: healthStats.excellent, name: '健康 (90-100)', itemStyle: { color: '#67c23a' } },
    { value: healthStats.good, name: '良好 (70-89)', itemStyle: { color: '#e6a23c' } },
    { value: healthStats.fair, name: '风险 (50-69)', itemStyle: { color: '#f56c6c' } },
    { value: healthStats.poor, name: '危险 (<50)', itemStyle: { color: '#f56c6c' } }
  ]

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '健康评分分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChart.value) return

  const chart = echarts.init(trendChart.value)

  // 生成模拟趋势数据
  const months = ['6个月前', '5个月前', '4个月前', '3个月前', '2个月前', '上个月']
  const avgScores = months.map(() => {
    const currentAvg = healthScores.value.length > 0
      ? Math.round(healthScores.value.reduce((sum, s) => sum + s.healthScore, 0) / healthScores.value.length)
      : 0
    return Math.max(0, Math.min(100, currentAvg + (Math.random() - 0.5) * 20))
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value}`
      }
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '平均健康评分',
        type: 'line',
        data: avgScores,
        smooth: true,
        lineStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.service-health {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 健康概览 */
.health-overview {
  margin-bottom: 20px;
}

.health-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.health-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.health-card.excellent {
  border-left: 4px solid #67c23a;
}

.health-card.good {
  border-left: 4px solid #e6a23c;
}

.health-card.fair {
  border-left: 4px solid #f56c6c;
}

.health-card.poor {
  border-left: 4px solid #f56c6c;
}

.health-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 8px;
}

.health-icon {
  margin-right: 16px;
}

.health-card.excellent .health-icon {
  color: #67c23a;
}

.health-card.good .health-icon {
  color: #e6a23c;
}

.health-card.fair .health-icon {
  color: #f56c6c;
}

.health-card.poor .health-icon {
  color: #f56c6c;
}

.health-info {
  flex: 1;
}

.health-count {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.health-label {
  font-size: 14px;
  color: #666;
}

/* 健康列表 */
.health-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  min-height: 300px;
}

.service-health-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-health-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.service-category {
  font-size: 14px;
  color: #666;
}

.health-score-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
}

.score-circle.excellent {
  background-color: #67c23a;
}

.score-circle.good {
  background-color: #e6a23c;
}

.score-circle.fair {
  background-color: #f56c6c;
}

.score-circle.poor {
  background-color: #f56c6c;
}

.score-value {
  font-size: 18px;
}

.score-level {
  font-size: 12px;
  color: #666;
}

/* 健康因子 */
.health-factors {
  margin-bottom: 16px;
}

.factor-item {
  margin-bottom: 12px;
}

.factor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.factor-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.factor-weight {
  font-size: 12px;
  color: #999;
}

.factor-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.factor-score {
  font-size: 12px;
  color: #666;
  min-width: 30px;
  text-align: right;
}

/* 改进建议 */
.recommendations {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.recommendations-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #409eff;
}

.recommendations-list {
  margin: 0;
  padding-left: 16px;
  font-size: 13px;
  color: #666;
}

.recommendations-list li {
  margin-bottom: 4px;
}

.more-recommendations {
  color: #409eff;
  cursor: pointer;
}

/* 服务操作 */
.service-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 健康趋势 */
.health-trends {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 服务详情 */
.service-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-display .score-value {
  font-size: 24px;
  font-weight: bold;
}

.detail-recommendations {
  margin: 0;
  padding-left: 20px;
  color: #666;
  line-height: 1.6;
}

.detail-recommendations li {
  margin-bottom: 8px;
}

/* 评分样式 */
.score-excellent {
  color: #67c23a;
  font-weight: 600;
}

.score-good {
  color: #e6a23c;
  font-weight: 600;
}

.score-fair {
  color: #f56c6c;
  font-weight: 600;
}

.score-poor {
  color: #f56c6c;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-health {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
    flex-wrap: wrap;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .service-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .health-score-display {
    align-self: flex-end;
  }

  .health-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .health-icon {
    margin-right: 0;
  }
}

/* 对话框样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-descriptions-item__label) {
  font-weight: 500;
}

:deep(.el-table .cell) {
  padding: 8px;
}

:deep(.el-progress-bar__outer) {
  border-radius: 3px;
}
</style>
