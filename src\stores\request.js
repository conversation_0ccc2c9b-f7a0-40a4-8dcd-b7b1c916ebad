import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getRequestOverview,
  getRequestStatistics,
  getServiceCatalog,
  getServiceCategories,
  getServiceDetail,
  createServiceRequest,
  getUserRequests,
  getRequestDetail,
  updateRequestStatus,
  cancelRequest,
  getPendingApprovals,
  submitApproval,
  getRequestHistory,
  submitSatisfactionRating,
  searchServices,
  getPopularServices,
  getRecommendedServices
} from '@/api/request'

export const useRequestStore = defineStore('request', () => {
  // 状态定义
  const overview = ref({
    totalRequests: 0,
    thisMonthRequests: 0,
    pendingRequests: 0,
    completedRequests: 0,
    automationRate: 0,
    avgProcessingTime: '',
    satisfactionScore: 0,
    statusDistribution: [],
    categoryDistribution: [],
    monthlyTrend: []
  })

  const statistics = ref({
    requestVolume: {},
    processingMetrics: {},
    topServices: [],
    departmentStats: []
  })

  const serviceCategories = ref([])
  const serviceCatalog = ref([])
  const currentService = ref(null)
  const userRequests = ref([])
  const currentRequest = ref(null)
  const pendingApprovals = ref([])
  const requestHistory = ref([])

  // 加载状态
  const loading = ref({
    overview: false,
    statistics: false,
    catalog: false,
    requests: false,
    detail: false,
    approvals: false
  })

  // 分页信息
  const pagination = ref({
    catalog: { page: 1, pageSize: 12, total: 0 },
    requests: { page: 1, pageSize: 10, total: 0 },
    approvals: { page: 1, pageSize: 10, total: 0 }
  })

  // 筛选条件
  const filters = ref({
    catalog: { category: 'all', search: '' },
    requests: { status: 'all', category: 'all' },
    approvals: { priority: 'all', department: 'all' }
  })

  // 计算属性
  const completionRate = computed(() => {
    if (overview.value.totalRequests === 0) return 0
    return Math.round((overview.value.completedRequests / overview.value.totalRequests) * 100)
  })

  const pendingRate = computed(() => {
    if (overview.value.totalRequests === 0) return 0
    return Math.round((overview.value.pendingRequests / overview.value.totalRequests) * 100)
  })

  const popularServices = computed(() => {
    return serviceCatalog.value
      .filter(service => service.popularity > 70)
      .sort((a, b) => b.popularity - a.popularity)
      .slice(0, 6)
  })

  const automatedServices = computed(() => {
    return serviceCatalog.value.filter(service => service.automationSupported)
  })

  const urgentRequests = computed(() => {
    return userRequests.value.filter(request => 
      request.priority === '紧急' || request.priority === '非常紧急'
    )
  })

  // 方法定义
  const loadOverview = async () => {
    loading.value.overview = true
    try {
      const response = await getRequestOverview()
      if (response.code === 200) {
        overview.value = response.data
      }
    } catch (error) {
      console.error('加载概览数据失败:', error)
      throw error
    } finally {
      loading.value.overview = false
    }
  }

  const loadStatistics = async (params = {}) => {
    loading.value.statistics = true
    try {
      const response = await getRequestStatistics(params)
      if (response.code === 200) {
        statistics.value = response.data
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
      throw error
    } finally {
      loading.value.statistics = false
    }
  }

  const loadServiceCategories = async () => {
    try {
      const response = await getServiceCategories()
      if (response.code === 200) {
        serviceCategories.value = response.data
      }
    } catch (error) {
      console.error('加载服务分类失败:', error)
      throw error
    }
  }

  const loadServiceCatalog = async (params = {}) => {
    loading.value.catalog = true
    try {
      const queryParams = {
        ...filters.value.catalog,
        ...pagination.value.catalog,
        ...params
      }
      
      const response = await getServiceCatalog(queryParams)
      if (response.code === 200) {
        serviceCatalog.value = response.data.list
        pagination.value.catalog.total = response.data.total
        pagination.value.catalog.page = response.data.page
      }
    } catch (error) {
      console.error('加载服务目录失败:', error)
      throw error
    } finally {
      loading.value.catalog = false
    }
  }

  const loadServiceDetail = async (serviceId) => {
    loading.value.detail = true
    try {
      const response = await getServiceDetail(serviceId)
      if (response.code === 200) {
        currentService.value = response.data
      }
    } catch (error) {
      console.error('加载服务详情失败:', error)
      throw error
    } finally {
      loading.value.detail = false
    }
  }

  const submitRequest = async (requestData) => {
    try {
      const response = await createServiceRequest(requestData)
      if (response.code === 200) {
        // 刷新用户请求列表
        await loadUserRequests()
        return response.data
      }
    } catch (error) {
      console.error('提交请求失败:', error)
      throw error
    }
  }

  const loadUserRequests = async (params = {}) => {
    loading.value.requests = true
    try {
      const queryParams = {
        ...filters.value.requests,
        ...pagination.value.requests,
        ...params
      }
      
      const response = await getUserRequests(queryParams)
      if (response.code === 200) {
        userRequests.value = response.data.list
        pagination.value.requests.total = response.data.total
        pagination.value.requests.page = response.data.page
      }
    } catch (error) {
      console.error('加载用户请求失败:', error)
      throw error
    } finally {
      loading.value.requests = false
    }
  }

  const loadRequestDetail = async (requestId) => {
    loading.value.detail = true
    try {
      const response = await getRequestDetail(requestId)
      if (response.code === 200) {
        currentRequest.value = response.data
      }
    } catch (error) {
      console.error('加载请求详情失败:', error)
      throw error
    } finally {
      loading.value.detail = false
    }
  }

  const updateRequest = async (requestId, updateData) => {
    try {
      const response = await updateRequestStatus(requestId, updateData)
      if (response.code === 200) {
        // 更新本地数据
        const index = userRequests.value.findIndex(r => r.id === requestId)
        if (index !== -1) {
          userRequests.value[index] = { ...userRequests.value[index], ...updateData }
        }
        if (currentRequest.value && currentRequest.value.id === requestId) {
          currentRequest.value = { ...currentRequest.value, ...updateData }
        }
        return response.data
      }
    } catch (error) {
      console.error('更新请求失败:', error)
      throw error
    }
  }

  const cancelUserRequest = async (requestId, reason) => {
    try {
      const response = await cancelRequest(requestId, { reason })
      if (response.code === 200) {
        // 刷新请求列表
        await loadUserRequests()
        return response.data
      }
    } catch (error) {
      console.error('取消请求失败:', error)
      throw error
    }
  }

  const loadPendingApprovals = async (params = {}) => {
    loading.value.approvals = true
    try {
      const queryParams = {
        ...filters.value.approvals,
        ...pagination.value.approvals,
        ...params
      }
      
      const response = await getPendingApprovals(queryParams)
      if (response.code === 200) {
        pendingApprovals.value = response.data.list
        pagination.value.approvals.total = response.data.total
        pagination.value.approvals.page = response.data.page
      }
    } catch (error) {
      console.error('加载待审批请求失败:', error)
      throw error
    } finally {
      loading.value.approvals = false
    }
  }

  const processApproval = async (requestId, approvalData) => {
    try {
      const response = await submitApproval(requestId, approvalData)
      if (response.code === 200) {
        // 刷新待审批列表
        await loadPendingApprovals()
        return response.data
      }
    } catch (error) {
      console.error('处理审批失败:', error)
      throw error
    }
  }

  const loadRequestHistory = async (requestId) => {
    try {
      const response = await getRequestHistory(requestId)
      if (response.code === 200) {
        requestHistory.value = response.data
      }
    } catch (error) {
      console.error('加载请求历史失败:', error)
      throw error
    }
  }

  const submitRating = async (requestId, ratingData) => {
    try {
      const response = await submitSatisfactionRating(requestId, ratingData)
      if (response.code === 200) {
        // 更新本地请求数据
        const index = userRequests.value.findIndex(r => r.id === requestId)
        if (index !== -1) {
          userRequests.value[index].rating = ratingData.rating
          userRequests.value[index].feedback = ratingData.feedback
        }
        return response.data
      }
    } catch (error) {
      console.error('提交评价失败:', error)
      throw error
    }
  }

  const searchCatalog = async (searchParams) => {
    loading.value.catalog = true
    try {
      const response = await searchServices(searchParams)
      if (response.code === 200) {
        serviceCatalog.value = response.data.list
        pagination.value.catalog.total = response.data.total
      }
    } catch (error) {
      console.error('搜索服务失败:', error)
      throw error
    } finally {
      loading.value.catalog = false
    }
  }

  // 筛选器方法
  const setCatalogFilter = (filterData) => {
    filters.value.catalog = { ...filters.value.catalog, ...filterData }
    pagination.value.catalog.page = 1 // 重置页码
  }

  const setRequestsFilter = (filterData) => {
    filters.value.requests = { ...filters.value.requests, ...filterData }
    pagination.value.requests.page = 1 // 重置页码
  }

  const setApprovalsFilter = (filterData) => {
    filters.value.approvals = { ...filters.value.approvals, ...filterData }
    pagination.value.approvals.page = 1 // 重置页码
  }

  // 重置方法
  const resetFilters = () => {
    filters.value = {
      catalog: { category: 'all', search: '' },
      requests: { status: 'all', category: 'all' },
      approvals: { priority: 'all', department: 'all' }
    }
  }

  const clearCurrentService = () => {
    currentService.value = null
  }

  const clearCurrentRequest = () => {
    currentRequest.value = null
  }

  return {
    // 状态
    overview,
    statistics,
    serviceCategories,
    serviceCatalog,
    currentService,
    userRequests,
    currentRequest,
    pendingApprovals,
    requestHistory,
    loading,
    pagination,
    filters,

    // 计算属性
    completionRate,
    pendingRate,
    popularServices,
    automatedServices,
    urgentRequests,

    // 方法
    loadOverview,
    loadStatistics,
    loadServiceCategories,
    loadServiceCatalog,
    loadServiceDetail,
    submitRequest,
    loadUserRequests,
    loadRequestDetail,
    updateRequest,
    cancelUserRequest,
    loadPendingApprovals,
    processApproval,
    loadRequestHistory,
    submitRating,
    searchCatalog,
    setCatalogFilter,
    setRequestsFilter,
    setApprovalsFilter,
    resetFilters,
    clearCurrentService,
    clearCurrentRequest
  }
})
