@echo off
echo 正在安装CMDB项目依赖...
echo.

echo 1. 清理npm缓存...
npm cache clean --force

echo.
echo 2. 删除现有的node_modules文件夹...
if exist node_modules (
    rmdir /s /q node_modules
)

echo.
echo 3. 删除package-lock.json文件...
if exist package-lock.json (
    del package-lock.json
)

echo.
echo 4. 安装项目依赖...
npm install

echo.
echo 5. 验证关键依赖是否安装成功...
npm list pinia
npm list @element-plus/icons-vue
npm list axios
npm list mockjs

echo.
echo 依赖安装完成！
echo.
echo 现在可以运行以下命令启动项目：
echo npm run dev
echo.
pause
