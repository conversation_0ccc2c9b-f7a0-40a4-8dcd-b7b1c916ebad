<template>
  <div id="app">
    <el-container>
      <el-header>
        <h1>CMDB配置管理系统</h1>
      </el-header>
      <el-main>
        <el-alert
          title="系统启动成功"
          type="success"
          description="如果您看到这个页面，说明基础配置正常。"
          show-icon
          :closable="false"
        />
        
        <div style="margin-top: 20px;">
          <el-card>
            <template #header>
              <span>系统状态检查</span>
            </template>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-statistic title="Vue版本" :value="vueVersion" />
              </el-col>
              <el-col :span="8">
                <el-statistic title="Element Plus" value="已加载" />
              </el-col>
              <el-col :span="8">
                <el-statistic title="当前时间" :value="currentTime" />
              </el-col>
            </el-row>
          </el-card>
        </div>

        <div style="margin-top: 20px;">
          <el-button type="primary" @click="testFunction">测试按钮</el-button>
          <el-button type="success" @click="showMessage">显示消息</el-button>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { version } from 'vue'

const vueVersion = ref(version)
const currentTime = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const testFunction = () => {
  ElMessage.success('测试功能正常！')
}

const showMessage = () => {
  ElMessage.info('Element Plus组件工作正常！')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
  console.log('App组件已挂载')
})
</script>

<style scoped>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.el-header {
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
}

.el-header h1 {
  margin: 0;
}

.el-main {
  padding: 20px;
}
</style>
