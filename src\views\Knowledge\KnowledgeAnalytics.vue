<template>
  <div class="knowledge-analytics">
    <div class="page-header">
      <h2>知识统计分析</h2>
      <p>知识库使用情况和效果分析</p>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="loadAnalyticsData"
        />
        <el-button @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 核心指标 -->
    <div class="metrics-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="metric in coreMetrics" :key="metric.key">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon :size="32" :color="metric.color">
                  <component :is="metric.icon" />
                </el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-trend" :class="metric.trend > 0 ? 'positive' : 'negative'">
                  <el-icon><component :is="metric.trend > 0 ? 'TrendCharts' : 'Bottom'" /></el-icon>
                  {{ Math.abs(metric.trend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 知识浏览趋势 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>知识浏览趋势</span>
            </template>
            <div class="chart-container" id="viewTrendChart"></div>
          </el-card>
        </el-col>

        <!-- 知识分类分布 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>知识分类分布</span>
            </template>
            <div class="chart-container" id="categoryChart"></div>
          </el-card>
        </el-col>

        <!-- 用户活跃度 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>用户活跃度</span>
            </template>
            <div class="chart-container" id="userActivityChart"></div>
          </el-card>
        </el-col>

        <!-- 知识质量评分 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>知识质量评分分布</span>
            </template>
            <div class="chart-container" id="qualityChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计 -->
    <div class="detailed-stats">
      <el-row :gutter="20">
        <!-- 热门知识排行 -->
        <el-col :xs="24" :lg="8">
          <el-card class="stats-card">
            <template #header>
              <span>热门知识排行</span>
            </template>
            <div class="ranking-list">
              <div 
                v-for="(item, index) in popularKnowledge" 
                :key="item.id"
                class="ranking-item"
                @click="viewKnowledge(item)"
              >
                <div class="ranking-number" :class="getRankingClass(index)">
                  {{ index + 1 }}
                </div>
                <div class="ranking-content">
                  <div class="ranking-title">{{ item.title }}</div>
                  <div class="ranking-stats">
                    <span class="stat-item">
                      <el-icon><View /></el-icon>
                      {{ item.views }}
                    </span>
                    <span class="stat-item">
                      <el-icon><Star /></el-icon>
                      {{ item.rating }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 活跃作者排行 -->
        <el-col :xs="24" :lg="8">
          <el-card class="stats-card">
            <template #header>
              <span>活跃作者排行</span>
            </template>
            <div class="ranking-list">
              <div 
                v-for="(author, index) in activeAuthors" 
                :key="author.id"
                class="ranking-item"
              >
                <div class="ranking-number" :class="getRankingClass(index)">
                  {{ index + 1 }}
                </div>
                <div class="ranking-content">
                  <div class="ranking-title">{{ author.name }}</div>
                  <div class="ranking-stats">
                    <span class="stat-item">
                      <el-icon><Document /></el-icon>
                      {{ author.knowledgeCount }} 篇
                    </span>
                    <span class="stat-item">
                      <el-icon><View /></el-icon>
                      {{ author.totalViews }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 搜索热词 -->
        <el-col :xs="24" :lg="8">
          <el-card class="stats-card">
            <template #header>
              <span>搜索热词</span>
            </template>
            <div class="hot-keywords">
              <el-tag
                v-for="keyword in hotKeywords"
                :key="keyword.id"
                :size="getKeywordSize(keyword.count)"
                :type="getKeywordType(keyword.count)"
                class="keyword-tag"
              >
                {{ keyword.text }} ({{ keyword.count }})
              </el-tag>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 知识质量分析 -->
    <div class="quality-analysis">
      <el-card class="quality-card">
        <template #header>
          <span>知识质量分析</span>
        </template>
        <el-row :gutter="20">
          <el-col :xs="24" :lg="16">
            <div class="quality-metrics">
              <div class="quality-item" v-for="quality in qualityMetrics" :key="quality.key">
                <div class="quality-label">{{ quality.label }}</div>
                <div class="quality-progress">
                  <el-progress 
                    :percentage="quality.value" 
                    :color="quality.color"
                    :stroke-width="8"
                  />
                </div>
                <div class="quality-value">{{ quality.value }}%</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :lg="8">
            <div class="quality-summary">
              <div class="summary-score">
                <el-progress 
                  type="circle" 
                  :percentage="overallQuality" 
                  :width="120"
                  :stroke-width="8"
                  :color="getQualityColor(overallQuality)"
                >
                  <template #default="{ percentage }">
                    <span class="score-text">{{ percentage }}</span>
                  </template>
                </el-progress>
                <div class="score-label">综合质量评分</div>
              </div>
              <div class="quality-suggestions">
                <h4>改进建议</h4>
                <ul>
                  <li v-for="suggestion in qualitySuggestions" :key="suggestion.id">
                    {{ suggestion.text }}
                  </li>
                </ul>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 使用效果分析 -->
    <div class="effectiveness-analysis">
      <el-card class="effectiveness-card">
        <template #header>
          <span>使用效果分析</span>
        </template>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="6" v-for="effect in effectivenessMetrics" :key="effect.key">
            <div class="effect-item">
              <div class="effect-icon">
                <el-icon :size="24" :color="effect.color">
                  <component :is="effect.icon" />
                </el-icon>
              </div>
              <div class="effect-content">
                <div class="effect-value">{{ effect.value }}</div>
                <div class="effect-label">{{ effect.label }}</div>
                <div class="effect-description">{{ effect.description }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Download, TrendCharts, Bottom, View, Star, Document
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const dateRange = ref([])

// 核心指标
const coreMetrics = ref([
  {
    key: 'totalViews',
    label: '总浏览量',
    value: '125,678',
    icon: 'View',
    color: '#409EFF',
    trend: 15.6
  },
  {
    key: 'totalKnowledge',
    label: '知识总数',
    value: '1,234',
    icon: 'Document',
    color: '#67C23A',
    trend: 8.2
  },
  {
    key: 'activeUsers',
    label: '活跃用户',
    value: '2,456',
    icon: 'User',
    color: '#E6A23C',
    trend: 12.3
  },
  {
    key: 'avgRating',
    label: '平均评分',
    value: '4.6',
    icon: 'Star',
    color: '#F56C6C',
    trend: 3.1
  }
])

// 热门知识
const popularKnowledge = ref([
  {
    id: 1,
    title: '服务器性能监控配置指南',
    views: 12456,
    rating: 4.8
  },
  {
    id: 2,
    title: '网络故障排除流程',
    views: 9876,
    rating: 4.6
  },
  {
    id: 3,
    title: '数据库备份恢复手册',
    views: 8765,
    rating: 4.5
  },
  {
    id: 4,
    title: 'VPN配置详细教程',
    views: 7654,
    rating: 4.7
  },
  {
    id: 5,
    title: '邮箱设置常见问题',
    views: 6543,
    rating: 4.4
  }
])

// 活跃作者
const activeAuthors = ref([
  {
    id: 1,
    name: '张工',
    knowledgeCount: 45,
    totalViews: 23456
  },
  {
    id: 2,
    name: '李工',
    knowledgeCount: 38,
    totalViews: 19876
  },
  {
    id: 3,
    name: '王工',
    knowledgeCount: 32,
    totalViews: 16543
  },
  {
    id: 4,
    name: '赵工',
    knowledgeCount: 28,
    totalViews: 14321
  },
  {
    id: 5,
    name: '孙工',
    knowledgeCount: 25,
    totalViews: 12098
  }
])

// 搜索热词
const hotKeywords = ref([
  { id: 1, text: '服务器', count: 1234 },
  { id: 2, text: '网络', count: 987 },
  { id: 3, text: '数据库', count: 765 },
  { id: 4, text: '监控', count: 654 },
  { id: 5, text: 'VPN', count: 543 },
  { id: 6, text: '备份', count: 432 },
  { id: 7, text: '安全', count: 321 },
  { id: 8, text: '配置', count: 234 }
])

// 质量指标
const qualityMetrics = ref([
  {
    key: 'completeness',
    label: '完整性',
    value: 85,
    color: '#409EFF'
  },
  {
    key: 'accuracy',
    label: '准确性',
    value: 92,
    color: '#67C23A'
  },
  {
    key: 'timeliness',
    label: '时效性',
    value: 78,
    color: '#E6A23C'
  },
  {
    key: 'usefulness',
    label: '有用性',
    value: 88,
    color: '#F56C6C'
  }
])

const overallQuality = ref(86)

const qualitySuggestions = ref([
  { id: 1, text: '定期更新过期内容' },
  { id: 2, text: '增加实例和截图说明' },
  { id: 3, text: '完善知识分类标签' },
  { id: 4, text: '鼓励用户反馈和评价' }
])

// 使用效果指标
const effectivenessMetrics = ref([
  {
    key: 'problemSolved',
    label: '问题解决率',
    value: '78%',
    description: '通过知识库解决的问题比例',
    icon: 'CircleCheck',
    color: '#67C23A'
  },
  {
    key: 'timeReduction',
    label: '处理时间缩短',
    value: '45%',
    description: '平均问题处理时间减少',
    icon: 'Timer',
    color: '#409EFF'
  },
  {
    key: 'userSatisfaction',
    label: '用户满意度',
    value: '4.6/5',
    description: '用户对知识库的满意度评分',
    icon: 'Star',
    color: '#E6A23C'
  },
  {
    key: 'knowledgeReuse',
    label: '知识复用率',
    value: '65%',
    description: '知识被多次引用的比例',
    icon: 'Refresh',
    color: '#F56C6C'
  }
])

// 生命周期
onMounted(() => {
  loadAnalyticsData()
  initCharts()
})

// 加载分析数据
const loadAnalyticsData = () => {
  ElMessage.success('统计数据加载完成')
}

// 初始化图表
const initCharts = () => {
  // 这里可以使用 ECharts 或其他图表库初始化图表
  // 由于篇幅限制，这里只是占位
  console.log('初始化图表')
}

// 事件处理
const viewKnowledge = (item) => {
  router.push(`/knowledge/articles/${item.id}`)
}

const exportReport = () => {
  ElMessage.info('导出报告功能开发中...')
}

// 工具函数
const getRankingClass = (index) => {
  if (index === 0) return 'gold'
  if (index === 1) return 'silver'
  if (index === 2) return 'bronze'
  return 'normal'
}

const getKeywordSize = (count) => {
  if (count > 1000) return 'large'
  if (count > 500) return 'default'
  return 'small'
}

const getKeywordType = (count) => {
  if (count > 1000) return 'danger'
  if (count > 500) return 'warning'
  return 'primary'
}

const getQualityColor = (score) => {
  if (score >= 90) return '#67C23A'
  if (score >= 80) return '#409EFF'
  if (score >= 70) return '#E6A23C'
  return '#F56C6C'
}
</script>

<style scoped>
.knowledge-analytics {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 核心指标样式 */
.metrics-section {
  margin-bottom: 20px;
}

.metric-card {
  height: 120px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;
}

.metric-icon {
  margin-right: 16px;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.metric-trend.positive {
  color: #67C23A;
}

.metric-trend.negative {
  color: #F56C6C;
}

/* 图表样式 */
.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 详细统计样式 */
.detailed-stats {
  margin-bottom: 20px;
}

.stats-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ranking-list {
  max-height: 400px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.ranking-item:hover {
  background-color: #f8f9fa;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.ranking-number.gold {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: white;
}

.ranking-number.silver {
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
  color: white;
}

.ranking-number.bronze {
  background: linear-gradient(135deg, #CD7F32, #B8860B);
  color: white;
}

.ranking-number.normal {
  background: #f0f0f0;
  color: #606266;
}

.ranking-content {
  flex: 1;
  min-width: 0;
}

.ranking-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ranking-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  color: #909399;
}

/* 搜索热词样式 */
.hot-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.keyword-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.keyword-tag:hover {
  transform: scale(1.05);
}

/* 质量分析样式 */
.quality-analysis {
  margin-bottom: 20px;
}

.quality-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quality-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.quality-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.quality-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
  flex-shrink: 0;
}

.quality-progress {
  flex: 1;
}

.quality-value {
  width: 50px;
  text-align: right;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.quality-summary {
  text-align: center;
}

.summary-score {
  margin-bottom: 20px;
}

.score-text {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.score-label {
  font-size: 14px;
  color: #606266;
  margin-top: 8px;
}

.quality-suggestions h4 {
  font-size: 16px;
  color: #303133;
  margin: 0 0 12px 0;
}

.quality-suggestions ul {
  text-align: left;
  margin: 0;
  padding-left: 20px;
}

.quality-suggestions li {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 4px;
}

/* 使用效果分析样式 */
.effectiveness-analysis {
  margin-bottom: 20px;
}

.effectiveness-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.effect-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.effect-icon {
  flex-shrink: 0;
}

.effect-content {
  flex: 1;
}

.effect-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.effect-label {
  font-size: 16px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.effect-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-analytics {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .metric-value {
    font-size: 24px;
  }
  
  .chart-container {
    height: 200px;
  }
  
  .quality-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .quality-label {
    width: auto;
  }
  
  .effect-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}
</style>
