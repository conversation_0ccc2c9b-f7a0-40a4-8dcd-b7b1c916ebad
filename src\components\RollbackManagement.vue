<template>
  <div class="rollback-management">
    <el-card class="rollback-card">
      <template #header>
        <div class="card-header">
          <span>回滚管理</span>
          <div class="header-actions">
            <el-button 
              type="danger" 
              :icon="RefreshLeft"
              @click="showRollbackDialog = true"
              :disabled="!canRollback"
            >
              一键回滚
            </el-button>
            <el-button :icon="Refresh" @click="refreshData" :loading="loading">
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 回滚状态概览 -->
      <div class="rollback-status" v-if="rollbackPlan">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="status-item">
              <div class="status-label">回滚准备状态</div>
              <div class="status-value">
                <el-tag :type="rollbackPlan.hasScript ? 'success' : 'warning'">
                  {{ rollbackPlan.hasScript ? '已准备' : '未准备' }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-item">
              <div class="status-label">预计回滚时间</div>
              <div class="status-value">{{ rollbackPlan.estimatedTime }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-item">
              <div class="status-label">复杂度评估</div>
              <div class="status-value">
                <el-tag :type="getComplexityType(rollbackPlan.riskAssessment?.complexity)">
                  {{ getComplexityLabel(rollbackPlan.riskAssessment?.complexity) }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 回滚步骤 -->
      <div class="rollback-steps" v-if="rollbackPlan?.steps">
        <h3>回滚步骤</h3>
        <div class="steps-timeline">
          <div 
            v-for="(step, index) in rollbackPlan.steps" 
            :key="step.id"
            class="step-item"
            :class="{ 
              'completed': step.status === 'completed',
              'in-progress': step.status === 'in-progress',
              'failed': step.status === 'failed'
            }"
          >
            <!-- 步骤连接线 -->
            <div v-if="index < rollbackPlan.steps.length - 1" class="step-connector"></div>
            
            <!-- 步骤图标 -->
            <div class="step-icon">
              <el-icon v-if="step.status === 'completed'">
                <SuccessFilled />
              </el-icon>
              <el-icon v-else-if="step.status === 'in-progress'">
                <Loading />
              </el-icon>
              <el-icon v-else-if="step.status === 'failed'">
                <CircleCloseFilled />
              </el-icon>
              <span v-else class="step-number">{{ step.id }}</span>
            </div>

            <!-- 步骤内容 -->
            <div class="step-content">
              <div class="step-header">
                <h4>{{ step.name }}</h4>
                <div class="step-meta">
                  <el-tag 
                    v-if="step.automated" 
                    type="info" 
                    size="small"
                  >
                    自动化
                  </el-tag>
                  <el-tag 
                    v-if="step.manual" 
                    type="warning" 
                    size="small"
                  >
                    手动
                  </el-tag>
                  <span class="step-time">{{ step.estimatedTime }}</span>
                </div>
              </div>
              
              <p class="step-description">{{ step.description }}</p>
              
              <div class="step-details" v-if="step.script || step.requiresConfirmation">
                <div v-if="step.script" class="detail-item">
                  <span class="detail-label">脚本路径：</span>
                  <code class="detail-value">{{ step.script }}</code>
                </div>
                <div v-if="step.requiresConfirmation" class="detail-item">
                  <el-alert
                    title="此步骤需要人工确认"
                    type="warning"
                    :closable="false"
                    show-icon
                  />
                </div>
              </div>

              <!-- 步骤操作 -->
              <div class="step-actions" v-if="step.status === 'pending' && canExecuteStep(step)">
                <el-button 
                  type="primary" 
                  size="small"
                  @click="executeStep(step)"
                  :loading="executingSteps.includes(step.id)"
                >
                  执行步骤
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 风险评估 -->
      <div class="risk-assessment" v-if="rollbackPlan?.riskAssessment">
        <h3>风险评估</h3>
        <div class="risk-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="risk-item">
                <div class="risk-label">数据丢失风险</div>
                <el-tag :type="getRiskType(rollbackPlan.riskAssessment.dataLoss)">
                  {{ getRiskLabel(rollbackPlan.riskAssessment.dataLoss) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="risk-item">
                <div class="risk-label">预计停机时间</div>
                <span class="risk-value">{{ rollbackPlan.riskAssessment.downtime }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="risk-item">
                <div class="risk-label">操作复杂度</div>
                <el-tag :type="getComplexityType(rollbackPlan.riskAssessment.complexity)">
                  {{ getComplexityLabel(rollbackPlan.riskAssessment.complexity) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          
          <div class="dependencies" v-if="rollbackPlan.riskAssessment.dependencies">
            <h4>依赖条件</h4>
            <ul class="dependencies-list">
              <li 
                v-for="dep in rollbackPlan.riskAssessment.dependencies" 
                :key="dep"
                class="dependency-item"
              >
                <el-icon><Check /></el-icon>
                {{ dep }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 审批状态 -->
      <div class="approval-status" v-if="rollbackPlan?.approvals">
        <h3>审批状态</h3>
        <div class="approvals-list">
          <div 
            v-for="approval in rollbackPlan.approvals" 
            :key="approval.role"
            class="approval-item"
            :class="{ 'required': approval.required }"
          >
            <div class="approval-info">
              <div class="approval-role">{{ approval.role }}</div>
              <div class="approval-meta">
                <el-tag v-if="approval.required" type="danger" size="small">必需</el-tag>
                <el-tag 
                  :type="approval.approved ? 'success' : 'info'" 
                  size="small"
                >
                  {{ approval.approved ? '已批准' : '待批准' }}
                </el-tag>
              </div>
            </div>
            <div class="approval-details" v-if="approval.approved">
              <div class="approver">批准人：{{ approval.approver }}</div>
              <div class="approval-time">时间：{{ approval.approvalTime }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 验证结果 -->
      <div class="validation-results" v-if="validationResult">
        <h3>回滚验证结果</h3>
        <div class="validation-summary">
          <el-alert
            :title="validationResult.recommendation"
            :type="validationResult.overallStatus === 'passed' ? 'success' : 
                   validationResult.overallStatus === 'failed' ? 'error' : 'warning'"
            show-icon
            :closable="false"
          />
        </div>
        
        <div class="validation-checks">
          <div 
            v-for="check in validationResult.checks" 
            :key="check.name"
            class="validation-item"
            :class="{ 
              'passed': check.status === 'passed',
              'failed': check.status === 'failed',
              'warning': check.status === 'warning'
            }"
          >
            <div class="validation-icon">
              <el-icon v-if="check.status === 'passed'">
                <SuccessFilled />
              </el-icon>
              <el-icon v-else-if="check.status === 'failed'">
                <CircleCloseFilled />
              </el-icon>
              <el-icon v-else>
                <WarningFilled />
              </el-icon>
            </div>
            <div class="validation-content">
              <div class="validation-name">{{ check.name }}</div>
              <div class="validation-description">{{ check.description }}</div>
              <div class="validation-details">{{ check.details }}</div>
            </div>
            <div class="validation-status">
              <el-tag 
                :type="check.status === 'passed' ? 'success' : 
                       check.status === 'failed' ? 'danger' : 'warning'"
                size="small"
              >
                {{ check.status === 'passed' ? '通过' : 
                   check.status === 'failed' ? '失败' : '警告' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 一键回滚对话框 -->
    <el-dialog
      v-model="showRollbackDialog"
      title="一键回滚确认"
      width="600px"
      :before-close="handleRollbackDialogClose"
    >
      <div class="rollback-dialog-content">
        <el-alert
          title="警告：此操作将回滚当前发布"
          type="warning"
          description="回滚操作不可逆，请确认您了解回滚的影响和风险"
          show-icon
          :closable="false"
        />
        
        <el-form :model="rollbackForm" :rules="rollbackRules" ref="rollbackFormRef" label-width="100px">
          <el-form-item label="回滚原因" prop="reason">
            <el-input 
              v-model="rollbackForm.reason"
              type="textarea"
              :rows="4"
              placeholder="请详细说明回滚原因"
            />
          </el-form-item>
          
          <el-form-item label="审批确认" v-if="rollbackPlan?.approvals">
            <div class="approval-confirmations">
              <div 
                v-for="approval in rollbackPlan.approvals" 
                :key="approval.role"
                class="approval-confirm"
              >
                <el-checkbox 
                  v-model="approval.approved"
                  :disabled="!approval.required"
                >
                  {{ approval.role }}审批
                  <el-tag v-if="approval.required" type="danger" size="small">必需</el-tag>
                </el-checkbox>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRollbackDialog = false">取消</el-button>
          <el-button 
            type="danger" 
            @click="executeRollback"
            :loading="rollbackLoading"
          >
            确认回滚
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  RefreshLeft, Refresh, SuccessFilled, Loading, CircleCloseFilled,
  Check, WarningFilled
} from '@element-plus/icons-vue'
import { 
  getRollbackPlan, 
  executeRollbackStep, 
  executeOneClickRollback,
  validateRollbackResult
} from '@/api/releaseApi.js'

// Props
const props = defineProps({
  releaseId: {
    type: String,
    required: true
  },
  releaseStatus: {
    type: String,
    required: true
  }
})

// 响应式数据
const loading = ref(false)
const rollbackLoading = ref(false)
const showRollbackDialog = ref(false)
const executingSteps = ref([])

const rollbackPlan = ref(null)
const validationResult = ref(null)

// 回滚表单
const rollbackForm = reactive({
  reason: ''
})

const rollbackFormRef = ref(null)

// 表单验证规则
const rollbackRules = {
  reason: [
    { required: true, message: '请输入回滚原因', trigger: 'blur' },
    { min: 10, message: '回滚原因至少10个字符', trigger: 'blur' }
  ]
}

// 计算属性
const canRollback = computed(() => {
  return ['prod', 'verified'].includes(props.releaseStatus)
})

// 生命周期
onMounted(() => {
  loadRollbackPlan()
})

// 方法
const loadRollbackPlan = async () => {
  loading.value = true
  try {
    const response = await getRollbackPlan(props.releaseId)
    rollbackPlan.value = response.data
  } catch (error) {
    ElMessage.error('加载回滚计划失败')
    console.error('Load rollback plan error:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadRollbackPlan()
  if (props.releaseStatus === 'rollback') {
    validateRollback()
  }
}

const validateRollback = async () => {
  try {
    const response = await validateRollbackResult(props.releaseId)
    validationResult.value = response.data
  } catch (error) {
    console.error('Validate rollback error:', error)
  }
}

const canExecuteStep = (step) => {
  // 只有前一个步骤完成才能执行当前步骤
  const stepIndex = rollbackPlan.value.steps.findIndex(s => s.id === step.id)
  if (stepIndex === 0) return true
  
  const prevStep = rollbackPlan.value.steps[stepIndex - 1]
  return prevStep.status === 'completed'
}

const executeStep = async (step) => {
  executingSteps.value.push(step.id)
  try {
    const response = await executeRollbackStep(props.releaseId, step.id)
    step.status = 'completed'
    ElMessage.success(`步骤 "${step.name}" 执行成功`)
  } catch (error) {
    step.status = 'failed'
    ElMessage.error(`步骤 "${step.name}" 执行失败`)
  } finally {
    executingSteps.value = executingSteps.value.filter(id => id !== step.id)
  }
}

const executeRollback = async () => {
  if (!rollbackFormRef.value) return
  
  try {
    await rollbackFormRef.value.validate()
    rollbackLoading.value = true
    
    const response = await executeOneClickRollback(
      props.releaseId, 
      rollbackForm.reason,
      rollbackPlan.value.approvals
    )
    
    ElMessage.success('回滚已启动')
    showRollbackDialog.value = false
    refreshData()
  } catch (error) {
    if (error.fields) return
    ElMessage.error('回滚启动失败')
  } finally {
    rollbackLoading.value = false
  }
}

const handleRollbackDialogClose = (done) => {
  rollbackForm.reason = ''
  if (rollbackFormRef.value) {
    rollbackFormRef.value.clearValidate()
  }
  done()
}

// 工具方法
const getComplexityType = (complexity) => {
  const complexityMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return complexityMap[complexity] || 'info'
}

const getComplexityLabel = (complexity) => {
  const complexityMap = {
    'low': '低复杂度',
    'medium': '中复杂度',
    'high': '高复杂度'
  }
  return complexityMap[complexity] || complexity
}

const getRiskType = (risk) => {
  const riskMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return riskMap[risk] || 'info'
}

const getRiskLabel = (risk) => {
  const riskMap = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return riskMap[risk] || risk
}
</script>

<style scoped>
.rollback-management {
  padding: 20px;
}

.rollback-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 回滚状态 */
.rollback-status {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 24px;
}

.status-item {
  text-align: center;
}

.status-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 回滚步骤 */
.rollback-steps h3 {
  color: #333;
  margin-bottom: 20px;
}

.steps-timeline {
  position: relative;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32px;
  position: relative;
}

.step-connector {
  position: absolute;
  left: 24px;
  top: 48px;
  width: 2px;
  height: 60px;
  background-color: #e0e0e0;
  z-index: 1;
}

.step-item.completed .step-connector {
  background-color: #4CAF50;
}

.step-item.in-progress .step-connector {
  background-color: #FF9800;
}

.step-item.failed .step-connector {
  background-color: #F44336;
}

.step-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e0e0;
  color: #666;
  font-size: 20px;
  font-weight: 600;
  margin-right: 20px;
  z-index: 2;
  position: relative;
}

.step-item.completed .step-icon {
  background-color: #4CAF50;
  color: white;
}

.step-item.in-progress .step-icon {
  background-color: #FF9800;
  color: white;
  animation: pulse 2s infinite;
}

.step-item.failed .step-icon {
  background-color: #F44336;
  color: white;
}

.step-number {
  font-size: 16px;
}

.step-content {
  flex: 1;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-item.in-progress .step-content {
  border-color: #FF9800;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
}

.step-item.completed .step-content {
  border-color: #4CAF50;
  background-color: #f8fff8;
}

.step-item.failed .step-content {
  border-color: #F44336;
  background-color: #fff8f8;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.step-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.step-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-time {
  font-size: 12px;
  color: #666;
}

.step-description {
  color: #666;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.step-details {
  margin-bottom: 16px;
}

.detail-item {
  margin-bottom: 8px;
}

.detail-label {
  font-weight: 600;
  color: #666;
}

.detail-value {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.step-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 风险评估 */
.risk-assessment {
  margin-bottom: 32px;
}

.risk-assessment h3 {
  color: #333;
  margin-bottom: 16px;
}

.risk-content {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.risk-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.risk-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.risk-value {
  font-weight: 600;
  color: #333;
}

.dependencies {
  margin-top: 20px;
}

.dependencies h4 {
  color: #333;
  margin-bottom: 12px;
}

.dependencies-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dependency-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  color: #666;
}

.dependency-item .el-icon {
  color: #4CAF50;
}

/* 审批状态 */
.approval-status {
  margin-bottom: 32px;
}

.approval-status h3 {
  color: #333;
  margin-bottom: 16px;
}

.approvals-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.approval-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.approval-item.required {
  border-left: 4px solid #f56c6c;
}

.approval-info {
  flex: 1;
}

.approval-role {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.approval-meta {
  display: flex;
  gap: 8px;
}

.approval-details {
  text-align: right;
  font-size: 12px;
  color: #666;
}

/* 验证结果 */
.validation-results {
  margin-bottom: 32px;
}

.validation-results h3 {
  color: #333;
  margin-bottom: 16px;
}

.validation-summary {
  margin-bottom: 20px;
}

.validation-checks {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.validation-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.validation-item.passed {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.validation-item.failed {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.validation-item.warning {
  border-color: #e6a23c;
  background-color: #fdf6ec;
}

.validation-icon {
  margin-right: 12px;
  font-size: 20px;
}

.validation-icon .el-icon {
  color: #67c23a;
}

.validation-item.failed .validation-icon .el-icon {
  color: #f56c6c;
}

.validation-item.warning .validation-icon .el-icon {
  color: #e6a23c;
}

.validation-content {
  flex: 1;
}

.validation-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.validation-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.validation-details {
  font-size: 12px;
  color: #999;
}

.validation-status {
  margin-left: 12px;
}

/* 对话框内容 */
.rollback-dialog-content {
  padding: 20px 0;
}

.approval-confirmations {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.approval-confirm {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rollback-management {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .rollback-status .el-row {
    flex-direction: column;
  }

  .rollback-status .el-col {
    margin-bottom: 16px;
  }

  .step-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .step-icon {
    margin-bottom: 12px;
    margin-right: 0;
  }

  .step-connector {
    display: none;
  }

  .step-content {
    width: 100%;
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .risk-content .el-row {
    flex-direction: column;
  }

  .risk-content .el-col {
    margin-bottom: 16px;
  }

  .approval-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .validation-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .validation-icon {
    margin-right: 0;
  }
}
</style>
