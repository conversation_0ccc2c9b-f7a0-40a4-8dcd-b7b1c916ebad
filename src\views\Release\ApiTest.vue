<template>
  <div class="api-test">
    <el-card>
      <template #header>
        <h3>API 测试页面</h3>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <div>
          <el-button @click="testGetReleaseStatistics" :loading="loading.stats">
            测试获取发布统计
          </el-button>
          <div v-if="results.stats" class="result">
            <h4>统计结果:</h4>
            <pre>{{ JSON.stringify(results.stats, null, 2) }}</pre>
          </div>
        </div>
        
        <div>
          <el-button @click="testGetReleaseList" :loading="loading.list">
            测试获取发布列表
          </el-button>
          <div v-if="results.list" class="result">
            <h4>列表结果:</h4>
            <pre>{{ JSON.stringify(results.list, null, 2) }}</pre>
          </div>
        </div>
        
        <div>
          <el-button @click="testValidateMapping" :loading="loading.validate">
            测试变更关联验证
          </el-button>
          <div v-if="results.validate" class="result">
            <h4>验证结果:</h4>
            <pre>{{ JSON.stringify(results.validate, null, 2) }}</pre>
          </div>
        </div>
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 加载状态
const loading = reactive({
  stats: false,
  list: false,
  validate: false
})

// 测试结果
const results = reactive({
  stats: null,
  list: null,
  validate: null
})

// 测试获取发布统计
const testGetReleaseStatistics = async () => {
  loading.stats = true
  try {
    const { getReleaseStatistics } = await import('@/api/releaseApi.js')
    const response = await getReleaseStatistics()
    results.stats = response
    ElMessage.success('获取发布统计成功')
  } catch (error) {
    console.error('获取发布统计失败:', error)
    ElMessage.error('获取发布统计失败: ' + error.message)
  } finally {
    loading.stats = false
  }
}

// 测试获取发布列表
const testGetReleaseList = async () => {
  loading.list = true
  try {
    const { getReleaseList } = await import('@/api/releaseApi.js')
    const response = await getReleaseList({ page: 1, pageSize: 5 })
    results.list = response
    ElMessage.success('获取发布列表成功')
  } catch (error) {
    console.error('获取发布列表失败:', error)
    ElMessage.error('获取发布列表失败: ' + error.message)
  } finally {
    loading.list = false
  }
}

// 测试变更关联验证
const testValidateMapping = async () => {
  loading.validate = true
  try {
    const { validateReleaseChangeMapping } = await import('@/api/releaseApi.js')
    const response = await validateReleaseChangeMapping('REL-001', 'CHG-001')
    results.validate = response
    ElMessage.success('变更关联验证成功')
  } catch (error) {
    console.error('变更关联验证失败:', error)
    ElMessage.error('变更关联验证失败: ' + error.message)
  } finally {
    loading.validate = false
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
}

.result {
  margin-top: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.result h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.result pre {
  margin: 0;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
