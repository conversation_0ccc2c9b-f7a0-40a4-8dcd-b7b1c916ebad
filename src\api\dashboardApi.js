import { createApiResponse, formatDateTime } from './index.js'

/**
 * 获取仪表盘数据
 * @returns {Promise} 仪表盘数据
 */
export function getDashboardData() {
  const data = {
    // 关键指标
    metrics: [
      {
        key: 'activeTickets',
        label: '活跃工单',
        value: '156',
        change: '12%',
        trend: 'up',
        type: 'primary',
        icon: 'Document'
      },
      {
        key: 'slaCompliance',
        label: 'SLA达成率',
        value: '98.5%',
        change: '2.1%',
        trend: 'up',
        type: 'success',
        icon: 'SuccessFilled'
      },
      {
        key: 'mttr',
        label: '平均修复时间',
        value: '2.3h',
        change: '15%',
        trend: 'down',
        type: 'warning',
        icon: 'Timer'
      },
      {
        key: 'satisfaction',
        label: '用户满意度',
        value: '4.6/5',
        change: '0.2',
        trend: 'up',
        type: 'info',
        icon: 'Star'
      }
    ],

    // 服务健康状态
    services: [
      { name: 'ERP系统', status: 'healthy', statusText: '正常', uptime: '99.9%' },
      { name: '邮件服务', status: 'healthy', statusText: '正常', uptime: '99.8%' },
      { name: 'OA系统', status: 'warning', statusText: '警告', uptime: '98.5%' },
      { name: '数据库', status: 'healthy', statusText: '正常', uptime: '99.9%' },
      { name: '网络服务', status: 'healthy', statusText: '正常', uptime: '99.7%' }
    ],

    // 待处理任务
    pendingTasks: [
      {
        id: 1,
        title: '服务器CPU使用率过高',
        priority: 'high',
        priorityText: '高',
        time: '2分钟前'
      },
      {
        id: 2,
        title: '用户权限申请审批',
        priority: 'medium',
        priorityText: '中',
        time: '15分钟前'
      },
      {
        id: 3,
        title: '打印机故障报修',
        priority: 'low',
        priorityText: '低',
        time: '1小时前'
      },
      {
        id: 4,
        title: '系统变更审批',
        priority: 'medium',
        priorityText: '中',
        time: '2小时前'
      }
    ],

    // 图表数据
    charts: {
      // 工单趋势
      ticketTrend: {
        categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        series: [
          {
            name: '新建',
            data: [12, 19, 15, 22, 18, 8, 6]
          },
          {
            name: '处理中',
            data: [8, 12, 18, 15, 20, 12, 9]
          },
          {
            name: '已解决',
            data: [15, 18, 20, 25, 22, 15, 12]
          }
        ]
      },

      // SLA达成率
      slaCompliance: {
        value: 98.5,
        target: 95
      },

      // 事件分类分布
      incidentCategory: [
        { value: 35, name: '网络问题' },
        { value: 28, name: '应用故障' },
        { value: 22, name: '硬件故障' },
        { value: 15, name: '其他' }
      ]
    }
  }

  return createApiResponse(data, '获取仪表盘数据成功')
}

/**
 * 获取实时统计数据
 * @returns {Promise} 实时统计数据
 */
export function getRealTimeStats() {
  const stats = {
    timestamp: formatDateTime(),
    activeUsers: Math.floor(Math.random() * 50) + 200,
    systemLoad: Math.floor(Math.random() * 30) + 40,
    responseTime: Math.floor(Math.random() * 100) + 150,
    errorRate: (Math.random() * 2).toFixed(2)
  }

  return createApiResponse(stats, '获取实时统计成功')
}

export default {
  getDashboardData,
  getRealTimeStats
}
