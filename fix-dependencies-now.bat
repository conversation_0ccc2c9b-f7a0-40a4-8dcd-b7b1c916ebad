@echo off
echo ========================================
echo 正在修复CMDB项目依赖问题...
echo ========================================
echo.

echo [1/4] 安装 pinia (状态管理)...
npm install pinia
if %errorlevel% neq 0 (
    echo 错误: pinia 安装失败
    pause
    exit /b 1
)
echo ✓ pinia 安装成功

echo.
echo [2/4] 安装 axios (HTTP客户端)...
npm install axios
if %errorlevel% neq 0 (
    echo 错误: axios 安装失败
    pause
    exit /b 1
)
echo ✓ axios 安装成功

echo.
echo [3/4] 安装 mockjs (模拟数据)...
npm install mockjs
if %errorlevel% neq 0 (
    echo 错误: mockjs 安装失败
    pause
    exit /b 1
)
echo ✓ mockjs 安装成功

echo.
echo [4/4] 验证所有依赖...
npm list pinia axios mockjs @element-plus/icons-vue

echo.
echo ========================================
echo 依赖修复完成！
echo ========================================
echo.
echo 现在可以启动项目了：
echo npm run dev
echo.
pause
