import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { ElMessage } from 'element-plus'

// 导入要测试的组件
import RequestFulfillment from '@/views/Request/RequestFulfillment.vue'
import ServiceCatalog from '@/views/Request/ServiceCatalog.vue'
import SelfServicePortal from '@/views/Request/SelfServicePortal.vue'
import { useRequestStore } from '@/stores/request'

// Mock Element Plus 消息组件
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    }
  }
})

// Mock ECharts
vi.mock('echarts', () => ({
  init: vi.fn(() => ({
    setOption: vi.fn(),
    resize: vi.fn(),
    dispose: vi.fn()
  })),
  graphic: {
    LinearGradient: vi.fn()
  }
}))

// Mock Vue Router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  go: vi.fn(),
  back: vi.fn(),
  forward: vi.fn()
}

const mockRoute = {
  path: '/request',
  query: {},
  params: {}
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute: () => mockRoute
}))

describe('请求履约模块测试', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    vi.clearAllMocks()
  })

  describe('RequestStore 状态管理测试', () => {
    it('应该正确初始化状态', () => {
      const store = useRequestStore()
      
      expect(store.overview).toBeDefined()
      expect(store.serviceCatalog).toEqual([])
      expect(store.userRequests).toEqual([])
      expect(store.loading).toBeDefined()
    })

    it('应该正确计算完成率', () => {
      const store = useRequestStore()
      
      // 设置测试数据
      store.overview.totalRequests = 100
      store.overview.completedRequests = 80
      
      expect(store.completionRate).toBe(80)
    })

    it('应该正确计算待处理率', () => {
      const store = useRequestStore()
      
      // 设置测试数据
      store.overview.totalRequests = 100
      store.overview.pendingRequests = 20
      
      expect(store.pendingRate).toBe(20)
    })

    it('应该正确筛选热门服务', () => {
      const store = useRequestStore()
      
      // 设置测试数据
      store.serviceCatalog = [
        { id: '1', name: '服务1', popularity: 85 },
        { id: '2', name: '服务2', popularity: 60 },
        { id: '3', name: '服务3', popularity: 90 }
      ]
      
      const popularServices = store.popularServices
      expect(popularServices).toHaveLength(2)
      expect(popularServices[0].popularity).toBe(90)
      expect(popularServices[1].popularity).toBe(85)
    })
  })

  describe('RequestFulfillment 组件测试', () => {
    it('应该正确渲染页面结构', () => {
      const wrapper = mount(RequestFulfillment, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      expect(wrapper.find('.request-fulfillment').exists()).toBe(true)
      expect(wrapper.find('.page-header').exists()).toBe(true)
      expect(wrapper.find('.stats-section').exists()).toBe(true)
      expect(wrapper.find('.charts-section').exists()).toBe(true)
    })

    it('应该正确显示统计卡片', async () => {
      const store = useRequestStore()
      store.overview = {
        totalRequests: 150,
        pendingRequests: 30,
        completedRequests: 100,
        automationRate: 75
      }

      const wrapper = mount(RequestFulfillment, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      await wrapper.vm.$nextTick()

      const statCards = wrapper.findAll('.stat-card')
      expect(statCards).toHaveLength(4)
    })

    it('应该正确处理刷新数据', async () => {
      const store = useRequestStore()
      const loadOverviewSpy = vi.spyOn(store, 'loadOverview').mockResolvedValue()
      const loadStatisticsSpy = vi.spyOn(store, 'loadStatistics').mockResolvedValue()

      const wrapper = mount(RequestFulfillment, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      await wrapper.vm.refreshData()

      expect(loadOverviewSpy).toHaveBeenCalled()
      expect(loadStatisticsSpy).toHaveBeenCalled()
      expect(ElMessage.success).toHaveBeenCalledWith('数据刷新成功')
    })
  })

  describe('ServiceCatalog 组件测试', () => {
    it('应该正确渲染服务目录', () => {
      const wrapper = mount(ServiceCatalog, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      expect(wrapper.find('.service-catalog').exists()).toBe(true)
      expect(wrapper.find('.search-section').exists()).toBe(true)
      expect(wrapper.find('.services-section').exists()).toBe(true)
    })

    it('应该正确处理搜索功能', async () => {
      const store = useRequestStore()
      const searchServicesSpy = vi.spyOn(store, 'searchCatalog').mockResolvedValue()

      const wrapper = mount(ServiceCatalog, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      const searchInput = wrapper.find('input[placeholder*="搜索"]')
      await searchInput.setValue('测试搜索')
      await searchInput.trigger('input')

      // 等待防抖
      await new Promise(resolve => setTimeout(resolve, 600))

      expect(searchServicesSpy).toHaveBeenCalled()
    })

    it('应该正确处理分类筛选', async () => {
      const store = useRequestStore()
      const loadServiceCatalogSpy = vi.spyOn(store, 'loadServiceCatalog').mockResolvedValue()

      const wrapper = mount(ServiceCatalog, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      await wrapper.vm.handleCategoryChange('personal')

      expect(loadServiceCatalogSpy).toHaveBeenCalled()
    })

    it('应该正确切换视图模式', async () => {
      const wrapper = mount(ServiceCatalog, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      // 默认为网格视图
      expect(wrapper.vm.viewMode).toBe('grid')
      expect(wrapper.find('.services-grid').exists()).toBe(true)

      // 切换到列表视图
      wrapper.vm.viewMode = 'list'
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.services-list').exists()).toBe(true)
    })
  })

  describe('SelfServicePortal 组件测试', () => {
    it('应该正确渲染自助门户', () => {
      const wrapper = mount(SelfServicePortal, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      expect(wrapper.find('.self-service-portal').exists()).toBe(true)
      expect(wrapper.find('.tabs-section').exists()).toBe(true)
    })

    it('应该正确处理标签页切换', async () => {
      const wrapper = mount(SelfServicePortal, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      await wrapper.vm.handleTabChange('my-requests')
      expect(wrapper.vm.activeTab).toBe('my-requests')

      await wrapper.vm.handleTabChange('approvals')
      expect(wrapper.vm.activeTab).toBe('approvals')
    })

    it('应该正确处理快速申请', async () => {
      const wrapper = mount(SelfServicePortal, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      const testService = { id: 'test', name: '测试服务' }
      await wrapper.vm.quickApply(testService)

      expect(wrapper.vm.selectedApplyService).toEqual(testService)
      expect(wrapper.vm.showApplyDialog).toBe(true)
    })

    it('应该正确处理请求取消', async () => {
      const store = useRequestStore()
      const cancelRequestSpy = vi.spyOn(store, 'cancelUserRequest').mockResolvedValue()

      const wrapper = mount(SelfServicePortal, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      const testRequest = { id: 'test', serviceName: '测试服务' }
      
      // Mock ElMessageBox
      const mockMessageBox = {
        confirm: vi.fn().mockResolvedValue(),
        prompt: vi.fn().mockResolvedValue({ value: '测试取消原因' })
      }
      
      wrapper.vm.$messageBox = mockMessageBox

      await wrapper.vm.cancelRequest(testRequest)

      expect(cancelRequestSpy).toHaveBeenCalledWith('test', '测试取消原因')
    })
  })

  describe('API 集成测试', () => {
    it('应该正确调用获取概览数据API', async () => {
      const store = useRequestStore()
      
      // Mock API 响应
      const mockResponse = {
        code: 200,
        data: {
          totalRequests: 100,
          pendingRequests: 20,
          completedRequests: 70
        }
      }

      // 这里应该 mock 实际的 API 调用
      vi.doMock('@/api/request', () => ({
        getRequestOverview: vi.fn().mockResolvedValue(mockResponse)
      }))

      await store.loadOverview()

      expect(store.overview.totalRequests).toBe(100)
      expect(store.overview.pendingRequests).toBe(20)
      expect(store.overview.completedRequests).toBe(70)
    })

    it('应该正确处理API错误', async () => {
      const store = useRequestStore()
      
      // Mock API 错误
      vi.doMock('@/api/request', () => ({
        getRequestOverview: vi.fn().mockRejectedValue(new Error('网络错误'))
      }))

      await expect(store.loadOverview()).rejects.toThrow('网络错误')
    })
  })

  describe('业务流程测试', () => {
    it('应该正确处理服务申请流程', async () => {
      const store = useRequestStore()
      const submitRequestSpy = vi.spyOn(store, 'submitRequest').mockResolvedValue({
        id: 'new-request',
        status: 'submitted'
      })

      const requestData = {
        serviceId: 'laptop',
        serviceName: '申请笔记本电脑',
        requester: '张三',
        priority: '中'
      }

      const result = await store.submitRequest(requestData)

      expect(submitRequestSpy).toHaveBeenCalledWith(requestData)
      expect(result.status).toBe('submitted')
    })

    it('应该正确处理审批流程', async () => {
      const store = useRequestStore()
      const processApprovalSpy = vi.spyOn(store, 'processApproval').mockResolvedValue({
        id: 'test-request',
        status: 'approved'
      })

      const approvalData = {
        action: 'approved',
        comment: '同意申请'
      }

      const result = await store.processApproval('test-request', approvalData)

      expect(processApprovalSpy).toHaveBeenCalledWith('test-request', approvalData)
      expect(result.status).toBe('approved')
    })

    it('应该正确处理满意度评价', async () => {
      const store = useRequestStore()
      const submitRatingSpy = vi.spyOn(store, 'submitRating').mockResolvedValue({
        id: 'test-request',
        rating: 5
      })

      const ratingData = {
        rating: 5,
        feedback: '服务很好'
      }

      const result = await store.submitRating('test-request', ratingData)

      expect(submitRatingSpy).toHaveBeenCalledWith('test-request', ratingData)
      expect(result.rating).toBe(5)
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动端正确显示', async () => {
      // Mock 移动端视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375
      })

      const wrapper = mount(RequestFulfillment, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      // 触发 resize 事件
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()

      // 验证移动端样式是否正确应用
      expect(wrapper.find('.request-fulfillment').exists()).toBe(true)
    })
  })

  describe('错误处理测试', () => {
    it('应该正确处理网络错误', async () => {
      const store = useRequestStore()
      
      // Mock 网络错误
      vi.spyOn(store, 'loadOverview').mockRejectedValue(new Error('网络连接失败'))

      const wrapper = mount(RequestFulfillment, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      await wrapper.vm.refreshData()

      expect(ElMessage.error).toHaveBeenCalledWith('数据刷新失败')
    })

    it('应该正确处理权限错误', async () => {
      const store = useRequestStore()
      
      // Mock 权限错误
      vi.spyOn(store, 'loadPendingApprovals').mockRejectedValue(new Error('权限不足'))

      const wrapper = mount(SelfServicePortal, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter,
            $route: mockRoute
          }
        }
      })

      await wrapper.vm.loadPendingApprovals()

      expect(ElMessage.error).toHaveBeenCalledWith('加载待审批列表失败')
    })
  })
})
