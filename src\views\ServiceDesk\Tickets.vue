<template>
  <div class="tickets-page">
    <div class="page-header">
      <h2>工单管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建工单
        </el-button>
        <el-button @click="exportTickets">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-card>
        <el-form :model="searchForm" :inline="true">
          <el-form-item label="工单号">
            <el-input v-model="searchForm.ticketId" placeholder="请输入工单号" clearable style="width: 150px" />
          </el-form-item>
          <el-form-item label="标题">
            <el-input v-model="searchForm.title" placeholder="请输入标题关键词" clearable style="width: 200px" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已解决" value="resolved" />
              <el-option label="已关闭" value="closed" />
              <el-option label="超时" value="overdue" />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级">
            <el-select v-model="searchForm.priority" placeholder="请选择优先级" clearable style="width: 120px">
              <el-option label="低" value="低" />
              <el-option label="中" value="中" />
              <el-option label="高" value="高" />
              <el-option label="紧急" value="紧急" />
            </el-select>
          </el-form-item>
          <el-form-item label="处理人">
            <el-select v-model="searchForm.assignee" placeholder="请选择处理人" clearable style="width: 120px">
              <el-option label="张工" value="张工" />
              <el-option label="李工" value="李工" />
              <el-option label="王工" value="王工" />
              <el-option label="未分配" value="" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item label="SLA状态">
            <el-select v-model="searchForm.slaStatus" placeholder="SLA状态" clearable style="width: 120px">
              <el-option label="正常" value="normal" />
              <el-option label="预警" value="warning" />
              <el-option label="超时" value="overdue" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchTickets" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button @click="showAdvancedSearch = !showAdvancedSearch">
              {{ showAdvancedSearch ? '收起' : '高级搜索' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 高级搜索 -->
        <div v-show="showAdvancedSearch" class="advanced-search">
          <el-divider content-position="left">高级搜索</el-divider>
          <el-form :model="advancedSearchForm" :inline="true">
            <el-form-item label="工单类型">
              <el-select v-model="advancedSearchForm.type" placeholder="请选择类型" clearable multiple style="width: 150px">
                <el-option label="事件" value="incident" />
                <el-option label="服务请求" value="request" />
                <el-option label="咨询" value="inquiry" />
                <el-option label="变更" value="change" />
              </el-select>
            </el-form-item>
            <el-form-item label="分类">
              <el-cascader
                v-model="advancedSearchForm.category"
                :options="categoryOptions"
                placeholder="请选择分类"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="关联CI">
              <el-select v-model="advancedSearchForm.relatedCI" placeholder="选择CI" clearable style="width: 150px">
                <el-option v-for="ci in availableCIs" :key="ci.id" :label="ci.name" :value="ci.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="报告人">
              <el-input v-model="advancedSearchForm.reporter" placeholder="报告人" clearable style="width: 120px" />
            </el-form-item>
            <el-form-item label="影响用户">
              <el-input v-model="advancedSearchForm.affectedUser" placeholder="影响用户" clearable style="width: 120px" />
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 工单列表 -->
    <div class="tickets-table">
      <el-card>
        <template #header>
          <div class="table-header">
            <div class="header-left">
              <span>工单列表 ({{ filteredTickets.length }})</span>
              <el-tag v-if="selectedTickets.length > 0" type="primary" size="small">
                已选择 {{ selectedTickets.length }} 个
              </el-tag>
            </div>
            <div class="header-right">
              <el-button-group size="small">
                <el-button @click="refreshTickets" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button @click="showColumnSettings = true">
                  <el-icon><Setting /></el-icon>
                  列设置
                </el-button>
              </el-button-group>
              <el-radio-group v-model="viewMode" size="small" style="margin-left: 12px;">
                <el-radio-button label="table">
                  <el-icon><Grid /></el-icon>
                  表格
                </el-radio-button>
                <el-radio-button label="card">
                  <el-icon><Collection /></el-icon>
                  卡片
                </el-radio-button>
                <el-radio-button label="kanban">
                  <el-icon><DataBoard /></el-icon>
                  看板
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'">
          <el-table
            :data="paginatedTickets"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="工单号" width="120" sortable />
            <el-table-column prop="title" label="标题" min-width="200" />
            <el-table-column prop="type" label="类型" width="100">
              <template #default="scope">
                <el-tag :type="getTypeColor(scope.row.type)" size="small">
                  {{ getTypeText(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="100">
              <template #default="scope">
                <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
                  {{ scope.row.priority }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusColor(scope.row.status)" size="small">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="assignee" label="处理人" width="120" />
            <el-table-column prop="reporter" label="报告人" width="120" />
            <el-table-column prop="createTime" label="创建时间" width="160" sortable />
            <el-table-column prop="updateTime" label="更新时间" width="160" sortable />
            <el-table-column label="SLA" width="120">
              <template #default="scope">
                <div class="sla-progress">
                  <div class="sla-indicator" :class="getSLAStatus(scope.row.sla)">
                    {{ scope.row.sla }}%
                  </div>
                  <el-progress
                    :percentage="scope.row.sla"
                    :status="getSLAProgressStatus(scope.row.sla)"
                    :stroke-width="4"
                    :show-text="false"
                  />
                  <div class="sla-time" v-if="scope.row.remainingTime">
                    剩余: {{ scope.row.remainingTime }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="scope">
                <el-button type="primary" size="small" text @click="viewTicket(scope.row)">
                  查看
                </el-button>
                <el-button type="primary" size="small" text @click="editTicket(scope.row)">
                  编辑
                </el-button>
                <el-dropdown @command="handleCommand">
                  <el-button type="primary" size="small" text>
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'assign', row: scope.row}">分配</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'escalate', row: scope.row}">升级</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'close', row: scope.row}">关闭</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else-if="viewMode === 'card'" class="card-view">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :lg="8" v-for="ticket in paginatedTickets" :key="ticket.id">
              <el-card class="ticket-card" @click="viewTicket(ticket)">
                <div class="ticket-header">
                  <div class="ticket-id">{{ ticket.id }}</div>
                  <div class="ticket-tags">
                    <el-tag :type="getTypeColor(ticket.type)" size="small">
                      {{ getTypeText(ticket.type) }}
                    </el-tag>
                    <el-tag :type="getPriorityColor(ticket.priority)" size="small">
                      {{ ticket.priority }}
                    </el-tag>
                    <el-tag :type="getStatusColor(ticket.status)" size="small">
                      {{ ticket.status }}
                    </el-tag>
                  </div>
                </div>
                <div class="ticket-title">{{ ticket.title }}</div>
                <div class="ticket-description">{{ ticket.description }}</div>
                <div class="ticket-meta">
                  <div class="meta-item">
                    <el-icon><User /></el-icon>
                    <span>{{ ticket.assignee || '未分配' }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Clock /></el-icon>
                    <span>{{ ticket.createTime }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Message /></el-icon>
                    <span>{{ ticket.reporter }}</span>
                  </div>
                </div>
                <div class="ticket-sla">
                  <div class="sla-label">SLA进度 ({{ ticket.sla }}%)</div>
                  <el-progress :percentage="ticket.sla" :status="getSLAProgressStatus(ticket.sla)" />
                  <div class="sla-time" v-if="ticket.remainingTime">
                    剩余时间: {{ ticket.remainingTime }}
                  </div>
                </div>
                <div class="ticket-actions">
                  <el-button size="small" type="primary" @click.stop="processTicket(ticket)">处理</el-button>
                  <el-button size="small" @click.stop="assignTicket(ticket)">分配</el-button>
                  <el-dropdown @command="handleTicketCommand" @click.stop>
                    <el-button size="small">
                      更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{action: 'edit', ticket}">编辑</el-dropdown-item>
                        <el-dropdown-item :command="{action: 'escalate', ticket}">升级</el-dropdown-item>
                        <el-dropdown-item :command="{action: 'close', ticket}">关闭</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 看板视图 -->
        <div v-else-if="viewMode === 'kanban'" class="kanban-view">
          <div class="kanban-columns">
            <div v-for="column in kanbanColumns" :key="column.status" class="kanban-column">
              <div class="column-header">
                <h3>{{ column.title }}</h3>
                <el-badge :value="column.tickets.length" class="column-badge" />
              </div>
              <div class="column-content">
                <div
                  v-for="ticket in column.tickets"
                  :key="ticket.id"
                  class="kanban-card"
                  @click="viewTicket(ticket)"
                  :class="{ 'overdue': ticket.sla < 20 }"
                >
                  <div class="card-header">
                    <span class="ticket-id">{{ ticket.id }}</span>
                    <el-tag :type="getPriorityColor(ticket.priority)" size="small">
                      {{ ticket.priority }}
                    </el-tag>
                  </div>
                  <div class="card-title">{{ ticket.title }}</div>
                  <div class="card-meta">
                    <div class="assignee">
                      <el-icon><User /></el-icon>
                      {{ ticket.assignee || '未分配' }}
                    </div>
                    <div class="sla-mini">
                      SLA: {{ ticket.sla }}%
                    </div>
                  </div>
                  <div class="card-progress">
                    <el-progress
                      :percentage="ticket.sla"
                      :status="getSLAProgressStatus(ticket.sla)"
                      :stroke-width="3"
                      :show-text="false"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredTickets.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedTickets.length > 0" class="batch-actions">
      <el-card>
        <div class="batch-info">
          已选择 {{ selectedTickets.length }} 个工单
        </div>
        <div class="batch-buttons">
          <el-button type="primary" @click="batchAssign">批量分配</el-button>
          <el-button type="warning" @click="batchEscalate">批量升级</el-button>
          <el-button type="danger" @click="batchClose">批量关闭</el-button>
        </div>
      </el-card>
    </div>

    <!-- 创建工单对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建工单" width="700px" :before-close="handleCreateDialogClose">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单类型" prop="type">
              <el-select v-model="createForm.type" placeholder="请选择工单类型">
                <el-option label="事件" value="incident" />
                <el-option label="服务请求" value="request" />
                <el-option label="咨询" value="inquiry" />
                <el-option label="变更" value="change" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="createForm.priority" placeholder="请选择优先级">
                <el-option label="低" value="低" />
                <el-option label="中" value="中" />
                <el-option label="高" value="高" />
                <el-option label="紧急" value="紧急" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="标题" prop="title">
          <el-input v-model="createForm.title" placeholder="请输入工单标题" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="createForm.description" type="textarea" :rows="4" placeholder="请详细描述问题或需求" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类">
              <el-cascader v-model="createForm.category" :options="categoryOptions" placeholder="请选择分类" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理人">
              <el-select v-model="createForm.assignee" placeholder="选择处理人" clearable>
                <el-option label="张工" value="张工" />
                <el-option label="李工" value="李工" />
                <el-option label="王工" value="王工" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createTicket" :loading="creating">创建工单</el-button>
      </template>
    </el-dialog>

    <!-- 批量分配对话框 -->
    <el-dialog v-model="showBatchAssignDialog" title="批量分配" width="500px">
      <el-form :model="batchAssignForm" label-width="100px">
        <el-form-item label="处理人" required>
          <el-select v-model="batchAssignForm.assignee" placeholder="请选择处理人" style="width: 100%">
            <el-option label="张工" value="张工" />
            <el-option label="李工" value="李工" />
            <el-option label="王工" value="王工" />
          </el-select>
        </el-form-item>
        <el-form-item label="分配说明">
          <el-input v-model="batchAssignForm.note" type="textarea" :rows="3" placeholder="请输入分配说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showBatchAssignDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchAssign">确认分配</el-button>
      </template>
    </el-dialog>

    <!-- 工单详情对话框 -->
    <el-dialog v-model="showDetailDialog" :title="`工单详情 - ${currentTicket?.id}`" width="80%" top="5vh">
      <div v-if="currentTicket" class="ticket-detail">
        <el-tabs v-model="activeDetailTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="工单号">{{ currentTicket.id }}</el-descriptions-item>
              <el-descriptions-item label="标题">{{ currentTicket.title }}</el-descriptions-item>
              <el-descriptions-item label="类型">
                <el-tag :type="getTypeColor(currentTicket.type)">{{ getTypeText(currentTicket.type) }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="优先级">
                <el-tag :type="getPriorityColor(currentTicket.priority)">{{ currentTicket.priority }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusColor(currentTicket.status)">{{ currentTicket.status }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="处理人">{{ currentTicket.assignee || '未分配' }}</el-descriptions-item>
              <el-descriptions-item label="报告人">{{ currentTicket.reporter }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ currentTicket.createTime }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ currentTicket.updateTime }}</el-descriptions-item>
              <el-descriptions-item label="SLA进度">
                <el-progress :percentage="currentTicket.sla" :status="getSLAProgressStatus(currentTicket.sla)" />
              </el-descriptions-item>
            </el-descriptions>
            <div class="description-section">
              <h4>问题描述</h4>
              <p>{{ currentTicket.description }}</p>
            </div>
          </el-tab-pane>

          <el-tab-pane label="处理记录" name="history">
            <div class="history-timeline">
              <el-timeline>
                <el-timeline-item
                  v-for="record in ticketHistory"
                  :key="record.id"
                  :timestamp="record.time"
                  :type="getHistoryType(record.action)"
                >
                  <div class="history-content">
                    <div class="history-action">{{ record.action }}</div>
                    <div class="history-user">操作人: {{ record.user }}</div>
                    <div class="history-note" v-if="record.note">{{ record.note }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-tab-pane>

          <el-tab-pane label="SLA跟踪" name="sla">
            <div class="sla-tracking">
              <el-card>
                <div class="sla-overview">
                  <div class="sla-item">
                    <label>响应时间SLA:</label>
                    <el-progress :percentage="currentTicket.responseSLA || 85" status="success" />
                    <span>目标: 2小时内响应</span>
                  </div>
                  <div class="sla-item">
                    <label>解决时间SLA:</label>
                    <el-progress :percentage="currentTicket.sla" :status="getSLAProgressStatus(currentTicket.sla)" />
                    <span>目标: {{ getSLATarget(currentTicket.priority) }}</span>
                  </div>
                </div>
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="processTicket(currentTicket)">处理工单</el-button>
      </template>
    </el-dialog>

    <!-- 列设置对话框 -->
    <el-dialog v-model="showColumnSettings" title="列设置" width="400px">
      <div class="column-settings">
        <el-checkbox-group v-model="visibleColumns">
          <div v-for="column in allColumns" :key="column.key" class="column-item">
            <el-checkbox :label="column.key">{{ column.title }}</el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      <template #footer>
        <el-button @click="showColumnSettings = false">取消</el-button>
        <el-button type="primary" @click="saveColumnSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { getTicketList, updateTicket, batchUpdateTickets } from '@/api/serviceDeskApi.js'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  ticketId: '',
  title: '',
  status: '',
  priority: '',
  assignee: '',
  dateRange: null,
  slaStatus: ''
})

// 高级搜索表单
const advancedSearchForm = reactive({
  type: [],
  category: [],
  relatedCI: '',
  reporter: '',
  affectedUser: ''
})

// 对话框状态
const showCreateDialog = ref(false)
const showBatchAssignDialog = ref(false)
const showDetailDialog = ref(false)
const showColumnSettings = ref(false)
const showAdvancedSearch = ref(false)

// 视图模式
const viewMode = ref('table')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 选中的工单
const selectedTickets = ref([])

// 表单引用
const createFormRef = ref()
const creating = ref(false)

// 当前查看的工单
const currentTicket = ref(null)
const activeDetailTab = ref('basic')

// 工单数据
const tickets = ref([
  {
    id: 'INC-2025-001',
    title: '服务器CPU使用率过高告警',
    type: 'incident',
    priority: '高',
    status: '处理中',
    assignee: '张工',
    reporter: '系统监控',
    createTime: '2025-01-30 14:30',
    updateTime: '2025-01-30 16:45',
    sla: 65,
    remainingTime: '4小时30分',
    description: '生产环境Web服务器CPU使用率持续超过90%，影响系统响应速度',
    responseSLA: 95
  },
  {
    id: 'REQ-2025-002',
    title: '新员工邮箱账号申请',
    type: 'request',
    priority: '中',
    status: '待处理',
    assignee: '李工',
    reporter: '人事部',
    createTime: '2025-01-30 13:45',
    updateTime: '2025-01-30 13:45',
    sla: 85,
    remainingTime: '1天2小时',
    description: '为新入职员工张三申请企业邮箱账号，部门：技术部',
    responseSLA: 100
  },
  {
    id: 'INC-2025-003',
    title: '打印机无法正常工作',
    type: 'incident',
    priority: '低',
    status: '已解决',
    assignee: '王工',
    reporter: '财务部',
    createTime: '2025-01-30 10:20',
    updateTime: '2025-01-30 15:30',
    sla: 100,
    remainingTime: '',
    description: '办公室A区打印机无法打印，显示错误代码E001',
    responseSLA: 100
  },
  {
    id: 'CHG-2025-004',
    title: '数据库版本升级',
    type: 'change',
    priority: '高',
    status: '待处理',
    assignee: '',
    reporter: 'DBA团队',
    createTime: '2025-01-30 09:15',
    updateTime: '2025-01-30 09:15',
    sla: 15,
    remainingTime: '1小时15分',
    description: '将生产环境MySQL数据库从5.7升级到8.0版本',
    responseSLA: 80
  }
])

const loading = ref(false)

// 创建工单表单
const createForm = reactive({
  type: '',
  title: '',
  description: '',
  priority: '',
  category: [],
  assignee: ''
})

// 表单验证规则
const createRules = {
  type: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入工单描述', trigger: 'blur' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
}

// 批量分配表单
const batchAssignForm = reactive({
  assignee: '',
  note: ''
})

// 分类选项
const categoryOptions = ref([
  {
    value: 'hardware',
    label: '硬件',
    children: [
      { value: 'computer', label: '计算机' },
      { value: 'printer', label: '打印机' },
      { value: 'network', label: '网络设备' }
    ]
  },
  {
    value: 'software',
    label: '软件',
    children: [
      { value: 'os', label: '操作系统' },
      { value: 'application', label: '应用软件' },
      { value: 'security', label: '安全软件' }
    ]
  }
])

// 可用CI列表
const availableCIs = ref([
  { id: 'CI-001', name: 'Web服务器-01' },
  { id: 'CI-002', name: '数据库服务器-01' },
  { id: 'CI-003', name: '网络交换机-01' }
])

// 工单历史记录
const ticketHistory = ref([
  {
    id: 1,
    action: '工单创建',
    user: '系统监控',
    time: '2025-01-30 14:30',
    note: '自动创建告警工单'
  },
  {
    id: 2,
    action: '工单分配',
    user: '管理员',
    time: '2025-01-30 14:35',
    note: '分配给张工处理'
  },
  {
    id: 3,
    action: '开始处理',
    user: '张工',
    time: '2025-01-30 15:00',
    note: '开始排查CPU使用率过高问题'
  }
])

// 看板列配置
const kanbanColumns = computed(() => {
  const columns = [
    { status: 'pending', title: '待处理', tickets: [] },
    { status: 'processing', title: '处理中', tickets: [] },
    { status: 'resolved', title: '已解决', tickets: [] },
    { status: 'closed', title: '已关闭', tickets: [] }
  ]

  // 将工单分配到对应列
  filteredTickets.value.forEach(ticket => {
    const statusMap = {
      '待处理': 'pending',
      '处理中': 'processing',
      '已解决': 'resolved',
      '已关闭': 'closed'
    }
    const status = statusMap[ticket.status] || 'pending'
    const column = columns.find(col => col.status === status)
    if (column) {
      column.tickets.push(ticket)
    }
  })

  return columns
})

// 列设置
const allColumns = ref([
  { key: 'id', title: '工单号' },
  { key: 'title', title: '标题' },
  { key: 'type', title: '类型' },
  { key: 'priority', title: '优先级' },
  { key: 'status', title: '状态' },
  { key: 'assignee', title: '处理人' },
  { key: 'reporter', title: '报告人' },
  { key: 'createTime', title: '创建时间' },
  { key: 'updateTime', title: '更新时间' },
  { key: 'sla', title: 'SLA' }
])

const visibleColumns = ref(['id', 'title', 'type', 'priority', 'status', 'assignee', 'createTime', 'sla'])

// 过滤后的工单
const filteredTickets = computed(() => {
  return tickets.value.filter(ticket => {
    // 基础搜索条件
    const basicMatch = (!searchForm.ticketId || ticket.id.includes(searchForm.ticketId)) &&
           (!searchForm.title || ticket.title.includes(searchForm.title)) &&
           (!searchForm.status || ticket.status === searchForm.status) &&
           (!searchForm.priority || ticket.priority === searchForm.priority) &&
           (!searchForm.assignee || ticket.assignee === searchForm.assignee)

    // SLA状态筛选
    let slaMatch = true
    if (searchForm.slaStatus) {
      if (searchForm.slaStatus === 'normal' && ticket.sla < 60) slaMatch = false
      if (searchForm.slaStatus === 'warning' && (ticket.sla < 20 || ticket.sla >= 60)) slaMatch = false
      if (searchForm.slaStatus === 'overdue' && ticket.sla >= 20) slaMatch = false
    }

    // 高级搜索条件
    const advancedMatch = (!advancedSearchForm.type.length || advancedSearchForm.type.includes(ticket.type)) &&
           (!advancedSearchForm.reporter || ticket.reporter.includes(advancedSearchForm.reporter))

    return basicMatch && slaMatch && advancedMatch
  })
})

// 分页后的工单
const paginatedTickets = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTickets.value.slice(start, end)
})

// 获取类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    incident: 'danger',
    request: 'primary',
    inquiry: 'info'
  }
  return colorMap[type] || 'info'
}

// 获取类型文本
const getTypeText = (type) => {
  const textMap = {
    incident: '事件',
    request: '请求',
    inquiry: '咨询'
  }
  return textMap[type] || type
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colorMap = {
    '低': 'info',
    '中': 'warning',
    '高': 'danger',
    '紧急': 'danger'
  }
  return colorMap[priority] || 'info'
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '待处理': 'info',
    '处理中': 'warning',
    '已解决': 'success',
    '已关闭': 'info'
  }
  return colorMap[status] || 'info'
}

// 获取SLA状态
const getSLAStatus = (sla) => {
  if (sla >= 80) return 'good'
  if (sla >= 60) return 'warning'
  return 'danger'
}

// 获取SLA进度条状态
const getSLAProgressStatus = (sla) => {
  if (sla >= 80) return 'success'
  if (sla >= 60) return 'warning'
  return 'exception'
}

// 加载工单列表
const loadTickets = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ticketId: searchForm.ticketId,
      title: searchForm.title,
      status: searchForm.status,
      priority: searchForm.priority,
      assignee: searchForm.assignee
    }

    const response = await getTicketList(params)
    if (response.code === 200) {
      tickets.value = response.data.list
    }
  } catch (error) {
    console.error('加载工单列表失败:', error)
    ElMessage.error('加载工单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索工单
const searchTickets = () => {
  currentPage.value = 1
  loadTickets()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  currentPage.value = 1
  loadTickets()
}

// 刷新工单列表
const refreshTickets = () => {
  loadTickets()
}

// 导出工单
const exportTickets = () => {
  ElMessage.success('正在导出工单数据...')
  // 这里可以实现导出逻辑
}

// 查看工单详情
const viewTicket = (ticket) => {
  currentTicket.value = ticket
  showDetailDialog.value = true
  activeDetailTab.value = 'basic'
}

// 编辑工单
const editTicket = (ticket) => {
  router.push(`/service-desk/tickets/${ticket.id}/edit`)
}

// 处理工单
const processTicket = (ticket) => {
  router.push(`/service-desk/tickets/${ticket.id}/process`)
}

// 分配工单
const assignTicket = (ticket) => {
  ElMessageBox.prompt('请选择处理人', '分配工单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '处理人不能为空'
  }).then(({ value }) => {
    ticket.assignee = value
    ticket.updateTime = new Date().toLocaleString()
    ElMessage.success(`工单已分配给 ${value}`)
  }).catch(() => {
    ElMessage.info('已取消分配')
  })
}

// 创建工单
const createTicket = async () => {
  if (!createFormRef.value) return

  try {
    const valid = await createFormRef.value.validate()
    if (!valid) return

    creating.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    const newTicket = {
      id: `${createForm.type.toUpperCase()}-${Date.now()}`,
      ...createForm,
      status: '待处理',
      reporter: '当前用户',
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString(),
      sla: 100,
      remainingTime: getSLATarget(createForm.priority)
    }

    tickets.value.unshift(newTicket)

    ElMessage.success('工单创建成功')
    showCreateDialog.value = false
    resetCreateForm()
  } catch (error) {
    ElMessage.error('创建工单失败')
  } finally {
    creating.value = false
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.keys(createForm).forEach(key => {
    if (Array.isArray(createForm[key])) {
      createForm[key] = []
    } else {
      createForm[key] = ''
    }
  })
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

// 对话框关闭处理
const handleCreateDialogClose = (done) => {
  const hasContent = createForm.title || createForm.description
  if (hasContent) {
    ElMessageBox.confirm('确认关闭？未保存的内容将丢失。')
      .then(() => {
        resetCreateForm()
        done()
      })
      .catch(() => {
        // 取消关闭
      })
  } else {
    done()
  }
}

// 获取SLA目标时间
const getSLATarget = (priority) => {
  const targetMap = {
    '紧急': '4小时内解决',
    '高': '8小时内解决',
    '中': '24小时内解决',
    '低': '72小时内解决'
  }
  return targetMap[priority] || '24小时内解决'
}

// 获取历史记录类型
const getHistoryType = (action) => {
  const typeMap = {
    '工单创建': 'primary',
    '工单分配': 'success',
    '开始处理': 'warning',
    '工单关闭': 'info'
  }
  return typeMap[action] || 'primary'
}

// 处理工单命令
const handleTicketCommand = (command) => {
  const { action, ticket } = command
  switch (action) {
    case 'edit':
      editTicket(ticket)
      break
    case 'escalate':
      escalateTicket(ticket)
      break
    case 'close':
      closeTicket(ticket)
      break
  }
}

// 升级工单
const escalateTicket = (ticket) => {
  ElMessageBox.confirm(
    `确定要升级工单 "${ticket.title}" 吗？`,
    '确认升级',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 提升优先级
    const priorities = ['低', '中', '高', '紧急']
    const currentIndex = priorities.indexOf(ticket.priority)
    if (currentIndex < priorities.length - 1) {
      ticket.priority = priorities[currentIndex + 1]
    }
    ticket.updateTime = new Date().toLocaleString()
    ElMessage.success('工单已升级')
  })
}

// 关闭工单
const closeTicket = (ticket) => {
  ElMessageBox.confirm(
    `确定要关闭工单 "${ticket.title}" 吗？`,
    '确认关闭',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ticket.status = '已关闭'
    ticket.updateTime = new Date().toLocaleString()
    ElMessage.success('工单已关闭')
  })
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedTickets.value = selection
}

// 处理命令
const handleCommand = (command) => {
  const { action, row } = command
  switch (action) {
    case 'assign':
      ElMessage.info(`分配工单: ${row.id}`)
      break
    case 'escalate':
      ElMessage.info(`升级工单: ${row.id}`)
      break
    case 'close':
      ElMessage.info(`关闭工单: ${row.id}`)
      break
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadTickets()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadTickets()
}

// 组件挂载时加载数据
onMounted(() => {
  loadTickets()
})

// 批量分配
const batchAssign = () => {
  if (selectedTickets.value.length === 0) {
    ElMessage.warning('请先选择要分配的工单')
    return
  }
  showBatchAssignDialog.value = true
}

// 确认批量分配
const confirmBatchAssign = () => {
  if (!batchAssignForm.assignee) {
    ElMessage.warning('请选择处理人')
    return
  }

  selectedTickets.value.forEach(ticket => {
    ticket.assignee = batchAssignForm.assignee
    ticket.updateTime = new Date().toLocaleString()
  })

  ElMessage.success(`已将 ${selectedTickets.value.length} 个工单分配给 ${batchAssignForm.assignee}`)
  showBatchAssignDialog.value = false
  selectedTickets.value = []

  // 重置表单
  batchAssignForm.assignee = ''
  batchAssignForm.note = ''
}

// 批量升级
const batchEscalate = () => {
  if (selectedTickets.value.length === 0) {
    ElMessage.warning('请先选择要升级的工单')
    return
  }

  ElMessageBox.confirm(
    `确定要升级选中的 ${selectedTickets.value.length} 个工单吗？`,
    '确认批量升级',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const priorities = ['低', '中', '高', '紧急']
    selectedTickets.value.forEach(ticket => {
      const currentIndex = priorities.indexOf(ticket.priority)
      if (currentIndex < priorities.length - 1) {
        ticket.priority = priorities[currentIndex + 1]
      }
      ticket.updateTime = new Date().toLocaleString()
    })

    ElMessage.success(`已升级 ${selectedTickets.value.length} 个工单`)
    selectedTickets.value = []
  })
}

// 批量关闭
const batchClose = () => {
  if (selectedTickets.value.length === 0) {
    ElMessage.warning('请先选择要关闭的工单')
    return
  }

  ElMessageBox.confirm(
    `确定要关闭选中的 ${selectedTickets.value.length} 个工单吗？`,
    '确认批量关闭',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    selectedTickets.value.forEach(ticket => {
      ticket.status = '已关闭'
      ticket.updateTime = new Date().toLocaleString()
    })

    ElMessage.success(`已关闭 ${selectedTickets.value.length} 个工单`)
    selectedTickets.value = []
  })
}

// 保存列设置
const saveColumnSettings = () => {
  ElMessage.success('列设置已保存')
  showColumnSettings.value = false
  // 这里可以将设置保存到本地存储或服务器
}
</script>

<style scoped>
.tickets-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #1976D2;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-filters {
  margin-bottom: 20px;
}

.tickets-table {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 高级搜索样式 */
.advanced-search {
  margin-top: 16px;
  padding-top: 16px;
}

.sla-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sla-indicator {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

.sla-indicator.good {
  background-color: #f0f9ff;
  color: #4CAF50;
}

.sla-indicator.warning {
  background-color: #fff8e1;
  color: #FF9800;
}

.sla-indicator.danger {
  background-color: #ffebee;
  color: #F44336;
}

.sla-time {
  font-size: 10px;
  color: #666;
  text-align: center;
}

.card-view {
  min-height: 400px;
}

.ticket-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.ticket-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.ticket-id {
  font-weight: bold;
  color: #1976D2;
}

.ticket-tags {
  display: flex;
  gap: 8px;
}

.ticket-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
  line-height: 1.4;
}

.ticket-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ticket-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.ticket-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #616161;
}

.ticket-sla {
  margin-top: 12px;
}

.sla-label {
  font-size: 12px;
  color: #616161;
  margin-bottom: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.batch-actions .el-card {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.batch-info {
  margin-right: 20px;
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: 12px;
}

/* 看板视图样式 */
.kanban-view {
  min-height: 600px;
  overflow-x: auto;
}

.kanban-columns {
  display: flex;
  gap: 20px;
  min-width: 1000px;
  padding: 20px 0;
}

.kanban-column {
  flex: 1;
  min-width: 280px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.column-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.column-badge {
  margin-left: 8px;
}

.column-content {
  max-height: 500px;
  overflow-y: auto;
}

.kanban-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid #e0e0e0;
}

.kanban-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.kanban-card.overdue {
  border-left-color: #F44336;
  background: #fff5f5;
}

.kanban-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.kanban-card .ticket-id {
  font-weight: bold;
  color: #1976D2;
  font-size: 12px;
}

.kanban-card .card-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.kanban-card .card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.kanban-card .assignee {
  display: flex;
  align-items: center;
  gap: 4px;
}

.kanban-card .sla-mini {
  font-weight: 500;
}

.kanban-card .card-progress {
  margin-top: 8px;
}

/* 工单详情样式 */
.ticket-detail {
  padding: 20px 0;
}

.description-section {
  margin-top: 20px;
}

.description-section h4 {
  color: #333;
  margin-bottom: 12px;
}

.description-section p {
  color: #666;
  line-height: 1.6;
}

.history-timeline {
  padding: 20px 0;
}

.history-content {
  padding: 8px 0;
}

.history-action {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.history-user {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.history-note {
  font-size: 14px;
  color: #333;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
}

.sla-tracking {
  padding: 20px 0;
}

.sla-overview {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sla-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sla-item label {
  font-weight: 500;
  color: #333;
}

.sla-item span {
  font-size: 12px;
  color: #666;
}

/* 列设置样式 */
.column-settings {
  padding: 20px 0;
}

.column-item {
  margin-bottom: 12px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .ticket-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
