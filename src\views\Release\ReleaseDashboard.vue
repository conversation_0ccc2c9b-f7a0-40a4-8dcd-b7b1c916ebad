<template>
  <div class="release-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>发布看板</h2>
          <p>发布流程可视化监控，实时跟踪发布进度和状态</p>
        </div>
        <div class="action-section">
          <el-button :icon="Plus" type="primary" @click="showCreateDialog = true">
            新建发布
          </el-button>
          <el-button :icon="Calendar" @click="$router.push('/release/calendar')">
            发布日历
          </el-button>
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <div class="filter-item">
          <label>状态筛选：</label>
          <el-select v-model="filters.status" placeholder="全部状态" clearable @change="applyFilters">
            <el-option label="全部状态" value="" />
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <label>类型筛选：</label>
          <el-select v-model="filters.type" placeholder="全部类型" clearable @change="applyFilters">
            <el-option label="全部类型" value="" />
            <el-option
              v-for="type in typeOptions"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <label>负责人：</label>
          <el-select v-model="filters.owner" placeholder="全部负责人" clearable @change="applyFilters">
            <el-option label="全部负责人" value="" />
            <el-option
              v-for="user in mockUsers"
              :key="user.id"
              :label="user.name"
              :value="user.name"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <label>搜索：</label>
          <el-input
            v-model="filters.keyword"
            placeholder="搜索发布名称或描述"
            :prefix-icon="Search"
            @input="debounceSearch"
            style="width: 200px"
          />
        </div>
      </div>
    </el-card>

    <!-- 看板视图 -->
    <div class="kanban-view">
      <!-- 阶段状态矩阵 -->
      <el-card class="matrix-card">
        <template #header>
          <div class="card-header">
            <span>发布阶段矩阵</span>
            <div class="header-actions">
              <el-tooltip content="自动刷新" placement="top">
                <el-switch
                  v-model="autoRefresh"
                  @change="toggleAutoRefresh"
                  active-text="自动刷新"
                />
              </el-tooltip>
            </div>
          </div>
        </template>

        <div class="stage-matrix">
          <div class="matrix-header">
            <div class="release-name-col">发布名称</div>
            <div class="stage-col" v-for="stage in releaseStages" :key="stage.key">
              <div class="stage-header" :style="{ backgroundColor: stage.color }">
                {{ stage.name }}
              </div>
            </div>
            <div class="actions-col">操作</div>
          </div>

          <div class="matrix-body" v-loading="loading">
            <div
              v-for="release in filteredReleases"
              :key="release.id"
              class="matrix-row"
              :class="{ 'urgent': isUrgent(release) }"
            >
              <div class="release-info-col">
                <div class="release-name">{{ release.name }}</div>
                <div class="release-meta">
                  <el-tag :type="getTypeTagType(release.type)" size="small">
                    {{ getTypeLabel(release.type) }}
                  </el-tag>
                  <span class="owner">{{ release.owner }}</span>
                  <span class="update-time">{{ formatTime(release.updatedAt) }}</span>
                </div>
              </div>

              <div
                v-for="stage in releaseStages"
                :key="stage.key"
                class="stage-status-col"
              >
                <div
                  class="stage-status"
                  :class="getStageStatusClass(release, stage.key)"
                  @click="handleStageClick(release, stage.key)"
                >
                  <el-icon v-if="getStageStatus(release, stage.key) === 'completed'">
                    <SuccessFilled />
                  </el-icon>
                  <el-icon v-else-if="getStageStatus(release, stage.key) === 'in-progress'">
                    <Loading />
                  </el-icon>
                  <el-icon v-else-if="getStageStatus(release, stage.key) === 'failed'">
                    <CircleCloseFilled />
                  </el-icon>
                  <span v-else class="pending-dot"></span>

                  <!-- 倒计时提醒 -->
                  <div
                    v-if="getStageStatus(release, stage.key) === 'in-progress' && getTimeRemaining(release, stage.key)"
                    class="countdown"
                  >
                    {{ getTimeRemaining(release, stage.key) }}
                  </div>
                </div>
              </div>

              <div class="actions-col">
                <el-dropdown @command="handleCommand" trigger="click">
                  <el-button type="text" :icon="More" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`view-${release.id}`">
                        <el-icon><View /></el-icon> 查看详情
                      </el-dropdown-item>
                      <el-dropdown-item
                        :command="`promote-${release.id}`"
                        v-if="canPromote(release.status)"
                      >
                        <el-icon><Top /></el-icon> 推进阶段
                      </el-dropdown-item>
                      <el-dropdown-item
                        :command="`rollback-${release.id}`"
                        v-if="canRollback(release.status)"
                        divided
                      >
                        <el-icon><RefreshLeft /></el-icon> 回滚
                      </el-dropdown-item>
                      <el-dropdown-item
                        :command="`health-${release.id}`"
                        v-if="release.status === 'prod'"
                      >
                        <el-icon><Monitor /></el-icon> 健康检查
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>


    <!-- 创建发布对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建发布"
      width="600px"
      :before-close="handleCreateDialogClose"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="发布名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入发布名称" />
        </el-form-item>
        <el-form-item label="发布描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入发布描述"
          />
        </el-form-item>
        <el-form-item label="发布类型" prop="type">
          <el-select v-model="createForm.type" placeholder="请选择发布类型" style="width: 100%">
            <el-option
              v-for="type in releaseTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联变更" prop="relatedChange">
          <el-input v-model="createForm.relatedChange" placeholder="请输入关联的变更请求ID" />
        </el-form-item>
        <el-form-item label="负责人" prop="owner">
          <el-select v-model="createForm.owner" placeholder="请选择负责人" style="width: 100%">
            <el-option
              v-for="user in mockUsers"
              :key="user.id"
              :label="user.name"
              :value="user.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计划时间" prop="plannedDate">
          <el-date-picker
            v-model="createForm.plannedDate"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreateRelease" :loading="createLoading">
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Calendar, Refresh, Search, SuccessFilled, Loading, CircleCloseFilled,
  More, View, Top, RefreshLeft, Monitor
} from '@element-plus/icons-vue'
import {
  getReleaseList,
  createRelease,
  promoteReleaseStage,
  rollbackRelease as rollbackReleaseApi,
  performHealthCheck,
  releaseTypes,
  releaseStages
} from '@/api/releaseApi.js'
import { mockUsers } from '@/api/index.js'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showCreateDialog = ref(false)

const autoRefresh = ref(false)


// 发布数据
const releases = ref([])
const filteredReleases = ref([])

// 筛选器
const filters = reactive({
  status: '',
  type: '',
  owner: '',
  keyword: ''
})

// 选项数据
const statusOptions = [
  { value: 'planning', label: '计划中' },
  { value: 'dev', label: '开发' },
  { value: 'test', label: '测试' },
  { value: 'uat', label: '预生产' },
  { value: 'prod', label: '生产' },
  { value: 'verified', label: '已验证' },
  { value: 'failed', label: '失败' },
  { value: 'rollback', label: '已回滚' },
  { value: 'closed', label: '已关闭' }
]

const typeOptions = [
  { value: 'major', label: '重大发布' },
  { value: 'minor', label: '次要发布' },
  { value: 'patch', label: '补丁发布' },
  { value: 'hotfix', label: '热修复' }
]

// 创建表单
const createForm = reactive({
  name: '',
  description: '',
  type: '',
  relatedChange: '',
  owner: '',
  plannedDate: []
})

const createFormRef = ref(null)

// 表单验证规则
const createRules = {
  name: [{ required: true, message: '请输入发布名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入发布描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择发布类型', trigger: 'change' }],
  relatedChange: [{ required: true, message: '请输入关联的变更请求ID', trigger: 'blur' }],
  owner: [{ required: true, message: '请选择负责人', trigger: 'change' }],
  plannedDate: [{ required: true, message: '请选择计划时间', trigger: 'change' }]
}

// 自动刷新定时器
let refreshTimer = null

// 生命周期
onMounted(() => {
  loadReleases()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 方法
const loadReleases = async () => {
  loading.value = true
  try {
    const response = await getReleaseList({ pageSize: 100 })
    releases.value = response.data.list
    applyFilters()
  } catch (error) {
    ElMessage.error('加载发布数据失败')
    console.error('Load releases error:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadReleases()
}

// 筛选功能
const applyFilters = () => {
  let filtered = [...releases.value]

  if (filters.status) {
    filtered = filtered.filter(item => item.status === filters.status)
  }

  if (filters.type) {
    filtered = filtered.filter(item => item.type === filters.type)
  }

  if (filters.owner) {
    filtered = filtered.filter(item => item.owner.includes(filters.owner))
  }

  if (filters.keyword) {
    filtered = filtered.filter(item =>
      item.name.includes(filters.keyword) ||
      item.description.includes(filters.keyword)
    )
  }

  // 按更新时间倒序排列
  filtered.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))

  filteredReleases.value = filtered
}

// 防抖搜索
let searchTimer = null
const debounceSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    applyFilters()
  }, 300)
}

// 自动刷新
const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    refreshTimer = setInterval(() => {
      loadReleases()
    }, 30000) // 30秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

// 阶段状态相关方法
const getStageStatus = (release, stageKey) => {
  const stage = release.stages?.find(s => s.key === stageKey)
  if (!stage) {
    // 根据发布状态推断阶段状态
    const stageIndex = releaseStages.findIndex(s => s.key === stageKey)
    const currentStageIndex = releaseStages.findIndex(s => s.key === release.currentStage)

    if (stageIndex < currentStageIndex) return 'completed'
    if (stageIndex === currentStageIndex) return 'in-progress'
    return 'pending'
  }
  return stage.status
}

const getStageStatusClass = (release, stageKey) => {
  const status = getStageStatus(release, stageKey)
  return {
    'stage-completed': status === 'completed',
    'stage-in-progress': status === 'in-progress',
    'stage-failed': status === 'failed',
    'stage-pending': status === 'pending'
  }
}

const handleStageClick = (release, stageKey) => {
  // 点击阶段时显示详细信息
  const stage = release.stages?.find(s => s.key === stageKey)
  const stageInfo = releaseStages.find(s => s.key === stageKey)

  const stageStatus = getStageStatus(release, stageKey)
  let statusText = '待开始'
  let statusColor = '#909399'

  switch (stageStatus) {
    case 'completed':
      statusText = '已完成'
      statusColor = '#67c23a'
      break
    case 'in-progress':
      statusText = '进行中'
      statusColor = '#e6a23c'
      break
    case 'failed':
      statusText = '失败'
      statusColor = '#f56c6c'
      break
  }

  ElMessageBox.alert(
    `
    <div style="text-align: left;">
      <div style="margin-bottom: 16px;">
        <span style="font-weight: 600; color: #333;">阶段状态：</span>
        <span style="color: ${statusColor}; font-weight: 600;">${statusText}</span>
      </div>
      <div style="margin-bottom: 12px;">
        <span style="font-weight: 600; color: #666;">阶段描述：</span>
        <span style="color: #333;">${stageInfo.description}</span>
      </div>
      <div style="margin-bottom: 12px;">
        <span style="font-weight: 600; color: #666;">负责团队：</span>
        <span style="color: #333;">${stageInfo.responsible}</span>
      </div>
      <div style="margin-bottom: 12px;">
        <span style="font-weight: 600; color: #666;">出口准则：</span>
        <span style="color: #333;">${stageInfo.exitCriteria}</span>
      </div>
      ${stage?.startDate ? `
        <div style="margin-bottom: 12px;">
          <span style="font-weight: 600; color: #666;">开始时间：</span>
          <span style="color: #333;">${stage.startDate}</span>
        </div>
      ` : ''}
      ${stage?.endDate ? `
        <div style="margin-bottom: 12px;">
          <span style="font-weight: 600; color: #666;">结束时间：</span>
          <span style="color: #333;">${stage.endDate}</span>
        </div>
      ` : ''}
      ${stage?.notes ? `
        <div style="margin-bottom: 12px;">
          <span style="font-weight: 600; color: #666;">备注：</span>
          <span style="color: #333;">${stage.notes}</span>
        </div>
      ` : ''}
    </div>
    `,
    `${release.name} - ${stageInfo.name}阶段详情`,
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定',
      customClass: 'stage-detail-dialog'
    }
  )
}

// 时间相关方法
const getTimeRemaining = (release, stageKey) => {
  // 这里可以实现倒计时逻辑
  // 暂时返回空，实际应该根据SLA计算剩余时间
  return null
}

const isUrgent = (release) => {
  // 判断是否紧急（如即将超时）
  return release.riskLevel === 'high' || release.riskLevel === 'critical'
}

const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 操作处理
const handleCommand = (command) => {
  const [action, id] = command.split('-')
  const release = releases.value.find(r => r.id === id)

  if (!release) return

  switch (action) {
    case 'view':
      viewReleaseDetail(release)
      break
    case 'promote':
      promoteStage(release)
      break
    case 'rollback':
      rollbackRelease(release)
      break
    case 'health':
      performHealthCheckAction(release)
      break
  }
}

const viewReleaseDetail = (release) => {
  // 跳转到发布详情页面
  router.push(`/release/detail/${release.id}`)
}

const promoteStage = async (release) => {
  const stageMap = {
    'planning': 'dev',
    'dev': 'test',
    'test': 'uat',
    'uat': 'prod'
  }

  const nextStage = stageMap[release.status]
  if (!nextStage) {
    ElMessage.warning('当前阶段无法推进')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将发布 "${release.name}" 推进到下一阶段吗？`,
      '推进确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await promoteReleaseStage(release.id, nextStage)
    ElMessage.success(response.msg)
    loadReleases()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('推进阶段失败')
    console.error('Promote stage error:', error)
  }
}

const rollbackRelease = async (release) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      `确定要回滚发布 "${release.name}" 吗？请输入回滚原因：`,
      '回滚确认',
      {
        confirmButtonText: '确定回滚',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入回滚原因'
      }
    )

    const response = await rollbackReleaseApi(release.id, reason)
    ElMessage.success(response.msg)
    loadReleases()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('回滚失败')
    console.error('Rollback error:', error)
  }
}

const performHealthCheckAction = async (release) => {
  try {
    const response = await performHealthCheck(release.id)
    const result = response.data.healthCheckResult

    if (result.passed) {
      ElMessage.success('健康检查通过')
    } else {
      ElMessage.warning('健康检查发现问题，请查看详情')
    }

    // 显示检查结果详情
    const checkResults = result.checks.map(check =>
      `<p><strong>${check.name}:</strong> ${check.status === 'passed' ? '✅ 通过' : '❌ 失败'}</p>`
    ).join('')

    ElMessageBox.alert(
      `<div>${checkResults}</div>`,
      '健康检查结果',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      }
    )

    loadReleases()
  } catch (error) {
    ElMessage.error('健康检查失败')
    console.error('Health check error:', error)
  }
}

// 创建发布
const handleCreateRelease = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    createLoading.value = true

    const releaseData = {
      ...createForm,
      plannedStartDate: createForm.plannedDate[0],
      plannedEndDate: createForm.plannedDate[1]
    }

    const response = await createRelease(releaseData)
    ElMessage.success('发布创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    loadReleases()
  } catch (error) {
    if (error.fields) return
    ElMessage.error('创建发布失败')
    console.error('Create release error:', error)
  } finally {
    createLoading.value = false
  }
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    description: '',
    type: '',
    relatedChange: '',
    owner: '',
    plannedDate: []
  })
  if (createFormRef.value) {
    createFormRef.value.clearValidate()
  }
}

const handleCreateDialogClose = (done) => {
  resetCreateForm()
  done()
}



// 工具方法
const getTypeLabel = (type) => {
  const typeMap = {
    major: '重大发布',
    minor: '次要发布',
    patch: '补丁发布',
    hotfix: '热修复'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    major: 'danger',
    minor: 'warning',
    patch: 'info',
    hotfix: 'danger'
  }
  return typeMap[type] || 'info'
}

const canPromote = (status) => {
  return ['planning', 'dev', 'test', 'uat'].includes(status)
}

const canRollback = (status) => {
  return ['prod', 'verified'].includes(status)
}
</script>

<style scoped>
.release-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.title-section p {
  color: #616161;
  margin: 0;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

/* 筛选器 */
.filter-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-content {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.filter-item .el-select {
  width: 150px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 看板视图 */
.kanban-view {
  margin-bottom: 20px;
}

.matrix-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 阶段矩阵 */
.stage-matrix {
  overflow-x: auto;
}

.matrix-header {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px 0;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
}

.release-name-col {
  width: 250px;
  padding: 0 16px;
  display: flex;
  align-items: center;
}

.stage-col {
  width: 120px;
  text-align: center;
}

.stage-header {
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.actions-col {
  width: 80px;
  text-align: center;
  padding: 0 16px;
}

.matrix-body {
  min-height: 200px;
}

.matrix-row {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
}

.matrix-row:hover {
  background-color: #f8f9fa;
}

.matrix-row.urgent {
  background-color: #fff5f5;
  border-left: 4px solid #f56c6c;
}

.release-info-col {
  width: 250px;
  padding: 0 16px;
}

.release-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
}

.release-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.owner {
  color: #1976D2;
}

.update-time {
  color: #999;
}

.stage-status-col {
  width: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stage-status {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 18px;
}

.stage-status:hover {
  transform: scale(1.1);
}

.stage-completed {
  background-color: #4CAF50;
  color: white;
}

.stage-in-progress {
  background-color: #FF9800;
  color: white;
  animation: pulse 2s infinite;
}

.stage-failed {
  background-color: #F44336;
  color: white;
}

.stage-pending {
  background-color: #e9ecef;
  border: 2px solid #dee2e6;
}

.pending-dot {
  width: 8px;
  height: 8px;
  background-color: #999;
  border-radius: 50%;
}

.countdown {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: #f56c6c;
  white-space: nowrap;
  background: white;
  padding: 2px 4px;
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 日历视图 */
.calendar-view {
  margin-bottom: 20px;
}

.calendar-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.calendar-content {
  min-height: 400px;
}

.calendar-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.calendar-placeholder p {
  margin: 0 0 12px 0;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .filter-item {
    width: 100%;
    justify-content: space-between;
  }

  .filter-item .el-select {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .release-dashboard {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
  }

  .stage-matrix {
    font-size: 12px;
  }

  .release-name-col {
    width: 200px;
  }

  .stage-col {
    width: 80px;
  }

  .stage-status {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }

  .release-name {
    font-size: 12px;
  }

  .release-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 阶段详情对话框样式 */
:global(.stage-detail-dialog) {
  .el-message-box__content {
    padding: 20px !important;
  }

  .el-message-box__message {
    margin: 0 !important;
  }
}
</style>
