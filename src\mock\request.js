import Mock from 'mockjs'
import { createApiResponse, generateId, formatDateTime, getRandomDate } from '@/api/index'

// 服务分类数据
const serviceCategories = [
  {
    id: 'personal',
    name: '个人服务',
    icon: 'User',
    description: '个人账号和基础服务',
    services: ['email', 'password-reset', 'vpn', 'meeting-room']
  },
  {
    id: 'equipment',
    name: '设备服务',
    icon: 'Monitor',
    description: '硬件设备申请和维护',
    services: ['laptop', 'monitor', 'mobile-disk', 'printer']
  },
  {
    id: 'network',
    name: '网络与安全',
    icon: 'Connection',
    description: '网络访问和安全服务',
    services: ['wifi-guest', 'firewall', 'two-factor', 'ssl-cert']
  },
  {
    id: 'application',
    name: '应用与权限',
    icon: 'Key',
    description: '系统权限和应用访问',
    services: ['erp-access', 'salesforce', 'database-query', 'admin-rights']
  },
  {
    id: 'other',
    name: '其他服务',
    icon: 'Service',
    description: '培训和技术支持服务',
    services: ['it-support', 'training', 'consultation', 'maintenance']
  }
]

// 服务目录数据
const serviceCatalog = [
  {
    id: 'laptop',
    name: '申请新笔记本电脑',
    category: 'equipment',
    description: '用于日常办公，预装标准系统与软件',
    icon: 'Monitor',
    estimatedTime: '3个工作日',
    cost: '免费（首次入职）/ ¥8,000（更换）',
    approvalRequired: true,
    approvalFlow: ['部门经理', 'IT主管'],
    relatedProcess: '采购 → 配置 → 分配',
    popularity: 95,
    automationSupported: false,
    tags: ['硬件', '办公设备', '高频'],
    formFields: [
      { name: 'purpose', label: '使用用途', type: 'textarea', required: true },
      { name: 'urgency', label: '紧急程度', type: 'select', options: ['普通', '紧急', '非常紧急'], required: true },
      { name: 'budgetSource', label: '预算来源', type: 'select', options: ['部门预算', '项目预算', '其他'], required: true }
    ]
  },
  {
    id: 'password-reset',
    name: '重置密码',
    category: 'personal',
    description: '重置域账号密码，立即生效',
    icon: 'Lock',
    estimatedTime: '立即处理',
    cost: '免费',
    approvalRequired: false,
    approvalFlow: [],
    relatedProcess: '自动化脚本执行',
    popularity: 88,
    automationSupported: true,
    tags: ['账号', '自助服务', '自动化'],
    formFields: [
      { name: 'username', label: '用户名', type: 'input', required: true },
      { name: 'reason', label: '重置原因', type: 'select', options: ['忘记密码', '密码过期', '安全原因'], required: true }
    ]
  },
  {
    id: 'vpn',
    name: '开通VPN访问',
    category: 'network',
    description: '远程办公VPN访问权限',
    icon: 'Connection',
    estimatedTime: '1个工作日',
    cost: '免费',
    approvalRequired: true,
    approvalFlow: ['直属主管'],
    relatedProcess: 'API调用 → 权限配置',
    popularity: 76,
    automationSupported: true,
    tags: ['网络', '远程办公', '权限'],
    formFields: [
      { name: 'accessPeriod', label: '访问期限', type: 'daterange', required: true },
      { name: 'businessJustification', label: '业务理由', type: 'textarea', required: true }
    ]
  },
  {
    id: 'email',
    name: '申请邮箱账号',
    category: 'personal',
    description: '企业邮箱账号开通',
    icon: 'Message',
    estimatedTime: '2个工作日',
    cost: '免费',
    approvalRequired: true,
    approvalFlow: ['部门经理'],
    relatedProcess: 'Exchange API → 账号创建',
    popularity: 65,
    automationSupported: true,
    tags: ['邮箱', '账号', '通讯'],
    formFields: [
      { name: 'preferredEmail', label: '期望邮箱地址', type: 'input', required: true },
      { name: 'department', label: '所属部门', type: 'select', required: true },
      { name: 'jobTitle', label: '职位', type: 'input', required: true }
    ]
  },
  {
    id: 'erp-access',
    name: '申请ERP系统访问权限',
    category: 'application',
    description: 'ERP系统模块访问权限申请',
    icon: 'Key',
    estimatedTime: '2个工作日',
    cost: '免费',
    approvalRequired: true,
    approvalFlow: ['部门经理', '系统管理员'],
    relatedProcess: '权限审核 → 角色分配',
    popularity: 54,
    automationSupported: false,
    tags: ['ERP', '权限', '业务系统'],
    formFields: [
      { name: 'modules', label: '需要访问的模块', type: 'checkbox', required: true },
      { name: 'accessLevel', label: '权限级别', type: 'select', options: ['只读', '读写', '管理员'], required: true },
      { name: 'businessNeed', label: '业务需求说明', type: 'textarea', required: true }
    ]
  }
]

// 请求状态枚举
const requestStatuses = [
  { value: 'draft', label: '草稿', color: '#909399' },
  { value: 'submitted', label: '已提交', color: '#409EFF' },
  { value: 'pending_approval', label: '待审批', color: '#E6A23C' },
  { value: 'approved', label: '已审批', color: '#67C23A' },
  { value: 'rejected', label: '已拒绝', color: '#F56C6C' },
  { value: 'in_progress', label: '处理中', color: '#409EFF' },
  { value: 'completed', label: '已完成', color: '#67C23A' },
  { value: 'cancelled', label: '已取消', color: '#909399' }
]

// 生成模拟请求数据
function generateMockRequests(count = 50) {
  const requests = []
  const users = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const departments = ['技术部', '市场部', '财务部', '人事部', '运营部']
  
  for (let i = 0; i < count; i++) {
    const service = serviceCatalog[Math.floor(Math.random() * serviceCatalog.length)]
    const status = requestStatuses[Math.floor(Math.random() * requestStatuses.length)]
    const user = users[Math.floor(Math.random() * users.length)]
    const department = departments[Math.floor(Math.random() * departments.length)]
    
    requests.push({
      id: generateId(),
      requestNumber: `REQ-${new Date().getFullYear()}-${String(i + 1).padStart(4, '0')}`,
      serviceId: service.id,
      serviceName: service.name,
      category: service.category,
      requester: user,
      department: department,
      status: status.value,
      statusLabel: status.label,
      statusColor: status.color,
      priority: ['低', '中', '高', '紧急'][Math.floor(Math.random() * 4)],
      description: `${user}申请${service.name}`,
      createdAt: getRandomDate(30),
      updatedAt: getRandomDate(7),
      estimatedCompletion: formatDateTime(new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000)),
      approvalProgress: Math.floor(Math.random() * 100),
      automationEnabled: service.automationSupported,
      cost: service.cost,
      attachments: Math.random() > 0.7 ? ['申请表.pdf', '相关文档.docx'] : [],
      comments: Math.floor(Math.random() * 5),
      rating: Math.random() > 0.5 ? Math.floor(Math.random() * 5) + 1 : null
    })
  }
  
  return requests
}

// Mock API 响应
Mock.mock('/api/request/overview', 'get', () => {
  const requests = generateMockRequests(100)
  const today = new Date()
  const thisMonth = requests.filter(r => new Date(r.createdAt).getMonth() === today.getMonth())
  
  return createApiResponse({
    totalRequests: requests.length,
    thisMonthRequests: thisMonth.length,
    pendingRequests: requests.filter(r => ['submitted', 'pending_approval', 'in_progress'].includes(r.status)).length,
    completedRequests: requests.filter(r => r.status === 'completed').length,
    automationRate: Math.round((requests.filter(r => r.automationEnabled).length / requests.length) * 100),
    avgProcessingTime: '2.3天',
    satisfactionScore: 4.2,
    statusDistribution: requestStatuses.map(status => ({
      name: status.label,
      value: requests.filter(r => r.status === status.value).length,
      color: status.color
    })),
    categoryDistribution: serviceCategories.map(category => ({
      name: category.name,
      value: requests.filter(r => r.category === category.id).length
    })),
    monthlyTrend: Array.from({ length: 12 }, (_, i) => ({
      month: `${i + 1}月`,
      requests: Math.floor(Math.random() * 50) + 20,
      completed: Math.floor(Math.random() * 45) + 15
    }))
  })
})

Mock.mock('/api/request/statistics', 'get', () => {
  return createApiResponse({
    requestVolume: {
      today: Math.floor(Math.random() * 20) + 5,
      yesterday: Math.floor(Math.random() * 25) + 8,
      thisWeek: Math.floor(Math.random() * 100) + 50,
      lastWeek: Math.floor(Math.random() * 120) + 60,
      thisMonth: Math.floor(Math.random() * 300) + 200,
      lastMonth: Math.floor(Math.random() * 350) + 250
    },
    processingMetrics: {
      avgResponseTime: '2.5小时',
      avgResolutionTime: '1.8天',
      firstCallResolutionRate: '78%',
      slaComplianceRate: '92%'
    },
    topServices: serviceCatalog.slice(0, 5).map(service => ({
      id: service.id,
      name: service.name,
      requests: Math.floor(Math.random() * 50) + 10,
      avgTime: `${Math.floor(Math.random() * 5) + 1}天`,
      satisfaction: (Math.random() * 2 + 3).toFixed(1)
    })),
    departmentStats: [
      { department: '技术部', requests: 45, completed: 38 },
      { department: '市场部', requests: 32, completed: 28 },
      { department: '财务部', requests: 28, completed: 25 },
      { department: '人事部', requests: 22, completed: 20 },
      { department: '运营部', requests: 18, completed: 16 }
    ]
  })
})

Mock.mock('/api/request/categories', 'get', () => {
  return createApiResponse(serviceCategories)
})

Mock.mock(/\/api\/request\/catalog(\?.*)?$/, 'get', (options) => {
  const url = new URL('http://localhost' + options.url)
  const category = url.searchParams.get('category')
  const search = url.searchParams.get('search')
  const page = parseInt(url.searchParams.get('page')) || 1
  const pageSize = parseInt(url.searchParams.get('pageSize')) || 10
  
  let filteredServices = [...serviceCatalog]
  
  if (category && category !== 'all') {
    filteredServices = filteredServices.filter(service => service.category === category)
  }
  
  if (search) {
    filteredServices = filteredServices.filter(service => 
      service.name.includes(search) || 
      service.description.includes(search) ||
      service.tags.some(tag => tag.includes(search))
    )
  }
  
  const total = filteredServices.length
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = filteredServices.slice(start, end)
  
  return createApiResponse({
    list,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  })
})

Mock.mock(/\/api\/request\/catalog\/(.+)$/, 'get', (options) => {
  const serviceId = options.url.split('/').pop()
  const service = serviceCatalog.find(s => s.id === serviceId)
  
  if (!service) {
    return createApiResponse(null, '服务不存在', 404)
  }
  
  return createApiResponse({
    ...service,
    relatedServices: serviceCatalog
      .filter(s => s.category === service.category && s.id !== service.id)
      .slice(0, 3),
    recentRequests: generateMockRequests(5).filter(r => r.serviceId === serviceId)
  })
})

Mock.mock('/api/request/requests', 'post', (options) => {
  const data = JSON.parse(options.body)
  const request = {
    id: generateId(),
    requestNumber: `REQ-${new Date().getFullYear()}-${String(Date.now()).slice(-4)}`,
    ...data,
    status: 'submitted',
    statusLabel: '已提交',
    statusColor: '#409EFF',
    createdAt: formatDateTime(new Date()),
    updatedAt: formatDateTime(new Date())
  }
  
  return createApiResponse(request, '请求提交成功')
})

Mock.mock(/\/api\/request\/requests(\?.*)?$/, 'get', (options) => {
  const url = new URL('http://localhost' + options.url)
  const status = url.searchParams.get('status')
  const category = url.searchParams.get('category')
  const page = parseInt(url.searchParams.get('page')) || 1
  const pageSize = parseInt(url.searchParams.get('pageSize')) || 10

  let requests = generateMockRequests(50)

  if (status && status !== 'all') {
    requests = requests.filter(r => r.status === status)
  }

  if (category && category !== 'all') {
    requests = requests.filter(r => r.category === category)
  }

  const total = requests.length
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = requests.slice(start, end)

  return createApiResponse({
    list,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  })
})

Mock.mock(/\/api\/request\/requests\/(.+)$/, 'get', (options) => {
  const requestId = options.url.split('/').pop()
  const requests = generateMockRequests(50)
  const request = requests.find(r => r.id === requestId)

  if (!request) {
    return createApiResponse(null, '请求不存在', 404)
  }

  // 添加详细信息
  const detailedRequest = {
    ...request,
    customFields: {
      purpose: '日常办公使用',
      urgency: '一般',
      budgetSource: '部门预算'
    },
    remarks: '需要安装Office套件和开发工具',
    attachments: [
      { name: '申请表.pdf', size: 1024000, uploadTime: '2024-01-15 10:30:00' },
      { name: '需求说明.docx', size: 512000, uploadTime: '2024-01-15 10:32:00' }
    ]
  }

  return createApiResponse(detailedRequest)
})

Mock.mock('/api/request/approvals/pending', 'get', () => {
  const pendingRequests = generateMockRequests(20).filter(r => r.status === 'pending_approval')

  return createApiResponse({
    list: pendingRequests,
    total: pendingRequests.length,
    page: 1,
    pageSize: 20
  })
})

Mock.mock(/\/api\/request\/requests\/(.+)\/approve$/, 'post', (options) => {
  const requestId = options.url.split('/')[4]
  const data = JSON.parse(options.body)

  return createApiResponse({
    id: requestId,
    action: data.action,
    comment: data.comment,
    processedAt: formatDateTime(new Date())
  }, '审批处理成功')
})

Mock.mock(/\/api\/request\/requests\/(.+)\/cancel$/, 'put', (options) => {
  const requestId = options.url.split('/')[4]
  const data = JSON.parse(options.body)

  return createApiResponse({
    id: requestId,
    reason: data.reason,
    cancelledAt: formatDateTime(new Date())
  }, '请求取消成功')
})

Mock.mock(/\/api\/request\/requests\/(.+)\/rating$/, 'post', (options) => {
  const requestId = options.url.split('/')[4]
  const data = JSON.parse(options.body)

  return createApiResponse({
    id: requestId,
    rating: data.rating,
    feedback: data.feedback,
    submittedAt: formatDateTime(new Date())
  }, '评价提交成功')
})

Mock.mock('/api/request/catalog/popular', 'get', () => {
  const popularServices = serviceCatalog
    .filter(service => service.popularity > 70)
    .sort((a, b) => b.popularity - a.popularity)
    .slice(0, 8)

  return createApiResponse(popularServices)
})

Mock.mock(/\/api\/request\/catalog\/search(\?.*)?$/, 'get', (options) => {
  const url = new URL('http://localhost' + options.url)
  const search = url.searchParams.get('search') || ''
  const category = url.searchParams.get('category')

  let filteredServices = serviceCatalog.filter(service =>
    service.name.includes(search) ||
    service.description.includes(search) ||
    service.tags.some(tag => tag.includes(search))
  )

  if (category && category !== 'all') {
    filteredServices = filteredServices.filter(service => service.category === category)
  }

  return createApiResponse({
    list: filteredServices,
    total: filteredServices.length
  })
})

export default {
  serviceCategories,
  serviceCatalog,
  requestStatuses,
  generateMockRequests
}
