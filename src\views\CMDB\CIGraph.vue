<template>
  <div class="ci-graph">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Link /></el-icon>
            配置项图谱
          </h1>
          <p class="page-description">可视化展示配置项之间的关系和依赖，支持多维度分析和探索</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshGraph">
            <el-icon><Refresh /></el-icon>
            刷新图谱
          </el-button>
          <el-button @click="showLayoutDialog = true">
            <el-icon><Grid /></el-icon>
            布局设置
          </el-button>
          <el-button @click="exportGraph">
            <el-icon><Download /></el-icon>
            导出图谱
          </el-button>
          <el-button @click="showFilterDialog = true">
            <el-icon><Search /></el-icon>
            筛选条件
          </el-button>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="graph-toolbar">
      <el-card>
        <div class="toolbar-content">
          <!-- 视图控制 -->
          <div class="view-controls">
            <el-button-group>
              <el-button
                :type="viewMode === 'dependency' ? 'primary' : ''"
                @click="setViewMode('dependency')"
              >
                <el-icon><Link /></el-icon>
                依赖视图
              </el-button>
              <el-button
                :type="viewMode === 'impact' ? 'primary' : ''"
                @click="setViewMode('impact')"
              >
                <el-icon><WarningFilled /></el-icon>
                影响分析
              </el-button>
              <el-button
                :type="viewMode === 'service' ? 'primary' : ''"
                @click="setViewMode('service')"
              >
                <el-icon><Tools /></el-icon>
                服务链路
              </el-button>
              <el-button
                :type="viewMode === 'business' ? 'primary' : ''"
                @click="setViewMode('business')"
              >
                <el-icon><Grid /></el-icon>
                业务视图
              </el-button>
            </el-button-group>
          </div>

          <!-- 搜索和筛选 -->
          <div class="search-controls">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索配置项..."
              style="width: 200px;"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select
              v-model="selectedCIType"
              placeholder="CI类型"
              style="width: 120px;"
              @change="handleTypeFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="服务器" value="server" />
              <el-option label="应用" value="application" />
              <el-option label="数据库" value="database" />
              <el-option label="网络设备" value="network" />
              <el-option label="存储" value="storage" />
            </el-select>

            <el-select
              v-model="selectedStatus"
              placeholder="状态"
              style="width: 100px;"
              @change="handleStatusFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="正常" value="normal" />
              <el-option label="告警" value="warning" />
              <el-option label="故障" value="error" />
              <el-option label="离线" value="offline" />
            </el-select>
          </div>

          <!-- 图谱控制 -->
          <div class="graph-controls">
            <el-button-group>
              <el-button @click="zoomIn">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
              <el-button @click="zoomOut">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetZoom">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-button-group>
            
            <el-button @click="toggleFullscreen">
              <el-icon><FullScreen /></el-icon>
              全屏
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 图谱展示区域 -->
        <el-col :span="18">
          <el-card class="graph-container">
            <div class="graph-canvas" ref="graphCanvas" :class="{ 'fullscreen': isFullscreen }">
              <!-- 图谱渲染区域 -->
              <div class="graph-viewport" :style="viewportStyle">
                <!-- 配置项节点 -->
                <div
                  v-for="node in visibleNodes"
                  :key="node.id"
                  class="ci-node"
                  :class="[
                    `node-${node.type}`,
                    `status-${node.status}`,
                    {
                      'node-selected': selectedNode?.id === node.id,
                      'node-highlighted': highlightedNodes.includes(node.id),
                      'node-dimmed': isDimmed && !highlightedNodes.includes(node.id)
                    }
                  ]"
                  :style="getNodeStyle(node)"
                  @click="selectNode(node)"
                  @mouseenter="handleNodeHover(node, true)"
                  @mouseleave="handleNodeHover(node, false)"
                >
                  <div class="node-icon">
                    <el-icon :size="getNodeSize(node)">
                      <component :is="getNodeIcon(node.type)" />
                    </el-icon>
                  </div>
                  <div class="node-label">{{ node.name }}</div>
                  <div class="node-status-indicator" :class="`status-${node.status}`"></div>
                  
                  <!-- 节点统计信息 -->
                  <div class="node-stats" v-if="node.stats">
                    <div class="stat-item" v-for="stat in node.stats" :key="stat.key">
                      <span class="stat-value">{{ stat.value }}</span>
                      <span class="stat-label">{{ stat.label }}</span>
                    </div>
                  </div>
                </div>

                <!-- 关系连线 -->
                <svg class="graph-connections" :width="canvasWidth" :height="canvasHeight">
                  <defs>
                    <!-- 箭头标记 -->
                    <marker
                      v-for="type in relationTypes"
                      :key="`arrow-${type}`"
                      :id="`arrow-${type}`"
                      markerWidth="10"
                      markerHeight="10"
                      refX="8"
                      refY="3"
                      orient="auto"
                      markerUnits="strokeWidth"
                    >
                      <path d="M0,0 L0,6 L9,3 z" :fill="getRelationColor(type)" />
                    </marker>
                  </defs>

                  <!-- 关系线 -->
                  <path
                    v-for="relation in visibleRelations"
                    :key="relation.id"
                    :d="relation.path"
                    :class="[
                      `relation-${relation.type}`,
                      {
                        'relation-highlighted': highlightedRelations.includes(relation.id),
                        'relation-dimmed': isDimmed && !highlightedRelations.includes(relation.id)
                      }
                    ]"
                    :marker-end="`url(#arrow-${relation.type})`"
                    :stroke="getRelationColor(relation.type)"
                    :stroke-width="relation.highlighted ? 3 : 2"
                    fill="none"
                  />

                  <!-- 关系标签 -->
                  <text
                    v-for="relation in visibleRelations"
                    :key="`label-${relation.id}`"
                    :x="relation.labelX"
                    :y="relation.labelY"
                    class="relation-label"
                    v-if="relation.label && showRelationLabels"
                  >
                    {{ relation.label }}
                  </text>
                </svg>

                <!-- 加载状态 -->
                <div class="graph-loading" v-if="loading">
                  <el-icon class="is-loading" :size="48"><Loading /></el-icon>
                  <p>正在加载图谱数据...</p>
                </div>

                <!-- 空状态 -->
                <div class="graph-empty" v-if="!loading && visibleNodes.length === 0">
                  <el-icon :size="64"><Link /></el-icon>
                  <h3>暂无图谱数据</h3>
                  <p>请调整筛选条件或刷新数据</p>
                  <el-button type="primary" @click="loadSampleData">加载示例数据</el-button>
                </div>
              </div>

              <!-- 缩放控制 -->
              <div class="zoom-controls">
                <el-button-group>
                  <el-button size="small" @click="zoomIn">
                    <el-icon><Plus /></el-icon>
                  </el-button>
                  <el-button size="small" @click="zoomOut">
                    <el-icon><Minus /></el-icon>
                  </el-button>
                  <el-button size="small" @click="resetZoom">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-button-group>
                <div class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</div>
              </div>

              <!-- 图例 -->
              <div class="graph-legend" v-if="showLegend">
                <div class="legend-title">图例</div>
                <div class="legend-items">
                  <div class="legend-item" v-for="type in ciTypes" :key="type.key">
                    <el-icon :size="16" :color="type.color">
                      <component :is="type.icon" />
                    </el-icon>
                    <span>{{ type.label }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 侧边栏信息面板 -->
        <el-col :span="6">
          <div class="info-panel">
            <!-- 选中节点详情 -->
            <el-card v-if="selectedNode" class="node-details">
              <template #header>
                <div class="card-header">
                  <span>{{ selectedNode.name }}</span>
                  <el-button type="text" size="small" @click="clearSelection">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </template>
              
              <div class="node-info">
                <div class="info-item">
                  <label>类型:</label>
                  <span>{{ getTypeText(selectedNode.type) }}</span>
                </div>
                <div class="info-item">
                  <label>状态:</label>
                  <el-tag :type="getStatusType(selectedNode.status)">
                    {{ getStatusText(selectedNode.status) }}
                  </el-tag>
                </div>
                <div class="info-item" v-if="selectedNode.ip">
                  <label>IP地址:</label>
                  <span>{{ selectedNode.ip }}</span>
                </div>
                <div class="info-item" v-if="selectedNode.owner">
                  <label>负责人:</label>
                  <span>{{ selectedNode.owner }}</span>
                </div>
                <div class="info-item" v-if="selectedNode.department">
                  <label>部门:</label>
                  <span>{{ selectedNode.department }}</span>
                </div>
                <div class="info-item">
                  <label>更新时间:</label>
                  <span>{{ selectedNode.updateTime }}</span>
                </div>
              </div>

              <div class="node-actions">
                <el-button size="small" @click="viewNodeDetails">查看详情</el-button>
                <el-button size="small" @click="analyzeImpact">影响分析</el-button>
                <el-button size="small" @click="showRelatedNodes">关联节点</el-button>
              </div>
            </el-card>

            <!-- 图谱统计 -->
            <el-card class="graph-stats">
              <template #header>
                <span>图谱统计</span>
              </template>
              
              <div class="stats-list">
                <div class="stat-item">
                  <span class="stat-label">节点总数:</span>
                  <span class="stat-value">{{ graphStats.totalNodes }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">关系总数:</span>
                  <span class="stat-value">{{ graphStats.totalRelations }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">显示节点:</span>
                  <span class="stat-value">{{ visibleNodes.length }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">显示关系:</span>
                  <span class="stat-value">{{ visibleRelations.length }}</span>
                </div>
              </div>
            </el-card>

            <!-- 快速操作 -->
            <el-card class="quick-actions">
              <template #header>
                <span>快速操作</span>
              </template>
              
              <div class="action-buttons">
                <el-button size="small" @click="centerGraph">居中显示</el-button>
                <el-button size="small" @click="fitToScreen">适应屏幕</el-button>
                <el-button size="small" @click="toggleLegend">
                  {{ showLegend ? '隐藏' : '显示' }}图例
                </el-button>
                <el-button size="small" @click="toggleRelationLabels">
                  {{ showRelationLabels ? '隐藏' : '显示' }}标签
                </el-button>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 布局设置对话框 -->
    <el-dialog
      v-model="showLayoutDialog"
      title="布局设置"
      width="500px"
      @close="resetLayoutSettings"
    >
      <el-form :model="layoutSettings" label-width="120px">
        <el-form-item label="布局算法">
          <el-select v-model="layoutSettings.algorithm" style="width: 100%;">
            <el-option label="力导向布局" value="force" />
            <el-option label="层次布局" value="hierarchical" />
            <el-option label="圆形布局" value="circular" />
            <el-option label="网格布局" value="grid" />
          </el-select>
        </el-form-item>

        <el-form-item label="节点间距">
          <el-slider
            v-model="layoutSettings.nodeSpacing"
            :min="50"
            :max="300"
            :step="10"
            show-input
          />
        </el-form-item>

        <el-form-item label="层级间距">
          <el-slider
            v-model="layoutSettings.levelSpacing"
            :min="100"
            :max="500"
            :step="20"
            show-input
          />
        </el-form-item>

        <el-form-item label="动画效果">
          <el-switch v-model="layoutSettings.animation" />
        </el-form-item>

        <el-form-item label="自动适应">
          <el-switch v-model="layoutSettings.autoFit" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showLayoutDialog = false">取消</el-button>
          <el-button @click="resetLayoutSettings">重置</el-button>
          <el-button type="primary" @click="applyLayoutSettings">
            应用设置
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 筛选条件对话框 -->
    <el-dialog
      v-model="showFilterDialog"
      title="筛选条件"
      width="600px"
      @close="resetFilterSettings"
    >
      <el-form :model="filterSettings" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="CI类型">
              <el-checkbox-group v-model="filterSettings.types">
                <el-checkbox
                  v-for="type in ciTypes"
                  :key="type.key"
                  :label="type.key"
                >
                  {{ type.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="状态">
              <el-checkbox-group v-model="filterSettings.statuses">
                <el-checkbox label="normal">正常</el-checkbox>
                <el-checkbox label="warning">告警</el-checkbox>
                <el-checkbox label="error">故障</el-checkbox>
                <el-checkbox label="offline">离线</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="关系类型">
          <el-checkbox-group v-model="filterSettings.relationTypes">
            <el-checkbox label="depends">依赖</el-checkbox>
            <el-checkbox label="connects">连接</el-checkbox>
            <el-checkbox label="hosts">托管</el-checkbox>
            <el-checkbox label="uses">使用</el-checkbox>
            <el-checkbox label="manages">管理</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="更新时间">
          <el-date-picker
            v-model="filterSettings.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="关键词">
          <el-input
            v-model="filterSettings.keyword"
            placeholder="输入关键词搜索..."
            clearable
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showFilterDialog = false">取消</el-button>
          <el-button @click="resetFilterSettings">重置</el-button>
          <el-button type="primary" @click="applyFilterSettings">
            应用筛选
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const selectedCIType = ref('')
const selectedStatus = ref('')
const viewMode = ref('dependency')
const selectedNode = ref(null)
const hoveredNode = ref(null)
const highlightedNodes = ref([])
const highlightedRelations = ref([])
const isDimmed = ref(false)
const zoomLevel = ref(1)
const panX = ref(0)
const panY = ref(0)
const canvasWidth = ref(1200)
const canvasHeight = ref(800)
const isFullscreen = ref(false)
const showLegend = ref(true)
const showRelationLabels = ref(true)
const showLayoutDialog = ref(false)
const showFilterDialog = ref(false)

// 布局设置
const layoutSettings = reactive({
  algorithm: 'force',
  nodeSpacing: 150,
  levelSpacing: 200,
  animation: true,
  autoFit: true
})

// 筛选设置
const filterSettings = reactive({
  types: ['server', 'application', 'database', 'network', 'storage'],
  statuses: ['normal', 'warning', 'error', 'offline'],
  relationTypes: ['depends', 'connects', 'hosts', 'uses', 'manages'],
  dateRange: [],
  keyword: ''
})

// 图谱数据
const nodes = ref([])
const relations = ref([])

// 图谱统计
const graphStats = reactive({
  totalNodes: 0,
  totalRelations: 0,
  nodesByType: {},
  relationsByType: {}
})

// CI类型配置
const ciTypes = ref([
  { key: 'server', label: '服务器', icon: 'Monitor', color: '#4CAF50' },
  { key: 'application', label: '应用系统', icon: 'Grid', color: '#2196F3' },
  { key: 'database', label: '数据库', icon: 'Coin', color: '#FF9800' },
  { key: 'network', label: '网络设备', icon: 'Connection', color: '#9C27B0' },
  { key: 'storage', label: '存储设备', icon: 'Box', color: '#607D8B' }
])

// 关系类型
const relationTypes = ref(['depends', 'connects', 'hosts', 'uses', 'manages'])

// 计算属性
const visibleNodes = computed(() => {
  return nodes.value.filter(node => {
    if (selectedCIType.value && node.type !== selectedCIType.value) return false
    if (selectedStatus.value && node.status !== selectedStatus.value) return false
    if (searchKeyword.value && !node.name.toLowerCase().includes(searchKeyword.value.toLowerCase())) return false
    return true
  })
})

const visibleRelations = computed(() => {
  const visibleNodeIds = new Set(visibleNodes.value.map(n => n.id))
  return relations.value.filter(relation =>
    visibleNodeIds.has(relation.source) && visibleNodeIds.has(relation.target)
  )
})

const viewportStyle = computed(() => ({
  transform: `scale(${zoomLevel.value}) translate(${panX.value}px, ${panY.value}px)`,
  transformOrigin: 'center center'
}))

// 方法
const refreshGraph = async () => {
  loading.value = true
  try {
    await loadGraphData()
    ElMessage.success('图谱数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadGraphData = async () => {
  // 模拟加载数据
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 生成示例数据
  nodes.value = generateSampleNodes()
  relations.value = generateSampleRelations()

  updateGraphStats()
  calculateNodePositions()
}

const generateSampleNodes = () => {
  const sampleNodes = [
    { id: 'web-01', name: 'Web服务器-01', type: 'server', status: 'normal', ip: '************', owner: '张三', department: 'IT部门', updateTime: '2024-01-15 10:30:00' },
    { id: 'app-01', name: '订单系统', type: 'application', status: 'normal', owner: '李四', department: '业务部门', updateTime: '2024-01-15 09:15:00' },
    { id: 'db-01', name: 'MySQL主库', type: 'database', status: 'warning', ip: '************', owner: '王五', department: 'IT部门', updateTime: '2024-01-15 11:45:00' },
    { id: 'lb-01', name: '负载均衡器', type: 'network', status: 'normal', ip: '***********', owner: '赵六', department: 'IT部门', updateTime: '2024-01-15 08:20:00' },
    { id: 'storage-01', name: '存储阵列', type: 'storage', status: 'normal', owner: '钱七', department: 'IT部门', updateTime: '2024-01-15 12:10:00' }
  ]

  return sampleNodes.map((node, index) => ({
    ...node,
    x: 200 + (index % 3) * 300,
    y: 150 + Math.floor(index / 3) * 200,
    layer: Math.floor(index / 3) + 1
  }))
}

const generateSampleRelations = () => {
  return [
    { id: 'rel-1', source: 'lb-01', target: 'web-01', type: 'connects', label: '负载均衡' },
    { id: 'rel-2', source: 'web-01', target: 'app-01', type: 'hosts', label: '托管' },
    { id: 'rel-3', source: 'app-01', target: 'db-01', type: 'uses', label: '使用' },
    { id: 'rel-4', source: 'db-01', target: 'storage-01', type: 'depends', label: '依赖' }
  ]
}

const updateGraphStats = () => {
  graphStats.totalNodes = nodes.value.length
  graphStats.totalRelations = relations.value.length

  // 按类型统计节点
  graphStats.nodesByType = nodes.value.reduce((acc, node) => {
    acc[node.type] = (acc[node.type] || 0) + 1
    return acc
  }, {})

  // 按类型统计关系
  graphStats.relationsByType = relations.value.reduce((acc, relation) => {
    acc[relation.type] = (acc[relation.type] || 0) + 1
    return acc
  }, {})
}

const calculateNodePositions = () => {
  // 简单的力导向布局算法
  const centerX = canvasWidth.value / 2
  const centerY = canvasHeight.value / 2

  nodes.value.forEach((node, index) => {
    if (!node.x || !node.y) {
      const angle = (index / nodes.value.length) * 2 * Math.PI
      const radius = Math.min(centerX, centerY) * 0.6
      node.x = centerX + Math.cos(angle) * radius
      node.y = centerY + Math.sin(angle) * radius
    }
  })

  // 计算关系线路径
  relations.value.forEach(relation => {
    const sourceNode = nodes.value.find(n => n.id === relation.source)
    const targetNode = nodes.value.find(n => n.id === relation.target)

    if (sourceNode && targetNode) {
      relation.x1 = sourceNode.x + 30
      relation.y1 = sourceNode.y + 30
      relation.x2 = targetNode.x + 30
      relation.y2 = targetNode.y + 30
      relation.labelX = (relation.x1 + relation.x2) / 2
      relation.labelY = (relation.y1 + relation.y2) / 2

      // 生成SVG路径
      relation.path = `M ${relation.x1} ${relation.y1} L ${relation.x2} ${relation.y2}`
    }
  })
}

const setViewMode = (mode) => {
  viewMode.value = mode
  ElMessage.info(`切换到${getViewModeText(mode)}`)
}

const getViewModeText = (mode) => {
  const modes = {
    dependency: '依赖视图',
    impact: '影响分析',
    service: '服务链路',
    business: '业务视图'
  }
  return modes[mode] || mode
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleTypeFilter = () => {
  // 筛选逻辑已在计算属性中实现
}

const handleStatusFilter = () => {
  // 筛选逻辑已在计算属性中实现
}

const selectNode = (node) => {
  selectedNode.value = node
  highlightRelatedNodes(node)
}

const highlightRelatedNodes = (node) => {
  const relatedNodeIds = new Set()
  const relatedRelationIds = new Set()

  relations.value.forEach(relation => {
    if (relation.source === node.id || relation.target === node.id) {
      relatedRelationIds.add(relation.id)
      relatedNodeIds.add(relation.source)
      relatedNodeIds.add(relation.target)
    }
  })

  highlightedNodes.value = Array.from(relatedNodeIds)
  highlightedRelations.value = Array.from(relatedRelationIds)
  isDimmed.value = true
}

const clearSelection = () => {
  selectedNode.value = null
  highlightedNodes.value = []
  highlightedRelations.value = []
  isDimmed.value = false
}

const handleNodeHover = (node, isEnter) => {
  hoveredNode.value = isEnter ? node : null
}

const getNodeStyle = (node) => ({
  left: node.x + 'px',
  top: node.y + 'px',
  zIndex: selectedNode.value?.id === node.id ? 1000 : node.layer * 10
})

const getNodeSize = (node) => {
  const sizes = { server: 32, application: 28, database: 30, network: 26, storage: 28 }
  return sizes[node.type] || 24
}

const getNodeIcon = (type) => {
  const icons = {
    server: 'Monitor',
    application: 'Grid',
    database: 'Coin',
    network: 'Connection',
    storage: 'Box'
  }
  return icons[type] || 'Box'
}

const getRelationColor = (type) => {
  const colors = {
    depends: '#F44336',
    connects: '#4CAF50',
    hosts: '#2196F3',
    uses: '#FF9800',
    manages: '#9C27B0'
  }
  return colors[type] || '#666'
}

const getTypeText = (type) => {
  const types = {
    server: '服务器',
    application: '应用系统',
    database: '数据库',
    network: '网络设备',
    storage: '存储设备'
  }
  return types[type] || type
}

const getStatusType = (status) => {
  const types = {
    normal: 'success',
    warning: 'warning',
    error: 'danger',
    offline: 'info'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    normal: '正常',
    warning: '告警',
    error: '故障',
    offline: '离线'
  }
  return texts[status] || status
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.1)
}

const resetZoom = () => {
  zoomLevel.value = 1
  panX.value = 0
  panY.value = 0
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const exportGraph = () => {
  ElMessage.success('图谱导出功能开发中...')
}

const loadSampleData = () => {
  loadGraphData()
}

const viewNodeDetails = () => {
  if (selectedNode.value) {
    ElMessage.info(`查看 ${selectedNode.value.name} 详情`)
  }
}

const analyzeImpact = () => {
  if (selectedNode.value) {
    ElMessage.info(`分析 ${selectedNode.value.name} 的影响范围`)
  }
}

const showRelatedNodes = () => {
  if (selectedNode.value) {
    ElMessage.info(`显示 ${selectedNode.value.name} 的关联节点`)
  }
}

const centerGraph = () => {
  panX.value = 0
  panY.value = 0
  ElMessage.success('图谱已居中')
}

const fitToScreen = () => {
  resetZoom()
  ElMessage.success('图谱已适应屏幕')
}

const toggleLegend = () => {
  showLegend.value = !showLegend.value
}

const toggleRelationLabels = () => {
  showRelationLabels.value = !showRelationLabels.value
}

// 布局设置相关方法
const resetLayoutSettings = () => {
  Object.assign(layoutSettings, {
    algorithm: 'force',
    nodeSpacing: 150,
    levelSpacing: 200,
    animation: true,
    autoFit: true
  })
}

const applyLayoutSettings = () => {
  ElMessage.success('布局设置已应用')

  // 根据设置重新计算节点位置
  if (layoutSettings.algorithm === 'force') {
    applyForceLayout()
  } else if (layoutSettings.algorithm === 'hierarchical') {
    applyHierarchicalLayout()
  } else if (layoutSettings.algorithm === 'circular') {
    applyCircularLayout()
  } else if (layoutSettings.algorithm === 'grid') {
    applyGridLayout()
  }

  showLayoutDialog.value = false
}

const applyForceLayout = () => {
  // 力导向布局算法
  const centerX = canvasWidth.value / 2
  const centerY = canvasHeight.value / 2

  nodes.value.forEach((node, index) => {
    const angle = (index / nodes.value.length) * 2 * Math.PI
    const radius = Math.min(centerX, centerY) * 0.6
    node.x = centerX + Math.cos(angle) * radius
    node.y = centerY + Math.sin(angle) * radius
  })

  calculateNodePositions()
}

const applyHierarchicalLayout = () => {
  // 层次布局算法
  const levels = {}
  nodes.value.forEach(node => {
    levels[node.layer || 1] = levels[node.layer || 1] || []
    levels[node.layer || 1].push(node)
  })

  Object.keys(levels).forEach((level, levelIndex) => {
    const nodesInLevel = levels[level]
    const y = 100 + levelIndex * layoutSettings.levelSpacing

    nodesInLevel.forEach((node, nodeIndex) => {
      node.x = 100 + nodeIndex * layoutSettings.nodeSpacing
      node.y = y
    })
  })

  calculateNodePositions()
}

const applyCircularLayout = () => {
  // 圆形布局算法
  const centerX = canvasWidth.value / 2
  const centerY = canvasHeight.value / 2
  const radius = Math.min(centerX, centerY) * 0.7

  nodes.value.forEach((node, index) => {
    const angle = (index / nodes.value.length) * 2 * Math.PI
    node.x = centerX + Math.cos(angle) * radius
    node.y = centerY + Math.sin(angle) * radius
  })

  calculateNodePositions()
}

const applyGridLayout = () => {
  // 网格布局算法
  const cols = Math.ceil(Math.sqrt(nodes.value.length))
  const rows = Math.ceil(nodes.value.length / cols)

  nodes.value.forEach((node, index) => {
    const col = index % cols
    const row = Math.floor(index / cols)
    node.x = 100 + col * layoutSettings.nodeSpacing
    node.y = 100 + row * layoutSettings.nodeSpacing
  })

  calculateNodePositions()
}

// 筛选设置相关方法
const resetFilterSettings = () => {
  Object.assign(filterSettings, {
    types: ['server', 'application', 'database', 'network', 'storage'],
    statuses: ['normal', 'warning', 'error', 'offline'],
    relationTypes: ['depends', 'connects', 'hosts', 'uses', 'manages'],
    dateRange: [],
    keyword: ''
  })
}

const applyFilterSettings = () => {
  ElMessage.success('筛选条件已应用')

  // 应用筛选逻辑
  applyFilters()
  showFilterDialog.value = false
}

const applyFilters = () => {
  // 这里可以添加更复杂的筛选逻辑
  // 当前的筛选逻辑已经在计算属性 visibleNodes 和 visibleRelations 中实现
  console.log('应用筛选条件:', filterSettings)
}

// 生命周期
onMounted(() => {
  loadGraphData()
})
</script>

<style scoped>
.ci-graph {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  color: #3b82f6;
}

.page-description {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 工具栏 */
.graph-toolbar {
  margin-bottom: 24px;
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.view-controls,
.search-controls,
.graph-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 图谱容器 */
.graph-container {
  height: 600px;
  position: relative;
  overflow: hidden;
}

.graph-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background: #fafbfc;
  border-radius: 4px;
}

.graph-canvas.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: white;
}

.graph-viewport {
  width: 100%;
  height: 100%;
  position: relative;
  transition: transform 0.3s ease;
}

/* 配置项节点 */
.ci-node {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: white;
  border: 2px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ci-node:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ci-node.node-selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ci-node.node-highlighted {
  border-color: #10b981;
  background: #ecfdf5;
}

.ci-node.node-dimmed {
  opacity: 0.3;
}

/* 节点类型样式 */
.node-server {
  border-color: #4CAF50;
}

.node-application {
  border-color: #2196F3;
}

.node-database {
  border-color: #FF9800;
}

.node-network {
  border-color: #9C27B0;
}

.node-storage {
  border-color: #607D8B;
}

.node-icon {
  margin-bottom: 4px;
}

.node-label {
  font-size: 10px;
  color: #374151;
  text-align: center;
  max-width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-status-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-normal {
  background: #4CAF50;
}

.status-warning {
  background: #FF9800;
}

.status-error {
  background: #F44336;
}

.status-offline {
  background: #9E9E9E;
}

.node-stats {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ci-node:hover .node-stats {
  opacity: 1;
}

/* 关系连线 */
.graph-connections {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.relation-label {
  font-size: 10px;
  fill: #6b7280;
  text-anchor: middle;
  dominant-baseline: middle;
}

/* 关系类型样式 */
path.relation-depends {
  stroke: #F44336;
}

path.relation-connects {
  stroke: #4CAF50;
}

path.relation-hosts {
  stroke: #2196F3;
}

path.relation-uses {
  stroke: #FF9800;
}

path.relation-manages {
  stroke: #9C27B0;
}

path.relation-highlighted {
  stroke-width: 3 !important;
  opacity: 1;
}

path.relation-dimmed {
  opacity: 0.2;
}

/* 加载和空状态 */
.graph-loading,
.graph-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #6b7280;
}

.graph-loading .el-icon {
  margin-bottom: 16px;
}

.graph-empty .el-icon {
  margin-bottom: 16px;
  color: #d1d5db;
}

.graph-empty h3 {
  margin: 16px 0 8px 0;
  color: #374151;
}

.graph-empty p {
  margin: 0 0 16px 0;
}

/* 缩放控制 */
.zoom-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.zoom-level {
  font-size: 12px;
  color: #6b7280;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

/* 图例 */
.graph-legend {
  position: absolute;
  top: 20px;
  left: 20px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.legend-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

/* 侧边栏信息面板 */
.info-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.node-details .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.node-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 500;
  color: #374151;
  min-width: 60px;
}

.node-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.graph-stats .stats-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
}

.quick-actions .action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-actions .action-buttons .el-button {
  width: 100%;
  justify-content: flex-start;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content .el-col:last-child {
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .ci-graph {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .toolbar-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .view-controls,
  .search-controls,
  .graph-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .graph-container {
    height: 400px;
  }

  .ci-node {
    width: 50px;
    height: 50px;
  }

  .node-label {
    font-size: 9px;
    max-width: 40px;
  }

  .graph-legend {
    position: relative;
    margin-bottom: 16px;
  }

  .zoom-controls {
    bottom: 10px;
    right: 10px;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .search-controls {
    flex-direction: column;
  }

  .search-controls .el-input,
  .search-controls .el-select {
    width: 100% !important;
  }

  .graph-container {
    height: 300px;
  }

  .ci-node {
    width: 40px;
    height: 40px;
  }

  .node-label {
    font-size: 8px;
    max-width: 30px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .node-actions {
    flex-direction: column;
  }

  .node-actions .el-button {
    width: 100%;
  }
}

/* 动画效果 */
.ci-node,
.graph-legend,
.zoom-controls {
  transition: all 0.3s ease;
}

.ci-node:hover {
  transform: scale(1.1);
}

.graph-viewport {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 自定义滚动条 */
.info-panel::-webkit-scrollbar {
  width: 6px;
}

.info-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.info-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.info-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
