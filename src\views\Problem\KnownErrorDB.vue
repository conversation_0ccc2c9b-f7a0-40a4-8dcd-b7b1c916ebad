<template>
  <div class="known-error-db">
    <div class="page-header">
      <div class="header-info">
        <h2>已知错误库</h2>
        <p>管理已知错误和解决方案</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="addError">
          <el-icon><Plus /></el-icon>
          添加错误
        </el-button>
        <el-button @click="showStatsDialog = true">
          <el-icon><TrendCharts /></el-icon>
          统计分析
        </el-button>
        <el-button @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="exportErrors">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in errorStats" :key="stat.key">
          <el-card class="stat-card" :class="stat.status" @click="filterByStatus(stat.key)">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24" :color="stat.color">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" :class="stat.trend">
                  <el-icon><component :is="stat.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ stat.change }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" :inline="true">
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索错误标题、症状或解决方案"
              style="width: 250px"
              prefix-icon="Search"
              @input="searchErrors"
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="searchForm.category" placeholder="选择分类" style="width: 150px" @change="searchErrors">
              <el-option label="全部" value="" />
              <el-option label="系统故障" value="system" />
              <el-option label="网络问题" value="network" />
              <el-option label="应用错误" value="application" />
              <el-option label="硬件故障" value="hardware" />
            </el-select>
          </el-form-item>
          <el-form-item label="严重程度">
            <el-select v-model="searchForm.severity" placeholder="选择严重程度" style="width: 120px" @change="searchErrors">
              <el-option label="全部" value="" />
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" style="width: 120px" @change="searchErrors">
              <el-option label="全部" value="" />
              <el-option label="活跃" value="活跃" />
              <el-option label="已解决" value="已解决" />
              <el-option label="已归档" value="已归档" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchErrors">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button @click="showAdvancedSearch = !showAdvancedSearch">
              {{ showAdvancedSearch ? '简单搜索' : '高级搜索' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 高级搜索 -->
        <div v-if="showAdvancedSearch" class="advanced-search">
          <el-divider content-position="left">高级搜索</el-divider>
          <el-form :model="advancedSearchForm" :inline="true">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="advancedSearchForm.createDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item label="更新时间">
              <el-date-picker
                v-model="advancedSearchForm.updateDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item label="关键词匹配">
              <el-select v-model="advancedSearchForm.matchType" style="width: 120px">
                <el-option label="包含" value="contains" />
                <el-option label="精确匹配" value="exact" />
                <el-option label="正则表达式" value="regex" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序方式">
              <el-select v-model="advancedSearchForm.sortBy" style="width: 150px">
                <el-option label="创建时间" value="createTime" />
                <el-option label="更新时间" value="updateTime" />
                <el-option label="严重程度" value="severity" />
                <el-option label="标题" value="title" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序顺序">
              <el-select v-model="advancedSearchForm.sortOrder" style="width: 100px">
                <el-option label="升序" value="asc" />
                <el-option label="降序" value="desc" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 错误列表 -->
    <div class="error-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>已知错误列表 ({{ filteredErrors.length }})</span>
            <div class="header-actions">
              <el-button type="primary" size="small" @click="addError">
                <el-icon><Plus /></el-icon>
                添加错误
              </el-button>
              <el-button size="small" @click="exportErrors">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </div>
        </template>

        <el-table :data="paginatedErrors" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="错误ID" width="120" />
          <el-table-column prop="title" label="错误标题" min-width="200">
            <template #default="scope">
              <el-link type="primary" @click="viewError(scope.row)">
                {{ scope.row.title }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="分类" width="120">
            <template #default="scope">
              <el-tag :type="getCategoryColor(scope.row.category)" size="small">
                {{ getCategoryText(scope.row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="severity" label="严重程度" width="100">
            <template #default="scope">
              <el-tag :type="getSeverityColor(scope.row.severity)" size="small">
                {{ getSeverityText(scope.row.severity) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column prop="updateTime" label="更新时间" width="160" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" size="small" text @click="viewError(scope.row)">
                查看
              </el-button>
              <el-button type="primary" size="small" text @click="editError(scope.row)">
                编辑
              </el-button>
              <el-button type="success" size="small" text @click="useWorkaround(scope.row)">
                应用解决方案
              </el-button>
              <el-dropdown @command="handleErrorCommand">
                <el-button type="primary" size="small" text>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'duplicate', error: scope.row}">复制</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'archive', error: scope.row}">归档</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'export', error: scope.row}">导出</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'history', error: scope.row}">查看历史</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'delete', error: scope.row}" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredErrors.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加/编辑错误对话框 -->
    <el-dialog
      v-model="showErrorDialog"
      :title="editingError ? '编辑已知错误' : '添加已知错误'"
      width="70%"
      :before-close="handleClose"
    >
      <el-form :model="errorForm" :rules="errorRules" ref="errorFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="错误标题" prop="title">
              <el-input v-model="errorForm.title" placeholder="请输入错误标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="errorForm.category" placeholder="请选择分类" style="width: 100%">
                <el-option label="系统故障" value="system" />
                <el-option label="网络问题" value="network" />
                <el-option label="应用错误" value="application" />
                <el-option label="硬件故障" value="hardware" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="严重程度" prop="severity">
              <el-select v-model="errorForm.severity" placeholder="请选择严重程度" style="width: 100%">
                <el-option label="高" value="high" />
                <el-option label="中" value="medium" />
                <el-option label="低" value="low" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="errorForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="活跃" value="活跃" />
                <el-option label="已解决" value="已解决" />
                <el-option label="已归档" value="已归档" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="错误症状" prop="symptoms">
          <el-input
            v-model="errorForm.symptoms"
            type="textarea"
            :rows="4"
            placeholder="请详细描述错误的症状和表现"
          />
        </el-form-item>

        <el-form-item label="根本原因" prop="rootCause">
          <el-input
            v-model="errorForm.rootCause"
            type="textarea"
            :rows="3"
            placeholder="请描述错误的根本原因"
          />
        </el-form-item>

        <el-form-item label="临时解决方案" prop="workaround">
          <el-input
            v-model="errorForm.workaround"
            type="textarea"
            :rows="4"
            placeholder="请提供临时解决方案或变通方法"
          />
        </el-form-item>

        <el-form-item label="永久解决方案" prop="permanentSolution">
          <el-input
            v-model="errorForm.permanentSolution"
            type="textarea"
            :rows="4"
            placeholder="请提供永久解决方案（如果有）"
          />
        </el-form-item>

        <el-form-item label="相关文档" prop="relatedDocs">
          <el-input
            v-model="errorForm.relatedDocs"
            placeholder="相关文档链接或参考资料"
          />
        </el-form-item>

        <el-form-item label="关键词" prop="keywords">
          <el-input
            v-model="errorForm.keywords"
            placeholder="用于搜索的关键词，用逗号分隔"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showErrorDialog = false">取消</el-button>
          <el-button type="primary" @click="saveError">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看错误详情对话框 -->
    <el-dialog v-model="showViewDialog" title="错误详情" width="60%">
      <div v-if="viewingError" class="error-details">
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="错误ID">{{ viewingError.id }}</el-descriptions-item>
            <el-descriptions-item label="标题">{{ viewingError.title }}</el-descriptions-item>
            <el-descriptions-item label="分类">
              <el-tag :type="getCategoryColor(viewingError.category)" size="small">
                {{ getCategoryText(viewingError.category) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="严重程度">
              <el-tag :type="getSeverityColor(viewingError.severity)" size="small">
                {{ getSeverityText(viewingError.severity) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusColor(viewingError.status)" size="small">
                {{ viewingError.status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ viewingError.createTime }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h3>错误症状</h3>
          <p>{{ viewingError.symptoms }}</p>
        </div>

        <div class="detail-section">
          <h3>根本原因</h3>
          <p>{{ viewingError.rootCause }}</p>
        </div>

        <div class="detail-section">
          <h3>临时解决方案</h3>
          <p>{{ viewingError.workaround }}</p>
        </div>

        <div class="detail-section" v-if="viewingError.permanentSolution">
          <h3>永久解决方案</h3>
          <p>{{ viewingError.permanentSolution }}</p>
        </div>

        <div class="detail-section" v-if="viewingError.relatedDocs">
          <h3>相关文档</h3>
          <el-link type="primary" :href="viewingError.relatedDocs" target="_blank">
            {{ viewingError.relatedDocs }}
          </el-link>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showViewDialog = false">关闭</el-button>
          <el-button type="primary" @click="editError(viewingError)">编辑</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog v-model="showStatsDialog" title="错误库统计分析" width="80%">
      <div class="stats-content">
        <el-tabs v-model="activeStatsTab">
          <el-tab-pane label="总体统计" name="overview">
            <div class="overview-stats">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-statistic title="总错误数" :value="errors.length" />
                </el-col>
                <el-col :span="8">
                  <el-statistic title="活跃错误" :value="activeErrorsCount" />
                </el-col>
                <el-col :span="8">
                  <el-statistic title="已解决错误" :value="resolvedErrorsCount" />
                </el-col>
              </el-row>

              <div class="charts-section">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="chart-container" ref="categoryChart"></div>
                  </el-col>
                  <el-col :span="12">
                    <div class="chart-container" ref="severityChart"></div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="趋势分析" name="trend">
            <div class="trend-analysis">
              <div class="chart-container" ref="trendChart"></div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="热门错误" name="popular">
            <div class="popular-errors">
              <el-table :data="popularErrors" style="width: 100%">
                <el-table-column prop="title" label="错误标题" />
                <el-table-column prop="viewCount" label="查看次数" width="120" />
                <el-table-column prop="useCount" label="应用次数" width="120" />
                <el-table-column prop="lastUsed" label="最后使用" width="160" />
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <el-button @click="showStatsDialog = false">关闭</el-button>
        <el-button type="primary" @click="exportStatsReport">导出报告</el-button>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog v-model="showImportDialog" title="批量导入错误" width="60%">
      <div class="import-content">
        <el-steps :active="importStep" finish-status="success">
          <el-step title="选择文件" description="上传Excel或CSV文件" />
          <el-step title="数据预览" description="预览导入数据" />
          <el-step title="导入完成" description="确认导入结果" />
        </el-steps>

        <div class="import-step-content">
          <!-- 步骤1：文件上传 -->
          <div v-if="importStep === 0" class="upload-step">
            <el-upload
              class="upload-dragger"
              drag
              :auto-upload="false"
              :on-change="handleFileChange"
              accept=".xlsx,.xls,.csv"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传 xlsx/xls/csv 文件，且不超过 10MB
                </div>
              </template>
            </el-upload>

            <div class="template-download">
              <el-divider content-position="center">或</el-divider>
              <el-button type="text" @click="downloadTemplate">
                <el-icon><Download /></el-icon>
                下载导入模板
              </el-button>
            </div>
          </div>

          <!-- 步骤2：数据预览 -->
          <div v-if="importStep === 1" class="preview-step">
            <div class="preview-info">
              <el-alert
                title="数据预览"
                :description="`共检测到 ${importPreviewData.length} 条记录，请确认数据格式正确`"
                type="info"
                show-icon
                :closable="false"
              />
            </div>

            <el-table :data="importPreviewData.slice(0, 10)" style="width: 100%" max-height="400">
              <el-table-column prop="title" label="错误标题" />
              <el-table-column prop="category" label="分类" width="120" />
              <el-table-column prop="severity" label="严重程度" width="100" />
              <el-table-column prop="symptoms" label="症状" show-overflow-tooltip />
            </el-table>

            <div v-if="importPreviewData.length > 10" class="preview-more">
              <el-text type="info">仅显示前10条记录，共 {{ importPreviewData.length }} 条</el-text>
            </div>
          </div>

          <!-- 步骤3：导入结果 -->
          <div v-if="importStep === 2" class="result-step">
            <el-result
              icon="success"
              title="导入完成"
              :sub-title="`成功导入 ${importResult.success} 条记录，失败 ${importResult.failed} 条`"
            >
              <template #extra>
                <el-button type="primary" @click="finishImport">完成</el-button>
              </template>
            </el-result>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button v-if="importStep === 0" type="primary" @click="nextImportStep" :disabled="!importFile">
          下一步
        </el-button>
        <el-button v-if="importStep === 1" @click="prevImportStep">上一步</el-button>
        <el-button v-if="importStep === 1" type="primary" @click="startImport" :loading="importing">
          开始导入
        </el-button>
      </template>
    </el-dialog>

    <!-- 解决方案应用对话框 -->
    <el-dialog v-model="showWorkaroundDialog" title="应用解决方案" width="50%">
      <div v-if="currentWorkaround" class="workaround-content">
        <div class="workaround-info">
          <h3>{{ currentWorkaround.title }}</h3>
          <el-tag :type="getSeverityColor(currentWorkaround.severity)" size="small">
            {{ getSeverityText(currentWorkaround.severity) }}
          </el-tag>
        </div>

        <div class="workaround-steps">
          <h4>临时解决方案步骤：</h4>
          <div class="steps-content">
            <pre>{{ currentWorkaround.workaround }}</pre>
          </div>
        </div>

        <div v-if="currentWorkaround.permanentSolution" class="permanent-solution">
          <h4>永久解决方案：</h4>
          <div class="solution-content">
            <pre>{{ currentWorkaround.permanentSolution }}</pre>
          </div>
        </div>

        <div class="workaround-feedback">
          <el-divider content-position="left">反馈</el-divider>
          <el-form :model="feedbackForm" label-width="100px">
            <el-form-item label="解决效果">
              <el-rate v-model="feedbackForm.rating" />
            </el-form-item>
            <el-form-item label="反馈意见">
              <el-input
                v-model="feedbackForm.comment"
                type="textarea"
                :rows="3"
                placeholder="请描述解决方案的使用效果和建议"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <el-button @click="showWorkaroundDialog = false">关闭</el-button>
        <el-button type="primary" @click="submitFeedback">提交反馈</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  severity: '',
  status: ''
})

// 高级搜索表单
const advancedSearchForm = reactive({
  createDateRange: [],
  updateDateRange: [],
  matchType: 'contains',
  sortBy: 'createTime',
  sortOrder: 'desc'
})

// 对话框状态
const showErrorDialog = ref(false)
const showViewDialog = ref(false)
const showStatsDialog = ref(false)
const showImportDialog = ref(false)
const showWorkaroundDialog = ref(false)
const showAdvancedSearch = ref(false)
const editingError = ref(null)
const viewingError = ref(null)
const currentWorkaround = ref(null)
const errorFormRef = ref()

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const loading = ref(false)

// 导入相关状态
const importing = ref(false)
const importStep = ref(0)
const importFile = ref(null)
const importPreviewData = ref([])
const importResult = ref({ success: 0, failed: 0 })
const activeStatsTab = ref('overview')

// 图表引用
const categoryChart = ref()
const severityChart = ref()
const trendChart = ref()

// 错误表单
const errorForm = reactive({
  title: '',
  category: '',
  severity: '',
  status: '活跃',
  symptoms: '',
  rootCause: '',
  workaround: '',
  permanentSolution: '',
  relatedDocs: '',
  keywords: ''
})

// 表单验证规则
const errorRules = {
  title: [
    { required: true, message: '请输入错误标题', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  severity: [
    { required: true, message: '请选择严重程度', trigger: 'change' }
  ],
  symptoms: [
    { required: true, message: '请输入错误症状', trigger: 'blur' }
  ],
  rootCause: [
    { required: true, message: '请输入根本原因', trigger: 'blur' }
  ],
  workaround: [
    { required: true, message: '请输入临时解决方案', trigger: 'blur' }
  ]
}

// 统计数据
const errorStats = ref([
  {
    key: 'total',
    label: '总错误数',
    value: '156',
    icon: 'Document',
    color: '#1976D2',
    status: 'info',
    trend: 'up',
    change: '+8'
  },
  {
    key: 'active',
    label: '活跃错误',
    value: '45',
    icon: 'Warning',
    color: '#FF9800',
    status: 'warning',
    trend: 'down',
    change: '-3'
  },
  {
    key: 'resolved',
    label: '已解决',
    value: '98',
    icon: 'CircleCheck',
    color: '#4CAF50',
    status: 'success',
    trend: 'up',
    change: '+12'
  },
  {
    key: 'archived',
    label: '已归档',
    value: '13',
    icon: 'FolderOpened',
    color: '#666',
    status: 'info',
    trend: 'stable',
    change: '0'
  }
])

// 反馈表单
const feedbackForm = reactive({
  rating: 0,
  comment: ''
})

// 错误数据
const errors = ref([
  {
    id: 'KE-2025-001',
    title: 'Windows蓝屏错误0x0000007E',
    category: 'system',
    severity: 'high',
    status: '活跃',
    symptoms: '系统突然出现蓝屏，错误代码0x0000007E，通常在启动或运行特定程序时发生',
    rootCause: '系统文件损坏或驱动程序不兼容导致',
    workaround: '1. 重启系统进入安全模式\n2. 运行系统文件检查器(sfc /scannow)\n3. 更新或回滚最近安装的驱动程序',
    permanentSolution: '重新安装操作系统或修复系统文件',
    relatedDocs: 'https://docs.microsoft.com/windows/stop-errors',
    keywords: '蓝屏,0x0000007E,系统崩溃,驱动',
    createTime: '2025-01-25 10:30',
    updateTime: '2025-01-30 14:20',
    viewCount: 89,
    useCount: 25,
    lastUsed: '2025-01-30 14:20'
  },
  {
    id: 'KE-2025-002',
    title: '网络连接间歇性中断',
    category: 'network',
    severity: 'medium',
    status: '活跃',
    symptoms: '用户反映网络连接不稳定，每隔10-15分钟会断开连接，需要重新连接',
    rootCause: 'DHCP租约时间过短或网络设备过热导致',
    workaround: '1. 重启网络设备\n2. 手动设置静态IP地址\n3. 检查网线连接',
    permanentSolution: '调整DHCP租约时间，改善网络设备散热',
    relatedDocs: '',
    keywords: '网络中断,DHCP,连接不稳定',
    createTime: '2025-01-28 16:45',
    updateTime: '2025-01-29 09:15',
    viewCount: 42,
    useCount: 15,
    lastUsed: '2025-01-29 14:30'
  },
  {
    id: 'KE-2025-003',
    title: 'Oracle数据库连接超时',
    category: 'application',
    severity: 'high',
    status: '已解决',
    symptoms: '应用程序连接Oracle数据库时频繁出现超时错误，错误信息：ORA-12170',
    rootCause: '数据库连接池配置不当，最大连接数设置过低',
    workaround: '重启应用服务器，临时增加连接超时时间',
    permanentSolution: '调整数据库连接池参数，增加最大连接数和连接超时时间',
    relatedDocs: 'https://docs.oracle.com/database/connection-pooling',
    keywords: 'Oracle,连接超时,ORA-12170,连接池',
    createTime: '2025-01-20 14:20',
    updateTime: '2025-01-30 11:30'
  }
])

// 过滤后的错误
const filteredErrors = computed(() => {
  let result = errors.value

  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase()
    result = result.filter(error =>
      error.title.toLowerCase().includes(keyword) ||
      error.symptoms.toLowerCase().includes(keyword) ||
      error.workaround.toLowerCase().includes(keyword) ||
      error.keywords.toLowerCase().includes(keyword)
    )
  }

  if (searchForm.category) {
    result = result.filter(error => error.category === searchForm.category)
  }

  if (searchForm.severity) {
    result = result.filter(error => error.severity === searchForm.severity)
  }

  if (searchForm.status) {
    result = result.filter(error => error.status === searchForm.status)
  }

  return result
})

// 分页后的错误
const paginatedErrors = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredErrors.value.slice(start, end)
})

// 统计计算属性
const activeErrorsCount = computed(() => {
  return errors.value.filter(e => e.status === '活跃').length
})

const resolvedErrorsCount = computed(() => {
  return errors.value.filter(e => e.status === '已解决').length
})

const popularErrors = computed(() => {
  return errors.value
    .sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0))
    .slice(0, 10)
})

// 搜索错误
const searchErrors = () => {
  currentPage.value = 1
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  Object.keys(advancedSearchForm).forEach(key => {
    if (Array.isArray(advancedSearchForm[key])) {
      advancedSearchForm[key] = []
    } else {
      advancedSearchForm[key] = key === 'matchType' ? 'contains' :
                                key === 'sortBy' ? 'createTime' :
                                key === 'sortOrder' ? 'desc' : ''
    }
  })
  searchErrors()
}

// 按状态过滤
const filterByStatus = (status) => {
  if (status === 'total') {
    searchForm.status = ''
  } else {
    const statusMap = {
      'active': '活跃',
      'resolved': '已解决',
      'archived': '已归档'
    }
    searchForm.status = statusMap[status] || ''
  }
  searchErrors()
}

// 重置错误表单
const resetErrorForm = () => {
  Object.keys(errorForm).forEach(key => {
    errorForm[key] = key === 'status' ? '活跃' : ''
  })
}

// 导出错误
const exportErrors = () => {
  ElMessage.success('错误数据导出中...')
  // 这里可以实现导出逻辑
}

// 错误操作命令处理
const handleErrorCommand = (command) => {
  const { action, error } = command

  switch (action) {
    case 'duplicate':
      duplicateError(error)
      break
    case 'archive':
      archiveError(error)
      break
    case 'export':
      exportSingleError(error)
      break
    case 'history':
      viewErrorHistory(error)
      break
    case 'delete':
      deleteError(error)
      break
  }
}

const duplicateError = (error) => {
  const newError = {
    ...error,
    id: `KE-2025-${String(errors.value.length + 1).padStart(3, '0')}`,
    title: `${error.title} (副本)`,
    status: '活跃',
    createTime: new Date().toLocaleString(),
    updateTime: new Date().toLocaleString(),
    viewCount: 0,
    useCount: 0,
    lastUsed: ''
  }

  errors.value.unshift(newError)
  ElMessage.success('错误已复制')
}

const archiveError = (error) => {
  const index = errors.value.findIndex(e => e.id === error.id)
  if (index > -1) {
    errors.value[index].status = '已归档'
    errors.value[index].updateTime = new Date().toLocaleString()
    ElMessage.success('错误已归档')
  }
}

const exportSingleError = (error) => {
  ElMessage.success(`正在导出错误 ${error.id}...`)
}

const viewErrorHistory = (error) => {
  ElMessage.info(`查看错误 ${error.id} 的历史记录`)
}

// 添加错误
const addError = () => {
  editingError.value = null
  showErrorDialog.value = true
  resetErrorForm()
}

// 编辑错误
const editError = (error) => {
  editingError.value = error
  showErrorDialog.value = true
  showViewDialog.value = false

  // 填充表单
  Object.keys(errorForm).forEach(key => {
    errorForm[key] = error[key] || ''
  })
}

// 查看错误详情
const viewError = (error) => {
  viewingError.value = error
  showViewDialog.value = true
}

// 应用解决方案
const useWorkaround = (error) => {
  currentWorkaround.value = error
  showWorkaroundDialog.value = true

  // 增加使用次数和查看次数
  const index = errors.value.findIndex(e => e.id === error.id)
  if (index > -1) {
    errors.value[index].useCount = (errors.value[index].useCount || 0) + 1
    errors.value[index].viewCount = (errors.value[index].viewCount || 0) + 1
    errors.value[index].lastUsed = new Date().toLocaleString()
  }
}

// 提交反馈
const submitFeedback = () => {
  if (feedbackForm.rating === 0) {
    ElMessage.warning('请给出评分')
    return
  }

  ElMessage.success('反馈已提交，感谢您的评价')
  showWorkaroundDialog.value = false

  // 重置反馈表单
  feedbackForm.rating = 0
  feedbackForm.comment = ''
}

// 导入相关函数
const handleFileChange = (file) => {
  importFile.value = file
}

const nextImportStep = () => {
  if (importStep.value === 0 && importFile.value) {
    // 模拟文件解析
    importPreviewData.value = [
      {
        title: '示例错误1',
        category: '硬件',
        severity: 'high',
        symptoms: '示例症状描述'
      },
      {
        title: '示例错误2',
        category: '软件',
        severity: 'medium',
        symptoms: '示例症状描述'
      }
    ]
    importStep.value = 1
  }
}

const prevImportStep = () => {
  if (importStep.value > 0) {
    importStep.value--
  }
}

const startImport = () => {
  importing.value = true

  // 模拟导入过程
  setTimeout(() => {
    importResult.value = {
      success: importPreviewData.value.length - 1,
      failed: 1
    }

    importing.value = false
    importStep.value = 2

    // 添加导入的数据到错误列表
    importPreviewData.value.forEach((item, index) => {
      if (index < importPreviewData.value.length - 1) { // 模拟最后一个失败
        const newError = {
          id: `KE-2025-${String(errors.value.length + index + 1).padStart(3, '0')}`,
          title: item.title,
          category: item.category,
          severity: item.severity,
          status: '活跃',
          symptoms: item.symptoms,
          rootCause: '待分析',
          workaround: '待补充',
          permanentSolution: '待制定',
          relatedDocs: '',
          keywords: '',
          createTime: new Date().toLocaleString(),
          updateTime: new Date().toLocaleString(),
          viewCount: 0,
          useCount: 0,
          lastUsed: ''
        }
        errors.value.unshift(newError)
      }
    })
  }, 3000)
}

const finishImport = () => {
  showImportDialog.value = false
  importStep.value = 0
  importFile.value = null
  importPreviewData.value = []
  ElMessage.success('导入完成')
}

const downloadTemplate = () => {
  ElMessage.success('正在下载导入模板...')
}

// 统计分析相关函数
const exportStatsReport = () => {
  ElMessage.success('正在导出统计报告...')
}

// 删除错误
const deleteError = (error) => {
  ElMessageBox.confirm(
    `确定要删除错误 "${error.title}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = errors.value.findIndex(item => item.id === error.id)
    if (index > -1) {
      errors.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}



// 保存错误
const saveError = () => {
  errorFormRef.value.validate((valid) => {
    if (valid) {
      if (editingError.value) {
        // 编辑模式
        Object.keys(errorForm).forEach(key => {
          editingError.value[key] = errorForm[key]
        })
        editingError.value.updateTime = new Date().toLocaleString()
        ElMessage.success('错误信息已更新')
      } else {
        // 新增模式
        const newError = {
          id: `KE-${Date.now()}`,
          ...errorForm,
          createTime: new Date().toLocaleString(),
          updateTime: new Date().toLocaleString()
        }
        errors.value.unshift(newError)
        ElMessage.success('错误已添加')
      }

      showErrorDialog.value = false
    }
  })
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 获取分类颜色
const getCategoryColor = (category) => {
  const colorMap = {
    'system': 'danger',
    'network': 'warning',
    'application': 'primary',
    'hardware': 'info'
  }
  return colorMap[category] || 'info'
}

// 获取分类文本
const getCategoryText = (category) => {
  const textMap = {
    'system': '系统故障',
    'network': '网络问题',
    'application': '应用错误',
    'hardware': '硬件故障'
  }
  return textMap[category] || category
}

// 获取严重程度颜色
const getSeverityColor = (severity) => {
  const colorMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'success'
  }
  return colorMap[severity] || 'info'
}

// 获取严重程度文本
const getSeverityText = (severity) => {
  const textMap = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return textMap[severity] || severity
}

// 初始化图表
const initCharts = () => {
  // 分类分布图
  if (categoryChart.value) {
    const categoryChartInstance = echarts.init(categoryChart.value)
    categoryChartInstance.setOption({
      title: {
        text: '错误分类分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '错误分类',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 45, name: '硬件' },
            { value: 38, name: '软件' },
            { value: 32, name: '网络' },
            { value: 25, name: '系统' },
            { value: 16, name: '其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }

  // 严重程度分布图
  if (severityChart.value) {
    const severityChartInstance = echarts.init(severityChart.value)
    severityChartInstance.setOption({
      title: {
        text: '严重程度分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '严重程度',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 25, name: '高' },
            { value: 68, name: '中' },
            { value: 63, name: '低' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })
  }

  // 趋势图
  if (trendChart.value) {
    const trendChartInstance = echarts.init(trendChart.value)
    trendChartInstance.setOption({
      title: {
        text: '错误趋势分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新增错误', '解决错误', '累计错误']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新增错误',
          type: 'line',
          data: [8, 12, 15, 18, 22, 25],
          itemStyle: { color: '#FF9800' }
        },
        {
          name: '解决错误',
          type: 'line',
          data: [5, 8, 12, 15, 18, 20],
          itemStyle: { color: '#4CAF50' }
        },
        {
          name: '累计错误',
          type: 'bar',
          data: [45, 49, 52, 55, 59, 64],
          itemStyle: { color: '#1976D2' }
        }
      ]
    })
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '活跃': 'success',
    '已解决': 'info',
    '已归档': 'warning'
  }
  return colorMap[status] || 'info'
}

// 对话框关闭处理
const handleClose = (done) => {
  ElMessageBox.confirm('确认关闭？未保存的内容将丢失。')
    .then(() => {
      done()
    })
    .catch(() => {
      // 取消关闭
    })
}
</script>

<style scoped>
.known-error-db {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-info h2 {
  color: #1976D2;
  margin: 0 0 8px 0;
}

.header-info p {
  color: #616161;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 统计概览样式 */
.stats-overview {
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.info {
  border-left: 4px solid #1976D2;
}

.stat-card.warning {
  border-left: 4px solid #FF9800;
}

.stat-card.success {
  border-left: 4px solid #4CAF50;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #616161;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-trend.up {
  color: #4CAF50;
}

.stat-trend.down {
  color: #F44336;
}

.stat-trend.stable {
  color: #666;
}

/* 高级搜索样式 */
.advanced-search {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 20px;
}

/* 错误列表样式 */
.error-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 统计分析对话框样式 */
.stats-content {
  padding: 20px 0;
}

.overview-stats {
  margin-bottom: 30px;
}

.charts-section {
  margin-top: 30px;
}

.chart-container {
  height: 300px;
  margin-top: 20px;
}

.trend-analysis {
  padding: 20px 0;
}

.popular-errors {
  padding: 20px 0;
}

/* 导入对话框样式 */
.import-content {
  padding: 20px 0;
}

.import-step-content {
  margin-top: 30px;
  min-height: 300px;
}

.upload-step {
  text-align: center;
  padding: 40px 0;
}

.upload-dragger {
  width: 100%;
}

.template-download {
  margin-top: 30px;
}

.preview-step {
  padding: 20px 0;
}

.preview-info {
  margin-bottom: 20px;
}

.preview-more {
  text-align: center;
  margin-top: 16px;
}

.result-step {
  padding: 40px 0;
  text-align: center;
}

/* 解决方案应用对话框样式 */
.workaround-content {
  padding: 20px 0;
}

.workaround-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.workaround-info h3 {
  margin: 0;
  color: #333;
}

.workaround-steps,
.permanent-solution {
  margin-bottom: 20px;
}

.workaround-steps h4,
.permanent-solution h4 {
  color: #333;
  margin-bottom: 12px;
}

.steps-content,
.solution-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1976D2;
}

.steps-content pre,
.solution-content pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
  color: #333;
}

.workaround-feedback {
  margin-top: 20px;
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 错误详情样式 */
.error-details {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  color: #333;
  margin-bottom: 12px;
  font-size: 16px;
  border-bottom: 2px solid #1976D2;
  padding-bottom: 8px;
}

.detail-section p {
  color: #666;
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格链接样式 */
.el-table .el-link {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .known-error-db {
    padding: 12px;
  }

  .search-section .el-form {
    flex-direction: column;
  }

  .search-section .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .header-actions .el-button {
    width: 100%;
  }

  /* 表格在移动端的优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-button {
    padding: 4px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .pagination-wrapper {
    overflow-x: auto;
  }

  .detail-section h3 {
    font-size: 14px;
  }

  .detail-section p {
    font-size: 13px;
  }
}

/* 表单优化 */
.el-form-item {
  margin-bottom: 18px;
}

.el-textarea {
  font-family: inherit;
}

/* 描述列表优化 */
.el-descriptions {
  margin-bottom: 20px;
}

/* 标签样式优化 */
.el-tag {
  font-weight: 500;
}

/* 链接样式 */
.el-link {
  font-weight: 500;
}

/* 加载状态 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}
</style>
