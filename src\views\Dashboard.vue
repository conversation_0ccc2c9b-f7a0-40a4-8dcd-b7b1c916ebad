<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>ITSM服务管理平台</h1>
      <p class="subtitle">智能化IT服务全生命周期管理</p>
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="metric in metrics" :key="metric.key">
          <el-card class="metric-card" :class="metric.type">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon :size="32">
                  <component :is="metric.icon" />
                </el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-trend" :class="metric.trend">
                  <el-icon :size="12">
                    <component :is="metric.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                  {{ metric.change }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 工单趋势图 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>工单趋势分析</span>
                <el-button type="text" size="small">查看详情</el-button>
              </div>
            </template>
            <div class="chart-container" ref="ticketTrendChart"></div>
          </el-card>
        </el-col>

        <!-- SLA达成率 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>SLA达成率</span>
                <el-button type="text" size="small">查看详情</el-button>
              </div>
            </template>
            <div class="chart-container" ref="slaChart"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 事件分类分布 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>事件分类分布</span>
              </div>
            </template>
            <div class="chart-container" ref="incidentCategoryChart"></div>
          </el-card>
        </el-col>

        <!-- 服务健康状态 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>服务健康状态</span>
              </div>
            </template>
            <div class="service-health">
              <div v-for="service in services" :key="service.name" class="service-item">
                <div class="service-name">{{ service.name }}</div>
                <div class="service-status">
                  <el-tag :type="service.status === 'healthy' ? 'success' : service.status === 'warning' ? 'warning' : 'danger'">
                    {{ service.statusText }}
                  </el-tag>
                  <span class="service-uptime">{{ service.uptime }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 待处理任务 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>待处理任务</span>
                <el-button type="text" size="small">查看全部</el-button>
              </div>
            </template>
            <div class="task-list">
              <div v-for="task in pendingTasks" :key="task.id" class="task-item">
                <div class="task-info">
                  <div class="task-title">{{ task.title }}</div>
                  <div class="task-meta">
                    <el-tag size="small" :type="task.priority === 'high' ? 'danger' : task.priority === 'medium' ? 'warning' : 'info'">
                      {{ task.priorityText }}
                    </el-tag>
                    <span class="task-time">{{ task.time }}</span>
                  </div>
                </div>
                <el-button type="primary" size="small" text>处理</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getDashboardData, getRealTimeStats } from '@/api/dashboardApi.js'
import { ElMessage } from 'element-plus'

// 关键指标数据
const metrics = ref([
  {
    key: 'activeTickets',
    label: '活跃工单',
    value: '156',
    change: '12%',
    trend: 'up',
    type: 'primary',
    icon: 'Document'
  },
  {
    key: 'slaCompliance',
    label: 'SLA达成率',
    value: '98.5%',
    change: '2.1%',
    trend: 'up',
    type: 'success',
    icon: 'SuccessFilled'
  },
  {
    key: 'mttr',
    label: '平均修复时间',
    value: '2.3h',
    change: '15%',
    trend: 'down',
    type: 'warning',
    icon: 'Timer'
  },
  {
    key: 'satisfaction',
    label: '用户满意度',
    value: '4.6/5',
    change: '0.2',
    trend: 'up',
    type: 'info',
    icon: 'Star'
  }
])

// 服务健康状态
const services = ref([
  { name: 'ERP系统', status: 'healthy', statusText: '正常', uptime: '99.9%' },
  { name: '邮件服务', status: 'healthy', statusText: '正常', uptime: '99.8%' },
  { name: 'OA系统', status: 'warning', statusText: '警告', uptime: '98.5%' },
  { name: '数据库', status: 'healthy', statusText: '正常', uptime: '99.9%' },
  { name: '网络服务', status: 'healthy', statusText: '正常', uptime: '99.7%' }
])

// 待处理任务
const pendingTasks = ref([
  {
    id: 1,
    title: '服务器CPU使用率过高',
    priority: 'high',
    priorityText: '高',
    time: '2分钟前'
  },
  {
    id: 2,
    title: '用户权限申请审批',
    priority: 'medium',
    priorityText: '中',
    time: '15分钟前'
  },
  {
    id: 3,
    title: '打印机故障报修',
    priority: 'low',
    priorityText: '低',
    time: '1小时前'
  },
  {
    id: 4,
    title: '系统变更审批',
    priority: 'medium',
    priorityText: '中',
    time: '2小时前'
  }
])

// 图表引用
const ticketTrendChart = ref(null)
const slaChart = ref(null)
const incidentCategoryChart = ref(null)

// 初始化图表
const initCharts = (chartsData) => {
  if (!chartsData) {
    // 使用默认数据
    chartsData = {
      ticketTrend: {
        categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        series: [
          { name: '新建', data: [12, 19, 15, 22, 18, 8, 6] },
          { name: '处理中', data: [8, 12, 18, 15, 20, 12, 9] },
          { name: '已解决', data: [15, 18, 20, 25, 22, 15, 12] }
        ]
      },
      slaCompliance: { value: 98.5, target: 95 },
      incidentCategory: [
        { value: 35, name: '网络问题' },
        { value: 28, name: '应用故障' },
        { value: 22, name: '硬件故障' },
        { value: 15, name: '其他' }
      ]
    }
  }

  // 工单趋势图
  if (ticketTrendChart.value) {
    const ticketChart = echarts.init(ticketTrendChart.value)
    ticketChart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: chartsData.ticketTrend.series.map(s => s.name) },
      xAxis: { type: 'category', data: chartsData.ticketTrend.categories },
      yAxis: { type: 'value' },
      series: chartsData.ticketTrend.series.map(s => ({
        name: s.name,
        type: 'line',
        data: s.data,
        smooth: true
      }))
    })
  }

  // SLA达成率图表
  if (slaChart.value) {
    const slaChartInstance = echarts.init(slaChart.value)
    slaChartInstance.setOption({
      tooltip: { formatter: '{a} <br/>{b} : {c}%' },
      series: [{
        name: 'SLA达成率',
        type: 'gauge',
        detail: { formatter: '{value}%' },
        data: [{ value: chartsData.slaCompliance.value, name: 'SLA达成率' }]
      }]
    })
  }

  // 事件分类分布图
  if (incidentCategoryChart.value) {
    const categoryChart = echarts.init(incidentCategoryChart.value)
    categoryChart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        name: '事件分类',
        type: 'pie',
        radius: '50%',
        data: chartsData.incidentCategory
      }]
    })
  }
}

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    const response = await getDashboardData()
    if (response.code === 200) {
      metrics.value = response.data.metrics
      services.value = response.data.services
      pendingTasks.value = response.data.pendingTasks

      // 更新图表数据
      nextTick(() => {
        initCharts(response.data.charts)
      })
    }
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

// 加载实时统计
const loadRealTimeStats = async () => {
  try {
    const response = await getRealTimeStats()
    if (response.code === 200) {
      // 更新实时数据
      console.log('实时统计数据:', response.data)
    }
  } catch (error) {
    console.error('加载实时统计失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()

  // 设置定时刷新实时数据
  const timer = setInterval(loadRealTimeStats, 30000) // 30秒刷新一次

  // 组件卸载时清除定时器
  onUnmounted(() => {
    if (timer) {
      clearInterval(timer)
    }
  })
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  color: #1976D2;
  margin-bottom: 8px;
}

.subtitle {
  color: #616161;
  font-size: 16px;
}

.metrics-cards {
  margin-bottom: 30px;
}

.metric-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.metric-icon {
  margin-right: 16px;
  color: #1976D2;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.metric-label {
  color: #616161;
  font-size: 14px;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.metric-trend.up {
  color: #4CAF50;
}

.metric-trend.down {
  color: #F44336;
}

.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.service-health {
  padding: 10px 0;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-name {
  font-weight: 500;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-uptime {
  font-size: 12px;
  color: #616161;
}

.task-list {
  max-height: 300px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-time {
  font-size: 12px;
  color: #616161;
}
</style>
